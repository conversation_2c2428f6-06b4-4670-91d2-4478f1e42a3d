import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, timer, of } from 'rxjs';
import { map, catchError, tap, switchMap, finalize } from 'rxjs/operators';
import { Router } from '@angular/router';
import { 
  User, 
  LoginRequest, 
  LoginResponse, 
  RefreshTokenResponse, 
  AuthState, 
  JwtPayload,
  ApiResponse,
  SecurityEvent,
  SecurityEventType,
  SecurityEventSeverity
} from '../models/auth.models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly API_URL = environment.apiUrl;
  private readonly TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiration
  
  private authStateSubject = new BehaviorSubject<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: false,
    error: null
  });

  public authState$ = this.authStateSubject.asObservable();
  private refreshTokenTimer?: any;

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.initializeAuth();
  }

  /**
   * Initialize authentication state on service creation
   */
  private initializeAuth(): void {
    this.updateAuthState({ loading: true });
    
    // Check if user is already authenticated
    this.validateCurrentSession().subscribe({
      next: (isValid) => {
        if (isValid) {
          this.getCurrentUser().subscribe({
            next: (user) => {
              this.updateAuthState({
                isAuthenticated: true,
                user,
                loading: false,
                error: null
              });
              this.scheduleTokenRefresh();
            },
            error: () => {
              this.clearAuthState();
            }
          });
        } else {
          this.clearAuthState();
        }
      },
      error: () => {
        this.clearAuthState();
      }
    });
  }

  /**
   * Login user with credentials
   */
  login(credentials: LoginRequest): Observable<LoginResponse> {
    this.updateAuthState({ loading: true, error: null });

    return this.http.post<ApiResponse<LoginResponse>>(`${this.API_URL}/auth/login`, credentials, {
      withCredentials: true // Important for httpOnly cookies
    }).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.message || 'Login failed');
        }
        return response.data;
      }),
      tap(loginResponse => {
        this.updateAuthState({
          isAuthenticated: true,
          user: loginResponse.user,
          loading: false,
          error: null
        });
        
        this.scheduleTokenRefresh();
        this.logSecurityEvent(SecurityEventType.LOGIN_SUCCESS, 'User logged in successfully');
      }),
      catchError(error => {
        this.updateAuthState({
          isAuthenticated: false,
          user: null,
          loading: false,
          error: this.getErrorMessage(error)
        });
        
        this.logSecurityEvent(
          SecurityEventType.LOGIN_FAILURE, 
          `Login failed: ${this.getErrorMessage(error)}`,
          SecurityEventSeverity.MEDIUM
        );
        
        return throwError(() => error);
      })
    );
  }

  /**
   * Logout user and clear session
   */
  logout(): Observable<void> {
    return this.http.post<ApiResponse<void>>(`${this.API_URL}/auth/logout`, {}, {
      withCredentials: true
    }).pipe(
      tap(() => {
        this.logSecurityEvent(SecurityEventType.LOGOUT, 'User logged out');
      }),
      map(() => void 0), // Convert to void
      catchError(error => {
        console.error('Logout error:', error);
        // Even if logout fails on server, clear local state
        this.clearAuthState();
        this.router.navigate(['/login']);
        return of(void 0);
      }),
      finalize(() => {
        this.clearAuthState();
        this.router.navigate(['/login']);
      })
    );
  }

  /**
   * Refresh authentication token
   */
  refreshToken(): Observable<RefreshTokenResponse> {
    return this.http.post<ApiResponse<RefreshTokenResponse>>(`${this.API_URL}/auth/refresh`, {}, {
      withCredentials: true
    }).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error('Token refresh failed');
        }
        return response.data;
      }),
      tap(() => {
        this.scheduleTokenRefresh();
        this.logSecurityEvent(SecurityEventType.TOKEN_REFRESH, 'Token refreshed successfully');
      }),
      catchError(error => {
        console.error('Token refresh failed:', error);
        this.clearAuthState();
        this.router.navigate(['/login']);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get current user information
   */
  getCurrentUser(): Observable<User> {
    return this.http.get<ApiResponse<User>>(`${this.API_URL}/auth/me`, {
      withCredentials: true
    }).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error('Failed to get user information');
        }
        return response.data;
      })
    );
  }

  /**
   * Validate current session
   */
  validateCurrentSession(): Observable<boolean> {
    return this.http.get<ApiResponse<{ valid: boolean }>>(`${this.API_URL}/auth/validate`, {
      withCredentials: true
    }).pipe(
      map(response => response.success && response.data?.valid === true),
      catchError(() => {
        return throwError(() => false);
      })
    );
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(resource: string, action: string): boolean {
    const user = this.authStateSubject.value.user;
    if (!user) return false;

    // Admin has all permissions
    if (user.roles.some(role => role.name === 'admin')) {
      return true;
    }

    // Check specific permissions
    return user.permissions.some(permission => 
      permission.resource === resource && permission.action === action
    );
  }

  /**
   * Check if user has specific role
   */
  hasRole(roleName: string): boolean {
    const user = this.authStateSubject.value.user;
    return user?.roles.some(role => role.name === roleName) || false;
  }

  /**
   * Get current authentication state
   */
  getAuthState(): AuthState {
    return this.authStateSubject.value;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.authStateSubject.value.isAuthenticated;
  }

  /**
   * Get current user
   */
  getCurrentUserSync(): User | null {
    return this.authStateSubject.value.user;
  }

  /**
   * Schedule automatic token refresh
   */
  private scheduleTokenRefresh(): void {
    if (this.refreshTokenTimer) {
      clearTimeout(this.refreshTokenTimer);
    }

    // Schedule refresh 5 minutes before token expiration
    this.refreshTokenTimer = setTimeout(() => {
      this.refreshToken().subscribe({
        error: (error) => {
          console.error('Automatic token refresh failed:', error);
        }
      });
    }, this.TOKEN_REFRESH_THRESHOLD);
  }

  /**
   * Update authentication state
   */
  private updateAuthState(updates: Partial<AuthState>): void {
    const currentState = this.authStateSubject.value;
    this.authStateSubject.next({ ...currentState, ...updates });
  }

  /**
   * Clear authentication state
   */
  private clearAuthState(): void {
    if (this.refreshTokenTimer) {
      clearTimeout(this.refreshTokenTimer);
    }
    
    this.authStateSubject.next({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null
    });
  }

  /**
   * Log security events
   */
  private logSecurityEvent(
    eventType: SecurityEventType, 
    description: string, 
    severity: SecurityEventSeverity = SecurityEventSeverity.LOW
  ): void {
    const event: Partial<SecurityEvent> = {
      eventType,
      description,
      severity,
      timestamp: new Date()
    };

    // Send to backend for logging
    this.http.post(`${this.API_URL}/security/events`, event, {
      withCredentials: true
    }).subscribe({
      error: (error) => console.error('Failed to log security event:', error)
    });
  }

  /**
   * Extract error message from HTTP error
   */
  private getErrorMessage(error: any): string {
    if (error?.error?.message) {
      return error.error.message;
    }
    if (error?.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }
}
