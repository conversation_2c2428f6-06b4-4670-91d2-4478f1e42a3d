{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/language.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/tooltip\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/sidebar\";\nimport * as i6 from \"primeng/panelmenu\";\nimport * as i7 from \"primeng/button\";\nconst _c0 = [\"*\"];\nfunction LayoutComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"\\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 24);\n    i0.ɵɵtext(4, \"\\u0645\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LayoutComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"Ahmed Mohamed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 24);\n    i0.ɵɵtext(4, \"System Admin\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LayoutComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"i\", 5);\n    i0.ɵɵelementStart(2, \"span\", 26);\n    i0.ɵɵtext(3, \"Menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class LayoutComponent {\n  constructor(languageService) {\n    this.languageService = languageService;\n    this.sidebarVisible = false;\n    this.menuItems = [];\n  }\n  ngOnInit() {\n    this.initializeMenuItems();\n    // Subscribe to language changes to update menu items\n    this.languageService.currentLanguage$.subscribe(() => {\n      this.initializeMenuItems();\n    });\n  }\n  initializeMenuItems() {\n    this.menuItems = [{\n      label: this.languageService.translate('nav.dashboard'),\n      icon: 'pi pi-home',\n      routerLink: '/dashboard'\n    }, {\n      label: this.languageService.translate('nav.inventoryManagement'),\n      icon: 'pi pi-box',\n      expanded: true,\n      items: [{\n        label: this.languageService.translate('nav.items'),\n        icon: 'pi pi-list',\n        routerLink: '/items'\n      }, {\n        label: this.languageService.translate('nav.categories'),\n        icon: 'pi pi-sitemap',\n        routerLink: '/categories'\n      }, {\n        label: this.languageService.translate('nav.warehouses'),\n        icon: 'pi pi-building',\n        routerLink: '/warehouses'\n      }, {\n        label: this.languageService.translate('nav.stockMovements'),\n        icon: 'pi pi-arrows-h',\n        routerLink: '/inventory/movements'\n      }, {\n        label: this.languageService.translate('nav.stockAdjustments'),\n        icon: 'pi pi-pencil',\n        routerLink: '/inventory/adjustments'\n      }, {\n        label: this.languageService.translate('nav.transfers'),\n        icon: 'pi pi-send',\n        routerLink: '/inventory/transfers'\n      }]\n    }, {\n      label: this.languageService.translate('nav.salesPurchases'),\n      icon: 'pi pi-shopping-cart',\n      items: [{\n        label: this.languageService.translate('nav.salesInvoices'),\n        icon: 'pi pi-file',\n        routerLink: '/invoices/sales'\n      }, {\n        label: this.languageService.translate('nav.purchaseInvoices'),\n        icon: 'pi pi-file-import',\n        routerLink: '/invoices/purchases'\n      }, {\n        label: this.languageService.translate('nav.salesReturns'),\n        icon: 'pi pi-undo',\n        routerLink: '/invoices/sales-returns'\n      }, {\n        label: this.languageService.translate('nav.purchaseReturns'),\n        icon: 'pi pi-replay',\n        routerLink: '/invoices/purchase-returns'\n      }]\n    }, {\n      label: this.languageService.translate('nav.customersSuppliers'),\n      icon: 'pi pi-users',\n      items: [{\n        label: this.languageService.translate('nav.customers'),\n        icon: 'pi pi-user',\n        routerLink: '/customers'\n      }, {\n        label: this.languageService.translate('nav.suppliers'),\n        icon: 'pi pi-user-plus',\n        routerLink: '/suppliers'\n      }]\n    }, {\n      label: this.languageService.translate('nav.financialManagement'),\n      icon: 'pi pi-dollar',\n      items: [{\n        label: this.languageService.translate('nav.payments'),\n        icon: 'pi pi-credit-card',\n        routerLink: '/payments'\n      }, {\n        label: this.languageService.translate('nav.accountStatements'),\n        icon: 'pi pi-file-pdf',\n        routerLink: '/reports/statements'\n      }, {\n        label: this.languageService.translate('nav.cashRegister'),\n        icon: 'pi pi-wallet',\n        routerLink: '/cash-register'\n      }]\n    }, {\n      label: this.languageService.translate('nav.reports'),\n      icon: 'pi pi-chart-bar',\n      items: [{\n        label: this.languageService.translate('nav.inventoryReports'),\n        icon: 'pi pi-chart-line',\n        routerLink: '/reports/inventory'\n      }, {\n        label: this.languageService.translate('nav.financialReports'),\n        icon: 'pi pi-chart-pie',\n        routerLink: '/reports/financial'\n      }, {\n        label: this.languageService.translate('nav.salesReports'),\n        icon: 'pi pi-trending-up',\n        routerLink: '/reports/sales'\n      }, {\n        label: this.languageService.translate('nav.purchaseReports'),\n        icon: 'pi pi-trending-down',\n        routerLink: '/reports/purchases'\n      }]\n    }];\n  }\n  toggleSidebar() {\n    this.sidebarVisible = !this.sidebarVisible;\n  }\n  static {\n    this.ɵfac = function LayoutComponent_Factory(t) {\n      return new (t || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LanguageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutComponent,\n      selectors: [[\"app-layout\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 28,\n      vars: 13,\n      consts: [[1, \"layout-wrapper\"], [1, \"layout-topbar\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bars\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"pi\", \"pi-box\", \"text-2xl\", \"text-primary\"], [1, \"m-0\", \"text-xl\", \"font-semibold\"], [1, \"relative\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bell\", \"tooltipPosition\", \"bottom\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pTooltip\"], [1, \"notification-badge\"], [1, \"user-profile\", \"flex\", \"align-items-center\", \"gap-2\", \"p-2\", \"border-round\", \"cursor-pointer\", 2, \"border\", \"1px solid var(--surface-border)\"], [1, \"w-2rem\", \"h-2rem\", \"border-circle\", \"bg-primary\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"pi\", \"pi-user\", \"text-white\"], [\"class\", \"flex flex-column text-right\", 4, \"ngIf\"], [\"class\", \"flex flex-column\", 4, \"ngIf\"], [1, \"pi\", \"pi-chevron-down\", \"text-500\"], [1, \"layout-main\"], [1, \"layout-sidebar\", \"hidden-mobile\"], [3, \"model\", \"multiple\"], [\"position\", \"left\", \"styleClass\", \"layout-sidebar-mobile\", 3, \"visibleChange\", \"visible\", \"modal\", \"dismissible\"], [\"pTemplate\", \"header\"], [1, \"layout-content\"], [1, \"flex\", \"flex-column\", \"text-right\"], [1, \"text-sm\", \"font-medium\"], [1, \"text-xs\", \"text-500\"], [1, \"flex\", \"flex-column\"], [1, \"font-semibold\"]],\n      template: function LayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LayoutComponent_Template_button_click_3_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementStart(6, \"h2\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 2);\n          i0.ɵɵelement(9, \"app-language-switcher\");\n          i0.ɵɵelementStart(10, \"div\", 7);\n          i0.ɵɵelement(11, \"button\", 8);\n          i0.ɵɵelementStart(12, \"span\", 9);\n          i0.ɵɵtext(13, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11);\n          i0.ɵɵelement(16, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, LayoutComponent_div_17_Template, 5, 0, \"div\", 13)(18, LayoutComponent_div_18_Template, 5, 0, \"div\", 14);\n          i0.ɵɵelement(19, \"i\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"div\", 17);\n          i0.ɵɵelement(22, \"p-panelMenu\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p-sidebar\", 19);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LayoutComponent_Template_p_sidebar_visibleChange_23_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sidebarVisible, $event) || (ctx.sidebarVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(24, LayoutComponent_ng_template_24_Template, 4, 0, \"ng-template\", 20);\n          i0.ɵɵelement(25, \"p-panelMenu\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 21);\n          i0.ɵɵprojection(27);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"hidden-desktop\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"app.title\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"pTooltip\", ctx.languageService.translate(\"app.notifications\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.languageService.isArabic());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.languageService.isArabic());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.sidebarVisible);\n          i0.ɵɵproperty(\"modal\", true)(\"dismissible\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, RouterModule, MenubarModule, i3.Tooltip, i4.PrimeTemplate, SidebarModule, i5.Sidebar, PanelMenuModule, i6.PanelMenu, ButtonModule, i7.ButtonDirective, TooltipModule, LanguageSwitcherComponent],\n      styles: [\".layout-wrapper[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.rtl[_nghost-%COMP%]   .layout-wrapper[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-wrapper[_ngcontent-%COMP%] {\\n  direction: rtl;\\n}\\n\\n.layout-topbar[_ngcontent-%COMP%] {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-200);\\n  padding: 1rem 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: sticky;\\n  top: 0;\\n  z-index: 100;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.rtl[_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%] {\\n  flex-direction: row-reverse;\\n}\\n.layout-topbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.layout-topbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover {\\n  background: var(--surface-hover);\\n  transform: translateY(-1px);\\n}\\n.layout-topbar[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -4px;\\n  right: -4px;\\n  min-width: 18px;\\n  height: 18px;\\n  border-radius: 50%;\\n  background: #ef4444;\\n  color: white;\\n  font-size: 0.7rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n}\\n.rtl[_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%] {\\n  right: auto;\\n  left: -4px;\\n}\\n\\n.layout-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n}\\n\\n.layout-sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: var(--surface-0);\\n  border-right: 1px solid var(--surface-200);\\n  overflow-y: auto;\\n  height: calc(100vh - 73px);\\n  position: sticky;\\n  top: 73px;\\n}\\n.rtl[_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%] {\\n  border-right: none;\\n  border-left: 1px solid var(--surface-200);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu {\\n  border: none;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-panel {\\n  border: none;\\n  margin-bottom: 0;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header {\\n  border: none;\\n  border-radius: 0;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link {\\n  border: none;\\n  border-radius: 0;\\n  padding: 1rem 1.5rem;\\n  background: transparent;\\n  color: var(--text-color);\\n  transition: all 0.2s;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:hover {\\n  background: var(--surface-100);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:focus {\\n  box-shadow: none;\\n  background: var(--surface-100);\\n}\\n.rtl[_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link .p-panelmenu-icon, .rtl   [_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link .p-panelmenu-icon {\\n  margin-right: 0;\\n  margin-left: 0.5rem;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content {\\n  border: none;\\n  background: var(--surface-50);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link {\\n  padding: 0.75rem 1.5rem 0.75rem 3rem;\\n  color: var(--text-color-secondary);\\n  border: none;\\n  border-radius: 0;\\n  transition: all 0.2s;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link:hover {\\n  background: var(--surface-100);\\n  color: var(--text-color);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link.router-link-active {\\n  background: var(--primary-color);\\n  color: var(--primary-color-text);\\n}\\n\\n.layout-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1.5rem;\\n  overflow-x: auto;\\n  background: var(--surface-50);\\n}\\n\\n@media (max-width: 768px) {\\n  .hidden-mobile[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .layout-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n    .layout-sidebar-mobile {\\n    width: 280px !important;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .hidden-desktop[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .layout-content[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.75rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n    .layout-sidebar-mobile {\\n    width: 100% !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MenubarModule", "SidebarModule", "PanelMenuModule", "ButtonModule", "TooltipModule", "LanguageSwitcherComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LayoutComponent", "constructor", "languageService", "sidebarVisible", "menuItems", "ngOnInit", "initializeMenuItems", "currentLanguage$", "subscribe", "label", "translate", "icon", "routerLink", "expanded", "items", "toggleSidebar", "ɵɵdirectiveInject", "i1", "LanguageService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "LayoutComponent_Template", "rf", "ctx", "ɵɵlistener", "LayoutComponent_Template_button_click_3_listener", "ɵɵtemplate", "LayoutComponent_div_17_Template", "LayoutComponent_div_18_Template", "ɵɵtwoWayListener", "LayoutComponent_Template_p_sidebar_visibleChange_23_listener", "$event", "ɵɵtwoWayBindingSet", "LayoutComponent_ng_template_24_Template", "ɵɵprojection", "ɵɵadvance", "ɵɵclassProp", "ɵɵtextInterpolate", "ɵɵproperty", "isArabic", "ɵɵtwoWayProperty", "i2", "NgIf", "i3", "<PERSON><PERSON><PERSON>", "i4", "PrimeTemplate", "i5", "Sidebar", "i6", "PanelMenu", "i7", "ButtonDirective", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\shared\\components\\layout\\layout.component.ts", "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\shared\\components\\layout\\layout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { MenuItem } from 'primeng/api';\nimport { LanguageService } from '@core/services/language.service';\nimport { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';\n\n@Component({\n  selector: 'app-layout',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MenubarModule,\n    SidebarModule,\n    PanelMenuModule,\n    ButtonModule,\n    TooltipModule,\n    LanguageSwitcherComponent\n  ],\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.scss']\n})\nexport class LayoutComponent implements OnInit {\n  sidebarVisible = false;\n  menuItems: MenuItem[] = [];\n\n  constructor(public languageService: LanguageService) {}\n\n  ngOnInit() {\n    this.initializeMenuItems();\n\n    // Subscribe to language changes to update menu items\n    this.languageService.currentLanguage$.subscribe(() => {\n      this.initializeMenuItems();\n    });\n  }\n\n  private initializeMenuItems() {\n    this.menuItems = [\n      {\n        label: this.languageService.translate('nav.dashboard'),\n        icon: 'pi pi-home',\n        routerLink: '/dashboard'\n      },\n      {\n        label: this.languageService.translate('nav.inventoryManagement'),\n        icon: 'pi pi-box',\n        expanded: true,\n        items: [\n          {\n            label: this.languageService.translate('nav.items'),\n            icon: 'pi pi-list',\n            routerLink: '/items'\n          },\n          {\n            label: this.languageService.translate('nav.categories'),\n            icon: 'pi pi-sitemap',\n            routerLink: '/categories'\n          },\n          {\n            label: this.languageService.translate('nav.warehouses'),\n            icon: 'pi pi-building',\n            routerLink: '/warehouses'\n          },\n          {\n            label: this.languageService.translate('nav.stockMovements'),\n            icon: 'pi pi-arrows-h',\n            routerLink: '/inventory/movements'\n          },\n          {\n            label: this.languageService.translate('nav.stockAdjustments'),\n            icon: 'pi pi-pencil',\n            routerLink: '/inventory/adjustments'\n          },\n          {\n            label: this.languageService.translate('nav.transfers'),\n            icon: 'pi pi-send',\n            routerLink: '/inventory/transfers'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.salesPurchases'),\n        icon: 'pi pi-shopping-cart',\n        items: [\n          {\n            label: this.languageService.translate('nav.salesInvoices'),\n            icon: 'pi pi-file',\n            routerLink: '/invoices/sales'\n          },\n          {\n            label: this.languageService.translate('nav.purchaseInvoices'),\n            icon: 'pi pi-file-import',\n            routerLink: '/invoices/purchases'\n          },\n          {\n            label: this.languageService.translate('nav.salesReturns'),\n            icon: 'pi pi-undo',\n            routerLink: '/invoices/sales-returns'\n          },\n          {\n            label: this.languageService.translate('nav.purchaseReturns'),\n            icon: 'pi pi-replay',\n            routerLink: '/invoices/purchase-returns'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.customersSuppliers'),\n        icon: 'pi pi-users',\n        items: [\n          {\n            label: this.languageService.translate('nav.customers'),\n            icon: 'pi pi-user',\n            routerLink: '/customers'\n          },\n          {\n            label: this.languageService.translate('nav.suppliers'),\n            icon: 'pi pi-user-plus',\n            routerLink: '/suppliers'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.financialManagement'),\n        icon: 'pi pi-dollar',\n        items: [\n          {\n            label: this.languageService.translate('nav.payments'),\n            icon: 'pi pi-credit-card',\n            routerLink: '/payments'\n          },\n          {\n            label: this.languageService.translate('nav.accountStatements'),\n            icon: 'pi pi-file-pdf',\n            routerLink: '/reports/statements'\n          },\n          {\n            label: this.languageService.translate('nav.cashRegister'),\n            icon: 'pi pi-wallet',\n            routerLink: '/cash-register'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.reports'),\n        icon: 'pi pi-chart-bar',\n        items: [\n          {\n            label: this.languageService.translate('nav.inventoryReports'),\n            icon: 'pi pi-chart-line',\n            routerLink: '/reports/inventory'\n          },\n          {\n            label: this.languageService.translate('nav.financialReports'),\n            icon: 'pi pi-chart-pie',\n            routerLink: '/reports/financial'\n          },\n          {\n            label: this.languageService.translate('nav.salesReports'),\n            icon: 'pi pi-trending-up',\n            routerLink: '/reports/sales'\n          },\n          {\n            label: this.languageService.translate('nav.purchaseReports'),\n            icon: 'pi pi-trending-down',\n            routerLink: '/reports/purchases'\n          }\n        ]\n      }\n    ];\n  }\n\n  toggleSidebar() {\n    this.sidebarVisible = !this.sidebarVisible;\n  }\n}\n", "<div class=\"layout-wrapper\">\n  <!-- Top Navigation Bar -->\n  <div class=\"layout-topbar\">\n    <div class=\"flex align-items-center gap-3\">\n      <button\n        pButton\n        type=\"button\"\n        icon=\"pi pi-bars\"\n        class=\"p-button-text p-button-rounded p-button-plain\"\n        (click)=\"toggleSidebar()\"\n        [class.hidden-desktop]=\"true\">\n      </button>\n\n      <div class=\"flex align-items-center gap-2\">\n        <i class=\"pi pi-box text-2xl text-primary\"></i>\n        <h2 class=\"m-0 text-xl font-semibold\">{{ languageService.translate('app.title') }}</h2>\n      </div>\n    </div>\n\n    <div class=\"flex align-items-center gap-3\">\n      <!-- Language Switcher -->\n      <app-language-switcher></app-language-switcher>\n\n      <!-- Notifications -->\n      <div class=\"relative\">\n        <button\n          pButton\n          type=\"button\"\n          icon=\"pi pi-bell\"\n          class=\"p-button-text p-button-rounded p-button-plain\"\n          [pTooltip]=\"languageService.translate('app.notifications')\"\n          tooltipPosition=\"bottom\">\n        </button>\n        <span class=\"notification-badge\">3</span>\n      </div>\n\n      <!-- User Profile -->\n      <div class=\"user-profile flex align-items-center gap-2 p-2 border-round cursor-pointer\"\n           style=\"border: 1px solid var(--surface-border);\">\n        <div class=\"w-2rem h-2rem border-circle bg-primary flex align-items-center justify-content-center\">\n          <i class=\"pi pi-user text-white\"></i>\n        </div>\n\n        <div class=\"flex flex-column text-right\" *ngIf=\"languageService.isArabic()\">\n          <span class=\"text-sm font-medium\">أحمد محمد</span>\n          <span class=\"text-xs text-500\">مدير النظام</span>\n        </div>\n\n        <div class=\"flex flex-column\" *ngIf=\"!languageService.isArabic()\">\n          <span class=\"text-sm font-medium\">Ahmed Mohamed</span>\n          <span class=\"text-xs text-500\">System Admin</span>\n        </div>\n\n        <i class=\"pi pi-chevron-down text-500\"></i>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"layout-main\">\n    <!-- Desktop Sidebar -->\n    <div class=\"layout-sidebar hidden-mobile\">\n      <p-panelMenu [model]=\"menuItems\" [multiple]=\"false\"></p-panelMenu>\n    </div>\n\n    <!-- Mobile Sidebar -->\n    <p-sidebar \n      [(visible)]=\"sidebarVisible\" \n      position=\"left\" \n      [modal]=\"true\"\n      [dismissible]=\"true\"\n      styleClass=\"layout-sidebar-mobile\">\n      <ng-template pTemplate=\"header\">\n        <div class=\"flex align-items-center gap-2\">\n          <i class=\"pi pi-box text-2xl text-primary\"></i>\n          <span class=\"font-semibold\">Menu</span>\n        </div>\n      </ng-template>\n      \n      <p-panelMenu [model]=\"menuItems\" [multiple]=\"false\"></p-panelMenu>\n    </p-sidebar>\n\n    <!-- Main Content Area -->\n    <div class=\"layout-content\">\n      <ng-content></ng-content>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAG/C,SAASC,yBAAyB,QAAQ,kDAAkD;;;;;;;;;;;;ICkClFC,EADF,CAAAC,cAAA,cAA4E,eACxC;IAAAD,EAAA,CAAAE,MAAA,wDAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,oEAAW;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;;;;;IAGJH,EADF,CAAAC,cAAA,cAAkE,eAC9B;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;;;;;IAqBNH,EAAA,CAAAC,cAAA,aAA2C;IACzCD,EAAA,CAAAI,SAAA,WAA+C;IAC/CJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;;;AD/Cd,OAAM,MAAOE,eAAe;EAI1BC,YAAmBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAHlC,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,SAAS,GAAe,EAAE;EAE4B;EAEtDC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACJ,eAAe,CAACK,gBAAgB,CAACC,SAAS,CAAC,MAAK;MACnD,IAAI,CAACF,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACF,SAAS,GAAG,CACf;MACEK,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;MACtDC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE;KACb,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,yBAAyB,CAAC;MAChEC,IAAI,EAAE,WAAW;MACjBE,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,WAAW,CAAC;QAClDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,gBAAgB,CAAC;QACvDC,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,gBAAgB,CAAC;QACvDC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,oBAAoB,CAAC;QAC3DC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;QACtDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,oBAAoB,CAAC;MAC3DC,IAAI,EAAE,qBAAqB;MAC3BG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,mBAAmB,CAAC;QAC1DC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,kBAAkB,CAAC;QACzDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,qBAAqB,CAAC;QAC5DC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,wBAAwB,CAAC;MAC/DC,IAAI,EAAE,aAAa;MACnBG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;QACtDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;QACtDC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,yBAAyB,CAAC;MAChEC,IAAI,EAAE,cAAc;MACpBG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,cAAc,CAAC;QACrDC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,uBAAuB,CAAC;QAC9DC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,kBAAkB,CAAC;QACzDC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,aAAa,CAAC;MACpDC,IAAI,EAAE,iBAAiB;MACvBG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,kBAAkB;QACxBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,kBAAkB,CAAC;QACzDC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,qBAAqB,CAAC;QAC5DC,IAAI,EAAE,qBAAqB;QAC3BC,UAAU,EAAE;OACb;KAEJ,CACF;EACH;EAEAG,aAAaA,CAAA;IACX,IAAI,CAACZ,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;;;uBAzJWH,eAAe,EAAAL,EAAA,CAAAqB,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAflB,eAAe;MAAAmB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1B,EAAA,CAAA2B,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCxBtBnC,EAJN,CAAAC,cAAA,aAA4B,aAEC,aACkB,gBAOT;UAD9BD,EAAA,CAAAqC,UAAA,mBAAAC,iDAAA;YAAA,OAASF,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAE3BpB,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,aAA2C;UACzCD,EAAA,CAAAI,SAAA,WAA+C;UAC/CJ,EAAA,CAAAC,cAAA,YAAsC;UAAAD,EAAA,CAAAE,MAAA,GAA4C;UAEtFF,EAFsF,CAAAG,YAAA,EAAK,EACnF,EACF;UAENH,EAAA,CAAAC,cAAA,aAA2C;UAEzCD,EAAA,CAAAI,SAAA,4BAA+C;UAG/CJ,EAAA,CAAAC,cAAA,cAAsB;UACpBD,EAAA,CAAAI,SAAA,iBAOS;UACTJ,EAAA,CAAAC,cAAA,eAAiC;UAAAD,EAAA,CAAAE,MAAA,SAAC;UACpCF,EADoC,CAAAG,YAAA,EAAO,EACrC;UAKJH,EAFF,CAAAC,cAAA,eACsD,eAC+C;UACjGD,EAAA,CAAAI,SAAA,aAAqC;UACvCJ,EAAA,CAAAG,YAAA,EAAM;UAONH,EALA,CAAAuC,UAAA,KAAAC,+BAAA,kBAA4E,KAAAC,+BAAA,kBAKV;UAKlEzC,EAAA,CAAAI,SAAA,aAA2C;UAGjDJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAIJH,EAFF,CAAAC,cAAA,eAAyB,eAEmB;UACxCD,EAAA,CAAAI,SAAA,uBAAkE;UACpEJ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,qBAKqC;UAJnCD,EAAA,CAAA0C,gBAAA,2BAAAC,6DAAAC,MAAA;YAAA5C,EAAA,CAAA6C,kBAAA,CAAAT,GAAA,CAAA5B,cAAA,EAAAoC,MAAA,MAAAR,GAAA,CAAA5B,cAAA,GAAAoC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAK5B5C,EAAA,CAAAuC,UAAA,KAAAO,uCAAA,0BAAgC;UAOhC9C,EAAA,CAAAI,SAAA,uBAAkE;UACpEJ,EAAA,CAAAG,YAAA,EAAY;UAGZH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAA+C,YAAA,IAAyB;UAG/B/C,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;UA5EEH,EAAA,CAAAgD,SAAA,GAA6B;UAA7BhD,EAAA,CAAAiD,WAAA,wBAA6B;UAKSjD,EAAA,CAAAgD,SAAA,GAA4C;UAA5ChD,EAAA,CAAAkD,iBAAA,CAAAd,GAAA,CAAA7B,eAAA,CAAAQ,SAAA,cAA4C;UAehFf,EAAA,CAAAgD,SAAA,GAA2D;UAA3DhD,EAAA,CAAAmD,UAAA,aAAAf,GAAA,CAAA7B,eAAA,CAAAQ,SAAA,sBAA2D;UAanBf,EAAA,CAAAgD,SAAA,GAAgC;UAAhChD,EAAA,CAAAmD,UAAA,SAAAf,GAAA,CAAA7B,eAAA,CAAA6C,QAAA,GAAgC;UAK3CpD,EAAA,CAAAgD,SAAA,EAAiC;UAAjChD,EAAA,CAAAmD,UAAA,UAAAf,GAAA,CAAA7B,eAAA,CAAA6C,QAAA,GAAiC;UAarDpD,EAAA,CAAAgD,SAAA,GAAmB;UAAChD,EAApB,CAAAmD,UAAA,UAAAf,GAAA,CAAA3B,SAAA,CAAmB,mBAAmB;UAKnDT,EAAA,CAAAgD,SAAA,EAA4B;UAA5BhD,EAAA,CAAAqD,gBAAA,YAAAjB,GAAA,CAAA5B,cAAA,CAA4B;UAG5BR,EADA,CAAAmD,UAAA,eAAc,qBACM;UASPnD,EAAA,CAAAgD,SAAA,GAAmB;UAAChD,EAApB,CAAAmD,UAAA,UAAAf,GAAA,CAAA3B,SAAA,CAAmB,mBAAmB;;;qBD9DrDjB,YAAY,EAAA8D,EAAA,CAAAC,IAAA,EACZ9D,YAAY,EACZC,aAAa,EAAA8D,EAAA,CAAAC,OAAA,EAAAC,EAAA,CAAAC,aAAA,EACbhE,aAAa,EAAAiE,EAAA,CAAAC,OAAA,EACbjE,eAAe,EAAAkE,EAAA,CAAAC,SAAA,EACflE,YAAY,EAAAmE,EAAA,CAAAC,eAAA,EACZnE,aAAa,EACbC,yBAAyB;MAAAmE,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}