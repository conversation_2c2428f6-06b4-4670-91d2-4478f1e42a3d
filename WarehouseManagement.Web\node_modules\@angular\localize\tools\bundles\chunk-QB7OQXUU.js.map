{"version": 3, "sources": ["../../../../../../../packages/localize/tools/src/extract/duplicates.ts", "../../../../../../../packages/localize/tools/src/extract/extraction.ts", "../../../../../../../packages/localize/tools/src/extract/source_files/es2015_extract_plugin.ts", "../../../../../../../packages/localize/tools/src/extract/source_files/es5_extract_plugin.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/utils.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/arb_translation_serializer.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/json_translation_serializer.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/format_options.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/icu_parsing.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/xml_file.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.ts", "../../../../../../../packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;AAkBM,SAAU,uBACZ,IAAsB,UACtB,0BAAsD,UAAwB;AAChF,QAAM,cAAc,IAAI,YAAW;AACnC,MAAI,6BAA6B;AAAU,WAAO;AAElD,QAAM,aAAa,oBAAI,IAAG;AAC1B,aAAW,WAAW,UAAU;AAC9B,QAAI,WAAW,IAAI,QAAQ,EAAE,GAAG;AAC9B,iBAAW,IAAI,QAAQ,EAAE,EAAG,KAAK,OAAO;IAC1C,OAAO;AACL,iBAAW,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC;IACtC;EACF;AAEA,aAAW,cAAc,WAAW,OAAM,GAAI;AAC5C,QAAI,WAAW,UAAU;AAAG;AAC5B,QAAI,WAAW,MAAM,CAAC,YAAY,QAAQ,SAAS,WAAW,GAAG,IAAI;AAAG;AAExE,UAAM,oBAAoB,+BAA+B,WAAW,GAAG;IACnE,WAAW,IAAI,CAAC,YAAY,iBAAiB,IAAI,UAAU,OAAO,CAAC,EAAE,KAAK,IAAI;AAClF,gBAAY,IAAI,0BAA0B,iBAAiB;EAC7D;AAEA,SAAO;AACT;AAKA,SAAS,iBACL,IAAsB,UAA0B,SAAuB;AACzE,MAAI,QAAQ,aAAa,QAAW;AAClC,WAAO,SAAS,QAAQ;EAC1B,OAAO;AACL,UAAM,eAAe,GAAG,SAAS,UAAU,QAAQ,SAAS,IAAI;AAChE,UAAM,mBAAmB,0BAA0B,QAAQ,QAAQ;AACnE,WAAO,SAAS,QAAQ,WAAW,gBAAgB;EACrD;AACF;;;AClDA,SAAgE,wBAAuB;AAEvF,SAAQ,qBAAoB;;;ACD5B,SAAwB,0BAAoB;AAKtC,SAAU,wBACZ,IAAsB,UAA4B,eAAe,aAAW;AAC9E,SAAO;IACL,SAAS;MACP,yBAAyB,MAA0C;AACjE,cAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,YAAI,kBAAkB,KAAK,YAAY,KAAK,mBAAmB,GAAG,GAAG;AACnE,gBAAM,YAAY,KAAK,IAAI,OAAO;AAClC,gBAAM,CAAC,cAAc,oBAAoB,IACrC,sCAAsC,UAAU,IAAI,QAAQ,GAAG,EAAE;AACrE,gBAAM,CAAC,aAAa,mBAAmB,IACnC,qCAAqC,WAAW,EAAE;AACtD,gBAAM,WAAW,YAAY,IAAI,SAAS;AAC1C,gBAAM,UAAU,mBACZ,cAAc,aAAa,UAAU,sBAAsB,mBAAmB;AAClF,mBAAS,KAAK,OAAO;QACvB;MACF;;;AAGN;;;ACzBA,SAAwB,sBAAAA,2BAAoB;AAKtC,SAAU,qBACZ,IAAsB,UAA4B,eAAe,aAAW;AAC9E,SAAO;IACL,SAAS;MACP,eAAe,UAAsC,OAAK;AACxD,YAAI;AACF,gBAAM,aAAa,SAAS,IAAI,QAAQ;AACxC,cAAI,kBAAkB,YAAY,YAAY,KAAK,mBAAmB,UAAU,GAAG;AACjF,kBAAM,CAAC,cAAc,oBAAoB,IACrC,mCAAmC,UAAU,EAAE;AACnD,kBAAM,CAAC,aAAa,mBAAmB,IACnC,oCAAoC,UAAU,EAAE;AACpD,kBAAM,CAAC,iBAAiB,cAAc,IAAI,SAAS,IAAI,WAAW;AAClE,kBAAM,WAAW,YAAY,IAAI,iBAAiB,cAAc;AAChE,kBAAM,UAAUC,oBACZ,cAAc,aAAa,UAAU,sBAAsB,mBAAmB;AAClF,qBAAS,KAAK,OAAO;UACvB;QACF,SAAS,GAAP;AACA,cAAI,kBAAkB,CAAC,GAAG;AAIxB,kBAAM,oBAAoB,IAAI,UAAU,MAAM,MAAM,CAAC;UACvD,OAAO;AACL,kBAAM;UACR;QACF;MACF;;;AAGN;;;AFlBM,IAAO,mBAAP,MAAuB;EAM3B,YACY,IAAgC,QACxC,EAAC,UAAU,gBAAgB,MAAM,eAAe,YAAW,GAAoB;AADvE,SAAA,KAAA;AAAgC,SAAA,SAAA;AAE1C,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,SAAS,IAAI,iBAAiB,KAAK,IAAI,KAAK,QAAQ,EAAC,SAAS,SAAQ,CAAC;EAC9E;EAEA,gBACI,UAAgB;AAElB,UAAM,WAA6B,CAAA;AACnC,UAAM,aAAa,KAAK,GAAG,SAAS,KAAK,GAAG,QAAQ,KAAK,UAAU,QAAQ,CAAC;AAC5E,QAAI,WAAW,SAAS,KAAK,YAAY,GAAG;AAE1C,oBAAc,YAAY;QACxB,YAAY,KAAK;QACjB;QACA,SAAS;UACP,wBAAwB,KAAK,IAAI,UAAU,KAAK,YAAY;UAC5D,qBAAqB,KAAK,IAAI,UAAU,KAAK,YAAY;;QAE3D,MAAM;QACN,KAAK;OACN;AACD,UAAI,KAAK,iBAAiB,SAAS,SAAS,GAAG;AAC7C,aAAK,sBAAsB,UAAU,YAAY,QAAQ;MAC3D;IACF;AACA,WAAO;EACT;EAMQ,sBAAsB,UAAkB,UAAkB,UAA0B;AAE1F,UAAM,aACF,KAAK,OAAO,eAAe,KAAK,GAAG,QAAQ,KAAK,UAAU,QAAQ,GAAG,QAAQ;AACjF,QAAI,eAAe,MAAM;AACvB;IACF;AACA,eAAW,WAAW,UAAU;AAC9B,UAAI,QAAQ,aAAa,QAAW;AAClC,gBAAQ,WAAW,KAAK,oBAAoB,YAAY,QAAQ,QAAQ;AAExE,YAAI,QAAQ,sBAAsB;AAChC,kBAAQ,uBAAuB,QAAQ,qBAAqB,IACxD,cAAY,YAAY,KAAK,oBAAoB,YAAY,QAAQ,CAAC;QAC5E;AAEA,YAAI,QAAQ,uBAAuB;AACjC,gBAAM,mBAAmB,OAAO,KAAK,QAAQ,qBAAqB;AAClE,qBAAW,mBAAmB,kBAAkB;AAC9C,kBAAM,WAAW,QAAQ,sBAAsB;AAC/C,oBAAQ,sBAAsB,mBAC1B,YAAY,KAAK,oBAAoB,YAAY,QAAQ;UAC/D;QACF;MACF;IACF;EACF;EAWQ,oBAAoB,YAAwB,UAAyB;AAC3E,UAAM,gBACF,WAAW,oBAAoB,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM;AAC7E,QAAI,kBAAkB,MAAM;AAC1B,aAAO;IACT;AACA,UAAM,cAAc,WAAW,oBAAoB,SAAS,IAAI,MAAM,SAAS,IAAI,MAAM;AACzF,UAAM,QAAQ,EAAC,MAAM,cAAc,MAAM,QAAQ,cAAc,OAAM;AAGrE,UAAM,MAAO,gBAAgB,QAAQ,YAAY,SAAS,cAAc,OACpE,EAAC,MAAM,YAAY,MAAM,QAAQ,YAAY,OAAM,IACnD;AACJ,UAAM,qBACF,WAAW,QAAQ,KAAK,SAAM,yBAAI,gBAAe,cAAc,IAAI;AACvE,UAAM,WAAW,mBAAmB,qBAAqB,MAAM,QAAQ,MAAM;AAC7E,UAAM,SAAS,mBAAmB,qBAAqB,IAAI,QAAQ,IAAI;AACvE,UAAM,OAAO,mBAAmB,SAAS,UAAU,UAAU,MAAM,EAAE,KAAI;AACzE,WAAO,EAAC,MAAM,cAAc,MAAM,OAAO,KAAK,KAAI;EACpD;;;;AGrGI,SAAU,oBACZ,UACAC,eAAiD;AACnD,QAAM,gBAAgB,oBAAI,IAAG;AAC7B,aAAW,WAAW,UAAU;AAC9B,UAAM,KAAKA,cAAa,OAAO;AAC/B,QAAI,CAAC,cAAc,IAAI,EAAE,GAAG;AAC1B,oBAAc,IAAI,IAAI,CAAC,OAAO,CAAC;IACjC,OAAO;AACL,oBAAc,IAAI,EAAE,EAAG,KAAK,OAAO;IACrC;EACF;AAIA,aAAWC,aAAY,cAAc,OAAM,GAAI;AAC7C,IAAAA,UAAS,KAAK,gBAAgB;EAChC;AAEA,SAAO,MAAM,KAAK,cAAc,OAAM,CAAE,EAAE,KAAK,CAAC,IAAI,OAAO,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3F;AAKM,SAAU,YAAY,SAAuB;AAEjD,SAAO,QAAQ,aAAa;AAC9B;AAEM,SAAU,iBACZ,EAAC,UAAU,UAAS,GAAmB,EAAC,UAAU,UAAS,GAAiB;AAC9E,MAAI,cAAc,WAAW;AAC3B,WAAO;EACT;AACA,MAAI,cAAc,QAAW;AAC3B,WAAO;EACT;AACA,MAAI,cAAc,QAAW;AAC3B,WAAO;EACT;AACA,MAAI,UAAU,SAAS,UAAU,MAAM;AACrC,WAAO,UAAU,OAAO,UAAU,OAAO,KAAK;EAChD;AACA,MAAI,UAAU,MAAM,SAAS,UAAU,MAAM,MAAM;AACjD,WAAO,UAAU,MAAM,OAAO,UAAU,MAAM,OAAO,KAAK;EAC5D;AACA,MAAI,UAAU,MAAM,WAAW,UAAU,MAAM,QAAQ;AACrD,WAAO,UAAU,MAAM,SAAS,UAAU,MAAM,SAAS,KAAK;EAChE;AACA,SAAO;AACT;;;ACtCM,IAAO,2BAAP,MAA+B;EACnC,YACY,cAA8B,UAC9B,IAAoB;AADpB,SAAA,eAAA;AAA8B,SAAA,WAAA;AAC9B,SAAA,KAAA;EAAuB;EAEnC,UAAU,UAA0B;AAClC,UAAM,gBAAgB,oBAAoB,UAAU,aAAW,aAAa,OAAO,CAAC;AAEpF,QAAI,SAAS;gBAAoB,KAAK,UAAU,KAAK,YAAY;AAEjE,eAAW,qBAAqB,eAAe;AAC7C,YAAM,UAAU,kBAAkB;AAClC,YAAM,KAAK,aAAa,OAAO;AAC/B,gBAAU,KAAK,iBAAiB,IAAI,OAAO;AAC3C,gBAAU,KAAK,cACX,IAAI,QAAQ,aAAa,QAAQ,SACjC,kBAAkB,OAAO,WAAW,EAAE,IAAI,OAAK,EAAE,QAAQ,CAAC;IAChE;AAEA,cAAU;AAEV,WAAO;EACT;EAEQ,iBAAiB,IAAY,SAAuB;AAC1D,WAAO;IAAQ,KAAK,UAAU,EAAE,MAAM,KAAK,UAAU,QAAQ,IAAI;EACnE;EAEQ,cACJ,IAAY,aAA+B,SAC3C,WAA4B;AAC9B,UAAM,OAAiB,CAAA;AAEvB,QAAI,aAAa;AACf,WAAK,KAAK;qBAAwB,KAAK,UAAU,WAAW,GAAG;IACjE;AAEA,QAAI,SAAS;AACX,WAAK,KAAK;mBAAsB,KAAK,UAAU,OAAO,GAAG;IAC3D;AAEA,QAAI,UAAU,SAAS,GAAG;AACxB,UAAI,cAAc;;AAClB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,wBAAgB,IAAI,IAAI,QAAQ,QAAQ,KAAK,kBAAkB,UAAU,EAAE;MAC7E;AACA,qBAAe;AACf,WAAK,KAAK,WAAW;IACvB;AAEA,WAAO,KAAK,SAAS,IAAI;IAAQ,KAAK,UAAU,MAAM,EAAE,OAAO,KAAK,KAAK,GAAG;OAAW;EACzF;EAEQ,kBAAkB,EAAC,MAAM,OAAO,IAAG,GAAkB;AAC3D,WAAO;MACL;MACA,mBAAmB,KAAK,UAAU,KAAK,GAAG,SAAS,KAAK,UAAU,IAAI,CAAC;MACvE,+BAA+B,MAAM,qBAAqB,MAAM;MAChE,6BAA6B,IAAI,qBAAqB,IAAI;MAC1D;MACA,KAAK,IAAI;EACb;;AAGF,SAAS,aAAa,SAAuB;AAC3C,SAAO,QAAQ,YAAY,QAAQ;AACrC;;;AChFM,IAAO,kCAAP,MAAsC;EAC1C,YAAoB,cAAoB;AAApB,SAAA,eAAA;EAAuB;EAC3C,UAAU,UAA0B;AAClC,UAAM,UAAqC,EAAC,QAAQ,KAAK,cAAc,cAAc,CAAA,EAAE;AACvF,eAAW,CAAC,OAAO,KAAK,oBAAoB,UAAU,CAACC,aAAYA,SAAQ,EAAE,GAAG;AAC9E,cAAQ,aAAa,QAAQ,MAAM,QAAQ;IAC7C;AACA,WAAO,KAAK,UAAU,SAAS,MAAM,CAAC;EACxC;;;;ACdI,IAAO,qCAAP,MAAyC;EAC7C,YAAoB,cAAyB;AAAzB,SAAA,eAAA;EAA4B;EAEhD,UAAU,UAAyB;AACjC,QAAI,cAAc;AAClB,UAAM,UAAU,SAAS,OAAO,CAAC,QAAQ,YAAW;AAClD,UAAI,cAAc,OAAO,GAAG;AAC1B,mBAAW,YAAY,QAAQ,WAAY;AACzC,cAAI,OAAO,eAAe,QAAQ,GAAG;AACnC,iBAAK,aAAa,KAAK,gCAAgC,WAAW;UACpE;AAEA,iBAAO,YAAY,QAAQ;AAC3B,wBAAc;QAChB;MACF;AACA,aAAO;IACT,GAAG,CAAA,CAA4B;AAE/B,QAAI,CAAC,aAAa;AAChB,WAAK,aAAa,KACd,2GACoC;IAC1C;AAEA,WAAO,KAAK,UAAU,SAAS,MAAM,CAAC;EACxC;;AAIF,SAAS,cAAc,SAAsB;AAC3C,SAAO,CAAC,QAAQ,YAAY,CAAC,CAAC,QAAQ,aAAa,QAAQ,UAAU,SAAS;AAChF;;;AC/BM,SAAU,gBAAgB,MAAc,cAA4B,SAAsB;AAC9F,QAAM,kBAAkB,IAAI,IAAoC,YAAY;AAC5E,aAAW,UAAU,SAAS;AAC5B,QAAI,CAAC,gBAAgB,IAAI,MAAM,GAAG;AAChC,YAAM,IAAI,MACN,6BAA6B,UAAU;sBAChB,KAAK,UAAU,MAAM,KAAK,gBAAgB,KAAI,CAAE,CAAC,IAAI;IAClF;AACA,UAAM,oBAAoB,gBAAgB,IAAI,MAAM;AACpD,UAAM,cAAc,QAAQ;AAC5B,QAAI,CAAC,kBAAkB,SAAS,WAAW,GAAG;AAC5C,YAAM,IAAI,MACN,mCAAmC,UAAU;4BAChB,KAAK,UAAU,iBAAiB,mBACzD,eAAe;IACzB;EACF;AACF;AAMM,SAAU,mBAAmB,eAAuB,MAAI;AAC5D,SAAO,KAAK,MAAM,YAAY;AAChC;;;ACpCA,SAAwB,qBAAsC;;;AC4CxD,SAAU,uBAAuB,MAAY;AACjD,QAAM,QAAQ,IAAI,WAAU;AAC5B,QAAM,SAAS,IAAI,UAAS;AAC5B,QAAM,SAAS;AAEf,MAAI,UAAU;AACd,MAAI;AACJ,SAAO,QAAQ,OAAO,KAAK,IAAI,GAAG;AAChC,QAAI,MAAM,MAAM,KAAK;AACnB,YAAM,WAAU;IAClB,OAAO;AAEL,YAAM,WAAU;IAClB;AAEA,QAAI,MAAM,WAAU,MAAO,eAAe;AACxC,YAAM,OAAO,oBAAoB,MAAM,OAAO,SAAS;AACvD,UAAI,MAAM;AAIR,eAAO,QAAQ,KAAK,UAAU,SAAS,OAAO,YAAY,CAAC,CAAC;AAC5D,eAAO,eAAe,IAAI;AAC1B,eAAO,aAAa,KAAK,SAAS;AAClC,cAAM,WAAU;MAClB,OAAO;AAGL,eAAO,QAAQ,KAAK,UAAU,SAAS,OAAO,SAAS,CAAC;AACxD,cAAM,UAAS;MACjB;IACF,OAAO;AACL,aAAO,QAAQ,KAAK,UAAU,SAAS,OAAO,SAAS,CAAC;IAC1D;AACA,cAAU,OAAO;EACnB;AAGA,SAAO,QAAQ,KAAK,UAAU,OAAO,CAAC;AACtC,SAAO,OAAO,QAAO;AACvB;AAKA,IAAM,YAAN,MAAe;EAAf,cAAA;AACU,SAAA,SAAmB,CAAC,EAAE;EA4BhC;EArBE,QAAQ,MAAY;AAClB,SAAK,OAAO,KAAK,OAAO,SAAS,MAAM;EACzC;EAKA,eAAe,MAAY;AACzB,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,OAAO,KAAK,EAAE;EACrB;EAQA,UAAO;AACL,WAAO,KAAK;EACd;;AASF,IAAM,aAAN,MAAgB;EAAhB,cAAA;AACU,SAAA,QAAuB,CAAA;EAoDjC;EA7CE,aAAU;AACR,UAAM,UAAU,KAAK,WAAU;AAC/B,YAAQ,SAAS;MACf,KAAK;AACH,aAAK,MAAM,KAAK,MAAM;AACtB;MACF,KAAK;AACH,aAAK,MAAM,KAAK,aAAa;AAC7B;MACF,KAAK;AACH,aAAK,MAAM,KAAK,MAAM;AACtB;MACF;AACE,aAAK,MAAM,KAAK,KAAK;AACrB;IACJ;EACF;EAOA,aAAU;AACR,WAAO,KAAK,MAAM,IAAG;EACvB;EAQA,YAAS;AACP,UAAM,UAAU,KAAK,MAAM,IAAG;AAC9B,WAAO,YAAY,eAAe,qDAAqD,OAAO;AAC9F,SAAK,MAAM,KAAK,KAAK;EACvB;EAKA,aAAU;AACR,WAAO,KAAK,MAAM,KAAK,MAAM,SAAS;EACxC;;AAcF,SAAS,oBAAoB,MAAc,OAAa;AACtD,WAAS,IAAI,OAAO,IAAI,KAAK,QAAQ,KAAK;AACxC,QAAI,KAAK,OAAO,KAAK;AACnB;IACF;AACA,QAAI,KAAK,OAAO,KAAK;AACnB,aAAO,KAAK,UAAU,OAAO,CAAC;IAChC;EACF;AACA,SAAO;AACT;AAEA,SAAS,OAAO,MAAe,SAAe;AAC5C,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,wBAAwB,OAAO;EACjD;AACF;;;AC1MM,IAAO,UAAP,MAAc;EAApB,cAAA;AACU,SAAA,SAAS;AACT,SAAA,SAAS;AACT,SAAA,WAAqB,CAAA;AACrB,SAAA,uBAAuB;EA2EjC;EA1EE,WAAQ;AACN,WAAO,KAAK;EACd;EAEA,SACI,MAAc,aAA+C,CAAA,GAC7D,EAAC,cAAc,OAAO,mBAAkB,IAAa,CAAA,GAAE;AACzD,QAAI,CAAC,KAAK,sBAAsB;AAC9B,WAAK,UAAU,KAAK;IACtB;AAEA,SAAK,UAAU,IAAI;AAEnB,eAAW,CAAC,UAAU,SAAS,KAAK,OAAO,QAAQ,UAAU,GAAG;AAC9D,UAAI,WAAW;AACb,aAAK,UAAU,IAAI,aAAa,UAAU,SAAS;MACrD;IACF;AAEA,QAAI,aAAa;AACf,WAAK,UAAU;IACjB,OAAO;AACL,WAAK,UAAU;AACf,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,UAAS;IAChB;AAEA,QAAI,uBAAuB,QAAW;AACpC,WAAK,uBAAuB;IAC9B;AACA,QAAI,CAAC,KAAK,sBAAsB;AAC9B,WAAK,UAAU;;IACjB;AACA,WAAO;EACT;EAEA,OAAO,MAAc,EAAC,mBAAkB,IAAa,CAAA,GAAE;AACrD,UAAM,cAAc,KAAK,SAAS,IAAG;AACrC,QAAI,gBAAgB,MAAM;AACxB,YAAM,IAAI,MAAM,4BAA4B,qBAAqB,cAAc;IACjF;AAEA,SAAK,UAAS;AAEd,QAAI,CAAC,KAAK,sBAAsB;AAC9B,WAAK,UAAU,KAAK;IACtB;AACA,SAAK,UAAU,KAAK;AAEpB,QAAI,uBAAuB,QAAW;AACpC,WAAK,uBAAuB;IAC9B;AACA,QAAI,CAAC,KAAK,sBAAsB;AAC9B,WAAK,UAAU;;IACjB;AACA,WAAO;EACT;EAEA,KAAK,KAAW;AACd,SAAK,UAAU,UAAU,GAAG;AAC5B,WAAO;EACT;EAEA,QAAQ,KAAW;AACjB,SAAK,UAAU;AACf,WAAO;EACT;EAEQ,YAAS;AACf,SAAK,SAAS,KAAK,SAAS;EAC9B;EACQ,YAAS;AACf,SAAK,SAAS,KAAK,OAAO,MAAM,GAAG,EAAE;EACvC;;AAGF,IAAM,iBAAqC;EACzC,CAAC,MAAM,OAAO;EACd,CAAC,MAAM,QAAQ;EACf,CAAC,MAAM,QAAQ;EACf,CAAC,MAAM,MAAM;EACb,CAAC,MAAM,MAAM;;AAGf,SAAS,UAAU,MAAY;AAC7B,SAAO,eAAe,OAClB,CAACC,OAAc,UAA4BA,MAAK,QAAQ,MAAM,IAAI,MAAM,EAAE,GAAG,IAAI;AACvF;;;AFxFA,IAAM,8BAA8B;AAW9B,IAAO,8BAAP,MAAkC;EACtC,YACY,cAA8B,UAAkC,cAChE,gBAA+B,CAAA,GAAY,KAAuB,cAAa,GAAE;AADjF,SAAA,eAAA;AAA8B,SAAA,WAAA;AAAkC,SAAA,eAAA;AAChE,SAAA,gBAAA;AAA2C,SAAA,KAAA;AACrD,oBAAgB,+BAA+B,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,GAAG,aAAa;EAC7F;EAEA,UAAU,UAA0B;AAClC,UAAM,gBAAgB,oBAAoB,UAAU,aAAW,KAAK,aAAa,OAAO,CAAC;AACzF,UAAM,MAAM,IAAI,QAAO;AACvB,QAAI,SAAS,SAAS,EAAC,WAAW,OAAO,SAAS,wCAAuC,CAAC;AAQ1F,QAAI,SAAS,QAAQ;MACnB,mBAAmB,KAAK;MACxB,YAAY;MACZ,YAAY;MACZ,GAAG,KAAK;KACT;AACD,QAAI,SAAS,MAAM;AACnB,eAAW,qBAAqB,eAAe;AAC7C,YAAM,UAAU,kBAAkB;AAClC,YAAM,KAAK,KAAK,aAAa,OAAO;AAEpC,UAAI,SAAS,cAAc,EAAC,IAAI,UAAU,OAAM,CAAC;AACjD,UAAI,SAAS,UAAU,CAAA,GAAI,EAAC,oBAAoB,KAAI,CAAC;AACrD,WAAK,iBAAiB,KAAK,OAAO;AAClC,UAAI,OAAO,UAAU,EAAC,oBAAoB,MAAK,CAAC;AAGhD,iBAAW,EAAC,SAAQ,KAAK,kBAAkB,OAAO,WAAW,GAAG;AAC9D,aAAK,kBAAkB,KAAK,QAAQ;MACtC;AAEA,UAAI,QAAQ,aAAa;AACvB,aAAK,cAAc,KAAK,eAAe,QAAQ,WAAW;MAC5D;AACA,UAAI,QAAQ,SAAS;AACnB,aAAK,cAAc,KAAK,WAAW,QAAQ,OAAO;MACpD;AACA,UAAI,OAAO,YAAY;IACzB;AACA,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,OAAO;AAClB,WAAO,IAAI,SAAQ;EACrB;EAEQ,iBAAiB,KAAc,SAAuB;AAjFhE;AAkFI,UAAM,SAAS,QAAQ,aAAa,SAAS;AAC7C,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,WAAK,kBAAkB,KAAK,QAAQ,aAAa,EAAE;AACnD,YAAM,OAAO,QAAQ,iBAAiB;AACtC,YAAM,YAAW,aAAQ,0BAAR,mBAAgC;AACjD,YAAM,sBACF,QAAQ,wBAAwB,QAAQ,qBAAqB;AACjE,WAAK,qBAAqB,KAAK,MAAM,qCAAU,MAAM,mBAAmB;IAC1E;AACA,SAAK,kBAAkB,KAAK,QAAQ,aAAa,OAAO;EAC1D;EAEQ,kBAAkB,KAAc,MAAY;AAClD,UAAM,SAAS,uBAAuB,IAAI;AAC1C,UAAM,SAAS,OAAO,SAAS;AAC/B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,UAAI,KAAK,OAAO,EAAE;AAClB,WAAK,qBAAqB,KAAK,OAAO,IAAI,IAAI,QAAW,MAAS;IACpE;AACA,QAAI,KAAK,OAAO,OAAO;EACzB;EAEQ,qBACJ,KAAc,IAAY,MAAwB,cAA8B;AAClF,UAAM,QAAgC,EAAC,GAAE;AACzC,UAAM,QAAQ,uBAAuB,EAAE;AACvC,QAAI,UAAU,MAAM;AAClB,YAAM,WAAW;IACnB;AACA,QAAI,SAAS,QAAW;AACtB,YAAM,gBAAgB;IACxB;AACA,QAAI,iBAAiB,QAAW;AAC9B,YAAM,SAAS;IACjB;AACA,QAAI,SAAS,KAAK,OAAO,EAAC,aAAa,KAAI,CAAC;EAC9C;EAEQ,cAAc,KAAc,MAAc,OAAa;AAC7D,QAAI,SAAS,QAAQ,EAAC,UAAU,KAAK,MAAM,KAAI,GAAG,EAAC,oBAAoB,KAAI,CAAC;AAC5E,QAAI,KAAK,KAAK;AACd,QAAI,OAAO,QAAQ,EAAC,oBAAoB,MAAK,CAAC;EAChD;EAEQ,kBAAkB,KAAc,UAAyB;AAC/D,QAAI,SAAS,iBAAiB,EAAC,SAAS,WAAU,CAAC;AACnD,SAAK,cAAc,KAAK,cAAc,KAAK,GAAG,SAAS,KAAK,UAAU,SAAS,IAAI,CAAC;AACpF,UAAM,gBAAgB,SAAS,QAAQ,UAAa,SAAS,IAAI,SAAS,SAAS,MAAM,OACrF,IAAI,SAAS,IAAI,OAAO,MACxB;AACJ,SAAK,cAAc,KAAK,cAAc,GAAG,SAAS,MAAM,OAAO,IAAI,eAAe;AAClF,QAAI,OAAO,eAAe;EAC5B;EAEQ,cAAc,KAAc,MAAc,OAAa;AAC7D,QAAI,SAAS,WAAW,EAAC,gBAAgB,KAAI,GAAG,EAAC,oBAAoB,KAAI,CAAC;AAC1E,QAAI,KAAK,KAAK;AACd,QAAI,OAAO,WAAW,EAAC,oBAAoB,MAAK,CAAC;EACnD;EAeQ,aAAa,SAAuB;AAC1C,WAAO,QAAQ,YACX,KAAK,gBAAgB,QAAQ,cAAc,UAC3C,QAAQ,UAAU,KAAK,QAAM,GAAG,WAAW,2BAA2B,KACtE,QAAQ;EACd;;AAkBF,SAAS,uBAAuB,aAAmB;AACjD,QAAM,MAAM,YAAY,QAAQ,oBAAoB,EAAE;AACtD,UAAQ,KAAK;IACX,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,YAAM,UAAU,IAAI,WAAW,MAAM,IACjC,IAAI,QAAQ,aAAa,CAAC,GAAG,YAAoB,QAAQ,YAAW,CAAE,IACtE,QAAQ;AACZ,UAAI,YAAY,QAAW;AACzB,eAAO;MACT;AACA,aAAO,KAAK;EAChB;AACF;AAEA,IAAM,UAAkC;EACtC,QAAQ;EACR,aAAa;EACb,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;EAClB,mBAAmB;EACnB,eAAe;EACf,aAAa;EACb,cAAc;EACd,gBAAgB;EAChB,aAAa;EACb,aAAa;EACb,sBAAsB;EACtB,cAAc;EACd,aAAa;EACb,eAAe;EACf,cAAc;EACd,cAAc;EACd,gBAAgB;EAChB,qBAAqB;EACrB,gBAAgB;EAChB,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,kBAAkB;;;;AG1NpB,SAAwB,iBAAAC,sBAAsC;AAU9D,IAAM,oCAAoC;AAUpC,IAAO,8BAAP,MAAkC;EAEtC,YACY,cAA8B,UAAkC,cAChE,gBAA+B,CAAA,GAAY,KAAuBC,eAAa,GAAE;AADjF,SAAA,eAAA;AAA8B,SAAA,WAAA;AAAkC,SAAA,eAAA;AAChE,SAAA,gBAAA;AAA2C,SAAA,KAAA;AAH/C,SAAA,uBAAuB;AAI7B,oBAAgB,+BAA+B,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,GAAG,aAAa;EAC7F;EAEA,UAAU,UAA0B;AAClC,UAAM,gBAAgB,oBAAoB,UAAU,aAAW,KAAK,aAAa,OAAO,CAAC;AACzF,UAAM,MAAM,IAAI,QAAO;AACvB,QAAI,SAAS,SAAS;MACpB,WAAW;MACX,SAAS;MACT,WAAW,KAAK;KACjB;AAQD,QAAI,SAAS,QAAQ,EAAC,MAAM,UAAU,YAAY,eAAe,GAAG,KAAK,cAAa,CAAC;AACvF,eAAW,qBAAqB,eAAe;AAC7C,YAAM,UAAU,kBAAkB;AAClC,YAAM,KAAK,KAAK,aAAa,OAAO;AAEpC,UAAI,SAAS,QAAQ,EAAC,GAAE,CAAC;AACzB,YAAM,wBAAwB,kBAAkB,OAAO,WAAW;AAClE,UAAI,QAAQ,WAAW,QAAQ,eAAe,sBAAsB,QAAQ;AAC1E,YAAI,SAAS,OAAO;AAGpB,mBAAW,EAAC,UAAU,EAAC,MAAM,OAAO,IAAG,EAAC,KAAK,uBAAuB;AAClE,gBAAM,gBACF,QAAQ,UAAa,IAAI,SAAS,MAAM,OAAO,IAAI,IAAI,OAAO,MAAM;AACxE,eAAK,cACD,KAAK,YACL,GAAG,KAAK,GAAG,SAAS,KAAK,UAAU,IAAI,KAAK,MAAM,OAAO,IAAI,eAAe;QAClF;AAEA,YAAI,QAAQ,aAAa;AACvB,eAAK,cAAc,KAAK,eAAe,QAAQ,WAAW;QAC5D;AACA,YAAI,QAAQ,SAAS;AACnB,eAAK,cAAc,KAAK,WAAW,QAAQ,OAAO;QACpD;AACA,YAAI,OAAO,OAAO;MACpB;AACA,UAAI,SAAS,SAAS;AACtB,UAAI,SAAS,UAAU,CAAA,GAAI,EAAC,oBAAoB,KAAI,CAAC;AACrD,WAAK,iBAAiB,KAAK,OAAO;AAClC,UAAI,OAAO,UAAU,EAAC,oBAAoB,MAAK,CAAC;AAChD,UAAI,OAAO,SAAS;AACpB,UAAI,OAAO,MAAM;IACnB;AACA,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,OAAO;AAClB,WAAO,IAAI,SAAQ;EACrB;EAEQ,iBAAiB,KAAc,SAAuB;AAC5D,SAAK,uBAAuB;AAC5B,UAAM,SAAS,QAAQ,aAAa,SAAS;AAC7C,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,WAAK,kBAAkB,KAAK,QAAQ,aAAa,EAAE;AACnD,YAAM,OAAO,QAAQ,iBAAiB;AACtC,YAAM,sBACF,QAAQ,wBAAwB,QAAQ,qBAAqB;AACjE,WAAK,qBAAqB,KAAK,MAAM,QAAQ,uBAAuB,mBAAmB;IACzF;AACA,SAAK,kBAAkB,KAAK,QAAQ,aAAa,OAAO;EAC1D;EAEQ,kBAAkB,KAAc,MAAY;AAClD,UAAM,SAAS,uBAAuB,IAAI;AAC1C,UAAM,SAAS,OAAO,SAAS;AAC/B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,UAAI,KAAK,OAAO,EAAE;AAClB,WAAK,qBAAqB,KAAK,OAAO,IAAI,IAAI,QAAW,MAAS;IACpE;AACA,QAAI,KAAK,OAAO,OAAO;EACzB;EAEQ,qBACJ,KAAc,iBACd,uBACA,qBAAqC;AAnH3C;AAoHI,UAAM,QAAO,oEAAwB,qBAAxB,mBAA0C;AAEvD,QAAI,gBAAgB,WAAW,QAAQ,GAAG;AAExC,YAAM,yBACF,gBAAgB,QAAQ,UAAU,OAAO,EAAE,QAAQ,SAAS,EAAE;AAClE,YAAM,eAAc,oEAAwB,4BAAxB,mBAAiD;AACrE,YAAM,QAAgC;QACpC,IAAI,GAAG,KAAK;QACZ,YAAY;QACZ,UAAU;;AAEZ,YAAM,OAAO,sBAAsB,eAAe;AAClD,UAAI,SAAS,MAAM;AACjB,cAAM,UAAU;MAClB;AACA,UAAI,SAAS,QAAW;AACtB,cAAM,eAAe;MACvB;AACA,UAAI,gBAAgB,QAAW;AAC7B,cAAM,aAAa;MACrB;AACA,UAAI,SAAS,MAAM,KAAK;IAC1B,WAAW,gBAAgB,WAAW,QAAQ,GAAG;AAC/C,UAAI,OAAO,IAAI;IACjB,OAAO;AACL,YAAM,QAAgC;QACpC,IAAI,GAAG,KAAK;QACZ,OAAO;;AAET,YAAM,OAAO,sBAAsB,eAAe;AAClD,UAAI,SAAS,MAAM;AACjB,cAAM,UAAU;MAClB;AACA,UAAI,SAAS,QAAW;AACtB,cAAM,UAAU;MAClB;AACA,UAAI,wBAAwB,QAAW;AACrC,cAAM,cAAc;MACtB;AACA,UAAI,SAAS,MAAM,OAAO,EAAC,aAAa,KAAI,CAAC;IAC/C;EACF;EAEQ,cAAc,KAAc,MAAc,OAAa;AAC7D,QAAI,SAAS,QAAQ,EAAC,UAAU,KAAI,GAAG,EAAC,oBAAoB,KAAI,CAAC;AACjE,QAAI,KAAK,KAAK;AACd,QAAI,OAAO,QAAQ,EAAC,oBAAoB,MAAK,CAAC;EAChD;EAgBQ,aAAa,SAAuB;AAC1C,WAAO,QAAQ,YACX,KAAK,gBAAgB,QAAQ,cAAc,UAC3C,QAAQ,UAAU,KACd,QAAM,GAAG,UAAU,qCAAqC,CAAC,SAAS,KAAK,EAAE,CAAC,KAC9E,QAAQ;EACd;;AAUF,SAAS,sBAAsB,aAAmB;AAChD,QAAM,MAAM,YAAY,QAAQ,oBAAoB,EAAE,EAAE,QAAQ,SAAS,EAAE;AAC3E,UAAQ,KAAK;IACX,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO,mBAAmB,KAAK,WAAW,IAAI,UAAU;EAC5D;AACF;;;AC9MA,SAAwB,iBAAAC,sBAAsC;AAgBxD,IAAO,2BAAP,MAA+B;EACnC,YACY,UAAkC,cAClC,KAAuBC,eAAa,GAAE;AADtC,SAAA,WAAA;AAAkC,SAAA,eAAA;AAClC,SAAA,KAAA;EAAyC;EAErD,UAAU,UAA0B;AAClC,UAAM,gBAAgB,oBAAoB,UAAU,aAAW,KAAK,aAAa,OAAO,CAAC;AACzF,UAAM,MAAM,IAAI,QAAO;AACvB,QAAI,QACA;;;;;;;;;;;;;;;;;;;;;CAoBM;AACV,QAAI,SAAS,eAAe;AAC5B,eAAW,qBAAqB,eAAe;AAC7C,YAAM,UAAU,kBAAkB;AAClC,YAAM,KAAK,KAAK,aAAa,OAAO;AACpC,UAAI,SACA,OAAO,EAAC,IAAI,MAAM,QAAQ,aAAa,SAAS,QAAQ,QAAO,GAC/D,EAAC,oBAAoB,KAAI,CAAC;AAC9B,UAAI,QAAQ,UAAU;AACpB,aAAK,kBAAkB,KAAK,QAAQ,QAAQ;MAC9C;AACA,WAAK,iBAAiB,KAAK,OAAO;AAClC,UAAI,OAAO,OAAO,EAAC,oBAAoB,MAAK,CAAC;IAC/C;AACA,QAAI,OAAO,eAAe;AAC1B,WAAO,IAAI,SAAQ;EACrB;EAEQ,kBAAkB,KAAc,UAAyB;AAC/D,QAAI,SAAS,QAAQ;AACrB,UAAM,gBAAgB,SAAS,QAAQ,UAAa,SAAS,IAAI,SAAS,SAAS,MAAM,OACrF,IAAI,SAAS,IAAI,OAAO,MACxB;AACJ,QAAI,KACA,GAAG,KAAK,GAAG,SAAS,KAAK,UAAU,SAAS,IAAI,KAAK,SAAS,MAAM,OAAO,eAAe;AAC9F,QAAI,OAAO,QAAQ;EACrB;EAEQ,iBAAiB,KAAc,SAAuB;AAC5D,UAAM,SAAS,QAAQ,aAAa,SAAS;AAC7C,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,WAAK,kBAAkB,KAAK,QAAQ,aAAa,EAAE;AACnD,UAAI,SAAS,MAAM,EAAC,MAAM,QAAQ,iBAAiB,GAAE,GAAG,EAAC,aAAa,KAAI,CAAC;IAC7E;AACA,SAAK,kBAAkB,KAAK,QAAQ,aAAa,OAAO;EAC1D;EAEQ,kBAAkB,KAAc,MAAY;AAClD,UAAM,SAAS,uBAAuB,IAAI;AAC1C,UAAM,SAAS,OAAO,SAAS;AAC/B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,UAAI,KAAK,OAAO,EAAE;AAClB,UAAI,SAAS,MAAM,EAAC,MAAM,OAAO,IAAI,GAAE,GAAG,EAAC,aAAa,KAAI,CAAC;IAC/D;AACA,QAAI,KAAK,OAAO,OAAO;EACzB;EAgBQ,aAAa,SAAuB;AAC1C,WAAO,QAAQ,YACX,KAAK,gBAAgB,QAAQ,cAAc,UAC3C,QAAQ,UAAU,KAAK,QAAM,GAAG,UAAU,MAAM,CAAC,SAAS,KAAK,EAAE,CAAC,KAClE,QAAQ;EACd;;", "names": ["ɵparseMessage", "ɵparseMessage", "getMessageId", "messages", "message", "text", "getFileSystem", "getFileSystem", "getFileSystem", "getFileSystem"]}