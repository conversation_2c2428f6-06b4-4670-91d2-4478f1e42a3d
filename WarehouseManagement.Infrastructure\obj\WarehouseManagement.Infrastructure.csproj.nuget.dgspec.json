{"format": 1, "restore": {"G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Infrastructure\\WarehouseManagement.Infrastructure.csproj": {}}, "projects": {"G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Application\\WarehouseManagement.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Application\\WarehouseManagement.Application.csproj", "projectName": "WarehouseManagement.Application", "projectPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Application\\WarehouseManagement.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\WarehouseManagement.Core.csproj": {"projectPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\WarehouseManagement.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\WarehouseManagement.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\WarehouseManagement.Core.csproj", "projectName": "WarehouseManagement.Core", "projectPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\WarehouseManagement.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Infrastructure\\WarehouseManagement.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Infrastructure\\WarehouseManagement.Infrastructure.csproj", "projectName": "WarehouseManagement.Infrastructure", "projectPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Infrastructure\\WarehouseManagement.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Application\\WarehouseManagement.Application.csproj": {"projectPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Application\\WarehouseManagement.Application.csproj"}, "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\WarehouseManagement.Core.csproj": {"projectPath": "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Core\\WarehouseManagement.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "QuestPDF": {"target": "Package", "version": "[2023.12.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}