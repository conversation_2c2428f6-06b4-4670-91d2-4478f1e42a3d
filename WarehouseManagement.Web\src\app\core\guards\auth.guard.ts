import { Injectable } from '@angular/core';
import { 
  CanActivate, 
  CanActivateChild, 
  CanLoad, 
  Router, 
  ActivatedRouteSnapshot, 
  RouterStateSnapshot, 
  Route, 
  UrlSegment 
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, tap, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { AuthorizationService } from '../services/authorization.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild, CanLoad {

  constructor(
    private authService: AuthService,
    private authorizationService: AuthorizationService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuthentication(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuthentication(state.url);
  }

  canLoad(route: Route, segments: UrlSegment[]): Observable<boolean> {
    const url = segments.map(segment => segment.path).join('/');
    return this.checkAuthentication(`/${url}`);
  }

  private checkAuthentication(url: string): Observable<boolean> {
    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        if (authState.loading) {
          // Still loading, wait for authentication to complete
          return false;
        }

        if (!authState.isAuthenticated) {
          // Not authenticated, redirect to login
          this.router.navigate(['/login'], { 
            queryParams: { returnUrl: url } 
          });
          return false;
        }

        // Check if user has permission to access this route
        if (!this.authorizationService.canAccessRoute(url)) {
          // User doesn't have permission, redirect to unauthorized page
          this.router.navigate(['/unauthorized']);
          this.authorizationService.logPermissionDenied('route', 'access', url);
          return false;
        }

        return true;
      }),
      catchError(() => {
        // Error occurred, redirect to login
        this.router.navigate(['/login'], { 
          queryParams: { returnUrl: url } 
        });
        return of(false);
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class NoAuthGuard implements CanActivate {
  
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        if (authState.isAuthenticated) {
          // User is already authenticated, redirect to dashboard
          this.router.navigate(['/dashboard']);
          return false;
        }
        return true;
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private authorizationService: AuthorizationService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkRole(route, state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkRole(childRoute, state.url);
  }

  private checkRole(route: ActivatedRouteSnapshot, url: string): Observable<boolean> {
    const requiredRoles = route.data['roles'] as string[];
    const requiredPermissions = route.data['permissions'] as Array<{resource: string, action: string}>;

    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        if (!authState.isAuthenticated) {
          this.router.navigate(['/login'], { 
            queryParams: { returnUrl: url } 
          });
          return false;
        }

        // Check required roles
        if (requiredRoles && requiredRoles.length > 0) {
          const hasRequiredRole = requiredRoles.some(role => 
            this.authorizationService.hasRole(role)
          );

          if (!hasRequiredRole) {
            this.router.navigate(['/unauthorized']);
            this.authorizationService.logPermissionDenied('role', requiredRoles.join(','), url);
            return false;
          }
        }

        // Check required permissions
        if (requiredPermissions && requiredPermissions.length > 0) {
          const hasRequiredPermissions = requiredPermissions.every(permission => 
            this.authorizationService.hasPermission(permission.resource, permission.action)
          );

          if (!hasRequiredPermissions) {
            this.router.navigate(['/unauthorized']);
            this.authorizationService.logPermissionDenied(
              'permission', 
              requiredPermissions.map(p => `${p.action}:${p.resource}`).join(','), 
              url
            );
            return false;
          }
        }

        return true;
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class PermissionGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private authorizationService: AuthorizationService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermission(route, state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermission(childRoute, state.url);
  }

  private checkPermission(route: ActivatedRouteSnapshot, url: string): Observable<boolean> {
    const resource = route.data['resource'] as string;
    const action = route.data['action'] as string;
    const permissions = route.data['permissions'] as Array<{resource: string, action: string}>;

    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        if (!authState.isAuthenticated) {
          this.router.navigate(['/login'], { 
            queryParams: { returnUrl: url } 
          });
          return false;
        }

        // Check single permission
        if (resource && action) {
          if (!this.authorizationService.hasPermission(resource, action)) {
            this.router.navigate(['/unauthorized']);
            this.authorizationService.logPermissionDenied(resource, action, url);
            return false;
          }
        }

        // Check multiple permissions
        if (permissions && permissions.length > 0) {
          const hasAllPermissions = permissions.every(permission => 
            this.authorizationService.hasPermission(permission.resource, permission.action)
          );

          if (!hasAllPermissions) {
            this.router.navigate(['/unauthorized']);
            this.authorizationService.logPermissionDenied(
              'multiple', 
              permissions.map(p => `${p.action}:${p.resource}`).join(','), 
              url
            );
            return false;
          }
        }

        return true;
      })
    );
  }
}
