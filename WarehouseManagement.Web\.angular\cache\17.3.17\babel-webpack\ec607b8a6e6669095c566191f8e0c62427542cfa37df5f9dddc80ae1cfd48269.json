{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:7001/api',\n  appName: 'Warehouse Management System',\n  version: '1.0.0',\n  // Security Configuration\n  security: {\n    enableCSRF: true,\n    enableRateLimiting: true,\n    enableSecurityHeaders: true,\n    enableXSSProtection: true,\n    sessionTimeout: 15,\n    maxLoginAttempts: 5,\n    lockoutDuration: 15,\n    passwordPolicy: {\n      minLength: 8,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecialChars: true,\n      preventCommonPasswords: true\n    },\n    rateLimits: {\n      login: {\n        maxAttempts: 5,\n        windowMinutes: 15\n      },\n      api: {\n        maxAttempts: 100,\n        windowMinutes: 1\n      },\n      passwordReset: {\n        maxAttempts: 3,\n        windowMinutes: 60\n      }\n    }\n  },\n  // Feature Flags\n  features: {\n    enableBiometricAuth: false,\n    enableTwoFactorAuth: false,\n    enableAuditLogging: true,\n    enableSecurityMonitoring: true\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}