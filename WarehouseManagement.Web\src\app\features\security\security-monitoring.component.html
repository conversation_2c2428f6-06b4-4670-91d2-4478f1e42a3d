<div class="security-monitoring" *appHasPermission="'audit_logs:read'">
  <!-- Header -->
  <div class="flex justify-content-between align-items-center mb-4">
    <h2 class="text-2xl font-bold m-0">
      {{ languageService.translate('security.monitoring.title') }}
    </h2>
    
    <div class="flex gap-2">
      <!-- Auto Refresh Toggle -->
      <p-inputSwitch 
        [(ngModel)]="autoRefresh" 
        (onChange)="toggleAutoRefresh()"
        [pTooltip]="languageService.translate('security.monitoring.autoRefresh')">
      </p-inputSwitch>
      
      <!-- Refresh Button -->
      <button pButton 
              type="button" 
              icon="pi pi-refresh" 
              class="p-button-outlined"
              [loading]="loading"
              (click)="loadData()"
              [pTooltip]="languageService.translate('security.monitoring.refresh')">
      </button>
      
      <!-- Export Button -->
      <button *appHasRole="'admin'"
              pButton 
              type="button" 
              icon="pi pi-download" 
              class="p-button-outlined"
              (click)="exportData()"
              [pTooltip]="languageService.translate('security.monitoring.export')">
      </button>
    </div>
  </div>

  <!-- Metrics Cards -->
  <div class="grid mb-4">
    <div class="col-12 md:col-6 lg:col-2">
      <div class="card text-center">
        <div class="text-blue-500 text-3xl mb-2">
          <i class="pi pi-shield"></i>
        </div>
        <div class="text-2xl font-bold text-blue-600">{{ metrics.totalEvents }}</div>
        <div class="text-sm text-gray-600">
          {{ languageService.translate('security.monitoring.totalEvents') }}
        </div>
      </div>
    </div>
    
    <div class="col-12 md:col-6 lg:col-2">
      <div class="card text-center">
        <div class="text-red-500 text-3xl mb-2">
          <i class="pi pi-exclamation-triangle"></i>
        </div>
        <div class="text-2xl font-bold text-red-600">{{ metrics.criticalEvents }}</div>
        <div class="text-sm text-gray-600">
          {{ languageService.translate('security.monitoring.criticalEvents') }}
        </div>
      </div>
    </div>
    
    <div class="col-12 md:col-6 lg:col-2">
      <div class="card text-center">
        <div class="text-green-500 text-3xl mb-2">
          <i class="pi pi-check-circle"></i>
        </div>
        <div class="text-2xl font-bold text-green-600">{{ metrics.successfulLogins }}</div>
        <div class="text-sm text-gray-600">
          {{ languageService.translate('security.monitoring.successfulLogins') }}
        </div>
      </div>
    </div>
    
    <div class="col-12 md:col-6 lg:col-2">
      <div class="card text-center">
        <div class="text-orange-500 text-3xl mb-2">
          <i class="pi pi-times-circle"></i>
        </div>
        <div class="text-2xl font-bold text-orange-600">{{ metrics.failedLogins }}</div>
        <div class="text-sm text-gray-600">
          {{ languageService.translate('security.monitoring.failedLogins') }}
        </div>
      </div>
    </div>
    
    <div class="col-12 md:col-6 lg:col-2">
      <div class="card text-center">
        <div class="text-purple-500 text-3xl mb-2">
          <i class="pi pi-globe"></i>
        </div>
        <div class="text-2xl font-bold text-purple-600">{{ metrics.uniqueIPs }}</div>
        <div class="text-sm text-gray-600">
          {{ languageService.translate('security.monitoring.uniqueIPs') }}
        </div>
      </div>
    </div>
    
    <div class="col-12 md:col-6 lg:col-2">
      <div class="card text-center">
        <div class="text-yellow-500 text-3xl mb-2">
          <i class="pi pi-lock"></i>
        </div>
        <div class="text-2xl font-bold text-yellow-600">{{ metrics.lockedAccounts }}</div>
        <div class="text-sm text-gray-600">
          {{ languageService.translate('security.monitoring.lockedAccounts') }}
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="card mb-4">
    <h5>{{ languageService.translate('security.monitoring.filters') }}</h5>
    <div class="grid">
      <div class="col-12 md:col-3">
        <label for="eventType" class="block text-sm font-medium mb-2">
          {{ languageService.translate('security.monitoring.eventType') }}
        </label>
        <p-dropdown id="eventType"
                    [(ngModel)]="eventTypeFilter"
                    [options]="[
                      {label: 'All', value: ''},
                      {label: 'Login Success', value: 'LoginSuccess'},
                      {label: 'Login Failure', value: 'LoginFailure'},
                      {label: 'Logout', value: 'Logout'},
                      {label: 'Account Locked', value: 'AccountLocked'},
                      {label: 'Suspicious Activity', value: 'SuspiciousActivity'}
                    ]"
                    optionLabel="label"
                    optionValue="value"
                    [style]="{'width': '100%'}">
        </p-dropdown>
      </div>
      
      <div class="col-12 md:col-3">
        <label for="severity" class="block text-sm font-medium mb-2">
          {{ languageService.translate('security.monitoring.severity') }}
        </label>
        <p-dropdown id="severity"
                    [(ngModel)]="severityFilter"
                    [options]="[
                      {label: 'All', value: ''},
                      {label: 'Critical', value: 'Critical'},
                      {label: 'High', value: 'High'},
                      {label: 'Medium', value: 'Medium'},
                      {label: 'Low', value: 'Low'}
                    ]"
                    optionLabel="label"
                    optionValue="value"
                    [style]="{'width': '100%'}">
        </p-dropdown>
      </div>
      
      <div class="col-12 md:col-3">
        <label for="dateRange" class="block text-sm font-medium mb-2">
          {{ languageService.translate('security.monitoring.dateRange') }}
        </label>
        <p-calendar id="dateRange"
                    [(ngModel)]="dateRangeFilter.start"
                    [showTime]="true"
                    [style]="{'width': '100%'}">
        </p-calendar>
      </div>
      
      <div class="col-12 md:col-3 flex align-items-end">
        <div class="flex gap-2 w-full">
          <button pButton 
                  type="button" 
                  label="{{ languageService.translate('security.monitoring.apply') }}"
                  icon="pi pi-search"
                  class="p-button-primary flex-1"
                  (click)="applyFilters()">
          </button>
          <button pButton 
                  type="button" 
                  label="{{ languageService.translate('security.monitoring.clear') }}"
                  icon="pi pi-times"
                  class="p-button-outlined flex-1"
                  (click)="clearFilters()">
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts -->
  <div class="grid mb-4">
    <div class="col-12 md:col-6">
      <div class="card">
        <h5>{{ languageService.translate('security.monitoring.eventsBySeverity') }}</h5>
        <p-chart type="doughnut" 
                 [data]="eventChartData"
                 [options]="{
                   plugins: {
                     legend: { position: 'bottom' }
                   }
                 }">
        </p-chart>
      </div>
    </div>
    
    <div class="col-12 md:col-6">
      <div class="card">
        <h5>{{ languageService.translate('security.monitoring.loginAttempts') }}</h5>
        <p-chart type="doughnut" 
                 [data]="loginChartData"
                 [options]="{
                   plugins: {
                     legend: { position: 'bottom' }
                   }
                 }">
        </p-chart>
      </div>
    </div>
  </div>

  <!-- Tabs -->
  <p-tabView (onChange)="onTabChange($event)">
    <!-- Security Events Tab -->
    <p-tabPanel header="{{ languageService.translate('security.monitoring.securityEvents') }}">
      <p-table [value]="securityEvents" 
               [paginator]="true" 
               [rows]="20"
               [loading]="loading"
               [responsive]="true">
        <ng-template pTemplate="header">
          <tr>
            <th>{{ languageService.translate('security.monitoring.timestamp') }}</th>
            <th>{{ languageService.translate('security.monitoring.eventType') }}</th>
            <th>{{ languageService.translate('security.monitoring.severity') }}</th>
            <th>{{ languageService.translate('security.monitoring.description') }}</th>
            <th>{{ languageService.translate('security.monitoring.ipAddress') }}</th>
            <th>{{ languageService.translate('security.monitoring.actions') }}</th>
          </tr>
        </ng-template>
        
        <ng-template pTemplate="body" let-event>
          <tr>
            <td>{{ formatDate(event.occurredAt) }}</td>
            <td>
              <div class="flex align-items-center gap-2">
                <i class="pi {{ getEventTypeIcon(event.eventType) }}"></i>
                {{ event.eventType }}
              </div>
            </td>
            <td>
              <span class="severity-badge {{ getSeverityClass(event.severity) }}">
                {{ event.severity }}
              </span>
            </td>
            <td>{{ event.description }}</td>
            <td>{{ event.ipAddress }}</td>
            <td>
              <button pButton 
                      type="button" 
                      icon="pi pi-eye" 
                      class="p-button-rounded p-button-text"
                      [pTooltip]="languageService.translate('security.monitoring.viewDetails')">
              </button>
            </td>
          </tr>
        </ng-template>
        
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="6" class="text-center">
              {{ languageService.translate('security.monitoring.noEvents') }}
            </td>
          </tr>
        </ng-template>
      </p-table>
    </p-tabPanel>

    <!-- Login Attempts Tab -->
    <p-tabPanel header="{{ languageService.translate('security.monitoring.loginAttempts') }}">
      <p-table [value]="loginAttempts" 
               [paginator]="true" 
               [rows]="20"
               [loading]="loading"
               [responsive]="true">
        <ng-template pTemplate="header">
          <tr>
            <th>{{ languageService.translate('security.monitoring.timestamp') }}</th>
            <th>{{ languageService.translate('security.monitoring.username') }}</th>
            <th>{{ languageService.translate('security.monitoring.status') }}</th>
            <th>{{ languageService.translate('security.monitoring.ipAddress') }}</th>
            <th>{{ languageService.translate('security.monitoring.failureReason') }}</th>
          </tr>
        </ng-template>
        
        <ng-template pTemplate="body" let-attempt>
          <tr>
            <td>{{ formatDate(attempt.attemptedAt) }}</td>
            <td>{{ attempt.username }}</td>
            <td>
              <span class="status-badge" 
                    [class.success]="attempt.isSuccessful" 
                    [class.failure]="!attempt.isSuccessful">
                <i class="pi" 
                   [class.pi-check]="attempt.isSuccessful" 
                   [class.pi-times]="!attempt.isSuccessful"></i>
                {{ attempt.isSuccessful ? 
                   languageService.translate('security.monitoring.success') : 
                   languageService.translate('security.monitoring.failure') }}
              </span>
            </td>
            <td>{{ attempt.ipAddress }}</td>
            <td>{{ attempt.failureReason || '-' }}</td>
          </tr>
        </ng-template>
        
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="5" class="text-center">
              {{ languageService.translate('security.monitoring.noAttempts') }}
            </td>
          </tr>
        </ng-template>
      </p-table>
    </p-tabPanel>
  </p-tabView>
</div>

<!-- Access Denied Message -->
<div *appHasPermission="'audit_logs:read'" 
     [appHasPermissionRequireAll]="false" 
     class="text-center p-6">
  <i class="pi pi-lock text-6xl text-gray-400 mb-3"></i>
  <h3>{{ languageService.translate('security.monitoring.accessDenied') }}</h3>
  <p class="text-gray-600">
    {{ languageService.translate('security.monitoring.accessDeniedMessage') }}
  </p>
</div>
