{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { LayoutComponent } from './shared/components/layout/layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"primeng/toast\";\nimport * as i3 from \"primeng/confirmdialog\";\nexport class AppComponent {\n  constructor(primengConfig) {\n    this.primengConfig = primengConfig;\n    this.title = 'Warehouse Management System';\n  }\n  ngOnInit() {\n    this.primengConfig.ripple = true;\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MessageService, ConfirmationService]), i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-layout\");\n          i0.ɵɵelement(1, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(2, \"p-toast\")(3, \"p-confirmDialog\");\n        }\n      },\n      dependencies: [CommonModule, RouterOutlet, ToastModule, i2.Toast, ConfirmDialogModule, i3.ConfirmDialog, LayoutComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  height: 100vh;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0UsY0FBQTtFQUNBLGFBQUE7QUFBRiIsInNvdXJjZXNDb250ZW50IjpbIi8vIEFwcCBjb21wb25lbnQgc3BlY2lmaWMgc3R5bGVzXG46aG9zdCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xuICBoZWlnaHQ6IDEwMHZoO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "ToastModule", "ConfirmDialogModule", "MessageService", "ConfirmationService", "LayoutComponent", "AppComponent", "constructor", "primengConfig", "title", "ngOnInit", "ripple", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "i2", "Toast", "i3", "ConfirmDialog", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { PrimeNGConfig } from 'primeng/api';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService, ConfirmationService } from 'primeng/api';\n\nimport { LayoutComponent } from './shared/components/layout/layout.component';\nimport { LanguageService } from '@core/services/language.service';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    ToastModule,\n    ConfirmDialogModule,\n    LayoutComponent\n  ],\n  providers: [\n    MessageService,\n    ConfirmationService\n  ],\n  template: `\n    <app-layout>\n      <router-outlet></router-outlet>\n    </app-layout>\n    <p-toast></p-toast>\n    <p-confirmDialog></p-confirmDialog>\n  `,\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'Warehouse Management System';\n\n  constructor(private primengConfig: PrimeNGConfig) {}\n\n  ngOnInit() {\n    this.primengConfig.ripple = true;\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AAEjE,SAASC,eAAe,QAAQ,6CAA6C;;;;;AA0B7E,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IAFjC,KAAAC,KAAK,GAAG,6BAA6B;EAEc;EAEnDC,QAAQA,CAAA;IACN,IAAI,CAACF,aAAa,CAACG,MAAM,GAAG,IAAI;EAClC;;;uBAPWL,YAAY,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAZT,YAAY;MAAAU,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,kBAAA,CAbZ,CACThB,cAAc,EACdC,mBAAmB,CACpB,GAAAQ,EAAA,CAAAQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAECb,EAAA,CAAAe,cAAA,iBAAY;UACVf,EAAA,CAAAgB,SAAA,oBAA+B;UACjChB,EAAA,CAAAiB,YAAA,EAAa;UAEbjB,EADA,CAAAgB,SAAA,cAAmB,sBACgB;;;qBAfnC7B,YAAY,EACZC,YAAY,EACZC,WAAW,EAAA6B,EAAA,CAAAC,KAAA,EACX7B,mBAAmB,EAAA8B,EAAA,CAAAC,aAAA,EACnB5B,eAAe;MAAA6B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}