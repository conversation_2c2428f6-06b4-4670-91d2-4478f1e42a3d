{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, numberAttribute, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"p-inputswitch p-component\": true,\n  \"p-inputswitch-checked\": a0,\n  \"p-disabled\": a1,\n  \"p-focus\": a2\n});\nconst INPUTSWITCH_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputSwitch),\n  multi: true\n};\n/**\n * InputSwitch is used to select a boolean value.\n * @group Components\n */\nlet InputSwitch = /*#__PURE__*/(() => {\n  class InputSwitch {\n    cd;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Identifier of the input element.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    name;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    readonly;\n    /**\n     * Value in checked state.\n     * @group Props\n     */\n    trueValue = true;\n    /**\n     * Value in unchecked state.\n     * @group Props\n     */\n    falseValue = false;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Callback to invoke when the on value change.\n     * @param {InputSwitchChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    input;\n    modelValue = false;\n    focused = false;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    constructor(cd) {\n      this.cd = cd;\n    }\n    onClick(event) {\n      if (!this.disabled && !this.readonly) {\n        this.modelValue = this.checked() ? this.falseValue : this.trueValue;\n        this.onModelChange(this.modelValue);\n        this.onChange.emit({\n          originalEvent: event,\n          checked: this.modelValue\n        });\n        this.input.nativeElement.focus();\n      }\n    }\n    onFocus() {\n      this.focused = true;\n    }\n    onBlur() {\n      this.focused = false;\n      this.onModelTouched();\n    }\n    writeValue(value) {\n      this.modelValue = value;\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    checked() {\n      return this.modelValue === this.trueValue;\n    }\n    static ɵfac = function InputSwitch_Factory(t) {\n      return new (t || InputSwitch)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: InputSwitch,\n      selectors: [[\"p-inputSwitch\"]],\n      viewQuery: function InputSwitch_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\",\n        tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n        inputId: \"inputId\",\n        name: \"name\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        readonly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"readonly\", \"readonly\", booleanAttribute],\n        trueValue: \"trueValue\",\n        falseValue: \"falseValue\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute]\n      },\n      outputs: {\n        onChange: \"onChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([INPUTSWITCH_VALUE_ACCESSOR]), i0.ɵɵInputTransformsFeature],\n      decls: 5,\n      vars: 23,\n      consts: [[\"input\", \"\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", \"role\", \"switch\", \"pAutoFocus\", \"\", 3, \"focus\", \"blur\", \"checked\", \"disabled\", \"autofocus\"], [1, \"p-inputswitch-slider\"]],\n      template: function InputSwitch_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function InputSwitch_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClick($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"input\", 3, 0);\n          i0.ɵɵlistener(\"focus\", function InputSwitch_Template_input_focus_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFocus());\n          })(\"blur\", function InputSwitch_Template_input_blur_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBlur());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(4, \"span\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(19, _c1, ctx.checked(), ctx.disabled, ctx.focused))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"inputswitch\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"data-pc-section\", \"hiddenInputWrapper\")(\"data-p-hidden-accessible\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"checked\", ctx.checked())(\"disabled\", ctx.disabled)(\"autofocus\", ctx.autofocus);\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"aria-checked\", ctx.checked())(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"name\", ctx.name)(\"tabindex\", ctx.tabindex)(\"data-pc-section\", \"hiddenInput\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵattribute(\"data-pc-section\", \"slider\");\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgStyle, i2.AutoFocus],\n      styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return InputSwitch;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InputSwitchModule = /*#__PURE__*/(() => {\n  class InputSwitchModule {\n    static ɵfac = function InputSwitchModule_Factory(t) {\n      return new (t || InputSwitchModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InputSwitchModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, AutoFocusModule]\n    });\n  }\n  return InputSwitchModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTSWITCH_VALUE_ACCESSOR, InputSwitch, InputSwitchModule };\n//# sourceMappingURL=primeng-inputswitch.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}