{"ast": null, "code": "import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport const asapScheduler = new AsapScheduler(AsapAction);\nexport const asap = asapScheduler;", "map": {"version": 3, "names": ["AsapAction", "AsapScheduler", "asapScheduler", "asap"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/rxjs/dist/esm/internal/scheduler/asap.js"], "sourcesContent": ["import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport const asapScheduler = new AsapScheduler(AsapAction);\nexport const asap = asapScheduler;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,MAAMC,aAAa,GAAG,IAAID,aAAa,CAACD,UAAU,CAAC;AAC1D,OAAO,MAAMG,IAAI,GAAGD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}