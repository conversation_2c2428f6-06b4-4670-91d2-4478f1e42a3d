using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WarehouseManagement.Core.Interfaces;
using WarehouseManagement.Core.Models;

namespace WarehouseManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : BaseController
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// Authenticate user and return JWT tokens
    /// </summary>
    /// <param name="request">Login credentials</param>
    /// <returns>Login response with tokens and user information</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        try
        {
            var ipAddress = GetClientIpAddress();
            var userAgent = Request.Headers.UserAgent.ToString();

            var result = await _authService.LoginAsync(request, ipAddress, userAgent);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            // Set refresh token as httpOnly cookie
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true, // HTTPS only
                SameSite = SameSiteMode.Strict,
                Expires = DateTime.UtcNow.AddDays(7)
            };

            Response.Cookies.Append("refreshToken", result.Value.RefreshToken, cookieOptions);

            // Don't return refresh token in response body for security
            var response = new
            {
                user = result.Value.User,
                accessToken = result.Value.AccessToken,
                expiresAt = result.Value.ExpiresAt,
                tokenType = result.Value.TokenType
            };

            return Ok(CreateSuccessResponse(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Login endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Refresh access token using refresh token
    /// </summary>
    /// <returns>New access token</returns>
    [HttpPost("refresh")]
    [AllowAnonymous]
    public async Task<IActionResult> RefreshToken()
    {
        try
        {
            var refreshToken = Request.Cookies["refreshToken"];
            if (string.IsNullOrEmpty(refreshToken))
            {
                return BadRequest(CreateErrorResponse("Refresh token not found"));
            }

            var ipAddress = GetClientIpAddress();
            var result = await _authService.RefreshTokenAsync(refreshToken, ipAddress);

            if (!result.IsSuccess)
            {
                // Clear invalid refresh token cookie
                Response.Cookies.Delete("refreshToken");
                return BadRequest(CreateErrorResponse(result.Error));
            }

            // Update refresh token cookie
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.Strict,
                Expires = DateTime.UtcNow.AddDays(7)
            };

            Response.Cookies.Append("refreshToken", result.Value.RefreshToken, cookieOptions);

            var response = new
            {
                accessToken = result.Value.AccessToken,
                expiresAt = result.Value.ExpiresAt
            };

            return Ok(CreateSuccessResponse(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RefreshToken endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Logout user and invalidate tokens
    /// </summary>
    /// <returns>Success response</returns>
    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        try
        {
            var userId = GetCurrentUserId();
            var ipAddress = GetClientIpAddress();

            var result = await _authService.LogoutAsync(userId, ipAddress);

            // Clear refresh token cookie
            Response.Cookies.Delete("refreshToken");

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse("Logged out successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Logout endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Get current authenticated user information
    /// </summary>
    /// <returns>Current user details</returns>
    [HttpGet("me")]
    [Authorize]
    public async Task<IActionResult> GetCurrentUser()
    {
        try
        {
            var userId = GetCurrentUserId();
            var result = await _authService.GetCurrentUserAsync(userId);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse(result.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetCurrentUser endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Validate current session
    /// </summary>
    /// <returns>Session validation result</returns>
    [HttpGet("validate")]
    [Authorize]
    public async Task<IActionResult> ValidateSession()
    {
        try
        {
            var userId = GetCurrentUserId();
            var result = await _authService.ValidateSessionAsync(userId);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse(new { valid = true }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ValidateSession endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Change user password
    /// </summary>
    /// <param name="request">Password change request</param>
    /// <returns>Success response</returns>
    [HttpPost("change-password")]
    [Authorize]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var result = await _authService.ChangePasswordAsync(userId, request);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            // Clear refresh token cookie to force re-login
            Response.Cookies.Delete("refreshToken");

            return Ok(CreateSuccessResponse("Password changed successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ChangePassword endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Request password reset
    /// </summary>
    /// <param name="request">Password reset request</param>
    /// <returns>Success response</returns>
    [HttpPost("reset-password")]
    [AllowAnonymous]
    public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        try
        {
            var result = await _authService.ResetPasswordAsync(request);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse("Password reset instructions sent to your email"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ResetPassword endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Confirm password reset with token
    /// </summary>
    /// <param name="request">Password reset confirmation request</param>
    /// <returns>Success response</returns>
    [HttpPost("confirm-reset-password")]
    [AllowAnonymous]
    public async Task<IActionResult> ConfirmResetPassword([FromBody] ConfirmResetPasswordRequest request)
    {
        try
        {
            var result = await _authService.ConfirmResetPasswordAsync(request);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse("Password reset successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ConfirmResetPassword endpoint");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                         User.FindFirst(JwtClaims.UserId)?.Value;

        if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Invalid user ID in token");
        }

        return userId;
    }

    private string GetClientIpAddress()
    {
        // Check for forwarded IP first (in case of proxy/load balancer)
        var forwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        // Check for real IP header
        var realIp = Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fall back to connection remote IP
        return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}
