{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { LayoutComponent } from './shared/components/layout/layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@core/services/language.service\";\nimport * as i3 from \"primeng/toast\";\nimport * as i4 from \"primeng/confirmdialog\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(primengConfig, languageService) {\n      this.primengConfig = primengConfig;\n      this.languageService = languageService;\n      this.title = 'Warehouse Management System';\n    }\n    ngOnInit() {\n      this.primengConfig.ripple = true;\n      // Initialize language service and subscribe to language changes\n      this.languageService.currentLanguage$.subscribe(language => {\n        // Update PrimeNG configuration for RTL\n        this.primengConfig.ripple = true;\n        // Set document title based on language\n        document.title = this.languageService.translate('app.title') || this.title;\n      });\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i2.LanguageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([MessageService, ConfirmationService]), i0.ɵɵStandaloneFeature],\n        decls: 4,\n        vars: 0,\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"app-layout\");\n            i0.ɵɵelement(1, \"router-outlet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(2, \"p-toast\")(3, \"p-confirmDialog\");\n          }\n        },\n        dependencies: [CommonModule, RouterOutlet, ToastModule, i3.Toast, ConfirmDialogModule, i4.ConfirmDialog, LayoutComponent],\n        styles: [\"[_nghost-%COMP%]{display:block;height:100vh}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}