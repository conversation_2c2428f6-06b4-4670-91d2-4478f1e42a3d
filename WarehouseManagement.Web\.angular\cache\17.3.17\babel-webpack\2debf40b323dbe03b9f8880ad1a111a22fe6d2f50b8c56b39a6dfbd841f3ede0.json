{"ast": null, "code": "import { HttpParams, HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ApiService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiUrl;\n  }\n  get(endpoint, params) {\n    let httpParams = new HttpParams();\n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n    }\n    return this.http.get(`${this.baseUrl}/${endpoint}`, {\n      params: httpParams\n    }).pipe(catchError(this.handleError));\n  }\n  getWithPagination(endpoint, params) {\n    let httpParams = new HttpParams();\n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n    }\n    return this.http.get(`${this.baseUrl}/${endpoint}`, {\n      params: httpParams,\n      observe: 'response'\n    }).pipe(map(response => {\n      const totalCount = parseInt(response.headers.get('X-Total-Count') || '0');\n      const pageNumber = parseInt(response.headers.get('X-Page-Number') || '1');\n      const pageSize = parseInt(response.headers.get('X-Page-Size') || '10');\n      const totalPages = parseInt(response.headers.get('X-Total-Pages') || '1');\n      return {\n        data: response.body || [],\n        totalCount,\n        pageNumber,\n        pageSize,\n        totalPages,\n        hasPreviousPage: pageNumber > 1,\n        hasNextPage: pageNumber < totalPages\n      };\n    }), catchError(this.handleError));\n  }\n  post(endpoint, data) {\n    return this.http.post(`${this.baseUrl}/${endpoint}`, data, {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    }).pipe(catchError(this.handleError));\n  }\n  put(endpoint, data) {\n    return this.http.put(`${this.baseUrl}/${endpoint}`, data, {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    }).pipe(catchError(this.handleError));\n  }\n  delete(endpoint) {\n    return this.http.delete(`${this.baseUrl}/${endpoint}`).pipe(catchError(this.handleError));\n  }\n  uploadFile(endpoint, file, additionalData) {\n    const formData = new FormData();\n    formData.append('file', file);\n    if (additionalData) {\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n    }\n    return this.http.post(`${this.baseUrl}/${endpoint}`, formData).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = 'An unknown error occurred';\n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error\n      if (error.status === 0) {\n        errorMessage = 'Unable to connect to the server. Please check your internet connection.';\n      } else if (error.status >= 400 && error.status < 500) {\n        errorMessage = error.error?.message || error.error || `Client error: ${error.status}`;\n      } else if (error.status >= 500) {\n        errorMessage = 'Server error. Please try again later.';\n      } else {\n        errorMessage = error.error?.message || error.message || errorMessage;\n      }\n    }\n    console.error('API Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function ApiService_Factory(t) {\n      return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ApiService,\n      factory: ApiService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "HttpHeaders", "throwError", "catchError", "map", "environment", "ApiService", "constructor", "http", "baseUrl", "apiUrl", "get", "endpoint", "params", "httpParams", "Object", "keys", "for<PERSON>ach", "key", "undefined", "set", "toString", "pipe", "handleError", "getWithPagination", "observe", "response", "totalCount", "parseInt", "headers", "pageNumber", "pageSize", "totalPages", "data", "body", "hasPreviousPage", "hasNextPage", "post", "put", "delete", "uploadFile", "file", "additionalData", "formData", "FormData", "append", "error", "errorMessage", "ErrorEvent", "message", "status", "console", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\services\\api.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '@environments/environment';\nimport { PagedResult } from '../models/common.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApiService {\n  private readonly baseUrl = environment.apiUrl;\n\n  constructor(private http: HttpClient) {}\n\n  get<T>(endpoint: string, params?: any): Observable<T> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n    }\n\n    return this.http.get<T>(`${this.baseUrl}/${endpoint}`, { params: httpParams })\n      .pipe(catchError(this.handleError));\n  }\n\n  getWithPagination<T>(endpoint: string, params?: any): Observable<PagedResult<T>> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n    }\n\n    return this.http.get<T[]>(`${this.baseUrl}/${endpoint}`, { \n      params: httpParams, \n      observe: 'response' \n    }).pipe(\n      map(response => {\n        const totalCount = parseInt(response.headers.get('X-Total-Count') || '0');\n        const pageNumber = parseInt(response.headers.get('X-Page-Number') || '1');\n        const pageSize = parseInt(response.headers.get('X-Page-Size') || '10');\n        const totalPages = parseInt(response.headers.get('X-Total-Pages') || '1');\n\n        return {\n          data: response.body || [],\n          totalCount,\n          pageNumber,\n          pageSize,\n          totalPages,\n          hasPreviousPage: pageNumber > 1,\n          hasNextPage: pageNumber < totalPages\n        } as PagedResult<T>;\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  post<T>(endpoint: string, data: any): Observable<T> {\n    return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data, {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    }).pipe(catchError(this.handleError));\n  }\n\n  put<T>(endpoint: string, data: any): Observable<T> {\n    return this.http.put<T>(`${this.baseUrl}/${endpoint}`, data, {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    }).pipe(catchError(this.handleError));\n  }\n\n  delete<T>(endpoint: string): Observable<T> {\n    return this.http.delete<T>(`${this.baseUrl}/${endpoint}`)\n      .pipe(catchError(this.handleError));\n  }\n\n  uploadFile(endpoint: string, file: File, additionalData?: any): Observable<any> {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    if (additionalData) {\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n    }\n\n    return this.http.post(`${this.baseUrl}/${endpoint}`, formData)\n      .pipe(catchError(this.handleError));\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An unknown error occurred';\n    \n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error\n      if (error.status === 0) {\n        errorMessage = 'Unable to connect to the server. Please check your internet connection.';\n      } else if (error.status >= 400 && error.status < 500) {\n        errorMessage = error.error?.message || error.error || `Client error: ${error.status}`;\n      } else if (error.status >= 500) {\n        errorMessage = 'Server error. Please try again later.';\n      } else {\n        errorMessage = error.error?.message || error.message || errorMessage;\n      }\n    }\n\n    console.error('API Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,EAAEC,WAAW,QAAQ,sBAAsB;AAC1E,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,2BAA2B;;;AAMvD,OAAM,MAAOC,UAAU;EAGrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;EAEN;EAEvCC,GAAGA,CAAIC,QAAgB,EAAEC,MAAY;IACnC,IAAIC,UAAU,GAAG,IAAId,UAAU,EAAE;IAEjC,IAAIa,MAAM,EAAE;MACVE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QAChC,IAAIL,MAAM,CAACK,GAAG,CAAC,KAAK,IAAI,IAAIL,MAAM,CAACK,GAAG,CAAC,KAAKC,SAAS,IAAIN,MAAM,CAACK,GAAG,CAAC,KAAK,EAAE,EAAE;UAC3EJ,UAAU,GAAGA,UAAU,CAACM,GAAG,CAACF,GAAG,EAAEL,MAAM,CAACK,GAAG,CAAC,CAACG,QAAQ,EAAE,CAAC;;MAE5D,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACb,IAAI,CAACG,GAAG,CAAI,GAAG,IAAI,CAACF,OAAO,IAAIG,QAAQ,EAAE,EAAE;MAAEC,MAAM,EAAEC;IAAU,CAAE,CAAC,CAC3EQ,IAAI,CAACnB,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAAC;EACvC;EAEAC,iBAAiBA,CAAIZ,QAAgB,EAAEC,MAAY;IACjD,IAAIC,UAAU,GAAG,IAAId,UAAU,EAAE;IAEjC,IAAIa,MAAM,EAAE;MACVE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QAChC,IAAIL,MAAM,CAACK,GAAG,CAAC,KAAK,IAAI,IAAIL,MAAM,CAACK,GAAG,CAAC,KAAKC,SAAS,IAAIN,MAAM,CAACK,GAAG,CAAC,KAAK,EAAE,EAAE;UAC3EJ,UAAU,GAAGA,UAAU,CAACM,GAAG,CAACF,GAAG,EAAEL,MAAM,CAACK,GAAG,CAAC,CAACG,QAAQ,EAAE,CAAC;;MAE5D,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACb,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,OAAO,IAAIG,QAAQ,EAAE,EAAE;MACvDC,MAAM,EAAEC,UAAU;MAClBW,OAAO,EAAE;KACV,CAAC,CAACH,IAAI,CACLlB,GAAG,CAACsB,QAAQ,IAAG;MACb,MAAMC,UAAU,GAAGC,QAAQ,CAACF,QAAQ,CAACG,OAAO,CAAClB,GAAG,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC;MACzE,MAAMmB,UAAU,GAAGF,QAAQ,CAACF,QAAQ,CAACG,OAAO,CAAClB,GAAG,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC;MACzE,MAAMoB,QAAQ,GAAGH,QAAQ,CAACF,QAAQ,CAACG,OAAO,CAAClB,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MACtE,MAAMqB,UAAU,GAAGJ,QAAQ,CAACF,QAAQ,CAACG,OAAO,CAAClB,GAAG,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC;MAEzE,OAAO;QACLsB,IAAI,EAAEP,QAAQ,CAACQ,IAAI,IAAI,EAAE;QACzBP,UAAU;QACVG,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVG,eAAe,EAAEL,UAAU,GAAG,CAAC;QAC/BM,WAAW,EAAEN,UAAU,GAAGE;OACT;IACrB,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAC7B;EACH;EAEAc,IAAIA,CAAIzB,QAAgB,EAAEqB,IAAS;IACjC,OAAO,IAAI,CAACzB,IAAI,CAAC6B,IAAI,CAAI,GAAG,IAAI,CAAC5B,OAAO,IAAIG,QAAQ,EAAE,EAAEqB,IAAI,EAAE;MAC5DJ,OAAO,EAAE,IAAI5B,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF,CAAC,CAACqB,IAAI,CAACnB,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAAC;EACvC;EAEAe,GAAGA,CAAI1B,QAAgB,EAAEqB,IAAS;IAChC,OAAO,IAAI,CAACzB,IAAI,CAAC8B,GAAG,CAAI,GAAG,IAAI,CAAC7B,OAAO,IAAIG,QAAQ,EAAE,EAAEqB,IAAI,EAAE;MAC3DJ,OAAO,EAAE,IAAI5B,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF,CAAC,CAACqB,IAAI,CAACnB,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAAC;EACvC;EAEAgB,MAAMA,CAAI3B,QAAgB;IACxB,OAAO,IAAI,CAACJ,IAAI,CAAC+B,MAAM,CAAI,GAAG,IAAI,CAAC9B,OAAO,IAAIG,QAAQ,EAAE,CAAC,CACtDU,IAAI,CAACnB,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAAC;EACvC;EAEAiB,UAAUA,CAAC5B,QAAgB,EAAE6B,IAAU,EAAEC,cAAoB;IAC3D,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,CAAC;IAE7B,IAAIC,cAAc,EAAE;MAClB3B,MAAM,CAACC,IAAI,CAAC0B,cAAc,CAAC,CAACzB,OAAO,CAACC,GAAG,IAAG;QACxCyB,QAAQ,CAACE,MAAM,CAAC3B,GAAG,EAAEwB,cAAc,CAACxB,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACV,IAAI,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAAC5B,OAAO,IAAIG,QAAQ,EAAE,EAAE+B,QAAQ,CAAC,CAC3DrB,IAAI,CAACnB,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAAC;EACvC;EAEQA,WAAWA,CAACuB,KAAU;IAC5B,IAAIC,YAAY,GAAG,2BAA2B;IAE9C,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACG,OAAO;KACnC,MAAM;MACL;MACA,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;QACtBH,YAAY,GAAG,yEAAyE;OACzF,MAAM,IAAID,KAAK,CAACI,MAAM,IAAI,GAAG,IAAIJ,KAAK,CAACI,MAAM,GAAG,GAAG,EAAE;QACpDH,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAIH,KAAK,CAACA,KAAK,IAAI,iBAAiBA,KAAK,CAACI,MAAM,EAAE;OACtF,MAAM,IAAIJ,KAAK,CAACI,MAAM,IAAI,GAAG,EAAE;QAC9BH,YAAY,GAAG,uCAAuC;OACvD,MAAM;QACLA,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAIH,KAAK,CAACG,OAAO,IAAIF,YAAY;;;IAIxEI,OAAO,CAACL,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO5C,UAAU,CAAC,MAAM,IAAIkD,KAAK,CAACL,YAAY,CAAC,CAAC;EAClD;;;uBA/GWzC,UAAU,EAAA+C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAVlD,UAAU;MAAAmD,OAAA,EAAVnD,UAAU,CAAAoD,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}