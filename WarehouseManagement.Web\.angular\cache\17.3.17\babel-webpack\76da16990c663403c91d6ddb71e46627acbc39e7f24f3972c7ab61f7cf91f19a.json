{"ast": null, "code": "import { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { TemplateRef, PLATFORM_ID, booleanAttribute, numberAttribute, Directive, Inject, Input, NgModule } from '@angular/core';\nimport { <PERSON>Handler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\n/**\n * Tooltip directive provides advisory information for a component.\n * @group Components\n */\nclass Tooltip {\n  platformId;\n  el;\n  zone;\n  config;\n  renderer;\n  viewContainer;\n  /**\n   * Position of the tooltip.\n   * @group Props\n   */\n  tooltipPosition;\n  /**\n   * Event to show the tooltip.\n   * @group Props\n   */\n  tooltipEvent = 'hover';\n  /**\n   *  Target element to attach the overlay, valid values are \"body\", \"target\" or a local ng-F variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Type of CSS position.\n   * @group Props\n   */\n  positionStyle;\n  /**\n   * Style class of the tooltip.\n   * @group Props\n   */\n  tooltipStyleClass;\n  /**\n   * Whether the z-index should be managed automatically to always go on top or have a fixed value.\n   * @group Props\n   */\n  tooltipZIndex;\n  /**\n   * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.\n   * @group Props\n   */\n  escape = true;\n  /**\n   * Delay to show the tooltip in milliseconds.\n   * @group Props\n   */\n  showDelay;\n  /**\n   * Delay to hide the tooltip in milliseconds.\n   * @group Props\n   */\n  hideDelay;\n  /**\n   * Time to wait in milliseconds to hide the tooltip even it is active.\n   * @group Props\n   */\n  life;\n  /**\n   * Specifies the additional vertical offset of the tooltip from its default position.\n   * @group Props\n   */\n  positionTop;\n  /**\n   * Specifies the additional horizontal offset of the tooltip from its default position.\n   * @group Props\n   */\n  positionLeft;\n  /**\n   * Whether to hide tooltip when hovering over tooltip content.\n   * @group Props\n   */\n  autoHide = true;\n  /**\n   * Automatically adjusts the element position when there is not enough space on the selected position.\n   * @group Props\n   */\n  fitContent = true;\n  /**\n   * Whether to hide tooltip on escape key press.\n   * @group Props\n   */\n  hideOnEscape = true;\n  /**\n   * Content of the tooltip.\n   * @group Props\n   */\n  content;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @defaultValue false\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(val) {\n    this._disabled = val;\n    this.deactivate();\n  }\n  /**\n   * Specifies the tooltip configuration options for the component.\n   * @group Props\n   */\n  tooltipOptions;\n  _tooltipOptions = {\n    tooltipLabel: null,\n    tooltipPosition: 'right',\n    tooltipEvent: 'hover',\n    appendTo: 'body',\n    positionStyle: null,\n    tooltipStyleClass: null,\n    tooltipZIndex: 'auto',\n    escape: true,\n    disabled: null,\n    showDelay: null,\n    hideDelay: null,\n    positionTop: null,\n    positionLeft: null,\n    life: null,\n    autoHide: true,\n    hideOnEscape: true,\n    id: UniqueComponentId() + '_tooltip'\n  };\n  _disabled;\n  container;\n  styleClass;\n  tooltipText;\n  showTimeout;\n  hideTimeout;\n  active;\n  mouseEnterListener;\n  mouseLeaveListener;\n  containerMouseleaveListener;\n  clickListener;\n  focusListener;\n  blurListener;\n  documentEscapeListener;\n  scrollHandler;\n  resizeListener;\n  interactionInProgress = false;\n  constructor(platformId, el, zone, config, renderer, viewContainer) {\n    this.platformId = platformId;\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n    this.renderer = renderer;\n    this.viewContainer = viewContainer;\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.zone.runOutsideAngular(() => {\n        const tooltipEvent = this.getOption('tooltipEvent');\n        if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n          this.mouseEnterListener = this.onMouseEnter.bind(this);\n          this.mouseLeaveListener = this.onMouseLeave.bind(this);\n          this.clickListener = this.onInputClick.bind(this);\n          this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n          this.el.nativeElement.addEventListener('click', this.clickListener);\n          this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n        }\n        if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n          this.focusListener = this.onFocus.bind(this);\n          this.blurListener = this.onBlur.bind(this);\n          let target = this.el.nativeElement.querySelector('.p-component');\n          if (!target) {\n            target = this.getTarget(this.el.nativeElement);\n          }\n          target.addEventListener('focus', this.focusListener);\n          target.addEventListener('blur', this.blurListener);\n        }\n      });\n    }\n  }\n  setAriaDescribedBy() {\n    const tooltipId = this.getOption('id');\n    if (tooltipId && this.active) {\n      this.renderer.setAttribute(this.el.nativeElement, 'aria-describedby', tooltipId);\n    }\n  }\n  removeAriaDescribedBy() {\n    this.renderer.removeAttribute(this.el.nativeElement, 'aria-describedby');\n  }\n  ngOnChanges(simpleChange) {\n    if (simpleChange.tooltipPosition) {\n      this.setOption({\n        tooltipPosition: simpleChange.tooltipPosition.currentValue\n      });\n    }\n    if (simpleChange.tooltipEvent) {\n      this.setOption({\n        tooltipEvent: simpleChange.tooltipEvent.currentValue\n      });\n    }\n    if (simpleChange.appendTo) {\n      this.setOption({\n        appendTo: simpleChange.appendTo.currentValue\n      });\n    }\n    if (simpleChange.positionStyle) {\n      this.setOption({\n        positionStyle: simpleChange.positionStyle.currentValue\n      });\n    }\n    if (simpleChange.tooltipStyleClass) {\n      this.setOption({\n        tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue\n      });\n    }\n    if (simpleChange.tooltipZIndex) {\n      this.setOption({\n        tooltipZIndex: simpleChange.tooltipZIndex.currentValue\n      });\n    }\n    if (simpleChange.escape) {\n      this.setOption({\n        escape: simpleChange.escape.currentValue\n      });\n    }\n    if (simpleChange.showDelay) {\n      this.setOption({\n        showDelay: simpleChange.showDelay.currentValue\n      });\n    }\n    if (simpleChange.hideDelay) {\n      this.setOption({\n        hideDelay: simpleChange.hideDelay.currentValue\n      });\n    }\n    if (simpleChange.life) {\n      this.setOption({\n        life: simpleChange.life.currentValue\n      });\n    }\n    if (simpleChange.positionTop) {\n      this.setOption({\n        positionTop: simpleChange.positionTop.currentValue\n      });\n    }\n    if (simpleChange.positionLeft) {\n      this.setOption({\n        positionLeft: simpleChange.positionLeft.currentValue\n      });\n    }\n    if (simpleChange.disabled) {\n      this.setOption({\n        disabled: simpleChange.disabled.currentValue\n      });\n    }\n    if (simpleChange.content) {\n      this.setOption({\n        tooltipLabel: simpleChange.content.currentValue\n      });\n      if (this.active) {\n        if (simpleChange.content.currentValue) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n    if (simpleChange.autoHide) {\n      this.setOption({\n        autoHide: simpleChange.autoHide.currentValue\n      });\n    }\n    if (simpleChange.id) {\n      this.setOption({\n        id: simpleChange.id.currentValue\n      });\n    }\n    if (simpleChange.tooltipOptions) {\n      this._tooltipOptions = {\n        ...this._tooltipOptions,\n        ...simpleChange.tooltipOptions.currentValue\n      };\n      this.deactivate();\n      if (this.active) {\n        if (this.getOption('tooltipLabel')) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n  }\n  isAutoHide() {\n    return this.getOption('autoHide');\n  }\n  onMouseEnter(e) {\n    if (!this.container && !this.showTimeout) {\n      this.activate();\n    }\n  }\n  onMouseLeave(e) {\n    if (!this.isAutoHide()) {\n      const valid = DomHandler.hasClass(e.relatedTarget, 'p-tooltip') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-text') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-arrow');\n      !valid && this.deactivate();\n    } else {\n      this.deactivate();\n    }\n  }\n  onFocus(e) {\n    this.activate();\n  }\n  onBlur(e) {\n    this.deactivate();\n  }\n  onInputClick(e) {\n    this.deactivate();\n  }\n  activate() {\n    if (!this.interactionInProgress) {\n      this.active = true;\n      this.clearHideTimeout();\n      if (this.getOption('showDelay')) this.showTimeout = setTimeout(() => {\n        this.show();\n      }, this.getOption('showDelay'));else this.show();\n      if (this.getOption('life')) {\n        let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n        this.hideTimeout = setTimeout(() => {\n          this.hide();\n        }, duration);\n      }\n      if (this.getOption('hideOnEscape')) {\n        this.documentEscapeListener = this.renderer.listen('document', 'keydown.escape', () => {\n          this.deactivate();\n          this.documentEscapeListener();\n        });\n      }\n    }\n    this.interactionInProgress = true;\n  }\n  deactivate() {\n    this.interactionInProgress = false;\n    this.active = false;\n    this.clearShowTimeout();\n    if (this.getOption('hideDelay')) {\n      this.clearHideTimeout(); //life timeout\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, this.getOption('hideDelay'));\n    } else {\n      this.hide();\n    }\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n    }\n  }\n  create() {\n    if (this.container) {\n      this.clearHideTimeout();\n      this.remove();\n    }\n    this.container = document.createElement('div');\n    this.container.setAttribute('id', this.getOption('id'));\n    this.container.setAttribute('role', 'tooltip');\n    let tooltipArrow = document.createElement('div');\n    tooltipArrow.className = 'p-tooltip-arrow';\n    this.container.appendChild(tooltipArrow);\n    this.tooltipText = document.createElement('div');\n    this.tooltipText.className = 'p-tooltip-text';\n    this.updateText();\n    if (this.getOption('positionStyle')) {\n      this.container.style.position = this.getOption('positionStyle');\n    }\n    this.container.appendChild(this.tooltipText);\n    if (this.getOption('appendTo') === 'body') document.body.appendChild(this.container);else if (this.getOption('appendTo') === 'target') DomHandler.appendChild(this.container, this.el.nativeElement);else DomHandler.appendChild(this.container, this.getOption('appendTo'));\n    this.container.style.display = 'inline-block';\n    if (this.fitContent) {\n      this.container.style.width = 'fit-content';\n    }\n    if (this.isAutoHide()) {\n      this.container.style.pointerEvents = 'none';\n    } else {\n      this.container.style.pointerEvents = 'unset';\n      this.bindContainerMouseleaveListener();\n    }\n    this.setAriaDescribedBy();\n  }\n  bindContainerMouseleaveListener() {\n    if (!this.containerMouseleaveListener) {\n      const targetEl = this.container ?? this.container.nativeElement;\n      this.containerMouseleaveListener = this.renderer.listen(targetEl, 'mouseleave', e => {\n        this.deactivate();\n      });\n    }\n  }\n  unbindContainerMouseleaveListener() {\n    if (this.containerMouseleaveListener) {\n      this.bindContainerMouseleaveListener();\n      this.containerMouseleaveListener = null;\n    }\n  }\n  show() {\n    if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n      return;\n    }\n    this.create();\n    const nativeElement = this.el.nativeElement;\n    const pDialogWrapper = nativeElement.closest('p-dialog');\n    if (pDialogWrapper) {\n      setTimeout(() => {\n        this.container && this.align();\n      }, 100);\n    } else {\n      this.align();\n    }\n    DomHandler.fadeIn(this.container, 250);\n    if (this.getOption('tooltipZIndex') === 'auto') ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);else this.container.style.zIndex = this.getOption('tooltipZIndex');\n    this.bindDocumentResizeListener();\n    this.bindScrollListener();\n  }\n  hide() {\n    if (this.getOption('tooltipZIndex') === 'auto') {\n      ZIndexUtils.clear(this.container);\n    }\n    this.remove();\n  }\n  updateText() {\n    const content = this.getOption('tooltipLabel');\n    if (content instanceof TemplateRef) {\n      const embeddedViewRef = this.viewContainer.createEmbeddedView(content);\n      embeddedViewRef.detectChanges();\n      embeddedViewRef.rootNodes.forEach(node => this.tooltipText.appendChild(node));\n    } else if (this.getOption('escape')) {\n      this.tooltipText.innerHTML = '';\n      this.tooltipText.appendChild(document.createTextNode(content));\n    } else {\n      this.tooltipText.innerHTML = content;\n    }\n  }\n  align() {\n    let position = this.getOption('tooltipPosition');\n    switch (position) {\n      case 'top':\n        this.alignTop();\n        if (this.isOutOfBounds()) {\n          this.alignBottom();\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n        break;\n      case 'bottom':\n        this.alignBottom();\n        if (this.isOutOfBounds()) {\n          this.alignTop();\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n        break;\n      case 'left':\n        this.alignLeft();\n        if (this.isOutOfBounds()) {\n          this.alignRight();\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n        break;\n      case 'right':\n        this.alignRight();\n        if (this.isOutOfBounds()) {\n          this.alignLeft();\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n        break;\n    }\n  }\n  getHostOffset() {\n    if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n      let offset = this.el.nativeElement.getBoundingClientRect();\n      let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n      let targetTop = offset.top + DomHandler.getWindowScrollTop();\n      return {\n        left: targetLeft,\n        top: targetTop\n      };\n    } else {\n      return {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n  alignRight() {\n    this.preAlign('right');\n    const el = this.activeElement;\n    const hostOffset = this.getHostOffset();\n    const left = hostOffset.left + DomHandler.getOuterWidth(el);\n    const top = hostOffset.top + (DomHandler.getOuterHeight(el) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  get activeElement() {\n    return this.el.nativeElement.nodeName.includes('P-') ? DomHandler.findSingle(this.el.nativeElement, '.p-component') || this.el.nativeElement : this.el.nativeElement;\n  }\n  alignLeft() {\n    this.preAlign('left');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  alignTop() {\n    this.preAlign('top');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  alignBottom() {\n    this.preAlign('bottom');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  setOption(option) {\n    this._tooltipOptions = {\n      ...this._tooltipOptions,\n      ...option\n    };\n  }\n  getOption(option) {\n    return this._tooltipOptions[option];\n  }\n  getTarget(el) {\n    return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n  }\n  preAlign(position) {\n    this.container.style.left = -999 + 'px';\n    this.container.style.top = -999 + 'px';\n    let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n    this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n  }\n  isOutOfBounds() {\n    let offset = this.container.getBoundingClientRect();\n    let targetTop = offset.top;\n    let targetLeft = offset.left;\n    let width = DomHandler.getOuterWidth(this.container);\n    let height = DomHandler.getOuterHeight(this.container);\n    let viewport = DomHandler.getViewport();\n    return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n  }\n  onWindowResize(e) {\n    this.hide();\n  }\n  bindDocumentResizeListener() {\n    this.zone.runOutsideAngular(() => {\n      this.resizeListener = this.onWindowResize.bind(this);\n      window.addEventListener('resize', this.resizeListener);\n    });\n  }\n  unbindDocumentResizeListener() {\n    if (this.resizeListener) {\n      window.removeEventListener('resize', this.resizeListener);\n      this.resizeListener = null;\n    }\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n        if (this.container) {\n          this.hide();\n        }\n      });\n    }\n    this.scrollHandler.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n  unbindEvents() {\n    const tooltipEvent = this.getOption('tooltipEvent');\n    if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n      this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n      this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n      this.el.nativeElement.removeEventListener('click', this.clickListener);\n    }\n    if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n      let target = this.el.nativeElement.querySelector('.p-component');\n      if (!target) {\n        target = this.getTarget(this.el.nativeElement);\n      }\n    }\n    this.unbindDocumentResizeListener();\n  }\n  remove() {\n    if (this.container && this.container.parentElement) {\n      if (this.getOption('appendTo') === 'body') document.body.removeChild(this.container);else if (this.getOption('appendTo') === 'target') this.el.nativeElement.removeChild(this.container);else DomHandler.removeChild(this.container, this.getOption('appendTo'));\n    }\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.unbindContainerMouseleaveListener();\n    this.clearTimeouts();\n    this.removeAriaDescribedBy();\n    this.container = null;\n    this.scrollHandler = null;\n  }\n  clearShowTimeout() {\n    if (this.showTimeout) {\n      clearTimeout(this.showTimeout);\n      this.showTimeout = null;\n    }\n  }\n  clearHideTimeout() {\n    if (this.hideTimeout) {\n      clearTimeout(this.hideTimeout);\n      this.hideTimeout = null;\n    }\n  }\n  clearTimeouts() {\n    this.clearShowTimeout();\n    this.clearHideTimeout();\n  }\n  ngOnDestroy() {\n    this.unbindEvents();\n    if (this.container) {\n      ZIndexUtils.clear(this.container);\n    }\n    this.remove();\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n    }\n  }\n  static ɵfac = function Tooltip_Factory(t) {\n    return new (t || Tooltip)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Tooltip,\n    selectors: [[\"\", \"pTooltip\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      tooltipPosition: \"tooltipPosition\",\n      tooltipEvent: \"tooltipEvent\",\n      appendTo: \"appendTo\",\n      positionStyle: \"positionStyle\",\n      tooltipStyleClass: \"tooltipStyleClass\",\n      tooltipZIndex: \"tooltipZIndex\",\n      escape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"escape\", \"escape\", booleanAttribute],\n      showDelay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showDelay\", \"showDelay\", numberAttribute],\n      hideDelay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideDelay\", \"hideDelay\", numberAttribute],\n      life: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"life\", \"life\", numberAttribute],\n      positionTop: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"positionTop\", \"positionTop\", numberAttribute],\n      positionLeft: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"positionLeft\", \"positionLeft\", numberAttribute],\n      autoHide: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoHide\", \"autoHide\", booleanAttribute],\n      fitContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitContent\", \"fitContent\", booleanAttribute],\n      hideOnEscape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideOnEscape\", \"hideOnEscape\", booleanAttribute],\n      content: [i0.ɵɵInputFlags.None, \"pTooltip\", \"content\"],\n      disabled: [i0.ɵɵInputFlags.None, \"tooltipDisabled\", \"disabled\"],\n      tooltipOptions: \"tooltipOptions\"\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[pTooltip]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.PrimeNGConfig\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    tooltipPosition: [{\n      type: Input\n    }],\n    tooltipEvent: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    positionStyle: [{\n      type: Input\n    }],\n    tooltipStyleClass: [{\n      type: Input\n    }],\n    tooltipZIndex: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showDelay: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    hideDelay: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    life: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    positionTop: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    positionLeft: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    autoHide: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fitContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideOnEscape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    content: [{\n      type: Input,\n      args: ['pTooltip']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['tooltipDisabled']\n    }],\n    tooltipOptions: [{\n      type: Input\n    }]\n  });\n})();\nclass TooltipModule {\n  static ɵfac = function TooltipModule_Factory(t) {\n    return new (t || TooltipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TooltipModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Tooltip],\n      declarations: [Tooltip]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };", "map": {"version": 3, "names": ["isPlatformBrowser", "CommonModule", "i0", "TemplateRef", "PLATFORM_ID", "booleanAttribute", "numberAttribute", "Directive", "Inject", "Input", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "UniqueComponentId", "ZIndexUtils", "i1", "<PERSON><PERSON><PERSON>", "platformId", "el", "zone", "config", "renderer", "viewContainer", "tooltipPosition", "tooltipEvent", "appendTo", "positionStyle", "tooltipStyleClass", "tooltipZIndex", "escape", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "life", "positionTop", "positionLeft", "autoHide", "<PERSON><PERSON><PERSON><PERSON>", "hideOnEscape", "content", "disabled", "_disabled", "val", "deactivate", "tooltipOptions", "_tooltipOptions", "tooltipLabel", "id", "container", "styleClass", "tooltipText", "showTimeout", "hideTimeout", "active", "mouseEnterListener", "mouseLeaveListener", "containerMouseleaveListener", "clickListener", "focusListener", "blurListener", "documentEscapeListener", "<PERSON><PERSON><PERSON><PERSON>", "resizeListener", "interactionInProgress", "constructor", "ngAfterViewInit", "runOutsideAngular", "getOption", "onMouseEnter", "bind", "onMouseLeave", "onInputClick", "nativeElement", "addEventListener", "onFocus", "onBlur", "target", "querySelector", "get<PERSON><PERSON><PERSON>", "setAriaDescribedBy", "tooltipId", "setAttribute", "removeAriaDescribedBy", "removeAttribute", "ngOnChanges", "simpleChange", "setOption", "currentValue", "offsetParent", "updateText", "align", "show", "hide", "isAutoHide", "e", "activate", "valid", "hasClass", "relatedTarget", "clearHideTimeout", "setTimeout", "duration", "listen", "clearShowTimeout", "create", "remove", "document", "createElement", "tooltipArrow", "className", "append<PERSON><PERSON><PERSON>", "style", "position", "body", "display", "width", "pointerEvents", "bindContainerMouseleaveListener", "targetEl", "unbindContainerMouseleaveListener", "pDialogWrapper", "closest", "fadeIn", "set", "zIndex", "tooltip", "bindDocumentResizeListener", "bindScrollListener", "clear", "embeddedViewRef", "createEmbeddedView", "detectChanges", "rootNodes", "for<PERSON>ach", "node", "innerHTML", "createTextNode", "alignTop", "isOutOfBounds", "alignBottom", "alignRight", "alignLeft", "getHostOffset", "offset", "getBoundingClientRect", "targetLeft", "left", "getWindowScrollLeft", "targetTop", "top", "getWindowScrollTop", "preAlign", "activeElement", "hostOffset", "getOuterWidth", "getOuterHeight", "nodeName", "includes", "findSingle", "option", "defaultClassName", "height", "viewport", "getViewport", "onWindowResize", "window", "unbindDocumentResizeListener", "removeEventListener", "unbindScrollListener", "unbindEvents", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeouts", "clearTimeout", "ngOnDestroy", "destroy", "ɵfac", "Tooltip_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgZone", "PrimeNGConfig", "Renderer2", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "None", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "undefined", "decorators", "transform", "TooltipModule", "TooltipModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-tooltip.mjs"], "sourcesContent": ["import { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { TemplateRef, PLATFORM_ID, booleanAttribute, numberAttribute, Directive, Inject, Input, NgModule } from '@angular/core';\nimport { <PERSON>Handler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\n/**\n * Tooltip directive provides advisory information for a component.\n * @group Components\n */\nclass Tooltip {\n    platformId;\n    el;\n    zone;\n    config;\n    renderer;\n    viewContainer;\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    tooltipPosition;\n    /**\n     * Event to show the tooltip.\n     * @group Props\n     */\n    tooltipEvent = 'hover';\n    /**\n     *  Target element to attach the overlay, valid values are \"body\", \"target\" or a local ng-F variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    positionStyle;\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    tooltipStyleClass;\n    /**\n     * Whether the z-index should be managed automatically to always go on top or have a fixed value.\n     * @group Props\n     */\n    tooltipZIndex;\n    /**\n     * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.\n     * @group Props\n     */\n    escape = true;\n    /**\n     * Delay to show the tooltip in milliseconds.\n     * @group Props\n     */\n    showDelay;\n    /**\n     * Delay to hide the tooltip in milliseconds.\n     * @group Props\n     */\n    hideDelay;\n    /**\n     * Time to wait in milliseconds to hide the tooltip even it is active.\n     * @group Props\n     */\n    life;\n    /**\n     * Specifies the additional vertical offset of the tooltip from its default position.\n     * @group Props\n     */\n    positionTop;\n    /**\n     * Specifies the additional horizontal offset of the tooltip from its default position.\n     * @group Props\n     */\n    positionLeft;\n    /**\n     * Whether to hide tooltip when hovering over tooltip content.\n     * @group Props\n     */\n    autoHide = true;\n    /**\n     * Automatically adjusts the element position when there is not enough space on the selected position.\n     * @group Props\n     */\n    fitContent = true;\n    /**\n     * Whether to hide tooltip on escape key press.\n     * @group Props\n     */\n    hideOnEscape = true;\n    /**\n     * Content of the tooltip.\n     * @group Props\n     */\n    content;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @defaultValue false\n     * @group Props\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(val) {\n        this._disabled = val;\n        this.deactivate();\n    }\n    /**\n     * Specifies the tooltip configuration options for the component.\n     * @group Props\n     */\n    tooltipOptions;\n    _tooltipOptions = {\n        tooltipLabel: null,\n        tooltipPosition: 'right',\n        tooltipEvent: 'hover',\n        appendTo: 'body',\n        positionStyle: null,\n        tooltipStyleClass: null,\n        tooltipZIndex: 'auto',\n        escape: true,\n        disabled: null,\n        showDelay: null,\n        hideDelay: null,\n        positionTop: null,\n        positionLeft: null,\n        life: null,\n        autoHide: true,\n        hideOnEscape: true,\n        id: UniqueComponentId() + '_tooltip'\n    };\n    _disabled;\n    container;\n    styleClass;\n    tooltipText;\n    showTimeout;\n    hideTimeout;\n    active;\n    mouseEnterListener;\n    mouseLeaveListener;\n    containerMouseleaveListener;\n    clickListener;\n    focusListener;\n    blurListener;\n    documentEscapeListener;\n    scrollHandler;\n    resizeListener;\n    interactionInProgress = false;\n    constructor(platformId, el, zone, config, renderer, viewContainer) {\n        this.platformId = platformId;\n        this.el = el;\n        this.zone = zone;\n        this.config = config;\n        this.renderer = renderer;\n        this.viewContainer = viewContainer;\n    }\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                const tooltipEvent = this.getOption('tooltipEvent');\n                if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n                    this.mouseEnterListener = this.onMouseEnter.bind(this);\n                    this.mouseLeaveListener = this.onMouseLeave.bind(this);\n                    this.clickListener = this.onInputClick.bind(this);\n                    this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n                    this.el.nativeElement.addEventListener('click', this.clickListener);\n                    this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n                }\n                if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n                    this.focusListener = this.onFocus.bind(this);\n                    this.blurListener = this.onBlur.bind(this);\n                    let target = this.el.nativeElement.querySelector('.p-component');\n                    if (!target) {\n                        target = this.getTarget(this.el.nativeElement);\n                    }\n                    target.addEventListener('focus', this.focusListener);\n                    target.addEventListener('blur', this.blurListener);\n                }\n            });\n        }\n    }\n    setAriaDescribedBy() {\n        const tooltipId = this.getOption('id');\n        if (tooltipId && this.active) {\n            this.renderer.setAttribute(this.el.nativeElement, 'aria-describedby', tooltipId);\n        }\n    }\n    removeAriaDescribedBy() {\n        this.renderer.removeAttribute(this.el.nativeElement, 'aria-describedby');\n    }\n    ngOnChanges(simpleChange) {\n        if (simpleChange.tooltipPosition) {\n            this.setOption({ tooltipPosition: simpleChange.tooltipPosition.currentValue });\n        }\n        if (simpleChange.tooltipEvent) {\n            this.setOption({ tooltipEvent: simpleChange.tooltipEvent.currentValue });\n        }\n        if (simpleChange.appendTo) {\n            this.setOption({ appendTo: simpleChange.appendTo.currentValue });\n        }\n        if (simpleChange.positionStyle) {\n            this.setOption({ positionStyle: simpleChange.positionStyle.currentValue });\n        }\n        if (simpleChange.tooltipStyleClass) {\n            this.setOption({ tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue });\n        }\n        if (simpleChange.tooltipZIndex) {\n            this.setOption({ tooltipZIndex: simpleChange.tooltipZIndex.currentValue });\n        }\n        if (simpleChange.escape) {\n            this.setOption({ escape: simpleChange.escape.currentValue });\n        }\n        if (simpleChange.showDelay) {\n            this.setOption({ showDelay: simpleChange.showDelay.currentValue });\n        }\n        if (simpleChange.hideDelay) {\n            this.setOption({ hideDelay: simpleChange.hideDelay.currentValue });\n        }\n        if (simpleChange.life) {\n            this.setOption({ life: simpleChange.life.currentValue });\n        }\n        if (simpleChange.positionTop) {\n            this.setOption({ positionTop: simpleChange.positionTop.currentValue });\n        }\n        if (simpleChange.positionLeft) {\n            this.setOption({ positionLeft: simpleChange.positionLeft.currentValue });\n        }\n        if (simpleChange.disabled) {\n            this.setOption({ disabled: simpleChange.disabled.currentValue });\n        }\n        if (simpleChange.content) {\n            this.setOption({ tooltipLabel: simpleChange.content.currentValue });\n            if (this.active) {\n                if (simpleChange.content.currentValue) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n        if (simpleChange.autoHide) {\n            this.setOption({ autoHide: simpleChange.autoHide.currentValue });\n        }\n        if (simpleChange.id) {\n            this.setOption({ id: simpleChange.id.currentValue });\n        }\n        if (simpleChange.tooltipOptions) {\n            this._tooltipOptions = { ...this._tooltipOptions, ...simpleChange.tooltipOptions.currentValue };\n            this.deactivate();\n            if (this.active) {\n                if (this.getOption('tooltipLabel')) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n    }\n    isAutoHide() {\n        return this.getOption('autoHide');\n    }\n    onMouseEnter(e) {\n        if (!this.container && !this.showTimeout) {\n            this.activate();\n        }\n    }\n    onMouseLeave(e) {\n        if (!this.isAutoHide()) {\n            const valid = DomHandler.hasClass(e.relatedTarget, 'p-tooltip') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-text') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-arrow');\n            !valid && this.deactivate();\n        }\n        else {\n            this.deactivate();\n        }\n    }\n    onFocus(e) {\n        this.activate();\n    }\n    onBlur(e) {\n        this.deactivate();\n    }\n    onInputClick(e) {\n        this.deactivate();\n    }\n    activate() {\n        if (!this.interactionInProgress) {\n            this.active = true;\n            this.clearHideTimeout();\n            if (this.getOption('showDelay'))\n                this.showTimeout = setTimeout(() => {\n                    this.show();\n                }, this.getOption('showDelay'));\n            else\n                this.show();\n            if (this.getOption('life')) {\n                let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n                this.hideTimeout = setTimeout(() => {\n                    this.hide();\n                }, duration);\n            }\n            if (this.getOption('hideOnEscape')) {\n                this.documentEscapeListener = this.renderer.listen('document', 'keydown.escape', () => {\n                    this.deactivate();\n                    this.documentEscapeListener();\n                });\n            }\n        }\n        this.interactionInProgress = true;\n    }\n    deactivate() {\n        this.interactionInProgress = false;\n        this.active = false;\n        this.clearShowTimeout();\n        if (this.getOption('hideDelay')) {\n            this.clearHideTimeout(); //life timeout\n            this.hideTimeout = setTimeout(() => {\n                this.hide();\n            }, this.getOption('hideDelay'));\n        }\n        else {\n            this.hide();\n        }\n        if (this.documentEscapeListener) {\n            this.documentEscapeListener();\n        }\n    }\n    create() {\n        if (this.container) {\n            this.clearHideTimeout();\n            this.remove();\n        }\n        this.container = document.createElement('div');\n        this.container.setAttribute('id', this.getOption('id'));\n        this.container.setAttribute('role', 'tooltip');\n        let tooltipArrow = document.createElement('div');\n        tooltipArrow.className = 'p-tooltip-arrow';\n        this.container.appendChild(tooltipArrow);\n        this.tooltipText = document.createElement('div');\n        this.tooltipText.className = 'p-tooltip-text';\n        this.updateText();\n        if (this.getOption('positionStyle')) {\n            this.container.style.position = this.getOption('positionStyle');\n        }\n        this.container.appendChild(this.tooltipText);\n        if (this.getOption('appendTo') === 'body')\n            document.body.appendChild(this.container);\n        else if (this.getOption('appendTo') === 'target')\n            DomHandler.appendChild(this.container, this.el.nativeElement);\n        else\n            DomHandler.appendChild(this.container, this.getOption('appendTo'));\n        this.container.style.display = 'inline-block';\n        if (this.fitContent) {\n            this.container.style.width = 'fit-content';\n        }\n        if (this.isAutoHide()) {\n            this.container.style.pointerEvents = 'none';\n        }\n        else {\n            this.container.style.pointerEvents = 'unset';\n            this.bindContainerMouseleaveListener();\n        }\n        this.setAriaDescribedBy();\n    }\n    bindContainerMouseleaveListener() {\n        if (!this.containerMouseleaveListener) {\n            const targetEl = this.container ?? this.container.nativeElement;\n            this.containerMouseleaveListener = this.renderer.listen(targetEl, 'mouseleave', (e) => {\n                this.deactivate();\n            });\n        }\n    }\n    unbindContainerMouseleaveListener() {\n        if (this.containerMouseleaveListener) {\n            this.bindContainerMouseleaveListener();\n            this.containerMouseleaveListener = null;\n        }\n    }\n    show() {\n        if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n            return;\n        }\n        this.create();\n        const nativeElement = this.el.nativeElement;\n        const pDialogWrapper = nativeElement.closest('p-dialog');\n        if (pDialogWrapper) {\n            setTimeout(() => {\n                this.container && this.align();\n            }, 100);\n        }\n        else {\n            this.align();\n        }\n        DomHandler.fadeIn(this.container, 250);\n        if (this.getOption('tooltipZIndex') === 'auto')\n            ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);\n        else\n            this.container.style.zIndex = this.getOption('tooltipZIndex');\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n    }\n    hide() {\n        if (this.getOption('tooltipZIndex') === 'auto') {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n    }\n    updateText() {\n        const content = this.getOption('tooltipLabel');\n        if (content instanceof TemplateRef) {\n            const embeddedViewRef = this.viewContainer.createEmbeddedView(content);\n            embeddedViewRef.detectChanges();\n            embeddedViewRef.rootNodes.forEach((node) => this.tooltipText.appendChild(node));\n        }\n        else if (this.getOption('escape')) {\n            this.tooltipText.innerHTML = '';\n            this.tooltipText.appendChild(document.createTextNode(content));\n        }\n        else {\n            this.tooltipText.innerHTML = content;\n        }\n    }\n    align() {\n        let position = this.getOption('tooltipPosition');\n        switch (position) {\n            case 'top':\n                this.alignTop();\n                if (this.isOutOfBounds()) {\n                    this.alignBottom();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'bottom':\n                this.alignBottom();\n                if (this.isOutOfBounds()) {\n                    this.alignTop();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'left':\n                this.alignLeft();\n                if (this.isOutOfBounds()) {\n                    this.alignRight();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n            case 'right':\n                this.alignRight();\n                if (this.isOutOfBounds()) {\n                    this.alignLeft();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n        }\n    }\n    getHostOffset() {\n        if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n            let offset = this.el.nativeElement.getBoundingClientRect();\n            let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n            let targetTop = offset.top + DomHandler.getWindowScrollTop();\n            return { left: targetLeft, top: targetTop };\n        }\n        else {\n            return { left: 0, top: 0 };\n        }\n    }\n    alignRight() {\n        this.preAlign('right');\n        const el = this.activeElement;\n        const hostOffset = this.getHostOffset();\n        const left = hostOffset.left + DomHandler.getOuterWidth(el);\n        const top = hostOffset.top + (DomHandler.getOuterHeight(el) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    get activeElement() {\n        return this.el.nativeElement.nodeName.includes('P-') ? DomHandler.findSingle(this.el.nativeElement, '.p-component') || this.el.nativeElement : this.el.nativeElement;\n    }\n    alignLeft() {\n        this.preAlign('left');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignTop() {\n        this.preAlign('top');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignBottom() {\n        this.preAlign('bottom');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    setOption(option) {\n        this._tooltipOptions = { ...this._tooltipOptions, ...option };\n    }\n    getOption(option) {\n        return this._tooltipOptions[option];\n    }\n    getTarget(el) {\n        return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n    }\n    preAlign(position) {\n        this.container.style.left = -999 + 'px';\n        this.container.style.top = -999 + 'px';\n        let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n        this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n    }\n    isOutOfBounds() {\n        let offset = this.container.getBoundingClientRect();\n        let targetTop = offset.top;\n        let targetLeft = offset.left;\n        let width = DomHandler.getOuterWidth(this.container);\n        let height = DomHandler.getOuterHeight(this.container);\n        let viewport = DomHandler.getViewport();\n        return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n    }\n    onWindowResize(e) {\n        this.hide();\n    }\n    bindDocumentResizeListener() {\n        this.zone.runOutsideAngular(() => {\n            this.resizeListener = this.onWindowResize.bind(this);\n            window.addEventListener('resize', this.resizeListener);\n        });\n    }\n    unbindDocumentResizeListener() {\n        if (this.resizeListener) {\n            window.removeEventListener('resize', this.resizeListener);\n            this.resizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n                if (this.container) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    unbindEvents() {\n        const tooltipEvent = this.getOption('tooltipEvent');\n        if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n            this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n            this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n            this.el.nativeElement.removeEventListener('click', this.clickListener);\n        }\n        if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n            let target = this.el.nativeElement.querySelector('.p-component');\n            if (!target) {\n                target = this.getTarget(this.el.nativeElement);\n            }\n        }\n        this.unbindDocumentResizeListener();\n    }\n    remove() {\n        if (this.container && this.container.parentElement) {\n            if (this.getOption('appendTo') === 'body')\n                document.body.removeChild(this.container);\n            else if (this.getOption('appendTo') === 'target')\n                this.el.nativeElement.removeChild(this.container);\n            else\n                DomHandler.removeChild(this.container, this.getOption('appendTo'));\n        }\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.unbindContainerMouseleaveListener();\n        this.clearTimeouts();\n        this.removeAriaDescribedBy();\n        this.container = null;\n        this.scrollHandler = null;\n    }\n    clearShowTimeout() {\n        if (this.showTimeout) {\n            clearTimeout(this.showTimeout);\n            this.showTimeout = null;\n        }\n    }\n    clearHideTimeout() {\n        if (this.hideTimeout) {\n            clearTimeout(this.hideTimeout);\n            this.hideTimeout = null;\n        }\n    }\n    clearTimeouts() {\n        this.clearShowTimeout();\n        this.clearHideTimeout();\n    }\n    ngOnDestroy() {\n        this.unbindEvents();\n        if (this.container) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n        if (this.documentEscapeListener) {\n            this.documentEscapeListener();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: Tooltip, deps: [{ token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.PrimeNGConfig }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.0.1\", type: Tooltip, selector: \"[pTooltip]\", inputs: { tooltipPosition: \"tooltipPosition\", tooltipEvent: \"tooltipEvent\", appendTo: \"appendTo\", positionStyle: \"positionStyle\", tooltipStyleClass: \"tooltipStyleClass\", tooltipZIndex: \"tooltipZIndex\", escape: [\"escape\", \"escape\", booleanAttribute], showDelay: [\"showDelay\", \"showDelay\", numberAttribute], hideDelay: [\"hideDelay\", \"hideDelay\", numberAttribute], life: [\"life\", \"life\", numberAttribute], positionTop: [\"positionTop\", \"positionTop\", numberAttribute], positionLeft: [\"positionLeft\", \"positionLeft\", numberAttribute], autoHide: [\"autoHide\", \"autoHide\", booleanAttribute], fitContent: [\"fitContent\", \"fitContent\", booleanAttribute], hideOnEscape: [\"hideOnEscape\", \"hideOnEscape\", booleanAttribute], content: [\"pTooltip\", \"content\"], disabled: [\"tooltipDisabled\", \"disabled\"], tooltipOptions: \"tooltipOptions\" }, host: { classAttribute: \"p-element\" }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: Tooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pTooltip]',\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.PrimeNGConfig }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }], propDecorators: { tooltipPosition: [{\n                type: Input\n            }], tooltipEvent: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], positionStyle: [{\n                type: Input\n            }], tooltipStyleClass: [{\n                type: Input\n            }], tooltipZIndex: [{\n                type: Input\n            }], escape: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], showDelay: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], hideDelay: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], life: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], positionTop: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], positionLeft: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], autoHide: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fitContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hideOnEscape: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], content: [{\n                type: Input,\n                args: ['pTooltip']\n            }], disabled: [{\n                type: Input,\n                args: ['tooltipDisabled']\n            }], tooltipOptions: [{\n                type: Input\n            }] } });\nclass TooltipModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: TooltipModule, declarations: [Tooltip], imports: [CommonModule], exports: [Tooltip] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TooltipModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Tooltip],\n                    declarations: [Tooltip]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC/H,SAASC,UAAU,EAAEC,6BAA6B,QAAQ,aAAa;AACvE,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,eAAe;AAC9D,OAAO,KAAKC,EAAE,MAAM,aAAa;;AAEjC;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,UAAU;EACVC,EAAE;EACFC,IAAI;EACJC,MAAM;EACNC,QAAQ;EACRC,aAAa;EACb;AACJ;AACA;AACA;EACIC,eAAe;EACf;AACJ;AACA;AACA;EACIC,YAAY,GAAG,OAAO;EACtB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,MAAM,GAAG,IAAI;EACb;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;AACA;EACIC,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,GAAG,EAAE;IACd,IAAI,CAACD,SAAS,GAAGC,GAAG;IACpB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIC,cAAc;EACdC,eAAe,GAAG;IACdC,YAAY,EAAE,IAAI;IAClBtB,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,OAAO;IACrBC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,IAAI;IACnBC,iBAAiB,EAAE,IAAI;IACvBC,aAAa,EAAE,MAAM;IACrBC,MAAM,EAAE,IAAI;IACZU,QAAQ,EAAE,IAAI;IACdT,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfE,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBF,IAAI,EAAE,IAAI;IACVG,QAAQ,EAAE,IAAI;IACdE,YAAY,EAAE,IAAI;IAClBS,EAAE,EAAEjC,iBAAiB,CAAC,CAAC,GAAG;EAC9B,CAAC;EACD2B,SAAS;EACTO,SAAS;EACTC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,WAAW;EACXC,MAAM;EACNC,kBAAkB;EAClBC,kBAAkB;EAClBC,2BAA2B;EAC3BC,aAAa;EACbC,aAAa;EACbC,YAAY;EACZC,sBAAsB;EACtBC,aAAa;EACbC,cAAc;EACdC,qBAAqB,GAAG,KAAK;EAC7BC,WAAWA,CAAC9C,UAAU,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC/D,IAAI,CAACL,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACA0C,eAAeA,CAAA,EAAG;IACd,IAAIhE,iBAAiB,CAAC,IAAI,CAACiB,UAAU,CAAC,EAAE;MACpC,IAAI,CAACE,IAAI,CAAC8C,iBAAiB,CAAC,MAAM;QAC9B,MAAMzC,YAAY,GAAG,IAAI,CAAC0C,SAAS,CAAC,cAAc,CAAC;QACnD,IAAI1C,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,MAAM,EAAE;UACrD,IAAI,CAAC6B,kBAAkB,GAAG,IAAI,CAACc,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC;UACtD,IAAI,CAACd,kBAAkB,GAAG,IAAI,CAACe,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC;UACtD,IAAI,CAACZ,aAAa,GAAG,IAAI,CAACc,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC;UACjD,IAAI,CAAClD,EAAE,CAACqD,aAAa,CAACC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACnB,kBAAkB,CAAC;UAC7E,IAAI,CAACnC,EAAE,CAACqD,aAAa,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAChB,aAAa,CAAC;UACnE,IAAI,CAACtC,EAAE,CAACqD,aAAa,CAACC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAClB,kBAAkB,CAAC;QACjF;QACA,IAAI9B,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,MAAM,EAAE;UACrD,IAAI,CAACiC,aAAa,GAAG,IAAI,CAACgB,OAAO,CAACL,IAAI,CAAC,IAAI,CAAC;UAC5C,IAAI,CAACV,YAAY,GAAG,IAAI,CAACgB,MAAM,CAACN,IAAI,CAAC,IAAI,CAAC;UAC1C,IAAIO,MAAM,GAAG,IAAI,CAACzD,EAAE,CAACqD,aAAa,CAACK,aAAa,CAAC,cAAc,CAAC;UAChE,IAAI,CAACD,MAAM,EAAE;YACTA,MAAM,GAAG,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC3D,EAAE,CAACqD,aAAa,CAAC;UAClD;UACAI,MAAM,CAACH,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACf,aAAa,CAAC;UACpDkB,MAAM,CAACH,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACd,YAAY,CAAC;QACtD;MACJ,CAAC,CAAC;IACN;EACJ;EACAoB,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAACb,SAAS,CAAC,IAAI,CAAC;IACtC,IAAIa,SAAS,IAAI,IAAI,CAAC3B,MAAM,EAAE;MAC1B,IAAI,CAAC/B,QAAQ,CAAC2D,YAAY,CAAC,IAAI,CAAC9D,EAAE,CAACqD,aAAa,EAAE,kBAAkB,EAAEQ,SAAS,CAAC;IACpF;EACJ;EACAE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC5D,QAAQ,CAAC6D,eAAe,CAAC,IAAI,CAAChE,EAAE,CAACqD,aAAa,EAAE,kBAAkB,CAAC;EAC5E;EACAY,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAIA,YAAY,CAAC7D,eAAe,EAAE;MAC9B,IAAI,CAAC8D,SAAS,CAAC;QAAE9D,eAAe,EAAE6D,YAAY,CAAC7D,eAAe,CAAC+D;MAAa,CAAC,CAAC;IAClF;IACA,IAAIF,YAAY,CAAC5D,YAAY,EAAE;MAC3B,IAAI,CAAC6D,SAAS,CAAC;QAAE7D,YAAY,EAAE4D,YAAY,CAAC5D,YAAY,CAAC8D;MAAa,CAAC,CAAC;IAC5E;IACA,IAAIF,YAAY,CAAC3D,QAAQ,EAAE;MACvB,IAAI,CAAC4D,SAAS,CAAC;QAAE5D,QAAQ,EAAE2D,YAAY,CAAC3D,QAAQ,CAAC6D;MAAa,CAAC,CAAC;IACpE;IACA,IAAIF,YAAY,CAAC1D,aAAa,EAAE;MAC5B,IAAI,CAAC2D,SAAS,CAAC;QAAE3D,aAAa,EAAE0D,YAAY,CAAC1D,aAAa,CAAC4D;MAAa,CAAC,CAAC;IAC9E;IACA,IAAIF,YAAY,CAACzD,iBAAiB,EAAE;MAChC,IAAI,CAAC0D,SAAS,CAAC;QAAE1D,iBAAiB,EAAEyD,YAAY,CAACzD,iBAAiB,CAAC2D;MAAa,CAAC,CAAC;IACtF;IACA,IAAIF,YAAY,CAACxD,aAAa,EAAE;MAC5B,IAAI,CAACyD,SAAS,CAAC;QAAEzD,aAAa,EAAEwD,YAAY,CAACxD,aAAa,CAAC0D;MAAa,CAAC,CAAC;IAC9E;IACA,IAAIF,YAAY,CAACvD,MAAM,EAAE;MACrB,IAAI,CAACwD,SAAS,CAAC;QAAExD,MAAM,EAAEuD,YAAY,CAACvD,MAAM,CAACyD;MAAa,CAAC,CAAC;IAChE;IACA,IAAIF,YAAY,CAACtD,SAAS,EAAE;MACxB,IAAI,CAACuD,SAAS,CAAC;QAAEvD,SAAS,EAAEsD,YAAY,CAACtD,SAAS,CAACwD;MAAa,CAAC,CAAC;IACtE;IACA,IAAIF,YAAY,CAACrD,SAAS,EAAE;MACxB,IAAI,CAACsD,SAAS,CAAC;QAAEtD,SAAS,EAAEqD,YAAY,CAACrD,SAAS,CAACuD;MAAa,CAAC,CAAC;IACtE;IACA,IAAIF,YAAY,CAACpD,IAAI,EAAE;MACnB,IAAI,CAACqD,SAAS,CAAC;QAAErD,IAAI,EAAEoD,YAAY,CAACpD,IAAI,CAACsD;MAAa,CAAC,CAAC;IAC5D;IACA,IAAIF,YAAY,CAACnD,WAAW,EAAE;MAC1B,IAAI,CAACoD,SAAS,CAAC;QAAEpD,WAAW,EAAEmD,YAAY,CAACnD,WAAW,CAACqD;MAAa,CAAC,CAAC;IAC1E;IACA,IAAIF,YAAY,CAAClD,YAAY,EAAE;MAC3B,IAAI,CAACmD,SAAS,CAAC;QAAEnD,YAAY,EAAEkD,YAAY,CAAClD,YAAY,CAACoD;MAAa,CAAC,CAAC;IAC5E;IACA,IAAIF,YAAY,CAAC7C,QAAQ,EAAE;MACvB,IAAI,CAAC8C,SAAS,CAAC;QAAE9C,QAAQ,EAAE6C,YAAY,CAAC7C,QAAQ,CAAC+C;MAAa,CAAC,CAAC;IACpE;IACA,IAAIF,YAAY,CAAC9C,OAAO,EAAE;MACtB,IAAI,CAAC+C,SAAS,CAAC;QAAExC,YAAY,EAAEuC,YAAY,CAAC9C,OAAO,CAACgD;MAAa,CAAC,CAAC;MACnE,IAAI,IAAI,CAAClC,MAAM,EAAE;QACb,IAAIgC,YAAY,CAAC9C,OAAO,CAACgD,YAAY,EAAE;UACnC,IAAI,IAAI,CAACvC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACwC,YAAY,EAAE;YAC/C,IAAI,CAACC,UAAU,CAAC,CAAC;YACjB,IAAI,CAACC,KAAK,CAAC,CAAC;UAChB,CAAC,MACI;YACD,IAAI,CAACC,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,MACI;UACD,IAAI,CAACC,IAAI,CAAC,CAAC;QACf;MACJ;IACJ;IACA,IAAIP,YAAY,CAACjD,QAAQ,EAAE;MACvB,IAAI,CAACkD,SAAS,CAAC;QAAElD,QAAQ,EAAEiD,YAAY,CAACjD,QAAQ,CAACmD;MAAa,CAAC,CAAC;IACpE;IACA,IAAIF,YAAY,CAACtC,EAAE,EAAE;MACjB,IAAI,CAACuC,SAAS,CAAC;QAAEvC,EAAE,EAAEsC,YAAY,CAACtC,EAAE,CAACwC;MAAa,CAAC,CAAC;IACxD;IACA,IAAIF,YAAY,CAACzC,cAAc,EAAE;MAC7B,IAAI,CAACC,eAAe,GAAG;QAAE,GAAG,IAAI,CAACA,eAAe;QAAE,GAAGwC,YAAY,CAACzC,cAAc,CAAC2C;MAAa,CAAC;MAC/F,IAAI,CAAC5C,UAAU,CAAC,CAAC;MACjB,IAAI,IAAI,CAACU,MAAM,EAAE;QACb,IAAI,IAAI,CAACc,SAAS,CAAC,cAAc,CAAC,EAAE;UAChC,IAAI,IAAI,CAACnB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACwC,YAAY,EAAE;YAC/C,IAAI,CAACC,UAAU,CAAC,CAAC;YACjB,IAAI,CAACC,KAAK,CAAC,CAAC;UAChB,CAAC,MACI;YACD,IAAI,CAACC,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,MACI;UACD,IAAI,CAACC,IAAI,CAAC,CAAC;QACf;MACJ;IACJ;EACJ;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1B,SAAS,CAAC,UAAU,CAAC;EACrC;EACAC,YAAYA,CAAC0B,CAAC,EAAE;IACZ,IAAI,CAAC,IAAI,CAAC9C,SAAS,IAAI,CAAC,IAAI,CAACG,WAAW,EAAE;MACtC,IAAI,CAAC4C,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAzB,YAAYA,CAACwB,CAAC,EAAE;IACZ,IAAI,CAAC,IAAI,CAACD,UAAU,CAAC,CAAC,EAAE;MACpB,MAAMG,KAAK,GAAGpF,UAAU,CAACqF,QAAQ,CAACH,CAAC,CAACI,aAAa,EAAE,WAAW,CAAC,IAAItF,UAAU,CAACqF,QAAQ,CAACH,CAAC,CAACI,aAAa,EAAE,gBAAgB,CAAC,IAAItF,UAAU,CAACqF,QAAQ,CAACH,CAAC,CAACI,aAAa,EAAE,iBAAiB,CAAC;MACpL,CAACF,KAAK,IAAI,IAAI,CAACrD,UAAU,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACA,UAAU,CAAC,CAAC;IACrB;EACJ;EACA+B,OAAOA,CAACoB,CAAC,EAAE;IACP,IAAI,CAACC,QAAQ,CAAC,CAAC;EACnB;EACApB,MAAMA,CAACmB,CAAC,EAAE;IACN,IAAI,CAACnD,UAAU,CAAC,CAAC;EACrB;EACA4B,YAAYA,CAACuB,CAAC,EAAE;IACZ,IAAI,CAACnD,UAAU,CAAC,CAAC;EACrB;EACAoD,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAChC,qBAAqB,EAAE;MAC7B,IAAI,CAACV,MAAM,GAAG,IAAI;MAClB,IAAI,CAAC8C,gBAAgB,CAAC,CAAC;MACvB,IAAI,IAAI,CAAChC,SAAS,CAAC,WAAW,CAAC,EAC3B,IAAI,CAAChB,WAAW,GAAGiD,UAAU,CAAC,MAAM;QAChC,IAAI,CAACT,IAAI,CAAC,CAAC;MACf,CAAC,EAAE,IAAI,CAACxB,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,KAEhC,IAAI,CAACwB,IAAI,CAAC,CAAC;MACf,IAAI,IAAI,CAACxB,SAAS,CAAC,MAAM,CAAC,EAAE;QACxB,IAAIkC,QAAQ,GAAG,IAAI,CAAClC,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC,MAAM,CAAC;QAC1H,IAAI,CAACf,WAAW,GAAGgD,UAAU,CAAC,MAAM;UAChC,IAAI,CAACR,IAAI,CAAC,CAAC;QACf,CAAC,EAAES,QAAQ,CAAC;MAChB;MACA,IAAI,IAAI,CAAClC,SAAS,CAAC,cAAc,CAAC,EAAE;QAChC,IAAI,CAACP,sBAAsB,GAAG,IAAI,CAACtC,QAAQ,CAACgF,MAAM,CAAC,UAAU,EAAE,gBAAgB,EAAE,MAAM;UACnF,IAAI,CAAC3D,UAAU,CAAC,CAAC;UACjB,IAAI,CAACiB,sBAAsB,CAAC,CAAC;QACjC,CAAC,CAAC;MACN;IACJ;IACA,IAAI,CAACG,qBAAqB,GAAG,IAAI;EACrC;EACApB,UAAUA,CAAA,EAAG;IACT,IAAI,CAACoB,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACV,MAAM,GAAG,KAAK;IACnB,IAAI,CAACkD,gBAAgB,CAAC,CAAC;IACvB,IAAI,IAAI,CAACpC,SAAS,CAAC,WAAW,CAAC,EAAE;MAC7B,IAAI,CAACgC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzB,IAAI,CAAC/C,WAAW,GAAGgD,UAAU,CAAC,MAAM;QAChC,IAAI,CAACR,IAAI,CAAC,CAAC;MACf,CAAC,EAAE,IAAI,CAACzB,SAAS,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC,MACI;MACD,IAAI,CAACyB,IAAI,CAAC,CAAC;IACf;IACA,IAAI,IAAI,CAAChC,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACA4C,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACxD,SAAS,EAAE;MAChB,IAAI,CAACmD,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACM,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAACzD,SAAS,GAAG0D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C,IAAI,CAAC3D,SAAS,CAACiC,YAAY,CAAC,IAAI,EAAE,IAAI,CAACd,SAAS,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,CAACnB,SAAS,CAACiC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;IAC9C,IAAI2B,YAAY,GAAGF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAChDC,YAAY,CAACC,SAAS,GAAG,iBAAiB;IAC1C,IAAI,CAAC7D,SAAS,CAAC8D,WAAW,CAACF,YAAY,CAAC;IACxC,IAAI,CAAC1D,WAAW,GAAGwD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAChD,IAAI,CAACzD,WAAW,CAAC2D,SAAS,GAAG,gBAAgB;IAC7C,IAAI,CAACpB,UAAU,CAAC,CAAC;IACjB,IAAI,IAAI,CAACtB,SAAS,CAAC,eAAe,CAAC,EAAE;MACjC,IAAI,CAACnB,SAAS,CAAC+D,KAAK,CAACC,QAAQ,GAAG,IAAI,CAAC7C,SAAS,CAAC,eAAe,CAAC;IACnE;IACA,IAAI,CAACnB,SAAS,CAAC8D,WAAW,CAAC,IAAI,CAAC5D,WAAW,CAAC;IAC5C,IAAI,IAAI,CAACiB,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM,EACrCuC,QAAQ,CAACO,IAAI,CAACH,WAAW,CAAC,IAAI,CAAC9D,SAAS,CAAC,CAAC,KACzC,IAAI,IAAI,CAACmB,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,EAC5CvD,UAAU,CAACkG,WAAW,CAAC,IAAI,CAAC9D,SAAS,EAAE,IAAI,CAAC7B,EAAE,CAACqD,aAAa,CAAC,CAAC,KAE9D5D,UAAU,CAACkG,WAAW,CAAC,IAAI,CAAC9D,SAAS,EAAE,IAAI,CAACmB,SAAS,CAAC,UAAU,CAAC,CAAC;IACtE,IAAI,CAACnB,SAAS,CAAC+D,KAAK,CAACG,OAAO,GAAG,cAAc;IAC7C,IAAI,IAAI,CAAC7E,UAAU,EAAE;MACjB,IAAI,CAACW,SAAS,CAAC+D,KAAK,CAACI,KAAK,GAAG,aAAa;IAC9C;IACA,IAAI,IAAI,CAACtB,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAAC7C,SAAS,CAAC+D,KAAK,CAACK,aAAa,GAAG,MAAM;IAC/C,CAAC,MACI;MACD,IAAI,CAACpE,SAAS,CAAC+D,KAAK,CAACK,aAAa,GAAG,OAAO;MAC5C,IAAI,CAACC,+BAA+B,CAAC,CAAC;IAC1C;IACA,IAAI,CAACtC,kBAAkB,CAAC,CAAC;EAC7B;EACAsC,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAAC7D,2BAA2B,EAAE;MACnC,MAAM8D,QAAQ,GAAG,IAAI,CAACtE,SAAS,IAAI,IAAI,CAACA,SAAS,CAACwB,aAAa;MAC/D,IAAI,CAAChB,2BAA2B,GAAG,IAAI,CAAClC,QAAQ,CAACgF,MAAM,CAACgB,QAAQ,EAAE,YAAY,EAAGxB,CAAC,IAAK;QACnF,IAAI,CAACnD,UAAU,CAAC,CAAC;MACrB,CAAC,CAAC;IACN;EACJ;EACA4E,iCAAiCA,CAAA,EAAG;IAChC,IAAI,IAAI,CAAC/D,2BAA2B,EAAE;MAClC,IAAI,CAAC6D,+BAA+B,CAAC,CAAC;MACtC,IAAI,CAAC7D,2BAA2B,GAAG,IAAI;IAC3C;EACJ;EACAmC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACxB,SAAS,CAAC,cAAc,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC,UAAU,CAAC,EAAE;MAC/D;IACJ;IACA,IAAI,CAACqC,MAAM,CAAC,CAAC;IACb,MAAMhC,aAAa,GAAG,IAAI,CAACrD,EAAE,CAACqD,aAAa;IAC3C,MAAMgD,cAAc,GAAGhD,aAAa,CAACiD,OAAO,CAAC,UAAU,CAAC;IACxD,IAAID,cAAc,EAAE;MAChBpB,UAAU,CAAC,MAAM;QACb,IAAI,CAACpD,SAAS,IAAI,IAAI,CAAC0C,KAAK,CAAC,CAAC;MAClC,CAAC,EAAE,GAAG,CAAC;IACX,CAAC,MACI;MACD,IAAI,CAACA,KAAK,CAAC,CAAC;IAChB;IACA9E,UAAU,CAAC8G,MAAM,CAAC,IAAI,CAAC1E,SAAS,EAAE,GAAG,CAAC;IACtC,IAAI,IAAI,CAACmB,SAAS,CAAC,eAAe,CAAC,KAAK,MAAM,EAC1CpD,WAAW,CAAC4G,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC3E,SAAS,EAAE,IAAI,CAAC3B,MAAM,CAACuG,MAAM,CAACC,OAAO,CAAC,CAAC,KAEvE,IAAI,CAAC7E,SAAS,CAAC+D,KAAK,CAACa,MAAM,GAAG,IAAI,CAACzD,SAAS,CAAC,eAAe,CAAC;IACjE,IAAI,CAAC2D,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACAnC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACzB,SAAS,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;MAC5CpD,WAAW,CAACiH,KAAK,CAAC,IAAI,CAAChF,SAAS,CAAC;IACrC;IACA,IAAI,CAACyD,MAAM,CAAC,CAAC;EACjB;EACAhB,UAAUA,CAAA,EAAG;IACT,MAAMlD,OAAO,GAAG,IAAI,CAAC4B,SAAS,CAAC,cAAc,CAAC;IAC9C,IAAI5B,OAAO,YAAYnC,WAAW,EAAE;MAChC,MAAM6H,eAAe,GAAG,IAAI,CAAC1G,aAAa,CAAC2G,kBAAkB,CAAC3F,OAAO,CAAC;MACtE0F,eAAe,CAACE,aAAa,CAAC,CAAC;MAC/BF,eAAe,CAACG,SAAS,CAACC,OAAO,CAAEC,IAAI,IAAK,IAAI,CAACpF,WAAW,CAAC4D,WAAW,CAACwB,IAAI,CAAC,CAAC;IACnF,CAAC,MACI,IAAI,IAAI,CAACnE,SAAS,CAAC,QAAQ,CAAC,EAAE;MAC/B,IAAI,CAACjB,WAAW,CAACqF,SAAS,GAAG,EAAE;MAC/B,IAAI,CAACrF,WAAW,CAAC4D,WAAW,CAACJ,QAAQ,CAAC8B,cAAc,CAACjG,OAAO,CAAC,CAAC;IAClE,CAAC,MACI;MACD,IAAI,CAACW,WAAW,CAACqF,SAAS,GAAGhG,OAAO;IACxC;EACJ;EACAmD,KAAKA,CAAA,EAAG;IACJ,IAAIsB,QAAQ,GAAG,IAAI,CAAC7C,SAAS,CAAC,iBAAiB,CAAC;IAChD,QAAQ6C,QAAQ;MACZ,KAAK,KAAK;QACN,IAAI,CAACyB,QAAQ,CAAC,CAAC;QACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACC,WAAW,CAAC,CAAC;UAClB,IAAI,IAAI,CAACD,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACE,UAAU,CAAC,CAAC;YACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACG,SAAS,CAAC,CAAC;YACpB;UACJ;QACJ;QACA;MACJ,KAAK,QAAQ;QACT,IAAI,CAACF,WAAW,CAAC,CAAC;QAClB,IAAI,IAAI,CAACD,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACD,QAAQ,CAAC,CAAC;UACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACE,UAAU,CAAC,CAAC;YACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACG,SAAS,CAAC,CAAC;YACpB;UACJ;QACJ;QACA;MACJ,KAAK,MAAM;QACP,IAAI,CAACA,SAAS,CAAC,CAAC;QAChB,IAAI,IAAI,CAACH,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACE,UAAU,CAAC,CAAC;UACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACD,QAAQ,CAAC,CAAC;YACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACC,WAAW,CAAC,CAAC;YACtB;UACJ;QACJ;QACA;MACJ,KAAK,OAAO;QACR,IAAI,CAACC,UAAU,CAAC,CAAC;QACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACG,SAAS,CAAC,CAAC;UAChB,IAAI,IAAI,CAACH,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACD,QAAQ,CAAC,CAAC;YACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACC,WAAW,CAAC,CAAC;YACtB;UACJ;QACJ;QACA;IACR;EACJ;EACAG,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC3E,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM,IAAI,IAAI,CAACA,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;MAClF,IAAI4E,MAAM,GAAG,IAAI,CAAC5H,EAAE,CAACqD,aAAa,CAACwE,qBAAqB,CAAC,CAAC;MAC1D,IAAIC,UAAU,GAAGF,MAAM,CAACG,IAAI,GAAGtI,UAAU,CAACuI,mBAAmB,CAAC,CAAC;MAC/D,IAAIC,SAAS,GAAGL,MAAM,CAACM,GAAG,GAAGzI,UAAU,CAAC0I,kBAAkB,CAAC,CAAC;MAC5D,OAAO;QAAEJ,IAAI,EAAED,UAAU;QAAEI,GAAG,EAAED;MAAU,CAAC;IAC/C,CAAC,MACI;MACD,OAAO;QAAEF,IAAI,EAAE,CAAC;QAAEG,GAAG,EAAE;MAAE,CAAC;IAC9B;EACJ;EACAT,UAAUA,CAAA,EAAG;IACT,IAAI,CAACW,QAAQ,CAAC,OAAO,CAAC;IACtB,MAAMpI,EAAE,GAAG,IAAI,CAACqI,aAAa;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAACX,aAAa,CAAC,CAAC;IACvC,MAAMI,IAAI,GAAGO,UAAU,CAACP,IAAI,GAAGtI,UAAU,CAAC8I,aAAa,CAACvI,EAAE,CAAC;IAC3D,MAAMkI,GAAG,GAAGI,UAAU,CAACJ,GAAG,GAAG,CAACzI,UAAU,CAAC+I,cAAc,CAACxI,EAAE,CAAC,GAAGP,UAAU,CAAC+I,cAAc,CAAC,IAAI,CAAC3G,SAAS,CAAC,IAAI,CAAC;IAC5G,IAAI,CAACA,SAAS,CAAC+D,KAAK,CAACmC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAAC/E,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACnB,SAAS,CAAC+D,KAAK,CAACsC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAClF,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACA,IAAIqF,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACrI,EAAE,CAACqD,aAAa,CAACoF,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,GAAGjJ,UAAU,CAACkJ,UAAU,CAAC,IAAI,CAAC3I,EAAE,CAACqD,aAAa,EAAE,cAAc,CAAC,IAAI,IAAI,CAACrD,EAAE,CAACqD,aAAa,GAAG,IAAI,CAACrD,EAAE,CAACqD,aAAa;EACxK;EACAqE,SAASA,CAAA,EAAG;IACR,IAAI,CAACU,QAAQ,CAAC,MAAM,CAAC;IACrB,IAAIE,UAAU,GAAG,IAAI,CAACX,aAAa,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGO,UAAU,CAACP,IAAI,GAAGtI,UAAU,CAAC8I,aAAa,CAAC,IAAI,CAAC1G,SAAS,CAAC;IACrE,IAAIqG,GAAG,GAAGI,UAAU,CAACJ,GAAG,GAAG,CAACzI,UAAU,CAAC+I,cAAc,CAAC,IAAI,CAACxI,EAAE,CAACqD,aAAa,CAAC,GAAG5D,UAAU,CAAC+I,cAAc,CAAC,IAAI,CAAC3G,SAAS,CAAC,IAAI,CAAC;IAC7H,IAAI,CAACA,SAAS,CAAC+D,KAAK,CAACmC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAAC/E,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACnB,SAAS,CAAC+D,KAAK,CAACsC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAClF,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACAsE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACc,QAAQ,CAAC,KAAK,CAAC;IACpB,IAAIE,UAAU,GAAG,IAAI,CAACX,aAAa,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGO,UAAU,CAACP,IAAI,GAAG,CAACtI,UAAU,CAAC8I,aAAa,CAAC,IAAI,CAACvI,EAAE,CAACqD,aAAa,CAAC,GAAG5D,UAAU,CAAC8I,aAAa,CAAC,IAAI,CAAC1G,SAAS,CAAC,IAAI,CAAC;IAC7H,IAAIqG,GAAG,GAAGI,UAAU,CAACJ,GAAG,GAAGzI,UAAU,CAAC+I,cAAc,CAAC,IAAI,CAAC3G,SAAS,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC+D,KAAK,CAACmC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAAC/E,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACnB,SAAS,CAAC+D,KAAK,CAACsC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAClF,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACAwE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACY,QAAQ,CAAC,QAAQ,CAAC;IACvB,IAAIE,UAAU,GAAG,IAAI,CAACX,aAAa,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGO,UAAU,CAACP,IAAI,GAAG,CAACtI,UAAU,CAAC8I,aAAa,CAAC,IAAI,CAACvI,EAAE,CAACqD,aAAa,CAAC,GAAG5D,UAAU,CAAC8I,aAAa,CAAC,IAAI,CAAC1G,SAAS,CAAC,IAAI,CAAC;IAC7H,IAAIqG,GAAG,GAAGI,UAAU,CAACJ,GAAG,GAAGzI,UAAU,CAAC+I,cAAc,CAAC,IAAI,CAACxI,EAAE,CAACqD,aAAa,CAAC;IAC3E,IAAI,CAACxB,SAAS,CAAC+D,KAAK,CAACmC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAAC/E,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACnB,SAAS,CAAC+D,KAAK,CAACsC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAClF,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACAmB,SAASA,CAACyE,MAAM,EAAE;IACd,IAAI,CAAClH,eAAe,GAAG;MAAE,GAAG,IAAI,CAACA,eAAe;MAAE,GAAGkH;IAAO,CAAC;EACjE;EACA5F,SAASA,CAAC4F,MAAM,EAAE;IACd,OAAO,IAAI,CAAClH,eAAe,CAACkH,MAAM,CAAC;EACvC;EACAjF,SAASA,CAAC3D,EAAE,EAAE;IACV,OAAOP,UAAU,CAACqF,QAAQ,CAAC9E,EAAE,EAAE,gBAAgB,CAAC,GAAGP,UAAU,CAACkJ,UAAU,CAAC3I,EAAE,EAAE,OAAO,CAAC,GAAGA,EAAE;EAC9F;EACAoI,QAAQA,CAACvC,QAAQ,EAAE;IACf,IAAI,CAAChE,SAAS,CAAC+D,KAAK,CAACmC,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI;IACvC,IAAI,CAAClG,SAAS,CAAC+D,KAAK,CAACsC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI;IACtC,IAAIW,gBAAgB,GAAG,kCAAkC,GAAGhD,QAAQ;IACpE,IAAI,CAAChE,SAAS,CAAC6D,SAAS,GAAG,IAAI,CAAC1C,SAAS,CAAC,mBAAmB,CAAC,GAAG6F,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAAC7F,SAAS,CAAC,mBAAmB,CAAC,GAAG6F,gBAAgB;EACpJ;EACAtB,aAAaA,CAAA,EAAG;IACZ,IAAIK,MAAM,GAAG,IAAI,CAAC/F,SAAS,CAACgG,qBAAqB,CAAC,CAAC;IACnD,IAAII,SAAS,GAAGL,MAAM,CAACM,GAAG;IAC1B,IAAIJ,UAAU,GAAGF,MAAM,CAACG,IAAI;IAC5B,IAAI/B,KAAK,GAAGvG,UAAU,CAAC8I,aAAa,CAAC,IAAI,CAAC1G,SAAS,CAAC;IACpD,IAAIiH,MAAM,GAAGrJ,UAAU,CAAC+I,cAAc,CAAC,IAAI,CAAC3G,SAAS,CAAC;IACtD,IAAIkH,QAAQ,GAAGtJ,UAAU,CAACuJ,WAAW,CAAC,CAAC;IACvC,OAAOlB,UAAU,GAAG9B,KAAK,GAAG+C,QAAQ,CAAC/C,KAAK,IAAI8B,UAAU,GAAG,CAAC,IAAIG,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGa,MAAM,GAAGC,QAAQ,CAACD,MAAM;EACzH;EACAG,cAAcA,CAACtE,CAAC,EAAE;IACd,IAAI,CAACF,IAAI,CAAC,CAAC;EACf;EACAkC,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAAC1G,IAAI,CAAC8C,iBAAiB,CAAC,MAAM;MAC9B,IAAI,CAACJ,cAAc,GAAG,IAAI,CAACsG,cAAc,CAAC/F,IAAI,CAAC,IAAI,CAAC;MACpDgG,MAAM,CAAC5F,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACX,cAAc,CAAC;IAC1D,CAAC,CAAC;EACN;EACAwG,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAACxG,cAAc,EAAE;MACrBuG,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACzG,cAAc,CAAC;MACzD,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;EACAiE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAClE,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG,IAAIhD,6BAA6B,CAAC,IAAI,CAACM,EAAE,CAACqD,aAAa,EAAE,MAAM;QAChF,IAAI,IAAI,CAACxB,SAAS,EAAE;UAChB,IAAI,CAAC4C,IAAI,CAAC,CAAC;QACf;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC/B,aAAa,CAACkE,kBAAkB,CAAC,CAAC;EAC3C;EACAyC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC3G,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC2G,oBAAoB,CAAC,CAAC;IAC7C;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,MAAMhJ,YAAY,GAAG,IAAI,CAAC0C,SAAS,CAAC,cAAc,CAAC;IACnD,IAAI1C,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,MAAM,EAAE;MACrD,IAAI,CAACN,EAAE,CAACqD,aAAa,CAAC+F,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACjH,kBAAkB,CAAC;MAChF,IAAI,CAACnC,EAAE,CAACqD,aAAa,CAAC+F,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAChH,kBAAkB,CAAC;MAChF,IAAI,CAACpC,EAAE,CAACqD,aAAa,CAAC+F,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC9G,aAAa,CAAC;IAC1E;IACA,IAAIhC,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,MAAM,EAAE;MACrD,IAAImD,MAAM,GAAG,IAAI,CAACzD,EAAE,CAACqD,aAAa,CAACK,aAAa,CAAC,cAAc,CAAC;MAChE,IAAI,CAACD,MAAM,EAAE;QACTA,MAAM,GAAG,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC3D,EAAE,CAACqD,aAAa,CAAC;MAClD;IACJ;IACA,IAAI,CAAC8F,4BAA4B,CAAC,CAAC;EACvC;EACA7D,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACzD,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC0H,aAAa,EAAE;MAChD,IAAI,IAAI,CAACvG,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM,EACrCuC,QAAQ,CAACO,IAAI,CAAC0D,WAAW,CAAC,IAAI,CAAC3H,SAAS,CAAC,CAAC,KACzC,IAAI,IAAI,CAACmB,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,EAC5C,IAAI,CAAChD,EAAE,CAACqD,aAAa,CAACmG,WAAW,CAAC,IAAI,CAAC3H,SAAS,CAAC,CAAC,KAElDpC,UAAU,CAAC+J,WAAW,CAAC,IAAI,CAAC3H,SAAS,EAAE,IAAI,CAACmB,SAAS,CAAC,UAAU,CAAC,CAAC;IAC1E;IACA,IAAI,CAACmG,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACE,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACjD,iCAAiC,CAAC,CAAC;IACxC,IAAI,CAACqD,aAAa,CAAC,CAAC;IACpB,IAAI,CAAC1F,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAClC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACa,aAAa,GAAG,IAAI;EAC7B;EACA0C,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACpD,WAAW,EAAE;MAClB0H,YAAY,CAAC,IAAI,CAAC1H,WAAW,CAAC;MAC9B,IAAI,CAACA,WAAW,GAAG,IAAI;IAC3B;EACJ;EACAgD,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC/C,WAAW,EAAE;MAClByH,YAAY,CAAC,IAAI,CAACzH,WAAW,CAAC;MAC9B,IAAI,CAACA,WAAW,GAAG,IAAI;IAC3B;EACJ;EACAwH,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACrE,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACJ,gBAAgB,CAAC,CAAC;EAC3B;EACA2E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAACzH,SAAS,EAAE;MAChBjC,WAAW,CAACiH,KAAK,CAAC,IAAI,CAAChF,SAAS,CAAC;IACrC;IACA,IAAI,CAACyD,MAAM,CAAC,CAAC;IACb,IAAI,IAAI,CAAC5C,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACkH,OAAO,CAAC,CAAC;MAC5B,IAAI,CAAClH,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,IAAI,CAACD,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACA,OAAOoH,IAAI,YAAAC,gBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFjK,OAAO,EAAjBd,EAAE,CAAAgL,iBAAA,CAAiC9K,WAAW,GAA9CF,EAAE,CAAAgL,iBAAA,CAAyDhL,EAAE,CAACiL,UAAU,GAAxEjL,EAAE,CAAAgL,iBAAA,CAAmFhL,EAAE,CAACkL,MAAM,GAA9FlL,EAAE,CAAAgL,iBAAA,CAAyGnK,EAAE,CAACsK,aAAa,GAA3HnL,EAAE,CAAAgL,iBAAA,CAAsIhL,EAAE,CAACoL,SAAS,GAApJpL,EAAE,CAAAgL,iBAAA,CAA+JhL,EAAE,CAACqL,gBAAgB;EAAA;EAC7Q,OAAOC,IAAI,kBAD8EtL,EAAE,CAAAuL,iBAAA;IAAAC,IAAA,EACJ1K,OAAO;IAAA2K,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAtK,eAAA;MAAAC,YAAA;MAAAC,QAAA;MAAAC,aAAA;MAAAC,iBAAA;MAAAC,aAAA;MAAAC,MAAA,GADL3B,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,sBACoQ1L,gBAAgB;MAAAyB,SAAA,GADtR5B,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,4BAC6TzL,eAAe;MAAAyB,SAAA,GAD9U7B,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,4BACqXzL,eAAe;MAAA0B,IAAA,GADtY9B,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,kBAC8ZzL,eAAe;MAAA2B,WAAA,GAD/a/B,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,gCAC4dzL,eAAe;MAAA4B,YAAA,GAD7ehC,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,kCAC6hBzL,eAAe;MAAA6B,QAAA,GAD9iBjC,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,0BACklB1L,gBAAgB;MAAA+B,UAAA,GADpmBlC,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,8BAC8oB1L,gBAAgB;MAAAgC,YAAA,GADhqBnC,EAAE,CAAA4L,YAAA,CAAAC,0BAAA,kCACgtB1L,gBAAgB;MAAAiC,OAAA,GADluBpC,EAAE,CAAA4L,YAAA,CAAAE,IAAA;MAAAzJ,QAAA,GAAFrC,EAAE,CAAA4L,YAAA,CAAAE,IAAA;MAAArJ,cAAA;IAAA;IAAAsJ,QAAA,GAAF/L,EAAE,CAAAgM,wBAAA,EAAFhM,EAAE,CAAAiM,oBAAA;EAAA;AAE/F;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FlM,EAAE,CAAAmM,iBAAA,CAGJrL,OAAO,EAAc,CAAC;IACrG0K,IAAI,EAAEnL,SAAS;IACf+L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEf,IAAI,EAAEgB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CjB,IAAI,EAAElL,MAAM;MACZ8L,IAAI,EAAE,CAAClM,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEsL,IAAI,EAAExL,EAAE,CAACiL;EAAW,CAAC,EAAE;IAAEO,IAAI,EAAExL,EAAE,CAACkL;EAAO,CAAC,EAAE;IAAEM,IAAI,EAAE3K,EAAE,CAACsK;EAAc,CAAC,EAAE;IAAEK,IAAI,EAAExL,EAAE,CAACoL;EAAU,CAAC,EAAE;IAAEI,IAAI,EAAExL,EAAE,CAACqL;EAAiB,CAAC,CAAC,EAAkB;IAAEhK,eAAe,EAAE,CAAC;MAC5KmK,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEe,YAAY,EAAE,CAAC;MACfkK,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEgB,QAAQ,EAAE,CAAC;MACXiK,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEiB,aAAa,EAAE,CAAC;MAChBgK,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEkB,iBAAiB,EAAE,CAAC;MACpB+J,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEmB,aAAa,EAAE,CAAC;MAChB8J,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEoB,MAAM,EAAE,CAAC;MACT6J,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvM;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyB,SAAS,EAAE,CAAC;MACZ4J,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEyB,SAAS,EAAE,CAAC;MACZ2J,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE0B,IAAI,EAAE,CAAC;MACP0J,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE2B,WAAW,EAAE,CAAC;MACdyJ,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE4B,YAAY,EAAE,CAAC;MACfwJ,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE6B,QAAQ,EAAE,CAAC;MACXuJ,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvM;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+B,UAAU,EAAE,CAAC;MACbsJ,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvM;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgC,YAAY,EAAE,CAAC;MACfqJ,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvM;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiC,OAAO,EAAE,CAAC;MACVoJ,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAE/J,QAAQ,EAAE,CAAC;MACXmJ,IAAI,EAAEjL,KAAK;MACX6L,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE3J,cAAc,EAAE,CAAC;MACjB+I,IAAI,EAAEjL;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMoM,aAAa,CAAC;EAChB,OAAO9B,IAAI,YAAA+B,sBAAA7B,CAAA;IAAA,YAAAA,CAAA,IAAwF4B,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAhE8E7M,EAAE,CAAA8M,gBAAA;IAAAtB,IAAA,EAgESmB;EAAa;EACjH,OAAOI,IAAI,kBAjE8E/M,EAAE,CAAAgN,gBAAA;IAAAC,OAAA,GAiEkClN,YAAY;EAAA;AAC7I;AACA;EAAA,QAAAmM,SAAA,oBAAAA,SAAA,KAnE6FlM,EAAE,CAAAmM,iBAAA,CAmEJQ,aAAa,EAAc,CAAC;IAC3GnB,IAAI,EAAEhL,QAAQ;IACd4L,IAAI,EAAE,CAAC;MACCa,OAAO,EAAE,CAAClN,YAAY,CAAC;MACvBmN,OAAO,EAAE,CAACpM,OAAO,CAAC;MAClBqM,YAAY,EAAE,CAACrM,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,OAAO,EAAE6L,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}