{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/language.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/dropdown\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => ({\n  \"min-width\": \"150px\"\n});\nfunction LanguageSwitcherComponent_ng_template_1_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getLanguageFlag(ctx_r0.selectedLanguage));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getLanguageName(ctx_r0.selectedLanguage));\n  }\n}\nfunction LanguageSwitcherComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LanguageSwitcherComponent_ng_template_1_div_0_Template, 5, 2, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedLanguage);\n  }\n}\nfunction LanguageSwitcherComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const language_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r2.flag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r2.name);\n  }\n}\nexport let LanguageSwitcherComponent = /*#__PURE__*/(() => {\n  class LanguageSwitcherComponent {\n    constructor(languageService) {\n      this.languageService = languageService;\n      this.languages = [];\n      this.selectedLanguage = '';\n    }\n    ngOnInit() {\n      this.languages = this.languageService.getAvailableLanguages();\n      this.selectedLanguage = this.languageService.getCurrentLanguage().code;\n      // Subscribe to language changes\n      this.languageService.currentLanguage$.subscribe(language => {\n        this.selectedLanguage = language.code;\n      });\n    }\n    onLanguageChange(event) {\n      const languageCode = event.value;\n      this.languageService.setLanguage(languageCode);\n    }\n    getLanguageFlag(code) {\n      const language = this.languages.find(lang => lang.code === code);\n      return language?.flag || '';\n    }\n    getLanguageName(code) {\n      const language = this.languages.find(lang => lang.code === code);\n      return language?.name || '';\n    }\n    static {\n      this.ɵfac = function LanguageSwitcherComponent_Factory(t) {\n        return new (t || LanguageSwitcherComponent)(i0.ɵɵdirectiveInject(i1.LanguageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LanguageSwitcherComponent,\n        selectors: [[\"app-language-switcher\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 5,\n        consts: [[\"optionLabel\", \"name\", \"optionValue\", \"code\", \"placeholder\", \"Select Language\", 1, \"language-switcher\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [\"class\", \"flex align-items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"]],\n        template: function LanguageSwitcherComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-dropdown\", 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LanguageSwitcherComponent_Template_p_dropdown_ngModelChange_0_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedLanguage, $event) || (ctx.selectedLanguage = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function LanguageSwitcherComponent_Template_p_dropdown_onChange_0_listener($event) {\n              return ctx.onLanguageChange($event);\n            });\n            i0.ɵɵtemplate(1, LanguageSwitcherComponent_ng_template_1_Template, 1, 1, \"ng-template\", 1)(2, LanguageSwitcherComponent_ng_template_2_Template, 5, 2, \"ng-template\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c0));\n            i0.ɵɵproperty(\"options\", ctx.languages);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedLanguage);\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, DropdownModule, i3.Dropdown, i4.PrimeTemplate, FormsModule, i5.NgControlStatus, i5.NgModel],\n        styles: [\".language-switcher[_ngcontent-%COMP%]   .p-dropdown[_ngcontent-%COMP%]{border:1px solid var(--surface-border);background:var(--surface-ground)}.language-switcher[_ngcontent-%COMP%]   .p-dropdown[_ngcontent-%COMP%]:not(.p-disabled):hover{border-color:var(--primary-color)}.language-switcher[_ngcontent-%COMP%]   .p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{outline:0 none;outline-offset:0;box-shadow:0 0 0 .2rem var(--primary-color-text);border-color:var(--primary-color)}.rtl[_nghost-%COMP%]   .language-switcher[_ngcontent-%COMP%]   .p-dropdown-label[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .language-switcher[_ngcontent-%COMP%]   .p-dropdown-label[_ngcontent-%COMP%]{text-align:right}\"]\n      });\n    }\n  }\n  return LanguageSwitcherComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}