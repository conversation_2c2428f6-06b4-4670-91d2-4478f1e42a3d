{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/language.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/tooltip\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/sidebar\";\nimport * as i6 from \"primeng/panelmenu\";\nimport * as i7 from \"primeng/button\";\nconst _c0 = [\"*\"];\nfunction LayoutComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"\\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 24);\n    i0.ɵɵtext(4, \"\\u0645\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LayoutComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"Ahmed Mohamed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 24);\n    i0.ɵɵtext(4, \"System Admin\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LayoutComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"i\", 5);\n    i0.ɵɵelementStart(2, \"span\", 26);\n    i0.ɵɵtext(3, \"Menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let LayoutComponent = /*#__PURE__*/(() => {\n  class LayoutComponent {\n    constructor(languageService) {\n      this.languageService = languageService;\n      this.sidebarVisible = false;\n      this.menuItems = [];\n    }\n    ngOnInit() {\n      this.initializeMenuItems();\n      // Subscribe to language changes to update menu items\n      this.languageService.currentLanguage$.subscribe(() => {\n        this.initializeMenuItems();\n      });\n    }\n    initializeMenuItems() {\n      this.menuItems = [{\n        label: this.languageService.translate('nav.dashboard'),\n        icon: 'pi pi-home',\n        routerLink: '/dashboard'\n      }, {\n        label: this.languageService.translate('nav.inventoryManagement'),\n        icon: 'pi pi-box',\n        expanded: true,\n        items: [{\n          label: this.languageService.translate('nav.items'),\n          icon: 'pi pi-list',\n          routerLink: '/items'\n        }, {\n          label: this.languageService.translate('nav.categories'),\n          icon: 'pi pi-sitemap',\n          routerLink: '/categories'\n        }, {\n          label: this.languageService.translate('nav.warehouses'),\n          icon: 'pi pi-building',\n          routerLink: '/warehouses'\n        }, {\n          label: this.languageService.translate('nav.stockMovements'),\n          icon: 'pi pi-arrows-h',\n          routerLink: '/inventory/movements'\n        }, {\n          label: this.languageService.translate('nav.stockAdjustments'),\n          icon: 'pi pi-pencil',\n          routerLink: '/inventory/adjustments'\n        }, {\n          label: this.languageService.translate('nav.transfers'),\n          icon: 'pi pi-send',\n          routerLink: '/inventory/transfers'\n        }]\n      }, {\n        label: this.languageService.translate('nav.salesPurchases'),\n        icon: 'pi pi-shopping-cart',\n        items: [{\n          label: this.languageService.translate('nav.salesInvoices'),\n          icon: 'pi pi-file',\n          routerLink: '/invoices/sales'\n        }, {\n          label: this.languageService.translate('nav.purchaseInvoices'),\n          icon: 'pi pi-file-import',\n          routerLink: '/invoices/purchases'\n        }, {\n          label: this.languageService.translate('nav.salesReturns'),\n          icon: 'pi pi-undo',\n          routerLink: '/invoices/sales-returns'\n        }, {\n          label: this.languageService.translate('nav.purchaseReturns'),\n          icon: 'pi pi-replay',\n          routerLink: '/invoices/purchase-returns'\n        }]\n      }, {\n        label: this.languageService.translate('nav.customersSuppliers'),\n        icon: 'pi pi-users',\n        items: [{\n          label: this.languageService.translate('nav.customers'),\n          icon: 'pi pi-user',\n          routerLink: '/customers'\n        }, {\n          label: this.languageService.translate('nav.suppliers'),\n          icon: 'pi pi-user-plus',\n          routerLink: '/suppliers'\n        }]\n      }, {\n        label: this.languageService.translate('nav.financialManagement'),\n        icon: 'pi pi-dollar',\n        items: [{\n          label: this.languageService.translate('nav.payments'),\n          icon: 'pi pi-credit-card',\n          routerLink: '/payments'\n        }, {\n          label: this.languageService.translate('nav.accountStatements'),\n          icon: 'pi pi-file-pdf',\n          routerLink: '/reports/statements'\n        }, {\n          label: this.languageService.translate('nav.cashRegister'),\n          icon: 'pi pi-wallet',\n          routerLink: '/cash-register'\n        }]\n      }, {\n        label: this.languageService.translate('nav.reports'),\n        icon: 'pi pi-chart-bar',\n        items: [{\n          label: this.languageService.translate('nav.inventoryReports'),\n          icon: 'pi pi-chart-line',\n          routerLink: '/reports/inventory'\n        }, {\n          label: this.languageService.translate('nav.financialReports'),\n          icon: 'pi pi-chart-pie',\n          routerLink: '/reports/financial'\n        }, {\n          label: this.languageService.translate('nav.salesReports'),\n          icon: 'pi pi-trending-up',\n          routerLink: '/reports/sales'\n        }, {\n          label: this.languageService.translate('nav.purchaseReports'),\n          icon: 'pi pi-trending-down',\n          routerLink: '/reports/purchases'\n        }]\n      }];\n    }\n    toggleSidebar() {\n      this.sidebarVisible = !this.sidebarVisible;\n    }\n    static {\n      this.ɵfac = function LayoutComponent_Factory(t) {\n        return new (t || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LanguageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LayoutComponent,\n        selectors: [[\"app-layout\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 28,\n        vars: 13,\n        consts: [[1, \"layout-wrapper\"], [1, \"layout-topbar\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bars\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"pi\", \"pi-box\", \"text-2xl\", \"text-primary\"], [1, \"m-0\", \"text-xl\", \"font-semibold\"], [1, \"relative\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bell\", \"tooltipPosition\", \"bottom\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pTooltip\"], [1, \"notification-badge\"], [1, \"user-profile\", \"flex\", \"align-items-center\", \"gap-2\", \"p-2\", \"border-round\", \"cursor-pointer\", 2, \"border\", \"1px solid var(--surface-border)\"], [1, \"w-2rem\", \"h-2rem\", \"border-circle\", \"bg-primary\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"pi\", \"pi-user\", \"text-white\"], [\"class\", \"flex flex-column text-right\", 4, \"ngIf\"], [\"class\", \"flex flex-column\", 4, \"ngIf\"], [1, \"pi\", \"pi-chevron-down\", \"text-500\"], [1, \"layout-main\"], [1, \"layout-sidebar\", \"hidden-mobile\"], [3, \"model\", \"multiple\"], [\"position\", \"left\", \"styleClass\", \"layout-sidebar-mobile\", 3, \"visibleChange\", \"visible\", \"modal\", \"dismissible\"], [\"pTemplate\", \"header\"], [1, \"layout-content\"], [1, \"flex\", \"flex-column\", \"text-right\"], [1, \"text-sm\", \"font-medium\"], [1, \"text-xs\", \"text-500\"], [1, \"flex\", \"flex-column\"], [1, \"font-semibold\"]],\n        template: function LayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function LayoutComponent_Template_button_click_3_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4);\n            i0.ɵɵelement(5, \"i\", 5);\n            i0.ɵɵelementStart(6, \"h2\", 6);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(8, \"div\", 2);\n            i0.ɵɵelement(9, \"app-language-switcher\");\n            i0.ɵɵelementStart(10, \"div\", 7);\n            i0.ɵɵelement(11, \"button\", 8);\n            i0.ɵɵelementStart(12, \"span\", 9);\n            i0.ɵɵtext(13, \"3\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11);\n            i0.ɵɵelement(16, \"i\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(17, LayoutComponent_div_17_Template, 5, 0, \"div\", 13)(18, LayoutComponent_div_18_Template, 5, 0, \"div\", 14);\n            i0.ɵɵelement(19, \"i\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(20, \"div\", 16)(21, \"div\", 17);\n            i0.ɵɵelement(22, \"p-panelMenu\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"p-sidebar\", 19);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function LayoutComponent_Template_p_sidebar_visibleChange_23_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.sidebarVisible, $event) || (ctx.sidebarVisible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(24, LayoutComponent_ng_template_24_Template, 4, 0, \"ng-template\", 20);\n            i0.ɵɵelement(25, \"p-panelMenu\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 21);\n            i0.ɵɵprojection(27);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"hidden-desktop\", true);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.languageService.translate(\"app.title\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"pTooltip\", ctx.languageService.translate(\"app.notifications\"));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.languageService.isArabic());\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.languageService.isArabic());\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.sidebarVisible);\n            i0.ɵɵproperty(\"modal\", true)(\"dismissible\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, RouterModule, MenubarModule, i3.Tooltip, i4.PrimeTemplate, SidebarModule, i5.Sidebar, PanelMenuModule, i6.PanelMenu, ButtonModule, i7.ButtonDirective, TooltipModule, LanguageSwitcherComponent],\n        styles: [\".layout-wrapper[_ngcontent-%COMP%]{min-height:100vh;display:flex;flex-direction:column}.rtl[_nghost-%COMP%]   .layout-wrapper[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-wrapper[_ngcontent-%COMP%]{direction:rtl}.layout-topbar[_ngcontent-%COMP%]{background:var(--surface-0);border-bottom:1px solid var(--surface-200);padding:1rem 1.5rem;display:flex;align-items:center;justify-content:space-between;position:sticky;top:0;z-index:100;box-shadow:0 2px 4px #0000001a}.rtl[_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%]{flex-direction:row-reverse}.layout-topbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{transition:all .2s ease}.layout-topbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover{background:var(--surface-hover);transform:translateY(-1px)}.layout-topbar[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%]{position:absolute;top:-4px;right:-4px;min-width:18px;height:18px;border-radius:50%;background:#ef4444;color:#fff;font-size:.7rem;display:flex;align-items:center;justify-content:center;font-weight:600}.rtl[_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-topbar[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%]{right:auto;left:-4px}.layout-main[_ngcontent-%COMP%]{flex:1;display:flex}.layout-sidebar[_ngcontent-%COMP%]{width:280px;background:var(--surface-0);border-right:1px solid var(--surface-200);overflow-y:auto;height:calc(100vh - 73px);position:sticky;top:73px}.rtl[_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%]{border-right:none;border-left:1px solid var(--surface-200)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu{border:none}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-panel{border:none;margin-bottom:0}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header{border:none;border-radius:0}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link{border:none;border-radius:0;padding:1rem 1.5rem;background:transparent;color:var(--text-color);transition:all .2s}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:hover{background:var(--surface-100)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:focus{box-shadow:none;background:var(--surface-100)}.rtl[_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link .p-panelmenu-icon, .rtl   [_nghost-%COMP%]   .layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link .p-panelmenu-icon{margin-right:0;margin-left:.5rem}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content{border:none;background:var(--surface-50)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link{padding:.75rem 1.5rem .75rem 3rem;color:var(--text-color-secondary);border:none;border-radius:0;transition:all .2s}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link:hover{background:var(--surface-100);color:var(--text-color)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link.router-link-active{background:var(--primary-color);color:var(--primary-color-text)}.layout-content[_ngcontent-%COMP%]{flex:1;padding:1.5rem;overflow-x:auto;background:var(--surface-50)}@media (max-width: 768px){.hidden-mobile[_ngcontent-%COMP%]{display:none!important}.layout-content[_ngcontent-%COMP%]{padding:1rem}.layout-topbar[_ngcontent-%COMP%]{padding:.75rem 1rem}  .layout-sidebar-mobile{width:280px!important}}@media (min-width: 769px){.hidden-desktop[_ngcontent-%COMP%]{display:none!important}}@media (max-width: 480px){.layout-content[_ngcontent-%COMP%]{padding:.75rem}.layout-topbar[_ngcontent-%COMP%]{padding:.5rem .75rem}.layout-topbar[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1rem!important}  .layout-sidebar-mobile{width:100%!important}}\"]\n      });\n    }\n  }\n  return LayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}