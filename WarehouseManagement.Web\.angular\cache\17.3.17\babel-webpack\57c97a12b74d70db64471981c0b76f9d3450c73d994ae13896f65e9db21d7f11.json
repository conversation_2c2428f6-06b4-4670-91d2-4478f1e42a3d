{"ast": null, "code": "import { operate } from '../util/lift';\nexport function subscribeOn(scheduler, delay = 0) {\n  return operate((source, subscriber) => {\n    subscriber.add(scheduler.schedule(() => source.subscribe(subscriber), delay));\n  });\n}", "map": {"version": 3, "names": ["operate", "subscribeOn", "scheduler", "delay", "source", "subscriber", "add", "schedule", "subscribe"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/rxjs/dist/esm/internal/operators/subscribeOn.js"], "sourcesContent": ["import { operate } from '../util/lift';\nexport function subscribeOn(scheduler, delay = 0) {\n    return operate((source, subscriber) => {\n        subscriber.add(scheduler.schedule(() => source.subscribe(subscriber), delay));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,WAAWA,CAACC,SAAS,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC9C,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnCA,UAAU,CAACC,GAAG,CAACJ,SAAS,CAACK,QAAQ,CAAC,MAAMH,MAAM,CAACI,SAAS,CAACH,UAAU,CAAC,EAAEF,KAAK,CAAC,CAAC;EACjF,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}