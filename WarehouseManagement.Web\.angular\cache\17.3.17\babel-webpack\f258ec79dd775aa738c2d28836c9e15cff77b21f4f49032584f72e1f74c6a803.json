{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let LanguageService = /*#__PURE__*/(() => {\n  class LanguageService {\n    constructor() {\n      this.STORAGE_KEY = 'selected-language';\n      this.languages = [{\n        code: 'en',\n        name: 'English',\n        direction: 'ltr',\n        flag: '🇺🇸'\n      }, {\n        code: 'ar',\n        name: 'العربية',\n        direction: 'rtl',\n        flag: '🇸🇦'\n      }];\n      this.currentLanguageSubject = new BehaviorSubject(this.getDefaultLanguage());\n      this.currentLanguage$ = this.currentLanguageSubject.asObservable();\n      this.initializeLanguage();\n    }\n    getDefaultLanguage() {\n      const savedLanguage = localStorage.getItem(this.STORAGE_KEY);\n      if (savedLanguage) {\n        const found = this.languages.find(lang => lang.code === savedLanguage);\n        if (found) return found;\n      }\n      // Default to Arabic\n      return this.languages.find(lang => lang.code === 'ar') || this.languages[0];\n    }\n    initializeLanguage() {\n      const currentLang = this.currentLanguageSubject.value;\n      this.applyLanguageSettings(currentLang);\n    }\n    getCurrentLanguage() {\n      return this.currentLanguageSubject.value;\n    }\n    getAvailableLanguages() {\n      return [...this.languages];\n    }\n    setLanguage(languageCode) {\n      const language = this.languages.find(lang => lang.code === languageCode);\n      if (language && language.code !== this.currentLanguageSubject.value.code) {\n        this.currentLanguageSubject.next(language);\n        localStorage.setItem(this.STORAGE_KEY, languageCode);\n        this.applyLanguageSettings(language);\n      }\n    }\n    applyLanguageSettings(language) {\n      // Set document direction\n      document.documentElement.dir = language.direction;\n      document.documentElement.lang = language.code;\n      // Add/remove RTL class to body for styling\n      if (language.direction === 'rtl') {\n        document.body.classList.add('rtl');\n        document.body.classList.remove('ltr');\n      } else {\n        document.body.classList.add('ltr');\n        document.body.classList.remove('rtl');\n      }\n      // Update PrimeNG configuration for RTL\n      this.updatePrimeNGDirection(language.direction);\n    }\n    updatePrimeNGDirection(direction) {\n      // This will be used to configure PrimeNG components for RTL\n      const primeNGConfig = window.PrimeNG?.config;\n      if (primeNGConfig) {\n        primeNGConfig.ripple = true;\n        primeNGConfig.inputStyle = 'outlined';\n        primeNGConfig.rtl = direction === 'rtl';\n      }\n    }\n    isRTL() {\n      return this.currentLanguageSubject.value.direction === 'rtl';\n    }\n    isArabic() {\n      return this.currentLanguageSubject.value.code === 'ar';\n    }\n    // Translation helper methods\n    translate(key, params) {\n      // This is a simple implementation. In a real app, you'd use Angular i18n or ngx-translate\n      const translations = this.getTranslations();\n      const currentLang = this.getCurrentLanguage().code;\n      let translation = translations[currentLang]?.[key] || translations['en']?.[key] || key;\n      // Simple parameter replacement\n      if (params) {\n        Object.keys(params).forEach(param => {\n          translation = translation.replace(`{{${param}}}`, params[param]);\n        });\n      }\n      return translation;\n    }\n    getTranslations() {\n      return {\n        en: {\n          // Navigation\n          'nav.dashboard': 'Dashboard',\n          'nav.items': 'Items',\n          'nav.categories': 'Categories',\n          'nav.warehouses': 'Warehouses',\n          'nav.inventory': 'Inventory',\n          'nav.customers': 'Customers',\n          'nav.suppliers': 'Suppliers',\n          'nav.invoices': 'Invoices',\n          'nav.payments': 'Payments',\n          'nav.reports': 'Reports',\n          'nav.settings': 'Settings',\n          'nav.logout': 'Logout',\n          // Dashboard\n          'dashboard.title': 'Dashboard',\n          'dashboard.totalSales': 'Total Sales',\n          'dashboard.totalPurchases': 'Total Purchases',\n          'dashboard.totalItems': 'Total Items',\n          'dashboard.lowStockItems': 'Low Stock Items',\n          'dashboard.recentTransactions': 'Recent Transactions',\n          'dashboard.salesTrend': 'Sales Trend',\n          'dashboard.inventoryOverview': 'Inventory Overview',\n          'dashboard.monthlyRevenue': 'Monthly Revenue',\n          // Common\n          'common.search': 'Search',\n          'common.add': 'Add',\n          'common.edit': 'Edit',\n          'common.delete': 'Delete',\n          'common.save': 'Save',\n          'common.cancel': 'Cancel',\n          'common.confirm': 'Confirm',\n          'common.yes': 'Yes',\n          'common.no': 'No',\n          'common.loading': 'Loading...',\n          'common.noData': 'No data available',\n          'common.actions': 'Actions',\n          'common.status': 'Status',\n          'common.date': 'Date',\n          'common.amount': 'Amount',\n          'common.total': 'Total',\n          'common.refresh': 'Refresh',\n          // Authentication\n          'auth.login.title': 'Sign In',\n          'auth.login.subtitle': 'Welcome to Warehouse Management System',\n          'auth.login.username': 'Username',\n          'auth.login.usernamePlaceholder': 'Enter your username',\n          'auth.login.password': 'Password',\n          'auth.login.passwordPlaceholder': 'Enter your password',\n          'auth.login.rememberMe': 'Remember me',\n          'auth.login.signIn': 'Sign In',\n          'auth.login.forgotPassword': 'Forgot password?',\n          'auth.login.success': 'Login successful',\n          'auth.login.welcomeBack': 'Welcome back {{name}}',\n          'auth.login.securityNotice': 'Your session is protected with SSL encryption and advanced security features',\n          'auth.login.attemptsRemaining': 'Attempts remaining: {{attempts}}',\n          'auth.login.error.title': 'Login Error',\n          'auth.login.error.generic': 'An error occurred during login. Please try again.',\n          'auth.login.error.invalidCredentials': 'Invalid username or password',\n          'auth.login.error.accountLocked': 'Account temporarily locked due to multiple failed login attempts',\n          'auth.login.error.tooManyAttempts': 'Too many attempts. Please try again later',\n          'auth.login.lockout.title': 'Account Locked',\n          'auth.login.lockout.message': 'Your account has been locked for {{minutes}} minutes due to multiple failed login attempts',\n          'auth.login.lockout.active': 'Account locked. Time remaining: {{time}}',\n          'auth.login.lockout.expired': 'Lockout period expired',\n          'auth.login.lockout.canTryAgain': 'You can now try again',\n          'auth.login.rateLimit.title': 'Rate Limit Exceeded',\n          'auth.login.rateLimit.message': 'Too many attempts. Please wait before trying again',\n          'auth.login.validation.invalid': 'Invalid value',\n          'auth.login.validation.username.required': 'Username is required',\n          'auth.login.validation.username.minLength': 'Username must be at least {{min}} characters',\n          'auth.login.validation.username.maxLength': 'Username must be at most {{max}} characters',\n          'auth.login.validation.password.required': 'Password is required',\n          'auth.login.validation.password.minLength': 'Password must be at least {{min}} characters',\n          'auth.login.footer.copyright': '© 2024 Warehouse Management System. All rights reserved.',\n          'auth.login.footer.privacy': 'Privacy Policy',\n          'auth.login.footer.terms': 'Terms of Service',\n          'auth.login.footer.support': 'Support',\n          // Unauthorized\n          'auth.unauthorized.title': 'Access Denied',\n          'auth.unauthorized.description': 'You do not have permission to access this resource.',\n          'auth.unauthorized.details': 'Please contact your administrator if you believe this is an error.',\n          'auth.unauthorized.goToDashboard': 'Go to Dashboard',\n          'auth.unauthorized.goBack': 'Go Back',\n          'auth.unauthorized.contactSupport': 'Contact Support',\n          'auth.unauthorized.logout': 'Logout',\n          'auth.unauthorized.autoRedirect': 'You will be redirected to the dashboard in 10 seconds.',\n          // Security Monitoring\n          'security.monitoring.title': 'Security Monitoring',\n          'security.monitoring.autoRefresh': 'Auto Refresh',\n          'security.monitoring.refresh': 'Refresh',\n          'security.monitoring.export': 'Export Data',\n          'security.monitoring.totalEvents': 'Total Events',\n          'security.monitoring.criticalEvents': 'Critical Events',\n          'security.monitoring.successfulLogins': 'Successful Logins',\n          'security.monitoring.failedLogins': 'Failed Logins',\n          'security.monitoring.uniqueIPs': 'Unique IPs',\n          'security.monitoring.lockedAccounts': 'Locked Accounts',\n          'security.monitoring.filters': 'Filters',\n          'security.monitoring.eventType': 'Event Type',\n          'security.monitoring.severity': 'Severity',\n          'security.monitoring.dateRange': 'Date Range',\n          'security.monitoring.apply': 'Apply',\n          'security.monitoring.clear': 'Clear',\n          'security.monitoring.eventsBySeverity': 'Events by Severity',\n          'security.monitoring.loginAttempts': 'Login Attempts',\n          'security.monitoring.securityEvents': 'Security Events',\n          'security.monitoring.timestamp': 'Timestamp',\n          'security.monitoring.description': 'Description',\n          'security.monitoring.ipAddress': 'IP Address',\n          'security.monitoring.actions': 'Actions',\n          'security.monitoring.username': 'Username',\n          'security.monitoring.status': 'Status',\n          'security.monitoring.failureReason': 'Failure Reason',\n          'security.monitoring.viewDetails': 'View Details',\n          'security.monitoring.noEvents': 'No security events found',\n          'security.monitoring.noAttempts': 'No login attempts found',\n          'security.monitoring.success': 'Success',\n          'security.monitoring.failure': 'Failure',\n          'security.monitoring.accessDenied': 'Access Denied',\n          'security.monitoring.accessDeniedMessage': 'You do not have permission to view security monitoring data.'\n        },\n        ar: {\n          // App\n          'app.title': 'نظام إدارة المستودعات',\n          'app.welcome': 'مرحباً بك',\n          'app.profile': 'الملف الشخصي',\n          'app.notifications': 'الإشعارات',\n          // Navigation\n          'nav.dashboard': 'لوحة التحكم',\n          'nav.inventoryManagement': 'إدارة المخزون',\n          'nav.items': 'الأصناف',\n          'nav.categories': 'الفئات',\n          'nav.warehouses': 'المستودعات',\n          'nav.stockMovements': 'حركات المخزون',\n          'nav.stockAdjustments': 'تسويات المخزون',\n          'nav.transfers': 'التحويلات',\n          'nav.salesPurchases': 'المبيعات والمشتريات',\n          'nav.salesInvoices': 'فواتير المبيعات',\n          'nav.purchaseInvoices': 'فواتير المشتريات',\n          'nav.salesReturns': 'مرتجعات المبيعات',\n          'nav.purchaseReturns': 'مرتجعات المشتريات',\n          'nav.customersSuppliers': 'العملاء والموردين',\n          'nav.customers': 'العملاء',\n          'nav.suppliers': 'الموردين',\n          'nav.financialManagement': 'الإدارة المالية',\n          'nav.payments': 'المدفوعات',\n          'nav.accountStatements': 'كشوف الحسابات',\n          'nav.cashRegister': 'الخزينة',\n          'nav.reports': 'التقارير',\n          'nav.inventoryReports': 'تقارير المخزون',\n          'nav.financialReports': 'التقارير المالية',\n          'nav.salesReports': 'تقارير المبيعات',\n          'nav.purchaseReports': 'تقارير المشتريات',\n          'nav.settings': 'الإعدادات',\n          'nav.logout': 'تسجيل الخروج',\n          // Dashboard\n          'dashboard.title': 'لوحة التحكم',\n          'dashboard.totalSales': 'إجمالي المبيعات',\n          'dashboard.totalPurchases': 'إجمالي المشتريات',\n          'dashboard.totalItems': 'إجمالي الأصناف',\n          'dashboard.lowStockItems': 'أصناف منخفضة المخزون',\n          'dashboard.recentTransactions': 'المعاملات الأخيرة',\n          'dashboard.salesTrend': 'اتجاه المبيعات',\n          'dashboard.inventoryOverview': 'نظرة عامة على المخزون',\n          'dashboard.monthlyRevenue': 'الإيرادات الشهرية',\n          // Common\n          'common.search': 'بحث',\n          'common.add': 'إضافة',\n          'common.edit': 'تعديل',\n          'common.delete': 'حذف',\n          'common.save': 'حفظ',\n          'common.cancel': 'إلغاء',\n          'common.confirm': 'تأكيد',\n          'common.yes': 'نعم',\n          'common.no': 'لا',\n          'common.loading': 'جاري التحميل...',\n          'common.noData': 'لا توجد بيانات متاحة',\n          'common.actions': 'الإجراءات',\n          'common.status': 'الحالة',\n          'common.date': 'التاريخ',\n          'common.amount': 'المبلغ',\n          'common.total': 'الإجمالي',\n          'common.refresh': 'تحديث',\n          // Authentication\n          'auth.login.title': 'تسجيل الدخول',\n          'auth.login.subtitle': 'مرحباً بك في نظام إدارة المخازن',\n          'auth.login.username': 'اسم المستخدم',\n          'auth.login.usernamePlaceholder': 'أدخل اسم المستخدم',\n          'auth.login.password': 'كلمة المرور',\n          'auth.login.passwordPlaceholder': 'أدخل كلمة المرور',\n          'auth.login.rememberMe': 'تذكرني',\n          'auth.login.signIn': 'تسجيل الدخول',\n          'auth.login.forgotPassword': 'نسيت كلمة المرور؟',\n          'auth.login.success': 'تم تسجيل الدخول بنجاح',\n          'auth.login.welcomeBack': 'مرحباً بعودتك {{name}}',\n          'auth.login.securityNotice': 'يتم حماية جلستك بتشفير SSL وتقنيات الأمان المتقدمة',\n          'auth.login.attemptsRemaining': 'محاولات متبقية: {{attempts}}',\n          'auth.login.error.title': 'خطأ في تسجيل الدخول',\n          'auth.login.error.generic': 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',\n          'auth.login.error.invalidCredentials': 'اسم المستخدم أو كلمة المرور غير صحيحة',\n          'auth.login.error.accountLocked': 'تم قفل الحساب مؤقتاً بسبب محاولات دخول متعددة فاشلة',\n          'auth.login.error.tooManyAttempts': 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً',\n          'auth.login.lockout.title': 'تم قفل الحساب',\n          'auth.login.lockout.message': 'تم قفل حسابك لمدة {{minutes}} دقيقة بسبب محاولات دخول فاشلة متعددة',\n          'auth.login.lockout.active': 'الحساب مقفل. الوقت المتبقي: {{time}}',\n          'auth.login.lockout.expired': 'انتهت مدة القفل',\n          'auth.login.lockout.canTryAgain': 'يمكنك الآن المحاولة مرة أخرى',\n          'auth.login.rateLimit.title': 'تم تجاوز الحد المسموح',\n          'auth.login.rateLimit.message': 'تم تجاوز عدد المحاولات المسموح. يرجى الانتظار قبل المحاولة مرة أخرى',\n          'auth.login.validation.invalid': 'القيمة المدخلة غير صحيحة',\n          'auth.login.validation.username.required': 'اسم المستخدم مطلوب',\n          'auth.login.validation.username.minLength': 'اسم المستخدم يجب أن يكون {{min}} أحرف على الأقل',\n          'auth.login.validation.username.maxLength': 'اسم المستخدم يجب أن يكون {{max}} حرف كحد أقصى',\n          'auth.login.validation.password.required': 'كلمة المرور مطلوبة',\n          'auth.login.validation.password.minLength': 'كلمة المرور يجب أن تكون {{min}} أحرف على الأقل',\n          'auth.login.footer.copyright': '© 2024 نظام إدارة المخازن. جميع الحقوق محفوظة.',\n          'auth.login.footer.privacy': 'سياسة الخصوصية',\n          'auth.login.footer.terms': 'شروط الاستخدام',\n          'auth.login.footer.support': 'الدعم الفني',\n          // Unauthorized\n          'auth.unauthorized.title': 'تم رفض الوصول',\n          'auth.unauthorized.description': 'ليس لديك صلاحية للوصول إلى هذا المورد.',\n          'auth.unauthorized.details': 'يرجى الاتصال بالمسؤول إذا كنت تعتقد أن هذا خطأ.',\n          'auth.unauthorized.goToDashboard': 'الذهاب إلى لوحة التحكم',\n          'auth.unauthorized.goBack': 'العودة',\n          'auth.unauthorized.contactSupport': 'الاتصال بالدعم',\n          'auth.unauthorized.logout': 'تسجيل الخروج',\n          'auth.unauthorized.autoRedirect': 'سيتم توجيهك إلى لوحة التحكم خلال 10 ثوانٍ.',\n          // Security Monitoring\n          'security.monitoring.title': 'مراقبة الأمان',\n          'security.monitoring.autoRefresh': 'التحديث التلقائي',\n          'security.monitoring.refresh': 'تحديث',\n          'security.monitoring.export': 'تصدير البيانات',\n          'security.monitoring.totalEvents': 'إجمالي الأحداث',\n          'security.monitoring.criticalEvents': 'الأحداث الحرجة',\n          'security.monitoring.successfulLogins': 'تسجيلات الدخول الناجحة',\n          'security.monitoring.failedLogins': 'تسجيلات الدخول الفاشلة',\n          'security.monitoring.uniqueIPs': 'عناوين IP الفريدة',\n          'security.monitoring.lockedAccounts': 'الحسابات المقفلة',\n          'security.monitoring.filters': 'المرشحات',\n          'security.monitoring.eventType': 'نوع الحدث',\n          'security.monitoring.severity': 'الخطورة',\n          'security.monitoring.dateRange': 'نطاق التاريخ',\n          'security.monitoring.apply': 'تطبيق',\n          'security.monitoring.clear': 'مسح',\n          'security.monitoring.eventsBySeverity': 'الأحداث حسب الخطورة',\n          'security.monitoring.loginAttempts': 'محاولات تسجيل الدخول',\n          'security.monitoring.securityEvents': 'أحداث الأمان',\n          'security.monitoring.timestamp': 'الوقت',\n          'security.monitoring.description': 'الوصف',\n          'security.monitoring.ipAddress': 'عنوان IP',\n          'security.monitoring.actions': 'الإجراءات',\n          'security.monitoring.username': 'اسم المستخدم',\n          'security.monitoring.status': 'الحالة',\n          'security.monitoring.failureReason': 'سبب الفشل',\n          'security.monitoring.viewDetails': 'عرض التفاصيل',\n          'security.monitoring.noEvents': 'لم يتم العثور على أحداث أمان',\n          'security.monitoring.noAttempts': 'لم يتم العثور على محاولات تسجيل دخول',\n          'security.monitoring.success': 'نجح',\n          'security.monitoring.failure': 'فشل',\n          'security.monitoring.accessDenied': 'تم رفض الوصول',\n          'security.monitoring.accessDeniedMessage': 'ليس لديك صلاحية لعرض بيانات مراقبة الأمان.'\n        }\n      };\n    }\n    static {\n      this.ɵfac = function LanguageService_Factory(t) {\n        return new (t || LanguageService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LanguageService,\n        factory: LanguageService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return LanguageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}