<div class="dashboard-container">
  <!-- Header -->
  <div class="flex justify-content-between align-items-center mb-4">
    <h1 class="text-2xl font-bold m-0">{{ languageService.translate('dashboard.title') }}</h1>
    <button
      pButton
      type="button"
      icon="pi pi-refresh"
      [label]="languageService.translate('common.refresh')"
      class="p-button-outlined"
      (click)="refreshDashboard()"
      [loading]="loading">
    </button>
  </div>

  <div *ngIf="loading" class="flex justify-content-center align-items-center" style="height: 400px;">
    <p-progressSpinner></p-progressSpinner>
  </div>

  <div *ngIf="!loading && dashboardData" class="dashboard-content">
    <!-- Key Metrics Cards -->
    <div class="grid">
      <div class="col-12 md:col-6 lg:col-3">
        <p-card styleClass="metric-card">
          <div class="flex justify-content-between align-items-center">
            <div>
              <div class="text-500 font-medium mb-2">{{ languageService.translate('dashboard.totalItems') }}</div>
              <div class="text-2xl font-bold text-900">{{ dashboardData.totalItems | number }}</div>
            </div>
            <div class="metric-icon bg-blue-100 text-blue-600">
              <i class="pi pi-box text-2xl"></i>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card styleClass="metric-card">
          <div class="flex justify-content-between align-items-center">
            <div>
              <div class="text-500 font-medium mb-2">{{ languageService.translate('dashboard.lowStockItems') }}</div>
              <div class="text-2xl font-bold text-orange-600">{{ dashboardData.lowStockItems.length }}</div>
            </div>
            <div class="metric-icon bg-orange-100 text-orange-600">
              <i class="pi pi-exclamation-triangle text-2xl"></i>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card styleClass="metric-card">
          <div class="flex justify-content-between align-items-center">
            <div>
              <div class="text-500 font-medium mb-2">{{ languageService.translate('dashboard.monthlyRevenue') }}</div>
              <div class="text-2xl font-bold text-green-600">{{ dashboardData.totalRevenue | currency }}</div>
            </div>
            <div class="metric-icon bg-green-100 text-green-600">
              <i class="pi pi-dollar text-2xl"></i>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card styleClass="metric-card">
          <div class="flex justify-content-between align-items-center">
            <div>
              <div class="text-500 font-medium mb-2">Pending Orders</div>
              <div class="text-2xl font-bold text-purple-600">{{ dashboardData.pendingOrders }}</div>
            </div>
            <div class="metric-icon bg-purple-100 text-purple-600">
              <i class="pi pi-clock text-2xl"></i>
            </div>
          </div>
        </p-card>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid">
      <!-- Sales Trend Chart -->
      <div class="col-12 lg:col-8">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3 class="card-title">Sales Trend</h3>
            </div>
          </ng-template>
          <p-chart 
            type="line" 
            [data]="salesChartData" 
            [options]="salesChartOptions"
            height="300px">
          </p-chart>
        </p-card>
      </div>

      <!-- Inventory Distribution -->
      <div class="col-12 lg:col-4">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3 class="card-title">Inventory Distribution</h3>
            </div>
          </ng-template>
          <p-chart 
            type="doughnut" 
            [data]="inventoryChartData" 
            [options]="inventoryChartOptions"
            height="300px">
          </p-chart>
        </p-card>
      </div>
    </div>

    <!-- Revenue & Profit Chart -->
    <div class="grid">
      <div class="col-12">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3 class="card-title">Monthly Revenue & Profit</h3>
            </div>
          </ng-template>
          <p-chart 
            type="bar" 
            [data]="revenueChartData" 
            [options]="revenueChartOptions"
            height="400px">
          </p-chart>
        </p-card>
      </div>
    </div>

    <!-- Tables Row -->
    <div class="grid">
      <!-- Low Stock Items -->
      <div class="col-12 lg:col-6">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3 class="card-title">Low Stock Items</h3>
            </div>
          </ng-template>
          <p-table 
            [value]="dashboardData.lowStockItems" 
            [paginator]="true" 
            [rows]="5"
            styleClass="p-datatable-sm">
            <ng-template pTemplate="header">
              <tr>
                <th>Item</th>
                <th>Current Stock</th>
                <th>Min Level</th>
                <th>Status</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item>
              <tr>
                <td>
                  <div class="font-medium">{{ item.name }}</div>
                  <div class="text-sm text-500">{{ item.code }}</div>
                </td>
                <td>{{ item.currentStock }}</td>
                <td>{{ item.minimumLevel }}</td>
                <td>
                  <p-progressBar
                    [value]="(item.currentStock / item.minimumLevel) * 100"
                    [showValue]="false"
                    styleClass="w-full"
                    [ngClass]="'progress-' + getStockLevelSeverity((item.currentStock / item.minimumLevel) * 100)">
                  </p-progressBar>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </p-card>
      </div>

      <!-- Recent Transactions -->
      <div class="col-12 lg:col-6">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3 class="card-title">Recent Transactions</h3>
            </div>
          </ng-template>
          <p-table 
            [value]="dashboardData.recentTransactions" 
            [paginator]="true" 
            [rows]="5"
            styleClass="p-datatable-sm">
            <ng-template pTemplate="header">
              <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Amount</th>
                <th>Status</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-transaction>
              <tr>
                <td>{{ transaction.date | date:'short' }}</td>
                <td>
                  <p-tag 
                    [value]="transaction.type" 
                    [severity]="getTransactionTypeSeverity(transaction.type)">
                  </p-tag>
                </td>
                <td>{{ transaction.amount | currency }}</td>
                <td>
                  <p-tag 
                    [value]="transaction.status" 
                    [severity]="transaction.status === 'Completed' ? 'success' : 'warning'">
                  </p-tag>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </p-card>
      </div>
    </div>
  </div>
</div>
