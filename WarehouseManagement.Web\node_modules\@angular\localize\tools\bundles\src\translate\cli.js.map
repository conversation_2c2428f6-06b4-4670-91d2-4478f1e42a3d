{"version": 3, "sources": ["../../../../../../../../../packages/localize/tools/src/translate/cli.ts", "../../../../../../../../../packages/localize/tools/src/translate/output_path.ts", "../../../../../../../../../packages/localize/tools/src/translate/index.ts", "../../../../../../../../../packages/localize/tools/src/translate/asset_files/asset_translation_handler.ts", "../../../../../../../../../packages/localize/tools/src/translate/source_files/source_file_translation_handler.ts", "../../../../../../../../../packages/localize/tools/src/translate/translation_files/translation_loader.ts", "../../../../../../../../../packages/localize/tools/src/translate/translator.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQA,SAAQ,kBAAkB,qBAAoB;AAC9C,OAAO,UAAU;AACjB,OAAO,WAAW;;;ACcZ,SAAU,gBAAgBA,KAAsB,cAA4B;AAChF,QAAM,CAAC,KAAK,IAAI,IAAI,aAAa,MAAM,YAAY;AACnD,SAAO,SAAS,SAAY,CAAC,SAAS,iBAAiBA,IAAG,KAAK,KAAK,YAAY,IACpD,CAAC,QAAQ,iBAAiBA,IAAG,KAAK,MAAM,SAAS,MAAM,YAAY;AACjG;;;ACrBA,SAAQ,eAAe,oBAAmB;;;ACA1C,SAAQ,oBAA4D;AAS9D,IAAO,0BAAP,MAA8B;EAClC,YAAoBC,KAAc;AAAd,SAAA,KAAAA;EAAiB;EAErC,aAAa,mBAA+C,WAAqB;AAC/E,WAAO;EACT;EAEA,UACIC,cAA0B,aAC1B,kBAA8C,UAC9CC,eAA4B,cAAmCC,eAAqB;AACtF,eAAW,eAAe,cAAc;AACtC,WAAK,eACDF,cAAaC,eAAc,YAAY,QAAQ,kBAAkB,QAAQ;IAC/E;AACA,QAAIC,kBAAiB,QAAW;AAC9B,WAAK,eAAeF,cAAaC,eAAcC,eAAc,kBAAkB,QAAQ;IACzF;EACF;EAEQ,eACJF,cAA0BC,eAA4B,QACtD,kBAA8C,UAAoB;AACpE,QAAI;AACF,YAAM,aAAa,aAAaA,cAAa,QAAQ,gBAAgB,CAAC;AACtE,WAAK,GAAG,UAAU,KAAK,GAAG,QAAQ,UAAU,CAAC;AAC7C,WAAK,GAAG,UAAU,YAAY,QAAQ;IACxC,SAAS,GAAP;AACA,MAAAD,aAAY,MAAO,EAAY,OAAO;IACxC;EACF;;;;ACvCF,SAAQ,gBAAAG,qBAA4D;AACpE,OAAO,WAAyB;AAe1B,IAAO,+BAAP,MAAmC;EAGvC,YAAoBC,KAAwB,qBAA6C,CAAA,GAAE;AAAvE,SAAA,KAAAA;AAAwB,SAAA,qBAAA;AAFpC,SAAA,sBACqB,EAAC,GAAG,KAAK,oBAAoB,oBAAoB,SAAQ;EACQ;EAE9F,aAAa,kBAA8C,WAAqB;AAC9E,WAAO,KAAK,GAAG,QAAQ,gBAAgB,MAAM;EAC/C;EAEA,UACIC,cAA0B,YAA4B,kBACtD,UAAsBC,eAA4B,cAClDC,eAAqB;AACvB,UAAM,aAAa,OAAO,KAAK,QAAQ,EAAE,SAAS,MAAM;AAGxD,QAAI,CAAC,WAAW,SAAS,WAAW,GAAG;AACrC,iBAAW,eAAe,cAAc;AACtC,aAAK,gBACDF,cAAaC,eAAc,YAAY,QAAQ,kBAAkB,QAAQ;MAC/E;AACA,UAAIC,kBAAiB,QAAW;AAC9B,aAAK,gBAAgBF,cAAaC,eAAcC,eAAc,kBAAkB,QAAQ;MAC1F;IACF,OAAO;AACL,YAAM,MAAM,MAAM,UAAU,YAAY,EAAC,YAAY,UAAU,iBAAgB,CAAC;AAChF,UAAI,CAAC,KAAK;AACR,QAAAF,aAAY,MACR,gCAAgC,KAAK,GAAG,KAAK,YAAY,gBAAgB,GAAG;AAChF;MACF;AAEA,iBAAW,qBAAqB,cAAc;AAC5C,aAAK,cACDA,cAAa,KAAK,mBAAmB,YAAY,kBAAkBC,eACnE,KAAK,kBAAkB;MAC7B;AACA,UAAIC,kBAAiB,QAAW;AAG9B,aAAK,cACDF,cAAa,KAAK,EAAC,QAAQE,eAAc,cAAc,CAAA,EAAE,GAAG,YAC5D,kBAAkBD,eAAc,KAAK,mBAAmB;MAC9D;IACF;EACF;EAEQ,cACJD,cAA0B,KAAuB,mBACjD,YAA4B,UAAuBC,eACnDE,UAA+B;AACjC,UAAM,aAAa,MAAM,qBAAqB,KAAK,QAAW;MAC5D,SAAS;MACT,eAAe,EAAC,UAAU,KAAI;MAC9B,SAAS;QACP,iBAAiB,kBAAkB,MAAM;QACzC,0BAA0BH,cAAa,kBAAkB,cAAcG,UAAS,KAAK,EAAE;QACvF,uBAAuBH,cAAa,kBAAkB,cAAcG,UAAS,KAAK,EAAE;;MAEtF,KAAK;MACL;KACD;AACD,QAAI,cAAc,WAAW,MAAM;AACjC,WAAK,gBACDH,cAAaC,eAAc,kBAAkB,QAAQ,UAAU,WAAW,IAAI;AAClF,YAAM,aAAaG,cAAaH,cAAa,kBAAkB,QAAQ,QAAQ,CAAC;AAChF,WAAK,GAAG,UAAU,KAAK,GAAG,QAAQ,UAAU,CAAC;AAC7C,WAAK,GAAG,UAAU,YAAY,WAAW,IAAI;IAC/C,OAAO;AACL,MAAAD,aAAY,MAAM,oCAAoC,KAAK,GAAG,KAAK,YAAY,QAAQ,GAAG;AAC1F;IACF;EACF;EAEQ,gBACJA,cAA0BC,eAA4B,QACtD,kBAA+B,UAA2B;AAC5D,QAAI;AACF,YAAM,aAAaG,cAAaH,cAAa,QAAQ,gBAAgB,CAAC;AACtE,WAAK,GAAG,UAAU,KAAK,GAAG,QAAQ,UAAU,CAAC;AAC7C,WAAK,GAAG,UAAU,YAAY,QAAQ;IACxC,SAAS,GAAP;AACA,MAAAD,aAAY,MAAO,EAAY,OAAO;IACxC;EACF;;;;AC3FI,IAAO,oBAAP,MAAwB;EAC5B,YACYK,KAAgC,oBAChCC,uBACmBC,cAAyB;AAF5C,SAAA,KAAAF;AAAgC,SAAA,qBAAA;AAChC,SAAA,uBAAAC;AACmB,SAAA,cAAAC;EAA4B;EAwB3D,YACIC,uBACAC,yBAA4C;AAC9C,WAAOD,sBAAqB,IAAI,CAAC,WAAW,UAAS;AACnD,YAAM,iBAAiBC,wBAAuB;AAC9C,aAAO,KAAK,aAAa,WAAW,cAAc;IACpD,CAAC;EACH;EAKQ,WAAW,UAA0B,gBAAgC;AAE3E,UAAM,eAAe,KAAK,GAAG,SAAS,QAAQ;AAC9C,UAAM,gBAAgB,oBAAI,IAAG;AAC7B,eAAW,qBAAqB,KAAK,oBAAoB;AACvD,YAAM,SAAS,kBAAkB,QAAQ,UAAU,YAAY;AAC/D,UAAI,CAAC,OAAO,UAAU;AACpB,sBAAc,IAAI,mBAAmB,MAAM;AAC3C;MACF;AAEA,YAAM,EAAC,QAAQ,cAAc,cAAc,aAAAF,aAAW,IAClD,kBAAkB,MAAM,UAAU,cAAc,OAAO,IAAI;AAC/D,UAAIA,aAAY,WAAW;AACzB,cAAM,IAAI,MAAMA,aAAY,kBACxB,yBAAyB,gCAAgC,CAAC;MAChE;AAEA,YAAM,SAAS,kBAAkB;AACjC,UAAI,WAAW,QAAW;AACxB,cAAM,IAAI,MAAM,yBACZ,+FAA+F;MACrG;AAEA,UAAI,iBAAiB,UAAa,mBAAmB,UACjD,iBAAiB,gBAAgB;AACnC,QAAAA,aAAY,KACR,wBAAwB,qDACpB,gDAAgD,YAAY;MACtE;AAGA,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,MAAMA,YAAW;MACpC;AAEA,aAAO,EAAC,QAAQ,cAAc,aAAAA,aAAW;IAC3C;AAEA,UAAM,sBAAgC,CAAA;AACtC,eAAW,CAAC,QAAQ,MAAM,KAAK,cAAc,QAAO,GAAI;AACtD,0BAAoB,KAAK,OAAO,YAAY,kBACxC;EAAK,OAAO,YAAY,qCAAqC,CAAC;IACpE;AACA,UAAM,IAAI,MACN,yEAAyE,cACzE,oBAAoB,KAAK,IAAI,CAAC;EACpC;EAMQ,aAAa,WAA6B,gBAAgC;AAEhF,UAAM,UAAU,UAAU,IAAI,cAAY,KAAK,WAAW,UAAU,cAAc,CAAC;AACnF,UAAM,SAAS,QAAQ;AACvB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAM,aAAa,QAAQ;AAC3B,UAAI,WAAW,WAAW,OAAO,QAAQ;AACvC,YAAI,KAAK,aAAa;AACpB,gBAAM,gBAAgB,UAAU,MAAM,GAAG,CAAC,EAAE,IAAI,OAAK,IAAI,IAAI,EAAE,KAAK,IAAI;AACxE,eAAK,YAAY,KAAK,+DAClB,WAAW,qBAAqB,UAAU,yCAC1C,OAAO,mCAAmC,iBAAiB;QACjE;MACF;AACA,aAAO,KAAK,WAAW,YAAY,EAAE,QAAQ,eAAY;AA9G/D;AA+GQ,YAAI,OAAO,aAAa,eAAe,QAAW;AAChD,qBAAK,gBAAL,mBAAkB,IACd,KAAK,sBACL,uCAAuC,4BAA4B,UAAU;QACnF,OAAO;AACL,iBAAO,aAAa,aAAa,WAAW,aAAa;QAC3D;MACF,CAAC;IACH;AACA,WAAO;EACT;;;;ACtEI,IAAO,aAAP,MAAiB;EACrB,YACYG,KAAgC,kBAChCC,cAAwB;AADxB,SAAA,KAAAD;AAAgC,SAAA,mBAAA;AAChC,SAAA,cAAAC;EAA2B;EAEvC,eACI,YAA2B,UAA0BC,eACrD,cAAmCC,eAAqB;AAC1D,eAAW,QAAQ,CAAC,cAAa;AAC/B,YAAM,eAAe,KAAK,GAAG,QAAQ,UAAU,SAAS;AACxD,YAAM,WAAW,KAAK,GAAG,eAAe,YAAY;AACpD,YAAM,eAAe,KAAK,GAAG,SAAS,UAAU,YAAY;AAC5D,iBAAW,mBAAmB,KAAK,kBAAkB;AACnD,YAAI,gBAAgB,aAAa,cAAc,QAAQ,GAAG;AACxD,iBAAO,gBAAgB,UACnB,KAAK,aAAa,UAAU,cAAc,UAAUD,eAAc,cAClEC,aAAY;QAClB;MACF;AACA,WAAK,YAAY,MAAM,mCAAmC,WAAW;IACvE,CAAC;EACH;;;;AJAI,SAAU,eAAe,EAC7B,gBAAAC,iBACA,iBAAAC,kBACA,sBAAAC,uBACA,wBAAAC,yBACA,cAAAC,eACA,aAAAC,cACA,oBAAAC,qBACA,sBAAAC,uBACA,cAAAC,cAAY,GACU;AACtB,QAAMC,MAAK,cAAa;AACxB,QAAM,oBAAoB,IAAI,kBAC1BA,KACA;IACE,IAAI,wBAAuB;IAC3B,IAAI,wBAAuB;IAC3B,IAAI,qBAAoB;IACxB,IAAI,4BAA2B;IAC/B,IAAI,qBAAoB;KAE1BF,uBAAsBF,YAAW;AAErC,QAAM,oBAAoB,IAAI,WAC1BI,KACA;IACE,IAAI,6BAA6BA,KAAI,EAAC,oBAAAH,oBAAkB,CAAC;IACzD,IAAI,wBAAwBG,GAAE;KAEhCJ,YAAW;AAGf,QAAM,6BAA6BH,sBAAqB,IACpD,eACI,MAAM,QAAQ,SAAS,IAAI,UAAU,IAAI,OAAKO,IAAG,QAAQ,CAAC,CAAC,IAAI,CAACA,IAAG,QAAQ,SAAS,CAAC,CAAC;AAE9F,QAAM,eACF,kBAAkB,YAAY,4BAA4BN,uBAAsB;AACpF,EAAAH,kBAAiBS,IAAG,QAAQT,eAAc;AAC1C,oBAAkB,eACdC,iBAAgB,IAAI,YAAY,GAAGQ,IAAG,QAAQT,eAAc,GAAGI,eAAc,cAC7EI,aAAY;AAClB;;;AF/GA,QAAQ,QAAQ;AAChB,IAAM,OAAO,QAAQ,KAAK,MAAM,CAAC;AACjC,IAAM,UACF,MAAM,IAAI,EACL,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,UACI;EACJ,MAAM;CACP,EACA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,UACI;EACJ,MAAM;CACP,EAEA,OAAO,KAAK;EACX,OAAO;EACP,UACI;EACJ,MAAM;CACP,EAEA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,OAAO;EACP,UACI;EAKJ,MAAM;CACP,EAEA,OAAO,kBAAkB;EACxB,OAAO;EACP,UACI;EAEJ,MAAM;CACP,EAEA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,UAAU;EAGV,MAAM;CACP,EAEA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,SAAS,CAAC,SAAS,WAAW,QAAQ;EACtC,SAAS;EACT,MAAM;CACP,EAEA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,SAAS,CAAC,SAAS,WAAW,QAAQ;EACtC,SAAS;EACT,MAAM;CACP,EAEA,OAAM,EACN,KAAI,EACJ,UAAS;AAElB,IAAM,KAAK,IAAI,iBAAgB;AAC/B,cAAc,EAAE;AAEhB,IAAM,iBAAiB,QAAQ;AAC/B,IAAM,kBAAkB,KAAK,KAAK,QAAQ,GAAG,EAAC,KAAK,gBAAgB,WAAW,KAAI,CAAC;AACnF,IAAM,uBAA4C,sBAAsB,QAAQ,CAAC;AACjF,IAAM,eAAe,gBAAgB,IAAI,GAAG,QAAQ,QAAQ,CAAC,CAAC;AAC9D,IAAM,cAAc,IAAI,YAAW;AACnC,IAAM,qBAAqB,QAAQ;AACnC,IAAM,uBAAuB,QAAQ;AACrC,IAAM,eAAiC,QAAQ;AAC/C,IAAM,yBAAmC,QAAQ,qBAAqB,CAAA;AAEtE,eAAe;EACb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAED,YAAY,SAAS,QAAQ,OAAK,QAAQ,KAAK,GAAG,EAAE,SAAS,EAAE,SAAS,CAAC;AACzE,QAAQ,KAAK,YAAY,YAAY,IAAI,CAAC;AAO1C,SAAS,sBAAsBE,OAAc;AAC3C,SAAOA,MAAK,IACR,SAAQ,IAAI,WAAW,GAAG,KAAK,IAAI,SAAS,GAAG,IAC3C,IAAI,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,IAAI,CAAAC,SAAOA,KAAI,KAAI,CAAE,IACjD,GAAG;AACb;", "names": ["fs", "fs", "diagnostics", "outputPathFn", "sourceLocale", "absoluteFrom", "fs", "diagnostics", "outputPathFn", "sourceLocale", "options", "absoluteFrom", "fs", "duplicateTranslation", "diagnostics", "translationFilePaths", "translationFileLocales", "fs", "diagnostics", "outputPathFn", "sourceLocale", "sourceRootPath", "sourceFilePaths", "translationFilePaths", "translationFileLocales", "outputPathFn", "diagnostics", "missingTranslation", "duplicateTranslation", "sourceLocale", "fs", "args", "arg"]}