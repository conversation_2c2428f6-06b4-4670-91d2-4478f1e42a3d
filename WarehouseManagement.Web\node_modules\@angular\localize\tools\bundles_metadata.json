{"inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs": {"bytes": 5429, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs": {"bytes": 54835, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.mjs": {"bytes": 7106, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es2015_extract_plugin.mjs": {"bytes": 4660, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es5_extract_plugin.mjs": {"bytes": 6565, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.mjs": {"bytes": 16729, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es2015_extract_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es5_extract_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.mjs": {"bytes": 8536, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.mjs": {"bytes": 11001, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.mjs": {"bytes": 3386, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.mjs": {"bytes": 5407, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.mjs": {"bytes": 5262, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.mjs": {"bytes": 20492, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.mjs": {"bytes": 9487, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.mjs": {"bytes": 28454, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.mjs": {"bytes": 28772, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.mjs": {"bytes": 15600, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.mjs": {"bytes": 6718, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.mjs": {"bytes": 6277, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.mjs": {"bytes": 12140, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.mjs": {"bytes": 8451, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.mjs": {"bytes": 9614, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.mjs": {"bytes": 2779, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.mjs": {"bytes": 3519, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.mjs": {"bytes": 18610, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/message_serializer.mjs": {"bytes": 12375, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/target_message_renderer.mjs": {"bytes": 6804, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.mjs": {"bytes": 3708, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/message_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/target_message_renderer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.mjs": {"bytes": 17429, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.mjs": {"bytes": 17751, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.mjs": {"bytes": 13448, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/index.mjs": {"bytes": 7332, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/index.mjs": {"bytes": 12840, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/cli.mjs": {"bytes": 13072, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/migrate.mjs": {"bytes": 2976, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/index.mjs": {"bytes": 4927, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/migrate.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/cli.mjs": {"bytes": 5601, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/output_path.mjs": {"bytes": 2977, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/asset_files/asset_translation_handler.mjs": {"bytes": 5539, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/source_file_translation_handler.mjs": {"bytes": 15353, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_loader.mjs": {"bytes": 18143, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translator.mjs": {"bytes": 7685, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/index.mjs": {"bytes": 11639, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/asset_files/asset_translation_handler.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/source_file_translation_handler.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_loader.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translator.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/cli.mjs": {"bytes": 15374, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/output_path.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/index.mjs", "kind": "import-statement"}]}}, "outputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 212}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-QB7OQXUU.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-HTD7ZW6Y.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AEXKEUHQ.js", "kind": "import-statement"}], "exports": ["ArbTranslationParser", "ArbTranslationSerializer", "Diagnostics", "LegacyMessageIdMigrationSerializer", "MessageExtractor", "SimpleJsonTranslationParser", "SimpleJsonTranslationSerializer", "Xliff1TranslationParser", "Xliff1TranslationSerializer", "Xliff2TranslationParser", "Xliff2TranslationSerializer", "XmbTranslationSerializer", "XtbTranslationParser", "buildLocalizeReplacement", "checkDuplicateMessages", "isGlobalIdentifier", "makeEs2015TranslatePlugin", "makeEs5TranslatePlugin", "makeLocalePlugin", "translate", "unwrapExpressionsFromTemplateLiteral", "unwrapMessagePartsFromLocalizeCall", "unwrapMessagePartsFromTemplateLiteral", "unwrapSubstitutionsFromLocalizeCall"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/index.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/index.mjs": {"bytesInOutput": 129}}, "bytes": 2058}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/extract/cli.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 2794}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/extract/cli.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-QB7OQXUU.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AEXKEUHQ.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/cli.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/cli.mjs": {"bytesInOutput": 3045}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/index.mjs": {"bytesInOutput": 2103}}, "bytes": 6125}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-QB7OQXUU.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 19695}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-QB7OQXUU.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AEXKEUHQ.js", "kind": "import-statement"}], "exports": ["ArbTranslationSerializer", "LegacyMessageIdMigrationSerializer", "MessageExtractor", "SimpleJsonTranslationSerializer", "Xliff1TranslationSerializer", "Xliff2TranslationSerializer", "XmbTranslationSerializer", "checkDuplicateMessages", "parseFormatOptions"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.mjs": {"bytesInOutput": 1244}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.mjs": {"bytesInOutput": 3116}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es2015_extract_plugin.mjs": {"bytesInOutput": 836}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es5_extract_plugin.mjs": {"bytesInOutput": 1142}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.mjs": {"bytesInOutput": 1180}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.mjs": {"bytesInOutput": 1945}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.mjs": {"bytesInOutput": 412}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.mjs": {"bytesInOutput": 925}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.mjs": {"bytesInOutput": 760}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.mjs": {"bytesInOutput": 5340}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.mjs": {"bytesInOutput": 2268}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.mjs": {"bytesInOutput": 1984}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.mjs": {"bytesInOutput": 5567}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.mjs": {"bytesInOutput": 2772}}, "bytes": 32327}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/migrate/cli.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 1649}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/migrate/cli.js": {"imports": [], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/cli.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/cli.mjs": {"bytesInOutput": 1163}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/index.mjs": {"bytesInOutput": 829}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/migrate.mjs": {"bytesInOutput": 405}}, "bytes": 3215}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/translate/cli.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8129}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/translate/cli.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-HTD7ZW6Y.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AEXKEUHQ.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/cli.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/cli.mjs": {"bytesInOutput": 3511}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/output_path.mjs": {"bytesInOutput": 259}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/index.mjs": {"bytesInOutput": 1423}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/asset_files/asset_translation_handler.mjs": {"bytesInOutput": 978}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/source_file_translation_handler.mjs": {"bytesInOutput": 3064}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_loader.mjs": {"bytesInOutput": 3415}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translator.mjs": {"bytesInOutput": 884}}, "bytes": 15136}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-HTD7ZW6Y.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 16689}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-HTD7ZW6Y.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AEXKEUHQ.js", "kind": "import-statement"}], "exports": ["ArbTranslationParser", "SimpleJsonTranslationParser", "Xliff1TranslationParser", "Xliff2TranslationParser", "XtbTranslationParser", "makeEs2015TranslatePlugin", "makeEs5TranslatePlugin", "makeLocalePlugin"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.mjs": {"bytesInOutput": 945}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.mjs": {"bytesInOutput": 1040}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.mjs": {"bytesInOutput": 1717}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.mjs": {"bytesInOutput": 1143}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.mjs": {"bytesInOutput": 1819}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.mjs": {"bytesInOutput": 3504}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.mjs": {"bytesInOutput": 376}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/message_serializer.mjs": {"bytesInOutput": 2396}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.mjs": {"bytesInOutput": 558}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.mjs": {"bytesInOutput": 3101}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/target_message_renderer.mjs": {"bytesInOutput": 1284}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.mjs": {"bytesInOutput": 428}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.mjs": {"bytesInOutput": 3551}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.mjs": {"bytesInOutput": 2481}}, "bytes": 27417}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AEXKEUHQ.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 7162}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AEXKEUHQ.js": {"imports": [], "exports": ["Diagnostics", "buildCodeFrameError", "buildLocalizeReplacement", "getLocation", "isBabelParseError", "isGlobalIdentifier", "isLocalize", "isNamedIdentifier", "serializeLocationPosition", "translate", "unwrapExpressionsFromTemplateLiteral", "unwrapMessagePartsFromLocalizeCall", "unwrapMessagePartsFromTemplateLiteral", "unwrapSubstitutionsFromLocalizeCall"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.mjs": {"bytesInOutput": 906}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.mjs": {"bytesInOutput": 10238}}, "bytes": 12067}}}