<div class="unauthorized-container" [class.rtl]="languageService.isArabic()">
  <div class="unauthorized-content">
    <!-- Error Icon -->
    <div class="error-icon">
      <i class="pi pi-shield text-6xl text-red-500"></i>
    </div>

    <!-- Error Message -->
    <div class="error-message">
      <h1 class="error-title">{{ languageService.translate('auth.unauthorized.title') }}</h1>
      <p class="error-description">
        {{ languageService.translate('auth.unauthorized.description') }}
      </p>
    </div>

    <!-- Error Details -->
    <div class="error-details">
      <p-message
        severity="warn"
        [text]="languageService.translate('auth.unauthorized.details')">
      </p-message>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button
        pButton
        type="button"
        [label]="languageService.translate('auth.unauthorized.goToDashboard')"
        icon="pi pi-home"
        class="p-button-primary"
        (click)="goToDashboard()">
      </button>

      <button
        pButton
        type="button"
        [label]="languageService.translate('auth.unauthorized.goBack')"
        icon="pi pi-arrow-left"
        class="p-button-secondary"
        (click)="goBack()">
      </button>

      <button
        pButton
        type="button"
        [label]="languageService.translate('auth.unauthorized.contactSupport')"
        icon="pi pi-envelope"
        class="p-button-help"
        (click)="contactSupport()">
      </button>

      <button
        pButton
        type="button"
        [label]="languageService.translate('auth.unauthorized.logout')"
        icon="pi pi-sign-out"
        class="p-button-danger"
        (click)="logout()">
      </button>
    </div>

    <!-- Auto Redirect Notice -->
    <div class="auto-redirect-notice">
      <p class="redirect-text">
        {{ languageService.translate('auth.unauthorized.autoRedirect') }}
      </p>
    </div>
  </div>
</div>
