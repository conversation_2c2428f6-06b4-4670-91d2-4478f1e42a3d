{"ast": null, "code": "import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus {\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus = false;\n  focused = false;\n  platformId = inject(PLATFORM_ID);\n  document = inject(DOCUMENT);\n  host = inject(ElementRef);\n  ngAfterContentChecked() {\n    // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n    if (this.autofocus === false) {\n      this.host.nativeElement.removeAttribute('autofocus');\n    } else {\n      this.host.nativeElement.setAttribute('autofocus', true);\n    }\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  ngAfterViewChecked() {\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  autoFocus() {\n    if (isPlatformBrowser(this.platformId) && this.autofocus) {\n      setTimeout(() => {\n        const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n        if (focusableElements.length === 0) {\n          this.host.nativeElement.focus();\n        }\n        if (focusableElements.length > 0) {\n          focusableElements[0].focus();\n        }\n        this.focused = true;\n      });\n    }\n  }\n  static ɵfac = function AutoFocus_Factory(t) {\n    return new (t || AutoFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AutoFocus,\n    selectors: [[\"\", \"pAutoFocus\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute]\n    },\n    standalone: true,\n    features: [i0.ɵɵInputTransformsFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[pAutoFocus]',\n      standalone: true,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], null, {\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass AutoFocusModule {\n  static ɵfac = function AutoFocusModule_Factory(t) {\n    return new (t || AutoFocusModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AutoFocusModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocusModule, [{\n    type: NgModule,\n    args: [{\n      imports: [AutoFocus],\n      exports: [AutoFocus]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };", "map": {"version": 3, "names": ["DOCUMENT", "isPlatformBrowser", "i0", "inject", "PLATFORM_ID", "ElementRef", "booleanAttribute", "Directive", "Input", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "AutoFocus", "autofocus", "focused", "platformId", "document", "host", "ngAfterContentChecked", "nativeElement", "removeAttribute", "setAttribute", "autoFocus", "ngAfterViewChecked", "setTimeout", "focusableElements", "getFocusableElements", "length", "focus", "ɵfac", "AutoFocus_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "standalone", "features", "ɵɵInputTransformsFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "class", "transform", "AutoFocusModule", "AutoFocusModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-autofocus.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus {\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus = false;\n    focused = false;\n    platformId = inject(PLATFORM_ID);\n    document = inject(DOCUMENT);\n    host = inject(ElementRef);\n    ngAfterContentChecked() {\n        // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n        if (this.autofocus === false) {\n            this.host.nativeElement.removeAttribute('autofocus');\n        }\n        else {\n            this.host.nativeElement.setAttribute('autofocus', true);\n        }\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n    ngAfterViewChecked() {\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n    autoFocus() {\n        if (isPlatformBrowser(this.platformId) && this.autofocus) {\n            setTimeout(() => {\n                const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n                if (focusableElements.length === 0) {\n                    this.host.nativeElement.focus();\n                }\n                if (focusableElements.length > 0) {\n                    focusableElements[0].focus();\n                }\n                this.focused = true;\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: AutoFocus, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.0.1\", type: AutoFocus, isStandalone: true, selector: \"[pAutoFocus]\", inputs: { autofocus: [\"autofocus\", \"autofocus\", booleanAttribute] }, host: { classAttribute: \"p-element\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: AutoFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pAutoFocus]',\n                    standalone: true,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], propDecorators: { autofocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass AutoFocusModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: AutoFocusModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: AutoFocusModule, imports: [AutoFocus], exports: [AutoFocus] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: AutoFocusModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: AutoFocusModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [AutoFocus],\n                    exports: [AutoFocus]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,iBAAiB,QAAQ,iBAAiB;AAC7D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC7G,SAASC,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ;AACJ;AACA;AACA;EACIC,SAAS,GAAG,KAAK;EACjBC,OAAO,GAAG,KAAK;EACfC,UAAU,GAAGX,MAAM,CAACC,WAAW,CAAC;EAChCW,QAAQ,GAAGZ,MAAM,CAACH,QAAQ,CAAC;EAC3BgB,IAAI,GAAGb,MAAM,CAACE,UAAU,CAAC;EACzBY,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACL,SAAS,KAAK,KAAK,EAAE;MAC1B,IAAI,CAACI,IAAI,CAACE,aAAa,CAACC,eAAe,CAAC,WAAW,CAAC;IACxD,CAAC,MACI;MACD,IAAI,CAACH,IAAI,CAACE,aAAa,CAACE,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;IAC3D;IACA,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE;MACf,IAAI,CAACQ,SAAS,CAAC,CAAC;IACpB;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACT,OAAO,EAAE;MACf,IAAI,CAACQ,SAAS,CAAC,CAAC;IACpB;EACJ;EACAA,SAASA,CAAA,EAAG;IACR,IAAIpB,iBAAiB,CAAC,IAAI,CAACa,UAAU,CAAC,IAAI,IAAI,CAACF,SAAS,EAAE;MACtDW,UAAU,CAAC,MAAM;QACb,MAAMC,iBAAiB,GAAGd,UAAU,CAACe,oBAAoB,CAAC,IAAI,CAACT,IAAI,EAAEE,aAAa,CAAC;QACnF,IAAIM,iBAAiB,CAACE,MAAM,KAAK,CAAC,EAAE;UAChC,IAAI,CAACV,IAAI,CAACE,aAAa,CAACS,KAAK,CAAC,CAAC;QACnC;QACA,IAAIH,iBAAiB,CAACE,MAAM,GAAG,CAAC,EAAE;UAC9BF,iBAAiB,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC;QAChC;QACA,IAAI,CAACd,OAAO,GAAG,IAAI;MACvB,CAAC,CAAC;IACN;EACJ;EACA,OAAOe,IAAI,YAAAC,kBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFnB,SAAS;EAAA;EAC5G,OAAOoB,IAAI,kBAD8E7B,EAAE,CAAA8B,iBAAA;IAAAC,IAAA,EACJtB,SAAS;IAAAuB,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAxB,SAAA,GADPV,EAAE,CAAAmC,YAAA,CAAAC,0BAAA,4BACqGhC,gBAAgB;IAAA;IAAAiC,UAAA;IAAAC,QAAA,GADvHtC,EAAE,CAAAuC,wBAAA;EAAA;AAE/F;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FxC,EAAE,CAAAyC,iBAAA,CAGJhC,SAAS,EAAc,CAAC;IACvGsB,IAAI,EAAE1B,SAAS;IACfqC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBN,UAAU,EAAE,IAAI;MAChBvB,IAAI,EAAE;QACF8B,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElC,SAAS,EAAE,CAAC;MAC1BqB,IAAI,EAAEzB,KAAK;MACXoC,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAEzC;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM0C,eAAe,CAAC;EAClB,OAAOpB,IAAI,YAAAqB,wBAAAnB,CAAA;IAAA,YAAAA,CAAA,IAAwFkB,eAAe;EAAA;EAClH,OAAOE,IAAI,kBAlB8EhD,EAAE,CAAAiD,gBAAA;IAAAlB,IAAA,EAkBSe;EAAe;EACnH,OAAOI,IAAI,kBAnB8ElD,EAAE,CAAAmD,gBAAA;AAoB/F;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KArB6FxC,EAAE,CAAAyC,iBAAA,CAqBJK,eAAe,EAAc,CAAC;IAC7Gf,IAAI,EAAExB,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCU,OAAO,EAAE,CAAC3C,SAAS,CAAC;MACpB4C,OAAO,EAAE,CAAC5C,SAAS;IACvB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,SAAS,EAAEqC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}