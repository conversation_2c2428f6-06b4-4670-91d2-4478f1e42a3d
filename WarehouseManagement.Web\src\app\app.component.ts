import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { PrimeNGConfig } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MessageService, ConfirmationService } from 'primeng/api';

import { LayoutComponent } from './shared/components/layout/layout.component';
import { LanguageService } from '@core/services/language.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    ToastModule,
    ConfirmDialogModule,
    LayoutComponent
  ],
  providers: [
    MessageService,
    ConfirmationService
  ],
  template: `
    <app-layout>
      <router-outlet></router-outlet>
    </app-layout>
    <p-toast></p-toast>
    <p-confirmDialog></p-confirmDialog>
  `,
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'Warehouse Management System';

  constructor(
    private primengConfig: PrimeNGConfig,
    private languageService: LanguageService
  ) {}

  ngOnInit() {
    this.primengConfig.ripple = true;

    // Initialize language service and subscribe to language changes
    this.languageService.currentLanguage$.subscribe(language => {
      // Update PrimeNG configuration for RTL
      this.primengConfig.ripple = true;

      // Set document title based on language
      document.title = this.languageService.translate('app.title') || this.title;
    });
  }
}
