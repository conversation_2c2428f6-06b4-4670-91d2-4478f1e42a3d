# Warehouse Management System - Authentication & Security Guide

## Overview

This guide provides comprehensive documentation for the authentication and security system implemented in the Warehouse Management System. The system includes JWT-based authentication, role-based access control (RBAC), security features, and bilingual support.

## Table of Contents

1. [Authentication System](#authentication-system)
2. [Authorization System](#authorization-system)
3. [Security Features](#security-features)
4. [Usage Examples](#usage-examples)
5. [Configuration](#configuration)
6. [Best Practices](#best-practices)

## Authentication System

### Features

- **JWT Token Authentication** with httpOnly cookies
- **Refresh Token Mechanism** for seamless token renewal
- **Session Management** with configurable timeouts
- **Login Rate Limiting** to prevent brute force attacks
- **Account Lockout** after failed attempts
- **Bilingual Support** (Arabic/English)

### Core Services

#### AuthService

The main authentication service handles:
- User login/logout
- Token refresh
- Session validation
- Authentication state management

```typescript
// Login
this.authService.login({ username, password, rememberMe }).subscribe({
  next: (response) => console.log('Login successful'),
  error: (error) => console.error('Login failed', error)
});

// Check authentication status
this.authService.isAuthenticated(); // boolean

// Get current user
const user = this.authService.getCurrentUserSync();

// Logout
this.authService.logout().subscribe();
```

### Login Component

Features:
- Responsive design with RTL support
- Form validation with real-time feedback
- Rate limiting with visual feedback
- Account lockout handling
- Security notifications

## Authorization System

### Role-Based Access Control (RBAC)

#### User Roles

1. **Admin** - Full system access
2. **Manager** - Management operations
3. **Employee** - Day-to-day operations
4. **Viewer** - Read-only access

#### Permissions

Granular permissions for each resource:
- **CREATE** - Add new records
- **READ** - View existing records
- **UPDATE** - Modify existing records
- **DELETE** - Remove records
- **MANAGE** - Full control over resource

#### Resources

- Dashboard
- Inventory
- Products
- Categories
- Suppliers
- Customers
- Orders
- Reports
- Users
- Settings
- Audit Logs

### AuthorizationService

```typescript
// Check permission
this.authorizationService.hasPermission('products', 'create'); // boolean

// Check role
this.authorizationService.hasRole('admin'); // boolean

// Check multiple permissions
this.authorizationService.hasAllPermissions([
  { resource: 'products', action: 'read' },
  { resource: 'categories', action: 'read' }
]); // boolean
```

## Security Features

### Rate Limiting

- **Login attempts**: 5 attempts per 15 minutes
- **API requests**: 100 requests per minute
- **Password reset**: 3 attempts per hour

### CSRF Protection

- Automatic CSRF token handling
- Token validation on state-changing requests
- Session-based token storage

### XSS Protection

- Input sanitization
- Content Security Policy (CSP)
- Angular's built-in XSS protection

### Security Headers

- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

## Usage Examples

### Route Protection

```typescript
// Require authentication
{
  path: 'dashboard',
  component: DashboardComponent,
  canActivate: [AuthGuard]
}

// Require specific role
{
  path: 'admin',
  component: AdminComponent,
  canActivate: [AuthGuard, RoleGuard],
  data: { roles: ['admin'] }
}

// Require specific permission
{
  path: 'products',
  component: ProductsComponent,
  canActivate: [AuthGuard, PermissionGuard],
  data: { 
    resource: 'products', 
    action: 'read' 
  }
}
```

### Template Directives

```html
<!-- Show content only to authenticated users -->
<div *appIsAuthenticated="true">
  Welcome back!
</div>

<!-- Show content based on role -->
<div *appHasRole="'admin'">
  Admin panel
</div>

<!-- Show content based on permission -->
<button *appHasPermission="'products:create'">
  Add Product
</button>

<!-- Multiple permissions (AND logic) -->
<div *appHasPermission="[
  {resource: 'products', action: 'read'},
  {resource: 'categories', action: 'read'}
]" [appHasPermissionRequireAll]="true">
  Product management
</div>
```

### Component Usage

```typescript
export class ProductComponent implements OnInit {
  canEdit = false;
  canDelete = false;

  constructor(
    private authService: AuthService,
    private authorizationService: AuthorizationService
  ) {}

  ngOnInit() {
    this.canEdit = this.authorizationService.hasPermission('products', 'update');
    this.canDelete = this.authorizationService.hasPermission('products', 'delete');
  }

  onEdit() {
    if (!this.canEdit) {
      // Handle unauthorized access
      return;
    }
    // Proceed with edit
  }
}
```

## Configuration

### Environment Settings

```typescript
// environment.ts
export const environment = {
  security: {
    enableCSRF: true,
    enableRateLimiting: true,
    sessionTimeout: 15, // minutes
    maxLoginAttempts: 5,
    lockoutDuration: 15, // minutes
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireNumbers: true,
      requireSpecialChars: true
    }
  }
};
```

### Module Setup

```typescript
// app.module.ts
import { CoreModule } from './core/core.module';
import { AuthModule } from './features/auth/auth.module';

@NgModule({
  imports: [
    CoreModule, // Import once in AppModule
    AuthModule
  ]
})
export class AppModule { }
```

## Best Practices

### Security

1. **Always validate permissions** on both client and server
2. **Use HTTPS** in production
3. **Implement proper session management**
4. **Regular security audits**
5. **Keep dependencies updated**

### Performance

1. **Lazy load modules** based on permissions
2. **Cache permission checks** when appropriate
3. **Use OnPush change detection** for auth components
4. **Minimize API calls** for permission checks

### User Experience

1. **Provide clear feedback** for authentication errors
2. **Implement progressive enhancement** based on permissions
3. **Support both RTL and LTR** layouts
4. **Graceful degradation** for unauthorized access

### Development

1. **Use TypeScript interfaces** for type safety
2. **Implement comprehensive error handling**
3. **Write unit tests** for auth logic
4. **Document permission requirements** for each feature

## Troubleshooting

### Common Issues

1. **Token expiration**: Implement automatic refresh
2. **CORS issues**: Configure backend properly
3. **Permission denied**: Check role assignments
4. **Rate limiting**: Implement exponential backoff

### Debugging

1. Enable authentication tracing in development
2. Use browser dev tools to inspect tokens
3. Check network requests for auth headers
4. Monitor console for security events

## API Integration

### Backend Requirements

The frontend expects the following API endpoints:

```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/me
GET  /api/auth/validate
POST /api/security/events
GET  /api/security/config
POST /api/security/login-attempts
GET  /api/security/csrf-token
```

### Response Format

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}
```

## Migration Guide

If upgrading from a previous version:

1. Update environment configuration
2. Import CoreModule in AppModule
3. Update route guards
4. Replace old auth directives
5. Test all protected routes

## Support

For issues or questions:
- Check the troubleshooting section
- Review the examples
- Contact the development team
