import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ButtonModule } from 'primeng/button';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DashboardService } from './dashboard.service';
import { DashboardData, InventorySummary, RecentTransaction, LowStockItem } from './dashboard.model';
import { LanguageService } from '@core/services/language.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ChartModule,
    TableModule,
    TagModule,
    ButtonModule,
    ProgressBarModule,
    ProgressSpinnerModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  dashboardData: DashboardData | null = null;
  loading = true;

  // Chart data
  salesChartData: any;
  salesChartOptions: any;
  inventoryChartData: any;
  inventoryChartOptions: any;
  revenueChartData: any;
  revenueChartOptions: any;

  constructor(
    private dashboardService: DashboardService,
    public languageService: LanguageService
  ) {}

  ngOnInit() {
    this.loadDashboardData();
    this.initializeChartOptions();
  }

  loadDashboardData() {
    this.loading = true;
    this.dashboardService.getDashboardData().subscribe({
      next: (data) => {
        this.dashboardData = data;
        this.setupCharts();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard data:', error);
        this.loading = false;
      }
    });
  }

  private initializeChartOptions() {
    const documentStyle = getComputedStyle(document.documentElement);
    const textColor = documentStyle.getPropertyValue('--text-color');
    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

    this.salesChartOptions = {
      maintainAspectRatio: false,
      aspectRatio: 0.8,
      plugins: {
        legend: {
          labels: {
            color: textColor
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: textColorSecondary,
            font: {
              weight: 500
            }
          },
          grid: {
            color: surfaceBorder,
            drawBorder: false
          }
        },
        y: {
          ticks: {
            color: textColorSecondary
          },
          grid: {
            color: surfaceBorder,
            drawBorder: false
          }
        }
      }
    };

    this.inventoryChartOptions = {
      plugins: {
        legend: {
          labels: {
            usePointStyle: true,
            color: textColor
          }
        }
      }
    };

    this.revenueChartOptions = {
      maintainAspectRatio: false,
      aspectRatio: 0.6,
      plugins: {
        legend: {
          labels: {
            color: textColor
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: textColorSecondary
          },
          grid: {
            color: surfaceBorder
          }
        },
        y: {
          ticks: {
            color: textColorSecondary
          },
          grid: {
            color: surfaceBorder
          }
        }
      }
    };
  }

  private setupCharts() {
    if (!this.dashboardData) return;

    // Sales Chart
    this.salesChartData = {
      labels: this.dashboardData.salesTrend.map(s => s.period),
      datasets: [
        {
          label: 'Sales',
          data: this.dashboardData.salesTrend.map(s => s.amount),
          fill: false,
          backgroundColor: '#3b82f6',
          borderColor: '#3b82f6',
          tension: 0.4
        }
      ]
    };

    // Inventory Chart
    this.inventoryChartData = {
      labels: ['Raw Materials', 'Semi-Finished', 'Finished Products', 'Consumables'],
      datasets: [
        {
          data: [
            this.dashboardData.inventorySummary.rawMaterials,
            this.dashboardData.inventorySummary.semiFinished,
            this.dashboardData.inventorySummary.finishedProducts,
            this.dashboardData.inventorySummary.consumables
          ],
          backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
          hoverBackgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626']
        }
      ]
    };

    // Revenue Chart
    this.revenueChartData = {
      labels: this.dashboardData.monthlyRevenue.map(r => r.month),
      datasets: [
        {
          type: 'line',
          label: 'Revenue',
          borderColor: '#3b82f6',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          data: this.dashboardData.monthlyRevenue.map(r => r.revenue)
        },
        {
          type: 'bar',
          label: 'Profit',
          backgroundColor: '#10b981',
          data: this.dashboardData.monthlyRevenue.map(r => r.profit)
        }
      ]
    };
  }

  getStockLevelSeverity(percentage: number): string {
    if (percentage <= 20) return 'danger';
    if (percentage <= 50) return 'warning';
    return 'success';
  }

  getTransactionTypeSeverity(type: string): "success" | "secondary" | "info" | "warning" | "danger" | "contrast" {
    switch (type.toLowerCase()) {
      case 'sale': return 'success';
      case 'purchase': return 'info';
      case 'return': return 'warning';
      case 'adjustment': return 'secondary';
      default: return 'info';
    }
  }

  refreshDashboard() {
    this.loadDashboardData();
  }
}
