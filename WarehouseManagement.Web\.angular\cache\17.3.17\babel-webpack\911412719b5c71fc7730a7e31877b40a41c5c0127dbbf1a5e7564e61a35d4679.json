{"ast": null, "code": "import { argsOrArgArray } from '../util/argsOrArgArray';\nimport { onErrorResumeNext as oERNCreate } from '../observable/onErrorResumeNext';\nexport function onErrorResumeNextWith(...sources) {\n  const nextSources = argsOrArgArray(sources);\n  return source => oERNCreate(source, ...nextSources);\n}\nexport const onErrorResumeNext = onErrorResumeNextWith;", "map": {"version": 3, "names": ["argsOrArgArray", "onErrorResumeNext", "oERNCreate", "onErrorResumeNextWith", "sources", "nextSources", "source"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/rxjs/dist/esm/internal/operators/onErrorResumeNextWith.js"], "sourcesContent": ["import { argsOrArgArray } from '../util/argsOrArgArray';\nimport { onErrorResumeNext as oERNCreate } from '../observable/onErrorResumeNext';\nexport function onErrorResumeNextWith(...sources) {\n    const nextSources = argsOrArgArray(sources);\n    return (source) => oERNCreate(source, ...nextSources);\n}\nexport const onErrorResumeNext = onErrorResumeNextWith;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,iBAAiB,IAAIC,UAAU,QAAQ,iCAAiC;AACjF,OAAO,SAASC,qBAAqBA,CAAC,GAAGC,OAAO,EAAE;EAC9C,MAAMC,WAAW,GAAGL,cAAc,CAACI,OAAO,CAAC;EAC3C,OAAQE,MAAM,IAAKJ,UAAU,CAACI,MAAM,EAAE,GAAGD,WAAW,CAAC;AACzD;AACA,OAAO,MAAMJ,iBAAiB,GAAGE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}