using Microsoft.AspNetCore.Cryptography.KeyDerivation;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using WarehouseManagement.Core.Interfaces;

namespace WarehouseManagement.Application.Services;

public class PasswordService : IPasswordService
{
    private const int SaltSize = 128 / 8; // 128 bits
    private const int HashSize = 256 / 8; // 256 bits
    private const int Iterations = 100000; // PBKDF2 iterations

    private static readonly string[] CommonPasswords = {
        "password", "123456", "123456789", "12345678", "12345", "1234567", "1234567890",
        "qwerty", "abc123", "111111", "123123", "admin", "letmein", "welcome", "monkey",
        "password123", "admin123", "root", "user", "test", "guest", "demo", "sample"
    };

    public string HashPassword(string password, out string salt)
    {
        // Generate a random salt
        var saltBytes = new byte[SaltSize];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(saltBytes);
        }
        salt = Convert.ToBase64String(saltBytes);

        // Hash the password with the salt
        var hash = KeyDerivation.Pbkdf2(
            password: password,
            salt: saltBytes,
            prf: KeyDerivationPrf.HMACSHA256,
            iterationCount: Iterations,
            numBytesRequested: HashSize);

        return Convert.ToBase64String(hash);
    }

    public bool VerifyPassword(string password, string hash, string salt)
    {
        try
        {
            var saltBytes = Convert.FromBase64String(salt);
            var hashBytes = Convert.FromBase64String(hash);

            // Hash the provided password with the stored salt
            var computedHash = KeyDerivation.Pbkdf2(
                password: password,
                salt: saltBytes,
                prf: KeyDerivationPrf.HMACSHA256,
                iterationCount: Iterations,
                numBytesRequested: HashSize);

            // Compare the computed hash with the stored hash
            return CryptographicOperations.FixedTimeEquals(computedHash, hashBytes);
        }
        catch
        {
            return false;
        }
    }

    public bool ValidatePasswordStrength(string password, out List<string> errors)
    {
        errors = new List<string>();

        if (string.IsNullOrWhiteSpace(password))
        {
            errors.Add("Password is required");
            return false;
        }

        // Check minimum length
        if (password.Length < 8)
        {
            errors.Add("Password must be at least 8 characters long");
        }

        // Check maximum length (prevent DoS attacks)
        if (password.Length > 128)
        {
            errors.Add("Password must be less than 128 characters long");
        }

        // Check for uppercase letter
        if (!password.Any(char.IsUpper))
        {
            errors.Add("Password must contain at least one uppercase letter");
        }

        // Check for lowercase letter
        if (!password.Any(char.IsLower))
        {
            errors.Add("Password must contain at least one lowercase letter");
        }

        // Check for digit
        if (!password.Any(char.IsDigit))
        {
            errors.Add("Password must contain at least one number");
        }

        // Check for special character
        if (!password.Any(ch => !char.IsLetterOrDigit(ch)))
        {
            errors.Add("Password must contain at least one special character");
        }

        // Check for common passwords
        if (CommonPasswords.Contains(password.ToLower()))
        {
            errors.Add("Password is too common and easily guessable");
        }

        // Check for sequential characters
        if (HasSequentialCharacters(password))
        {
            errors.Add("Password should not contain sequential characters (e.g., 123, abc)");
        }

        // Check for repeated characters
        if (HasRepeatedCharacters(password))
        {
            errors.Add("Password should not contain too many repeated characters");
        }

        // Check for keyboard patterns
        if (HasKeyboardPattern(password))
        {
            errors.Add("Password should not contain keyboard patterns (e.g., qwerty, asdf)");
        }

        return errors.Count == 0;
    }

    public string GenerateRandomPassword(int length = 12)
    {
        if (length < 8) length = 8;
        if (length > 128) length = 128;

        const string lowercase = "abcdefghijklmnopqrstuvwxyz";
        const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string digits = "0123456789";
        const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        var password = new StringBuilder();
        using var rng = RandomNumberGenerator.Create();

        // Ensure at least one character from each category
        password.Append(GetRandomCharacter(lowercase, rng));
        password.Append(GetRandomCharacter(uppercase, rng));
        password.Append(GetRandomCharacter(digits, rng));
        password.Append(GetRandomCharacter(specialChars, rng));

        // Fill the rest with random characters from all categories
        var allChars = lowercase + uppercase + digits + specialChars;
        for (int i = 4; i < length; i++)
        {
            password.Append(GetRandomCharacter(allChars, rng));
        }

        // Shuffle the password to avoid predictable patterns
        return ShuffleString(password.ToString(), rng);
    }

    public string GeneratePasswordResetToken()
    {
        var tokenBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(tokenBytes);
        return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    private static char GetRandomCharacter(string chars, RandomNumberGenerator rng)
    {
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var value = BitConverter.ToUInt32(bytes, 0);
        return chars[(int)(value % (uint)chars.Length)];
    }

    private static string ShuffleString(string input, RandomNumberGenerator rng)
    {
        var array = input.ToCharArray();
        for (int i = array.Length - 1; i > 0; i--)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var j = (int)(BitConverter.ToUInt32(bytes, 0) % (uint)(i + 1));
            (array[i], array[j]) = (array[j], array[i]);
        }
        return new string(array);
    }

    private static bool HasSequentialCharacters(string password)
    {
        for (int i = 0; i < password.Length - 2; i++)
        {
            var char1 = password[i];
            var char2 = password[i + 1];
            var char3 = password[i + 2];

            // Check for ascending sequence
            if (char2 == char1 + 1 && char3 == char2 + 1)
                return true;

            // Check for descending sequence
            if (char2 == char1 - 1 && char3 == char2 - 1)
                return true;
        }
        return false;
    }

    private static bool HasRepeatedCharacters(string password)
    {
        var charCounts = password.GroupBy(c => c).ToDictionary(g => g.Key, g => g.Count());
        var maxRepeats = charCounts.Values.Max();
        
        // Allow up to 2 repeated characters for passwords longer than 8 characters
        var allowedRepeats = password.Length > 8 ? 2 : 1;
        return maxRepeats > allowedRepeats;
    }

    private static bool HasKeyboardPattern(string password)
    {
        var keyboardPatterns = new[]
        {
            "qwerty", "qwertyuiop", "asdf", "asdfghjkl", "zxcv", "zxcvbnm",
            "1234", "12345", "123456", "1234567890"
        };

        var lowerPassword = password.ToLower();
        return keyboardPatterns.Any(pattern => lowerPassword.Contains(pattern));
    }

    public bool IsPasswordExpired(DateTime? passwordChangedAt, int maxAgeInDays = 90)
    {
        if (!passwordChangedAt.HasValue)
            return true; // Force password change if never set

        return DateTime.UtcNow > passwordChangedAt.Value.AddDays(maxAgeInDays);
    }

    public int CalculatePasswordStrength(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            return 0;

        var score = 0;

        // Length scoring
        if (password.Length >= 8) score += 1;
        if (password.Length >= 12) score += 1;
        if (password.Length >= 16) score += 1;

        // Character variety scoring
        if (password.Any(char.IsLower)) score += 1;
        if (password.Any(char.IsUpper)) score += 1;
        if (password.Any(char.IsDigit)) score += 1;
        if (password.Any(ch => !char.IsLetterOrDigit(ch))) score += 1;

        // Complexity scoring
        if (!HasSequentialCharacters(password)) score += 1;
        if (!HasRepeatedCharacters(password)) score += 1;
        if (!HasKeyboardPattern(password)) score += 1;
        if (!CommonPasswords.Contains(password.ToLower())) score += 1;

        // Return score as percentage (0-100)
        return Math.Min(100, (score * 100) / 11);
    }
}
