{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/language.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/sidebar\";\nimport * as i4 from \"primeng/panelmenu\";\nimport * as i5 from \"primeng/button\";\nconst _c0 = [\"*\"];\nfunction LayoutComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"i\", 5);\n    i0.ɵɵelementStart(2, \"span\", 15);\n    i0.ɵɵtext(3, \"Menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let LayoutComponent = /*#__PURE__*/(() => {\n  class LayoutComponent {\n    constructor(languageService) {\n      this.languageService = languageService;\n      this.sidebarVisible = false;\n      this.menuItems = [];\n    }\n    ngOnInit() {\n      this.initializeMenuItems();\n    }\n    initializeMenuItems() {\n      this.menuItems = [{\n        label: this.languageService.translate('nav.dashboard'),\n        icon: 'pi pi-home',\n        routerLink: '/dashboard'\n      }, {\n        label: 'Inventory Management',\n        icon: 'pi pi-box',\n        expanded: true,\n        items: [{\n          label: 'Items',\n          icon: 'pi pi-list',\n          routerLink: '/items'\n        }, {\n          label: 'Categories',\n          icon: 'pi pi-sitemap',\n          routerLink: '/categories'\n        }, {\n          label: 'Warehouses',\n          icon: 'pi pi-building',\n          routerLink: '/warehouses'\n        }, {\n          label: 'Stock Movements',\n          icon: 'pi pi-arrows-h',\n          routerLink: '/inventory/movements'\n        }, {\n          label: 'Stock Adjustments',\n          icon: 'pi pi-pencil',\n          routerLink: '/inventory/adjustments'\n        }, {\n          label: 'Transfers',\n          icon: 'pi pi-send',\n          routerLink: '/inventory/transfers'\n        }]\n      }, {\n        label: 'Sales & Purchases',\n        icon: 'pi pi-shopping-cart',\n        items: [{\n          label: 'Sales Invoices',\n          icon: 'pi pi-file',\n          routerLink: '/invoices/sales'\n        }, {\n          label: 'Purchase Invoices',\n          icon: 'pi pi-file-import',\n          routerLink: '/invoices/purchases'\n        }, {\n          label: 'Sales Returns',\n          icon: 'pi pi-undo',\n          routerLink: '/invoices/sales-returns'\n        }, {\n          label: 'Purchase Returns',\n          icon: 'pi pi-replay',\n          routerLink: '/invoices/purchase-returns'\n        }]\n      }, {\n        label: 'Customers & Suppliers',\n        icon: 'pi pi-users',\n        items: [{\n          label: 'Customers',\n          icon: 'pi pi-user',\n          routerLink: '/customers'\n        }, {\n          label: 'Suppliers',\n          icon: 'pi pi-user-plus',\n          routerLink: '/suppliers'\n        }]\n      }, {\n        label: 'Financial Management',\n        icon: 'pi pi-dollar',\n        items: [{\n          label: 'Payments',\n          icon: 'pi pi-credit-card',\n          routerLink: '/payments'\n        }, {\n          label: 'Account Statements',\n          icon: 'pi pi-file-pdf',\n          routerLink: '/reports/statements'\n        }, {\n          label: 'Cash Register',\n          icon: 'pi pi-wallet',\n          routerLink: '/cash-register'\n        }]\n      }, {\n        label: 'Reports',\n        icon: 'pi pi-chart-bar',\n        items: [{\n          label: 'Inventory Reports',\n          icon: 'pi pi-chart-line',\n          routerLink: '/reports/inventory'\n        }, {\n          label: 'Financial Reports',\n          icon: 'pi pi-chart-pie',\n          routerLink: '/reports/financial'\n        }, {\n          label: 'Sales Reports',\n          icon: 'pi pi-trending-up',\n          routerLink: '/reports/sales'\n        }, {\n          label: 'Purchase Reports',\n          icon: 'pi pi-trending-down',\n          routerLink: '/reports/purchases'\n        }]\n      }];\n    }\n    toggleSidebar() {\n      this.sidebarVisible = !this.sidebarVisible;\n    }\n    static {\n      this.ɵfac = function LayoutComponent_Factory(t) {\n        return new (t || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LanguageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LayoutComponent,\n        selectors: [[\"app-layout\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 20,\n        vars: 9,\n        consts: [[1, \"layout-wrapper\"], [1, \"layout-topbar\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bars\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"pi\", \"pi-box\", \"text-2xl\", \"text-primary\"], [1, \"m-0\", \"text-xl\", \"font-semibold\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bell\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-user\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\"], [1, \"layout-main\"], [1, \"layout-sidebar\", \"hidden-mobile\"], [3, \"model\", \"multiple\"], [\"position\", \"left\", \"styleClass\", \"layout-sidebar-mobile\", 3, \"visibleChange\", \"visible\", \"modal\", \"dismissible\"], [\"pTemplate\", \"header\"], [1, \"layout-content\"], [1, \"font-semibold\"]],\n        template: function LayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function LayoutComponent_Template_button_click_3_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4);\n            i0.ɵɵelement(5, \"i\", 5);\n            i0.ɵɵelementStart(6, \"h2\", 6);\n            i0.ɵɵtext(7, \"Warehouse Management\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(8, \"div\", 2);\n            i0.ɵɵelement(9, \"app-language-switcher\")(10, \"button\", 7)(11, \"button\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10);\n            i0.ɵɵelement(14, \"p-panelMenu\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"p-sidebar\", 12);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function LayoutComponent_Template_p_sidebar_visibleChange_15_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.sidebarVisible, $event) || (ctx.sidebarVisible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(16, LayoutComponent_ng_template_16_Template, 4, 0, \"ng-template\", 13);\n            i0.ɵɵelement(17, \"p-panelMenu\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 14);\n            i0.ɵɵprojection(19);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"hidden-desktop\", true);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.sidebarVisible);\n            i0.ɵɵproperty(\"modal\", true)(\"dismissible\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n          }\n        },\n        dependencies: [CommonModule, RouterModule, MenubarModule, i2.PrimeTemplate, SidebarModule, i3.Sidebar, PanelMenuModule, i4.PanelMenu, ButtonModule, i5.ButtonDirective, LanguageSwitcherComponent],\n        styles: [\".layout-wrapper[_ngcontent-%COMP%]{min-height:100vh;display:flex;flex-direction:column}.layout-topbar[_ngcontent-%COMP%]{background:var(--surface-0);border-bottom:1px solid var(--surface-200);padding:1rem 1.5rem;display:flex;align-items:center;justify-content:space-between;position:sticky;top:0;z-index:100;box-shadow:0 2px 4px #0000001a}.layout-main[_ngcontent-%COMP%]{flex:1;display:flex}.layout-sidebar[_ngcontent-%COMP%]{width:280px;background:var(--surface-0);border-right:1px solid var(--surface-200);overflow-y:auto;height:calc(100vh - 73px);position:sticky;top:73px}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu{border:none}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-panel{border:none;margin-bottom:0}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header{border:none;border-radius:0}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link{border:none;border-radius:0;padding:1rem 1.5rem;background:transparent;color:var(--text-color);transition:all .2s}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:hover{background:var(--surface-100)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:focus{box-shadow:none;background:var(--surface-100)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content{border:none;background:var(--surface-50)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link{padding:.75rem 1.5rem .75rem 3rem;color:var(--text-color-secondary);border:none;border-radius:0;transition:all .2s}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link:hover{background:var(--surface-100);color:var(--text-color)}.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link.router-link-active{background:var(--primary-color);color:var(--primary-color-text)}.layout-content[_ngcontent-%COMP%]{flex:1;padding:1.5rem;overflow-x:auto;background:var(--surface-50)}@media (max-width: 768px){.hidden-mobile[_ngcontent-%COMP%]{display:none!important}.layout-content[_ngcontent-%COMP%]{padding:1rem}.layout-topbar[_ngcontent-%COMP%]{padding:.75rem 1rem}  .layout-sidebar-mobile{width:280px!important}}@media (min-width: 769px){.hidden-desktop[_ngcontent-%COMP%]{display:none!important}}@media (max-width: 480px){.layout-content[_ngcontent-%COMP%]{padding:.75rem}.layout-topbar[_ngcontent-%COMP%]{padding:.5rem .75rem}.layout-topbar[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1rem!important}  .layout-sidebar-mobile{width:100%!important}}\"]\n      });\n    }\n  }\n  return LayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}