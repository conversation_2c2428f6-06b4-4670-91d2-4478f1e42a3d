import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

// Guards
import { AuthGuard, NoAuthGuard, RoleGuard, PermissionGuard } from './core/guards/auth.guard';

// Components
import { LoginComponent } from './features/auth/login/login.component';
import { UnauthorizedComponent } from './features/auth/unauthorized/unauthorized.component';
import { DashboardComponent } from './features/dashboard/dashboard.component';

// Authorization constants
import { AuthorizationService } from './core/services/authorization.service';
import { UserRoleType, PermissionAction } from './core/models/auth.models';

const routes: Routes = [
  // Public routes (no authentication required)
  {
    path: 'login',
    component: LoginComponent,
    canActivate: [NoAuthGuard], // Redirect to dashboard if already authenticated
    data: { title: 'Login' }
  },
  {
    path: 'unauthorized',
    component: UnauthorizedComponent,
    data: { title: 'Access Denied' }
  },

  // Protected routes (authentication required)
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard],
    data: { 
      title: 'Dashboard',
      permissions: [
        { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ }
      ]
    }
  },

  // Dashboard route - for now, all other routes will redirect here
  // TODO: Add other feature modules as they are implemented

  // Default redirects
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: '**',
    redirectTo: '/unauthorized'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    enableTracing: false, // Set to true for debugging
    preloadingStrategy: undefined, // Lazy load modules on demand
    scrollPositionRestoration: 'top',
    anchorScrolling: 'enabled',
    scrollOffset: [0, 64] // Offset for fixed header
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
