import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MenubarModule } from 'primeng/menubar';
import { SidebarModule } from 'primeng/sidebar';
import { PanelMenuModule } from 'primeng/panelmenu';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { MenuItem } from 'primeng/api';
import { LanguageService } from '@core/services/language.service';
import { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MenubarModule,
    SidebarModule,
    PanelMenuModule,
    ButtonModule,
    TooltipModule,
    LanguageSwitcherComponent
  ],
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit {
  sidebarVisible = false;
  menuItems: MenuItem[] = [];

  constructor(public languageService: LanguageService) {}

  ngOnInit() {
    this.initializeMenuItems();

    // Subscribe to language changes to update menu items
    this.languageService.currentLanguage$.subscribe(() => {
      this.initializeMenuItems();
    });
  }

  private initializeMenuItems() {
    this.menuItems = [
      {
        label: this.languageService.translate('nav.dashboard'),
        icon: 'pi pi-home',
        routerLink: '/dashboard'
      },
      {
        label: this.languageService.translate('nav.inventoryManagement'),
        icon: 'pi pi-box',
        expanded: true,
        items: [
          {
            label: this.languageService.translate('nav.items'),
            icon: 'pi pi-list',
            routerLink: '/items'
          },
          {
            label: this.languageService.translate('nav.categories'),
            icon: 'pi pi-sitemap',
            routerLink: '/categories'
          },
          {
            label: this.languageService.translate('nav.warehouses'),
            icon: 'pi pi-building',
            routerLink: '/warehouses'
          },
          {
            label: this.languageService.translate('nav.stockMovements'),
            icon: 'pi pi-arrows-h',
            routerLink: '/inventory/movements'
          },
          {
            label: this.languageService.translate('nav.stockAdjustments'),
            icon: 'pi pi-pencil',
            routerLink: '/inventory/adjustments'
          },
          {
            label: this.languageService.translate('nav.transfers'),
            icon: 'pi pi-send',
            routerLink: '/inventory/transfers'
          }
        ]
      },
      {
        label: this.languageService.translate('nav.salesPurchases'),
        icon: 'pi pi-shopping-cart',
        items: [
          {
            label: this.languageService.translate('nav.salesInvoices'),
            icon: 'pi pi-file',
            routerLink: '/invoices/sales'
          },
          {
            label: this.languageService.translate('nav.purchaseInvoices'),
            icon: 'pi pi-file-import',
            routerLink: '/invoices/purchases'
          },
          {
            label: this.languageService.translate('nav.salesReturns'),
            icon: 'pi pi-undo',
            routerLink: '/invoices/sales-returns'
          },
          {
            label: this.languageService.translate('nav.purchaseReturns'),
            icon: 'pi pi-replay',
            routerLink: '/invoices/purchase-returns'
          }
        ]
      },
      {
        label: this.languageService.translate('nav.customersSuppliers'),
        icon: 'pi pi-users',
        items: [
          {
            label: this.languageService.translate('nav.customers'),
            icon: 'pi pi-user',
            routerLink: '/customers'
          },
          {
            label: this.languageService.translate('nav.suppliers'),
            icon: 'pi pi-user-plus',
            routerLink: '/suppliers'
          }
        ]
      },
      {
        label: this.languageService.translate('nav.financialManagement'),
        icon: 'pi pi-dollar',
        items: [
          {
            label: this.languageService.translate('nav.payments'),
            icon: 'pi pi-credit-card',
            routerLink: '/payments'
          },
          {
            label: this.languageService.translate('nav.accountStatements'),
            icon: 'pi pi-file-pdf',
            routerLink: '/reports/statements'
          },
          {
            label: this.languageService.translate('nav.cashRegister'),
            icon: 'pi pi-wallet',
            routerLink: '/cash-register'
          }
        ]
      },
      {
        label: this.languageService.translate('nav.reports'),
        icon: 'pi pi-chart-bar',
        items: [
          {
            label: this.languageService.translate('nav.inventoryReports'),
            icon: 'pi pi-chart-line',
            routerLink: '/reports/inventory'
          },
          {
            label: this.languageService.translate('nav.financialReports'),
            icon: 'pi pi-chart-pie',
            routerLink: '/reports/financial'
          },
          {
            label: this.languageService.translate('nav.salesReports'),
            icon: 'pi pi-trending-up',
            routerLink: '/reports/sales'
          },
          {
            label: this.languageService.translate('nav.purchaseReports'),
            icon: 'pi pi-trending-down',
            routerLink: '/reports/purchases'
          }
        ]
      }
    ];
  }

  toggleSidebar() {
    this.sidebarVisible = !this.sidebarVisible;
  }
}
