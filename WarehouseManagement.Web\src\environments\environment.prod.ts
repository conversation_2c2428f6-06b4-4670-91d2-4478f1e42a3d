export const environment = {
  production: true,
  apiUrl: 'https://your-production-api.com/api',
  appName: 'Warehouse Management System',
  version: '1.0.0',

  // Security Configuration (Production)
  security: {
    enableCSRF: true,
    enableRateLimiting: true,
    enableSecurityHeaders: true,
    enableXSSProtection: true,
    sessionTimeout: 15, // minutes
    maxLoginAttempts: 3, // Stricter in production
    lockoutDuration: 30, // minutes - longer in production
    passwordPolicy: {
      minLength: 12, // Stronger in production
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      preventCommonPasswords: true
    },
    rateLimits: {
      login: { maxAttempts: 3, windowMinutes: 15 }, // Stricter
      api: { maxAttempts: 60, windowMinutes: 1 }, // More conservative
      passwordReset: { maxAttempts: 2, windowMinutes: 60 } // Stricter
    }
  },

  // Feature Flags (Production)
  features: {
    enableBiometricAuth: true,
    enableTwoFactorAuth: true,
    enableAuditLogging: true,
    enableSecurityMonitoring: true
  }
};
