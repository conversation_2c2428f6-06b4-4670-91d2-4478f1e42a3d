{"ast": null, "code": "import { executeSchedule } from '../util/executeSchedule';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function observeOn(scheduler, delay = 0) {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, value => executeSchedule(subscriber, scheduler, () => subscriber.next(value), delay), () => executeSchedule(subscriber, scheduler, () => subscriber.complete(), delay), err => executeSchedule(subscriber, scheduler, () => subscriber.error(err), delay)));\n  });\n}", "map": {"version": 3, "names": ["executeSchedule", "operate", "createOperatorSubscriber", "observeOn", "scheduler", "delay", "source", "subscriber", "subscribe", "value", "next", "complete", "err", "error"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/rxjs/dist/esm/internal/operators/observeOn.js"], "sourcesContent": ["import { executeSchedule } from '../util/executeSchedule';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function observeOn(scheduler, delay = 0) {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => executeSchedule(subscriber, scheduler, () => subscriber.next(value), delay), () => executeSchedule(subscriber, scheduler, () => subscriber.complete(), delay), (err) => executeSchedule(subscriber, scheduler, () => subscriber.error(err), delay)));\n    });\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,yBAAyB;AACzD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC5C,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnCD,MAAM,CAACE,SAAS,CAACN,wBAAwB,CAACK,UAAU,EAAGE,KAAK,IAAKT,eAAe,CAACO,UAAU,EAAEH,SAAS,EAAE,MAAMG,UAAU,CAACG,IAAI,CAACD,KAAK,CAAC,EAAEJ,KAAK,CAAC,EAAE,MAAML,eAAe,CAACO,UAAU,EAAEH,SAAS,EAAE,MAAMG,UAAU,CAACI,QAAQ,CAAC,CAAC,EAAEN,KAAK,CAAC,EAAGO,GAAG,IAAKZ,eAAe,CAACO,UAAU,EAAEH,SAAS,EAAE,MAAMG,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC,EAAEP,KAAK,CAAC,CAAC,CAAC;EACzT,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}