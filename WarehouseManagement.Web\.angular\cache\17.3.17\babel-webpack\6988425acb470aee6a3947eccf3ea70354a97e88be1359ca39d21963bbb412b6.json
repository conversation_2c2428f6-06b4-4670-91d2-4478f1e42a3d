{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/language.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/sidebar\";\nimport * as i4 from \"primeng/panelmenu\";\nimport * as i5 from \"primeng/button\";\nconst _c0 = [\"*\"];\nfunction LayoutComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"i\", 5);\n    i0.ɵɵelementStart(2, \"span\", 15);\n    i0.ɵɵtext(3, \"Menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class LayoutComponent {\n  constructor(languageService) {\n    this.languageService = languageService;\n    this.sidebarVisible = false;\n    this.menuItems = [];\n  }\n  ngOnInit() {\n    this.initializeMenuItems();\n  }\n  initializeMenuItems() {\n    this.menuItems = [{\n      label: 'Dashboard',\n      icon: 'pi pi-home',\n      routerLink: '/dashboard'\n    }, {\n      label: 'Inventory Management',\n      icon: 'pi pi-box',\n      expanded: true,\n      items: [{\n        label: 'Items',\n        icon: 'pi pi-list',\n        routerLink: '/items'\n      }, {\n        label: 'Categories',\n        icon: 'pi pi-sitemap',\n        routerLink: '/categories'\n      }, {\n        label: 'Warehouses',\n        icon: 'pi pi-building',\n        routerLink: '/warehouses'\n      }, {\n        label: 'Stock Movements',\n        icon: 'pi pi-arrows-h',\n        routerLink: '/inventory/movements'\n      }, {\n        label: 'Stock Adjustments',\n        icon: 'pi pi-pencil',\n        routerLink: '/inventory/adjustments'\n      }, {\n        label: 'Transfers',\n        icon: 'pi pi-send',\n        routerLink: '/inventory/transfers'\n      }]\n    }, {\n      label: 'Sales & Purchases',\n      icon: 'pi pi-shopping-cart',\n      items: [{\n        label: 'Sales Invoices',\n        icon: 'pi pi-file',\n        routerLink: '/invoices/sales'\n      }, {\n        label: 'Purchase Invoices',\n        icon: 'pi pi-file-import',\n        routerLink: '/invoices/purchases'\n      }, {\n        label: 'Sales Returns',\n        icon: 'pi pi-undo',\n        routerLink: '/invoices/sales-returns'\n      }, {\n        label: 'Purchase Returns',\n        icon: 'pi pi-replay',\n        routerLink: '/invoices/purchase-returns'\n      }]\n    }, {\n      label: 'Customers & Suppliers',\n      icon: 'pi pi-users',\n      items: [{\n        label: 'Customers',\n        icon: 'pi pi-user',\n        routerLink: '/customers'\n      }, {\n        label: 'Suppliers',\n        icon: 'pi pi-user-plus',\n        routerLink: '/suppliers'\n      }]\n    }, {\n      label: 'Financial Management',\n      icon: 'pi pi-dollar',\n      items: [{\n        label: 'Payments',\n        icon: 'pi pi-credit-card',\n        routerLink: '/payments'\n      }, {\n        label: 'Account Statements',\n        icon: 'pi pi-file-pdf',\n        routerLink: '/reports/statements'\n      }, {\n        label: 'Cash Register',\n        icon: 'pi pi-wallet',\n        routerLink: '/cash-register'\n      }]\n    }, {\n      label: 'Reports',\n      icon: 'pi pi-chart-bar',\n      items: [{\n        label: 'Inventory Reports',\n        icon: 'pi pi-chart-line',\n        routerLink: '/reports/inventory'\n      }, {\n        label: 'Financial Reports',\n        icon: 'pi pi-chart-pie',\n        routerLink: '/reports/financial'\n      }, {\n        label: 'Sales Reports',\n        icon: 'pi pi-trending-up',\n        routerLink: '/reports/sales'\n      }, {\n        label: 'Purchase Reports',\n        icon: 'pi pi-trending-down',\n        routerLink: '/reports/purchases'\n      }]\n    }];\n  }\n  toggleSidebar() {\n    this.sidebarVisible = !this.sidebarVisible;\n  }\n  static {\n    this.ɵfac = function LayoutComponent_Factory(t) {\n      return new (t || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LanguageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutComponent,\n      selectors: [[\"app-layout\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 19,\n      vars: 9,\n      consts: [[1, \"layout-wrapper\"], [1, \"layout-topbar\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bars\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"pi\", \"pi-box\", \"text-2xl\", \"text-primary\"], [1, \"m-0\", \"text-xl\", \"font-semibold\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bell\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-user\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\"], [1, \"layout-main\"], [1, \"layout-sidebar\", \"hidden-mobile\"], [3, \"model\", \"multiple\"], [\"position\", \"left\", \"styleClass\", \"layout-sidebar-mobile\", 3, \"visibleChange\", \"visible\", \"modal\", \"dismissible\"], [\"pTemplate\", \"header\"], [1, \"layout-content\"], [1, \"font-semibold\"]],\n      template: function LayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LayoutComponent_Template_button_click_3_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementStart(6, \"h2\", 6);\n          i0.ɵɵtext(7, \"Warehouse Management\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 2);\n          i0.ɵɵelement(9, \"button\", 7)(10, \"button\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10);\n          i0.ɵɵelement(13, \"p-panelMenu\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p-sidebar\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LayoutComponent_Template_p_sidebar_visibleChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sidebarVisible, $event) || (ctx.sidebarVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(15, LayoutComponent_ng_template_15_Template, 4, 0, \"ng-template\", 13);\n          i0.ɵɵelement(16, \"p-panelMenu\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 14);\n          i0.ɵɵprojection(18);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"hidden-desktop\", true);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.sidebarVisible);\n          i0.ɵɵproperty(\"modal\", true)(\"dismissible\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, MenubarModule, i2.PrimeTemplate, SidebarModule, i3.Sidebar, PanelMenuModule, i4.PanelMenu, ButtonModule, i5.ButtonDirective],\n      styles: [\".layout-wrapper[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.layout-topbar[_ngcontent-%COMP%] {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-200);\\n  padding: 1rem 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: sticky;\\n  top: 0;\\n  z-index: 100;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.layout-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n}\\n\\n.layout-sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: var(--surface-0);\\n  border-right: 1px solid var(--surface-200);\\n  overflow-y: auto;\\n  height: calc(100vh - 73px);\\n  position: sticky;\\n  top: 73px;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu {\\n  border: none;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-panel {\\n  border: none;\\n  margin-bottom: 0;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header {\\n  border: none;\\n  border-radius: 0;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link {\\n  border: none;\\n  border-radius: 0;\\n  padding: 1rem 1.5rem;\\n  background: transparent;\\n  color: var(--text-color);\\n  transition: all 0.2s;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:hover {\\n  background: var(--surface-100);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:focus {\\n  box-shadow: none;\\n  background: var(--surface-100);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content {\\n  border: none;\\n  background: var(--surface-50);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link {\\n  padding: 0.75rem 1.5rem 0.75rem 3rem;\\n  color: var(--text-color-secondary);\\n  border: none;\\n  border-radius: 0;\\n  transition: all 0.2s;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link:hover {\\n  background: var(--surface-100);\\n  color: var(--text-color);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link.router-link-active {\\n  background: var(--primary-color);\\n  color: var(--primary-color-text);\\n}\\n\\n.layout-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1.5rem;\\n  overflow-x: auto;\\n  background: var(--surface-50);\\n}\\n\\n@media (max-width: 768px) {\\n  .hidden-mobile[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .layout-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n    .layout-sidebar-mobile {\\n    width: 280px !important;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .hidden-desktop[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .layout-content[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.75rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n    .layout-sidebar-mobile {\\n    width: 100% !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MenubarModule", "SidebarModule", "PanelMenuModule", "ButtonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "LayoutComponent", "constructor", "languageService", "sidebarVisible", "menuItems", "ngOnInit", "initializeMenuItems", "label", "icon", "routerLink", "expanded", "items", "toggleSidebar", "ɵɵdirectiveInject", "i1", "LanguageService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "LayoutComponent_Template", "rf", "ctx", "ɵɵlistener", "LayoutComponent_Template_button_click_3_listener", "ɵɵtwoWayListener", "LayoutComponent_Template_p_sidebar_visibleChange_14_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵtemplate", "LayoutComponent_ng_template_15_Template", "ɵɵprojection", "ɵɵadvance", "ɵɵclassProp", "ɵɵproperty", "ɵɵtwoWayProperty", "i2", "PrimeTemplate", "i3", "Sidebar", "i4", "PanelMenu", "i5", "ButtonDirective", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\shared\\components\\layout\\layout.component.ts", "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\shared\\components\\layout\\layout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { MenuItem } from 'primeng/api';\nimport { LanguageService } from '@core/services/language.service';\nimport { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';\n\n@Component({\n  selector: 'app-layout',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MenubarModule,\n    SidebarModule,\n    PanelMenuModule,\n    ButtonModule,\n    LanguageSwitcherComponent\n  ],\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.scss']\n})\nexport class LayoutComponent implements OnInit {\n  sidebarVisible = false;\n  menuItems: MenuItem[] = [];\n\n  constructor(private languageService: LanguageService) {}\n\n  ngOnInit() {\n    this.initializeMenuItems();\n  }\n\n  private initializeMenuItems() {\n    this.menuItems = [\n      {\n        label: 'Dashboard',\n        icon: 'pi pi-home',\n        routerLink: '/dashboard'\n      },\n      {\n        label: 'Inventory Management',\n        icon: 'pi pi-box',\n        expanded: true,\n        items: [\n          {\n            label: 'Items',\n            icon: 'pi pi-list',\n            routerLink: '/items'\n          },\n          {\n            label: 'Categories',\n            icon: 'pi pi-sitemap',\n            routerLink: '/categories'\n          },\n          {\n            label: 'Warehouses',\n            icon: 'pi pi-building',\n            routerLink: '/warehouses'\n          },\n          {\n            label: 'Stock Movements',\n            icon: 'pi pi-arrows-h',\n            routerLink: '/inventory/movements'\n          },\n          {\n            label: 'Stock Adjustments',\n            icon: 'pi pi-pencil',\n            routerLink: '/inventory/adjustments'\n          },\n          {\n            label: 'Transfers',\n            icon: 'pi pi-send',\n            routerLink: '/inventory/transfers'\n          }\n        ]\n      },\n      {\n        label: 'Sales & Purchases',\n        icon: 'pi pi-shopping-cart',\n        items: [\n          {\n            label: 'Sales Invoices',\n            icon: 'pi pi-file',\n            routerLink: '/invoices/sales'\n          },\n          {\n            label: 'Purchase Invoices',\n            icon: 'pi pi-file-import',\n            routerLink: '/invoices/purchases'\n          },\n          {\n            label: 'Sales Returns',\n            icon: 'pi pi-undo',\n            routerLink: '/invoices/sales-returns'\n          },\n          {\n            label: 'Purchase Returns',\n            icon: 'pi pi-replay',\n            routerLink: '/invoices/purchase-returns'\n          }\n        ]\n      },\n      {\n        label: 'Customers & Suppliers',\n        icon: 'pi pi-users',\n        items: [\n          {\n            label: 'Customers',\n            icon: 'pi pi-user',\n            routerLink: '/customers'\n          },\n          {\n            label: 'Suppliers',\n            icon: 'pi pi-user-plus',\n            routerLink: '/suppliers'\n          }\n        ]\n      },\n      {\n        label: 'Financial Management',\n        icon: 'pi pi-dollar',\n        items: [\n          {\n            label: 'Payments',\n            icon: 'pi pi-credit-card',\n            routerLink: '/payments'\n          },\n          {\n            label: 'Account Statements',\n            icon: 'pi pi-file-pdf',\n            routerLink: '/reports/statements'\n          },\n          {\n            label: 'Cash Register',\n            icon: 'pi pi-wallet',\n            routerLink: '/cash-register'\n          }\n        ]\n      },\n      {\n        label: 'Reports',\n        icon: 'pi pi-chart-bar',\n        items: [\n          {\n            label: 'Inventory Reports',\n            icon: 'pi pi-chart-line',\n            routerLink: '/reports/inventory'\n          },\n          {\n            label: 'Financial Reports',\n            icon: 'pi pi-chart-pie',\n            routerLink: '/reports/financial'\n          },\n          {\n            label: 'Sales Reports',\n            icon: 'pi pi-trending-up',\n            routerLink: '/reports/sales'\n          },\n          {\n            label: 'Purchase Reports',\n            icon: 'pi pi-trending-down',\n            routerLink: '/reports/purchases'\n          }\n        ]\n      }\n    ];\n  }\n\n  toggleSidebar() {\n    this.sidebarVisible = !this.sidebarVisible;\n  }\n}\n", "<div class=\"layout-wrapper\">\n  <!-- Top Navigation Bar -->\n  <div class=\"layout-topbar\">\n    <div class=\"flex align-items-center gap-3\">\n      <button \n        pButton \n        type=\"button\" \n        icon=\"pi pi-bars\" \n        class=\"p-button-text p-button-rounded p-button-plain\"\n        (click)=\"toggleSidebar()\"\n        [class.hidden-desktop]=\"true\">\n      </button>\n      \n      <div class=\"flex align-items-center gap-2\">\n        <i class=\"pi pi-box text-2xl text-primary\"></i>\n        <h2 class=\"m-0 text-xl font-semibold\">Warehouse Management</h2>\n      </div>\n    </div>\n\n    <div class=\"flex align-items-center gap-3\">\n      <button \n        pButton \n        type=\"button\" \n        icon=\"pi pi-bell\" \n        class=\"p-button-text p-button-rounded p-button-plain\">\n      </button>\n      \n      <button \n        pButton \n        type=\"button\" \n        icon=\"pi pi-user\" \n        class=\"p-button-text p-button-rounded p-button-plain\">\n      </button>\n    </div>\n  </div>\n\n  <div class=\"layout-main\">\n    <!-- Desktop Sidebar -->\n    <div class=\"layout-sidebar hidden-mobile\">\n      <p-panelMenu [model]=\"menuItems\" [multiple]=\"false\"></p-panelMenu>\n    </div>\n\n    <!-- Mobile Sidebar -->\n    <p-sidebar \n      [(visible)]=\"sidebarVisible\" \n      position=\"left\" \n      [modal]=\"true\"\n      [dismissible]=\"true\"\n      styleClass=\"layout-sidebar-mobile\">\n      <ng-template pTemplate=\"header\">\n        <div class=\"flex align-items-center gap-2\">\n          <i class=\"pi pi-box text-2xl text-primary\"></i>\n          <span class=\"font-semibold\">Menu</span>\n        </div>\n      </ng-template>\n      \n      <p-panelMenu [model]=\"menuItems\" [multiple]=\"false\"></p-panelMenu>\n    </p-sidebar>\n\n    <!-- Main Content Area -->\n    <div class=\"layout-content\">\n      <ng-content></ng-content>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;;;;;;;;;;IC4CrCC,EAAA,CAAAC,cAAA,aAA2C;IACzCD,EAAA,CAAAE,SAAA,WAA+C;IAC/CF,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAClCH,EADkC,CAAAI,YAAA,EAAO,EACnC;;;AD3Bd,OAAM,MAAOC,eAAe;EAI1BC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAHnC,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,SAAS,GAAe,EAAE;EAE6B;EAEvDC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACF,SAAS,GAAG,CACf;MACEG,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE;KACb,EACD;MACEF,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,WAAW;MACjBE,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,CACL;QACEJ,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,iBAAiB;QACxBC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,WAAW;QAClBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEF,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,qBAAqB;MAC3BG,KAAK,EAAE,CACL;QACEJ,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEF,KAAK,EAAE,uBAAuB;MAC9BC,IAAI,EAAE,aAAa;MACnBG,KAAK,EAAE,CACL;QACEJ,KAAK,EAAE,WAAW;QAClBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,WAAW;QAClBC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEF,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,cAAc;MACpBG,KAAK,EAAE,CACL;QACEJ,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,oBAAoB;QAC3BC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEF,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,iBAAiB;MACvBG,KAAK,EAAE,CACL;QACEJ,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,kBAAkB;QACxBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEF,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,qBAAqB;QAC3BC,UAAU,EAAE;OACb;KAEJ,CACF;EACH;EAEAG,aAAaA,CAAA;IACX,IAAI,CAACT,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;;;uBApJWH,eAAe,EAAAL,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAff,eAAe;MAAAgB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvB,EAAA,CAAAwB,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtBtBhC,EAJN,CAAAC,cAAA,aAA4B,aAEC,aACkB,gBAOT;UAD9BD,EAAA,CAAAkC,UAAA,mBAAAC,iDAAA;YAAA,OAASF,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAE3BjB,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAC,cAAA,aAA2C;UACzCD,EAAA,CAAAE,SAAA,WAA+C;UAC/CF,EAAA,CAAAC,cAAA,YAAsC;UAAAD,EAAA,CAAAG,MAAA,2BAAoB;UAE9DH,EAF8D,CAAAI,YAAA,EAAK,EAC3D,EACF;UAENJ,EAAA,CAAAC,cAAA,aAA2C;UAQzCD,EAPA,CAAAE,SAAA,gBAKS,iBAOA;UAEbF,EADE,CAAAI,YAAA,EAAM,EACF;UAIJJ,EAFF,CAAAC,cAAA,cAAyB,eAEmB;UACxCD,EAAA,CAAAE,SAAA,uBAAkE;UACpEF,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,qBAKqC;UAJnCD,EAAA,CAAAoC,gBAAA,2BAAAC,6DAAAC,MAAA;YAAAtC,EAAA,CAAAuC,kBAAA,CAAAN,GAAA,CAAAzB,cAAA,EAAA8B,MAAA,MAAAL,GAAA,CAAAzB,cAAA,GAAA8B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAK5BtC,EAAA,CAAAwC,UAAA,KAAAC,uCAAA,0BAAgC;UAOhCzC,EAAA,CAAAE,SAAA,uBAAkE;UACpEF,EAAA,CAAAI,YAAA,EAAY;UAGZJ,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAA0C,YAAA,IAAyB;UAG/B1C,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;UAtDEJ,EAAA,CAAA2C,SAAA,GAA6B;UAA7B3C,EAAA,CAAA4C,WAAA,wBAA6B;UA6BlB5C,EAAA,CAAA2C,SAAA,IAAmB;UAAC3C,EAApB,CAAA6C,UAAA,UAAAZ,GAAA,CAAAxB,SAAA,CAAmB,mBAAmB;UAKnDT,EAAA,CAAA2C,SAAA,EAA4B;UAA5B3C,EAAA,CAAA8C,gBAAA,YAAAb,GAAA,CAAAzB,cAAA,CAA4B;UAG5BR,EADA,CAAA6C,UAAA,eAAc,qBACM;UASP7C,EAAA,CAAA2C,SAAA,GAAmB;UAAC3C,EAApB,CAAA6C,UAAA,UAAAZ,GAAA,CAAAxB,SAAA,CAAmB,mBAAmB;;;qBDzCrDf,YAAY,EACZC,YAAY,EACZC,aAAa,EAAAmD,EAAA,CAAAC,aAAA,EACbnD,aAAa,EAAAoD,EAAA,CAAAC,OAAA,EACbpD,eAAe,EAAAqD,EAAA,CAAAC,SAAA,EACfrD,YAAY,EAAAsD,EAAA,CAAAC,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}