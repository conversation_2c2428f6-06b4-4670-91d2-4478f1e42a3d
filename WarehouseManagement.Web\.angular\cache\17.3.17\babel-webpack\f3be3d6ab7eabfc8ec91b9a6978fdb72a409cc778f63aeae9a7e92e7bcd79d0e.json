{"ast": null, "code": "export const routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)\n},\n// TODO: Add other feature routes after implementing them\n{\n  path: '**',\n  redirectTo: '/dashboard'\n}];", "map": {"version": 3, "names": ["routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "DashboardComponent"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    redirectTo: '/dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'dashboard',\n    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)\n  },\n  // TODO: Add other feature routes after implementing them\n  {\n    path: '**',\n    redirectTo: '/dashboard'\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,WAAW;EACjBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB;CACvG;AACD;AACA;EACEN,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}