{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/language.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/dropdown\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => ({\n  \"min-width\": \"150px\"\n});\nfunction LanguageSwitcherComponent_ng_template_1_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getLanguageFlag(ctx_r0.selectedLanguage));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getLanguageName(ctx_r0.selectedLanguage));\n  }\n}\nfunction LanguageSwitcherComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LanguageSwitcherComponent_ng_template_1_div_0_Template, 5, 2, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedLanguage);\n  }\n}\nfunction LanguageSwitcherComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const language_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r2.flag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r2.name);\n  }\n}\nexport class LanguageSwitcherComponent {\n  constructor(languageService) {\n    this.languageService = languageService;\n    this.languages = [];\n    this.selectedLanguage = '';\n  }\n  ngOnInit() {\n    this.languages = this.languageService.getAvailableLanguages();\n    this.selectedLanguage = this.languageService.getCurrentLanguage().code;\n    // Subscribe to language changes\n    this.languageService.currentLanguage$.subscribe(language => {\n      this.selectedLanguage = language.code;\n    });\n  }\n  onLanguageChange(event) {\n    const languageCode = event.value;\n    this.languageService.setLanguage(languageCode);\n  }\n  getLanguageFlag(code) {\n    const language = this.languages.find(lang => lang.code === code);\n    return language?.flag || '';\n  }\n  getLanguageName(code) {\n    const language = this.languages.find(lang => lang.code === code);\n    return language?.name || '';\n  }\n  static {\n    this.ɵfac = function LanguageSwitcherComponent_Factory(t) {\n      return new (t || LanguageSwitcherComponent)(i0.ɵɵdirectiveInject(i1.LanguageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LanguageSwitcherComponent,\n      selectors: [[\"app-language-switcher\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 5,\n      consts: [[\"optionLabel\", \"name\", \"optionValue\", \"code\", \"placeholder\", \"Select Language\", 1, \"language-switcher\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [\"class\", \"flex align-items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"]],\n      template: function LanguageSwitcherComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-dropdown\", 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LanguageSwitcherComponent_Template_p_dropdown_ngModelChange_0_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedLanguage, $event) || (ctx.selectedLanguage = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function LanguageSwitcherComponent_Template_p_dropdown_onChange_0_listener($event) {\n            return ctx.onLanguageChange($event);\n          });\n          i0.ɵɵtemplate(1, LanguageSwitcherComponent_ng_template_1_Template, 1, 1, \"ng-template\", 1)(2, LanguageSwitcherComponent_ng_template_2_Template, 5, 2, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c0));\n          i0.ɵɵproperty(\"options\", ctx.languages);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedLanguage);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, DropdownModule, i3.Dropdown, i4.PrimeTemplate, FormsModule, i5.NgControlStatus, i5.NgModel],\n      styles: [\".language-switcher[_ngcontent-%COMP%]   .p-dropdown[_ngcontent-%COMP%] {\\n  border: 1px solid var(--surface-border);\\n  background: var(--surface-ground);\\n}\\n.language-switcher[_ngcontent-%COMP%]   .p-dropdown[_ngcontent-%COMP%]:not(.p-disabled):hover {\\n  border-color: var(--primary-color);\\n}\\n.language-switcher[_ngcontent-%COMP%]   .p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus {\\n  outline: 0 none;\\n  outline-offset: 0;\\n  box-shadow: 0 0 0 0.2rem var(--primary-color-text);\\n  border-color: var(--primary-color);\\n}\\n\\n.rtl[_nghost-%COMP%]   .language-switcher[_ngcontent-%COMP%]   .p-dropdown-label[_ngcontent-%COMP%], .rtl   [_nghost-%COMP%]   .language-switcher[_ngcontent-%COMP%]   .p-dropdown-label[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvbGFuZ3VhZ2Utc3dpdGNoZXIvbGFuZ3VhZ2Utc3dpdGNoZXIuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVNO0VBQ0UsdUNBQUE7RUFDQSxpQ0FBQTtBQURSO0FBSU07RUFDRSxrQ0FBQTtBQUZSO0FBS007RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxrREFBQTtFQUNBLGtDQUFBO0FBSFI7O0FBUU07RUFDRSxpQkFBQTtBQUxSIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmxhbmd1YWdlLXN3aXRjaGVyIHtcbiAgICAgIC5wLWRyb3Bkb3duIHtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tc3VyZmFjZS1ib3JkZXIpO1xuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLWdyb3VuZCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC5wLWRyb3Bkb3duOm5vdCgucC1kaXNhYmxlZCk6aG92ZXIge1xuICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgfVxuICAgICAgXG4gICAgICAucC1kcm9wZG93bjpub3QoLnAtZGlzYWJsZWQpLnAtZm9jdXMge1xuICAgICAgICBvdXRsaW5lOiAwIG5vbmU7XG4gICAgICAgIG91dGxpbmUtb2Zmc2V0OiAwO1xuICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAwLjJyZW0gdmFyKC0tcHJpbWFyeS1jb2xvci10ZXh0KTtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgOmhvc3QtY29udGV4dCgucnRsKSAubGFuZ3VhZ2Utc3dpdGNoZXIge1xuICAgICAgLnAtZHJvcGRvd24tbGFiZWwge1xuICAgICAgICB0ZXh0LWFsaWduOiByaWdodDtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "DropdownModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getLanguageFlag", "selectedLanguage", "getLanguageName", "ɵɵtemplate", "LanguageSwitcherComponent_ng_template_1_div_0_Template", "ɵɵproperty", "language_r2", "flag", "name", "LanguageSwitcherComponent", "constructor", "languageService", "languages", "ngOnInit", "getAvailableLanguages", "getCurrentLanguage", "code", "currentLanguage$", "subscribe", "language", "onLanguageChange", "event", "languageCode", "value", "setLanguage", "find", "lang", "ɵɵdirectiveInject", "i1", "LanguageService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LanguageSwitcherComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "LanguageSwitcherComponent_Template_p_dropdown_ngModelChange_0_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "LanguageSwitcherComponent_Template_p_dropdown_onChange_0_listener", "LanguageSwitcherComponent_ng_template_1_Template", "LanguageSwitcherComponent_ng_template_2_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "i2", "NgIf", "i3", "Dropdown", "i4", "PrimeTemplate", "i5", "NgControlStatus", "NgModel", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\shared\\components\\language-switcher\\language-switcher.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule } from '@angular/forms';\nimport { LanguageService, Language } from '@core/services/language.service';\n\n@Component({\n  selector: 'app-language-switcher',\n  standalone: true,\n  imports: [\n    CommonModule,\n    DropdownModule,\n    FormsModule\n  ],\n  template: `\n    <p-dropdown\n      [options]=\"languages\"\n      [(ngModel)]=\"selectedLanguage\"\n      (onChange)=\"onLanguageChange($event)\"\n      optionLabel=\"name\"\n      optionValue=\"code\"\n      [style]=\"{'min-width': '150px'}\"\n      placeholder=\"Select Language\"\n      class=\"language-switcher\">\n      \n      <ng-template pTemplate=\"selectedItem\">\n        <div class=\"flex align-items-center gap-2\" *ngIf=\"selectedLanguage\">\n          <span>{{ getLanguageFlag(selectedLanguage) }}</span>\n          <span>{{ getLanguageName(selectedLanguage) }}</span>\n        </div>\n      </ng-template>\n      \n      <ng-template pTemplate=\"item\" let-language>\n        <div class=\"flex align-items-center gap-2\">\n          <span>{{ language.flag }}</span>\n          <span>{{ language.name }}</span>\n        </div>\n      </ng-template>\n    </p-dropdown>\n  `,\n  styles: [`\n    .language-switcher {\n      .p-dropdown {\n        border: 1px solid var(--surface-border);\n        background: var(--surface-ground);\n      }\n      \n      .p-dropdown:not(.p-disabled):hover {\n        border-color: var(--primary-color);\n      }\n      \n      .p-dropdown:not(.p-disabled).p-focus {\n        outline: 0 none;\n        outline-offset: 0;\n        box-shadow: 0 0 0 0.2rem var(--primary-color-text);\n        border-color: var(--primary-color);\n      }\n    }\n    \n    :host-context(.rtl) .language-switcher {\n      .p-dropdown-label {\n        text-align: right;\n      }\n    }\n  `]\n})\nexport class LanguageSwitcherComponent implements OnInit {\n  languages: Language[] = [];\n  selectedLanguage: string = '';\n\n  constructor(private languageService: LanguageService) {}\n\n  ngOnInit(): void {\n    this.languages = this.languageService.getAvailableLanguages();\n    this.selectedLanguage = this.languageService.getCurrentLanguage().code;\n    \n    // Subscribe to language changes\n    this.languageService.currentLanguage$.subscribe(language => {\n      this.selectedLanguage = language.code;\n    });\n  }\n\n  onLanguageChange(event: any): void {\n    const languageCode = event.value;\n    this.languageService.setLanguage(languageCode);\n  }\n\n  getLanguageFlag(code: string): string {\n    const language = this.languages.find(lang => lang.code === code);\n    return language?.flag || '';\n  }\n\n  getLanguageName(code: string): string {\n    const language = this.languages.find(lang => lang.code === code);\n    return language?.name || '';\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;IAwBlCC,EADF,CAAAC,cAAA,aAAoE,WAC5D;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EAChD;;;;IAFEH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,gBAAA,EAAuC;IACvCR,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAG,eAAA,CAAAH,MAAA,CAAAE,gBAAA,EAAuC;;;;;IAF/CR,EAAA,CAAAU,UAAA,IAAAC,sDAAA,iBAAoE;;;;IAAxBX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAE,gBAAA,CAAsB;;;;;IAQhER,EADF,CAAAC,cAAA,aAA2C,WACnC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC3BF,EAD2B,CAAAG,YAAA,EAAO,EAC5B;;;;IAFEH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAQ,WAAA,CAAAC,IAAA,CAAmB;IACnBd,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAQ,WAAA,CAAAE,IAAA,CAAmB;;;AA+BnC,OAAM,MAAOC,yBAAyB;EAIpCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAHnC,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAX,gBAAgB,GAAW,EAAE;EAE0B;EAEvDY,QAAQA,CAAA;IACN,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,eAAe,CAACG,qBAAqB,EAAE;IAC7D,IAAI,CAACb,gBAAgB,GAAG,IAAI,CAACU,eAAe,CAACI,kBAAkB,EAAE,CAACC,IAAI;IAEtE;IACA,IAAI,CAACL,eAAe,CAACM,gBAAgB,CAACC,SAAS,CAACC,QAAQ,IAAG;MACzD,IAAI,CAAClB,gBAAgB,GAAGkB,QAAQ,CAACH,IAAI;IACvC,CAAC,CAAC;EACJ;EAEAI,gBAAgBA,CAACC,KAAU;IACzB,MAAMC,YAAY,GAAGD,KAAK,CAACE,KAAK;IAChC,IAAI,CAACZ,eAAe,CAACa,WAAW,CAACF,YAAY,CAAC;EAChD;EAEAtB,eAAeA,CAACgB,IAAY;IAC1B,MAAMG,QAAQ,GAAG,IAAI,CAACP,SAAS,CAACa,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACV,IAAI,KAAKA,IAAI,CAAC;IAChE,OAAOG,QAAQ,EAAEZ,IAAI,IAAI,EAAE;EAC7B;EAEAL,eAAeA,CAACc,IAAY;IAC1B,MAAMG,QAAQ,GAAG,IAAI,CAACP,SAAS,CAACa,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACV,IAAI,KAAKA,IAAI,CAAC;IAChE,OAAOG,QAAQ,EAAEX,IAAI,IAAI,EAAE;EAC7B;;;uBA7BWC,yBAAyB,EAAAhB,EAAA,CAAAkC,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAzBpB,yBAAyB;MAAAqB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvC,EAAA,CAAAwC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnDlC9C,EAAA,CAAAC,cAAA,oBAQ4B;UAN1BD,EAAA,CAAAgD,gBAAA,2BAAAC,uEAAAC,MAAA;YAAAlD,EAAA,CAAAmD,kBAAA,CAAAJ,GAAA,CAAAvC,gBAAA,EAAA0C,MAAA,MAAAH,GAAA,CAAAvC,gBAAA,GAAA0C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAC9BlD,EAAA,CAAAoD,UAAA,sBAAAC,kEAAAH,MAAA;YAAA,OAAYH,GAAA,CAAApB,gBAAA,CAAAuB,MAAA,CAAwB;UAAA,EAAC;UAcrClD,EAPA,CAAAU,UAAA,IAAA4C,gDAAA,yBAAsC,IAAAC,gDAAA,yBAOK;UAM7CvD,EAAA,CAAAG,YAAA,EAAa;;;UAjBXH,EAAA,CAAAwD,UAAA,CAAAxD,EAAA,CAAAyD,eAAA,IAAAC,GAAA,EAAgC;UALhC1D,EAAA,CAAAY,UAAA,YAAAmC,GAAA,CAAA5B,SAAA,CAAqB;UACrBnB,EAAA,CAAA2D,gBAAA,YAAAZ,GAAA,CAAAvC,gBAAA,CAA8B;;;qBAPhCX,YAAY,EAAA+D,EAAA,CAAAC,IAAA,EACZ/D,cAAc,EAAAgE,EAAA,CAAAC,QAAA,EAAAC,EAAA,CAAAC,aAAA,EACdlE,WAAW,EAAAmE,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}