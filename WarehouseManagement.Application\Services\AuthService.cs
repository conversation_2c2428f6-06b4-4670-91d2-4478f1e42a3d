using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WarehouseManagement.Core.Common;
using WarehouseManagement.Core.Entities;
using WarehouseManagement.Core.Interfaces;
using WarehouseManagement.Core.Models;

namespace WarehouseManagement.Application.Services;

public class AuthService : IAuthService
{
    private readonly IRepository<User> _userRepository;
    private readonly IJwtService _jwtService;
    private readonly IPasswordService _passwordService;
    private readonly ILogger<AuthService> _logger;
    private readonly IConfiguration _configuration;

    private readonly int _maxLoginAttempts;
    private readonly int _lockoutDurationMinutes;

    public AuthService(
        IRepository<User> userRepository,
        IJwtService jwtService,
        IPasswordService passwordService,
        ILogger<AuthService> logger,
        IConfiguration configuration)
    {
        _userRepository = userRepository;
        _jwtService = jwtService;
        _passwordService = passwordService;
        _logger = logger;
        _configuration = configuration;

        _maxLoginAttempts = int.Parse(_configuration["Security:MaxLoginAttempts"] ?? "5");
        _lockoutDurationMinutes = int.Parse(_configuration["Security:LockoutDurationMinutes"] ?? "15");
    }

    public async Task<Result<LoginResponse>> LoginAsync(LoginRequest request, string ipAddress, string userAgent)
    {
        try
        {
            // Find user by username
            var user = await _userRepository.FirstOrDefaultAsync(u => u.Username == request.Username);
            if (user == null)
            {
                _logger.LogWarning("Login attempt with invalid username: {Username}", request.Username);
                return Result.Failure<LoginResponse>("Invalid username or password.");
            }

            // Check if user is locked out
            if (user.IsLockedOut)
            {
                _logger.LogWarning("Login attempt for locked account: {Username}", request.Username);
                return Result.Failure<LoginResponse>("Account is temporarily locked. Please try again later.");
            }

            // Check if user is active
            if (!user.IsActive)
            {
                _logger.LogWarning("Login attempt for inactive account: {Username}", request.Username);
                return Result.Failure<LoginResponse>("Account is inactive. Please contact administrator.");
            }

            // Verify password
            if (!_passwordService.VerifyPassword(request.Password, user.PasswordHash, user.Salt))
            {
                // Increment failed login attempts
                user.FailedLoginAttempts++;
                
                // Lock account if max attempts reached
                if (user.FailedLoginAttempts >= _maxLoginAttempts)
                {
                    user.LockedOutUntil = DateTime.UtcNow.AddMinutes(_lockoutDurationMinutes);
                    _logger.LogWarning("Account locked due to failed login attempts: {Username}", request.Username);
                }

                await _userRepository.UpdateAsync(user);
                return Result.Failure<LoginResponse>("Invalid username or password.");
            }

            // Reset failed login attempts on successful login
            user.FailedLoginAttempts = 0;
            user.LockedOutUntil = null;
            user.LastLoginAt = DateTime.UtcNow;

            // Generate refresh token
            user.RefreshToken = _jwtService.GenerateRefreshToken();
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7); // 7 days

            await _userRepository.UpdateAsync(user);

            // For now, use basic roles - this would be expanded with proper role/permission loading
            var roles = new List<string> { "User" }; // Default role
            var allPermissions = new List<string> { "dashboard:read" }; // Default permissions

            // Generate access token
            var accessToken = _jwtService.GenerateAccessToken(user, roles, allPermissions);

            var userDto = new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                FullName = user.FullName,
                IsActive = user.IsActive,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt ?? user.CreatedAt,
                Roles = new List<RoleDto>(),
                Permissions = new List<PermissionDto>()
            };

            var response = new LoginResponse
            {
                User = userDto,
                AccessToken = accessToken,
                RefreshToken = user.RefreshToken,
                ExpiresAt = DateTime.UtcNow.AddMinutes(15), // Access token expiration
                TokenType = "Bearer"
            };

            _logger.LogInformation("User logged in successfully: {Username}", request.Username);
            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user {Username}", request.Username);
            return Result.Failure<LoginResponse>("An error occurred during login. Please try again.");
        }
    }

    public async Task<Result<RefreshTokenResponse>> RefreshTokenAsync(string refreshToken, string ipAddress)
    {
        try
        {
            var user = await _userRepository.FirstOrDefaultAsync(u => u.RefreshToken == refreshToken);
            if (user == null || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
            {
                _logger.LogWarning("Invalid or expired refresh token used from IP: {IpAddress}", ipAddress);
                return Result.Failure<RefreshTokenResponse>("Invalid or expired refresh token.");
            }

            if (!user.IsActive)
            {
                return Result.Failure<RefreshTokenResponse>("Account is inactive.");
            }

            // For now, use basic roles
            var roles = new List<string> { "User" };
            var allPermissions = new List<string> { "dashboard:read" };

            // Generate new tokens
            var newAccessToken = _jwtService.GenerateAccessToken(user, roles, allPermissions);
            var newRefreshToken = _jwtService.GenerateRefreshToken();

            // Update refresh token
            user.RefreshToken = newRefreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
            await _userRepository.UpdateAsync(user);

            var response = new RefreshTokenResponse
            {
                AccessToken = newAccessToken,
                RefreshToken = newRefreshToken,
                ExpiresAt = DateTime.UtcNow.AddMinutes(15)
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return Result.Failure<RefreshTokenResponse>("An error occurred during token refresh.");
        }
    }

    public async Task<Result> LogoutAsync(int userId, string ipAddress)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return Result.Failure("User not found.");
            }

            // Invalidate refresh token
            user.RefreshToken = null;
            user.RefreshTokenExpiryTime = null;
            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("User logged out: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout for user {UserId}", userId);
            return Result.Failure("An error occurred during logout.");
        }
    }

    public async Task<Result<UserDto>> GetCurrentUserAsync(int userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return Result.Failure<UserDto>("User not found.");
            }

            var userDto = new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                FullName = user.FullName,
                IsActive = user.IsActive,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt ?? user.CreatedAt,
                Roles = new List<RoleDto>(),
                Permissions = new List<PermissionDto>()
            };

            return Result.Success(userDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user {UserId}", userId);
            return Result.Failure<UserDto>("An error occurred while getting user information.");
        }
    }

    public async Task<Result> ValidateSessionAsync(int userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null || !user.IsActive)
            {
                return Result.Failure("Invalid session.");
            }

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating session for user {UserId}", userId);
            return Result.Failure("Session validation failed.");
        }
    }

    public async Task<Result> ChangePasswordAsync(int userId, ChangePasswordRequest request)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return Result.Failure("User not found.");
            }

            // Verify current password
            if (!_passwordService.VerifyPassword(request.CurrentPassword, user.PasswordHash, user.Salt))
            {
                return Result.Failure("Current password is incorrect.");
            }

            // Validate new password strength
            if (!_passwordService.ValidatePasswordStrength(request.NewPassword, out var errors))
            {
                return Result.Failure($"Password does not meet requirements: {string.Join(", ", errors)}");
            }

            // Hash new password
            var newPasswordHash = _passwordService.HashPassword(request.NewPassword, out var newSalt);
            
            user.PasswordHash = newPasswordHash;
            user.Salt = newSalt;
            user.PasswordChangedAt = DateTime.UtcNow;
            
            // Invalidate refresh token to force re-login
            user.RefreshToken = null;
            user.RefreshTokenExpiryTime = null;

            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("Password changed for user {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user {UserId}", userId);
            return Result.Failure("An error occurred while changing password.");
        }
    }

    public async Task<Result> ResetPasswordAsync(ResetPasswordRequest request)
    {
        // Implementation for password reset would go here
        // This would typically involve sending an email with a reset token
        await Task.CompletedTask;
        return Result.Failure("Password reset not implemented yet.");
    }

    public async Task<Result> ConfirmResetPasswordAsync(ConfirmResetPasswordRequest request)
    {
        // Implementation for confirming password reset would go here
        await Task.CompletedTask;
        return Result.Failure("Password reset confirmation not implemented yet.");
    }
}
