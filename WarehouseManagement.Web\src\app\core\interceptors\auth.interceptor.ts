import { Injectable } from '@angular/core';
import { 
  HttpInterceptor, 
  HttpRequest, 
  HttpHandler, 
  HttpEvent, 
  HttpErrorResponse,
  HttpResponse 
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, EMPTY } from 'rxjs';
import { catchError, filter, take, switchMap, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Skip authentication for certain endpoints
    if (this.shouldSkipAuth(request)) {
      return next.handle(request);
    }

    // Add credentials for cookie-based authentication
    const authRequest = request.clone({
      setHeaders: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      withCredentials: true
    });

    return next.handle(authRequest).pipe(
      tap((event: HttpEvent<any>) => {
        // Handle successful responses
        if (event instanceof HttpResponse) {
          this.handleSuccessfulResponse(event);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        return this.handleErrorResponse(error, authRequest, next);
      })
    );
  }

  private shouldSkipAuth(request: HttpRequest<any>): boolean {
    const skipAuthUrls = [
      '/auth/login',
      '/auth/refresh',
      '/auth/logout',
      '/public/',
      '/assets/',
      '.json'
    ];

    return skipAuthUrls.some(url => request.url.includes(url));
  }

  private handleSuccessfulResponse(response: HttpResponse<any>): void {
    // Handle rate limiting headers
    if (response.headers.has('X-RateLimit-Remaining')) {
      const remaining = parseInt(response.headers.get('X-RateLimit-Remaining') || '0', 10);
      const limit = parseInt(response.headers.get('X-RateLimit-Limit') || '0', 10);
      const resetTime = response.headers.get('X-RateLimit-Reset');

      if (remaining < limit * 0.1) { // Less than 10% remaining
        console.warn(`Rate limit warning: ${remaining}/${limit} requests remaining`);
      }
    }

    // Handle CSRF token updates
    if (response.headers.has('X-CSRF-Token')) {
      const csrfToken = response.headers.get('X-CSRF-Token');
      if (csrfToken) {
        // Store CSRF token for future requests
        sessionStorage.setItem('csrf-token', csrfToken);
      }
    }
  }

  private handleErrorResponse(
    error: HttpErrorResponse, 
    request: HttpRequest<any>, 
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    
    // Handle different error status codes
    switch (error.status) {
      case 401:
        return this.handle401Error(request, next);
      
      case 403:
        return this.handle403Error(error);
      
      case 429:
        return this.handle429Error(error);
      
      case 419: // CSRF token mismatch
        return this.handle419Error(error);
      
      default:
        return this.handleGenericError(error);
    }
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.authService.refreshToken().pipe(
        switchMap(() => {
          this.isRefreshing = false;
          this.refreshTokenSubject.next(true);
          
          // Retry the original request
          return next.handle(request);
        }),
        catchError((refreshError) => {
          this.isRefreshing = false;
          this.refreshTokenSubject.next(null);
          
          // Refresh failed, redirect to login
          this.authService.logout().subscribe();
          this.router.navigate(['/login']);
          
          return throwError(() => refreshError);
        })
      );
    } else {
      // Wait for refresh to complete
      return this.refreshTokenSubject.pipe(
        filter(result => result !== null),
        take(1),
        switchMap(() => next.handle(request))
      );
    }
  }

  private handle403Error(error: HttpErrorResponse): Observable<HttpEvent<any>> {
    // Forbidden - user doesn't have permission
    console.error('Access forbidden:', error.message);
    
    // Don't redirect automatically, let the component handle it
    // The route guards should prevent most 403 errors
    
    return throwError(() => error);
  }

  private handle429Error(error: HttpErrorResponse): Observable<HttpEvent<any>> {
    // Rate limit exceeded
    const retryAfter = error.headers.get('Retry-After');
    const retryAfterMs = retryAfter ? parseInt(retryAfter, 10) * 1000 : 5000;
    
    console.warn(`Rate limit exceeded. Retry after ${retryAfterMs}ms`);
    
    // You could implement automatic retry with exponential backoff here
    // For now, just pass the error through
    
    return throwError(() => error);
  }

  private handle419Error(error: HttpErrorResponse): Observable<HttpEvent<any>> {
    // CSRF token mismatch - clear stored token and let user retry
    sessionStorage.removeItem('csrf-token');
    
    console.error('CSRF token mismatch:', error.message);
    
    return throwError(() => error);
  }

  private handleGenericError(error: HttpErrorResponse): Observable<HttpEvent<any>> {
    // Log error for debugging
    console.error('HTTP Error:', {
      status: error.status,
      message: error.message,
      url: error.url,
      error: error.error
    });

    // Handle network errors
    if (error.status === 0) {
      console.error('Network error - check your internet connection');
    }

    // Handle server errors
    if (error.status >= 500) {
      console.error('Server error - please try again later');
    }

    return throwError(() => error);
  }
}

@Injectable()
export class CsrfInterceptor implements HttpInterceptor {
  
  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Only add CSRF token to state-changing requests
    if (this.shouldAddCsrfToken(request)) {
      const csrfToken = sessionStorage.getItem('csrf-token');
      
      if (csrfToken) {
        const csrfRequest = request.clone({
          setHeaders: {
            'X-CSRF-Token': csrfToken
          }
        });
        
        return next.handle(csrfRequest);
      }
    }

    return next.handle(request);
  }

  private shouldAddCsrfToken(request: HttpRequest<any>): boolean {
    // Add CSRF token to POST, PUT, PATCH, DELETE requests
    const stateMutatingMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];
    return stateMutatingMethods.includes(request.method.toUpperCase());
  }
}

@Injectable()
export class SecurityHeadersInterceptor implements HttpInterceptor {
  
  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Add security headers to all requests
    const secureRequest = request.clone({
      setHeaders: {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    });

    return next.handle(secureRequest);
  }
}
