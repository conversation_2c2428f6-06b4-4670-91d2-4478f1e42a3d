import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { AuthService } from '../../../core/services/auth.service';
import { LanguageService } from '../../../core/services/language.service';
import { LoginRequest } from '../../../core/models/auth.models';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit, OnDestroy {
  loginForm: FormGroup;
  loading = false;
  returnUrl = '/dashboard';
  showPassword = false;
  loginAttempts = 0;
  maxLoginAttempts = 5;
  lockoutTime = 15; // minutes
  isLockedOut = false;
  lockoutEndTime?: Date;
  
  private destroy$ = new Subject<void>();

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    public languageService: LanguageService,
    private router: Router,
    private route: ActivatedRoute,
    private messageService: MessageService
  ) {
    this.loginForm = this.createLoginForm();
  }

  ngOnInit(): void {
    // Get return URL from route parameters or default to dashboard
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
    
    // Check if user is already authenticated
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authState => {
        if (authState.isAuthenticated) {
          this.router.navigate([this.returnUrl]);
        }
        
        if (authState.error) {
          this.handleLoginError(authState.error);
        }
        
        this.loading = authState.loading;
      });

    // Check for lockout status
    this.checkLockoutStatus();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createLoginForm(): FormGroup {
    return this.formBuilder.group({
      username: ['', [
        Validators.required,
        Validators.minLength(3),
        Validators.maxLength(50)
      ]],
      password: ['', [
        Validators.required,
        Validators.minLength(6)
      ]],
      rememberMe: [false]
    });
  }

  onSubmit(): void {
    if (this.loginForm.invalid || this.loading || this.isLockedOut) {
      this.markFormGroupTouched();
      return;
    }

    this.loading = true;
    const loginRequest: LoginRequest = {
      username: this.loginForm.value.username.trim(),
      password: this.loginForm.value.password,
      rememberMe: this.loginForm.value.rememberMe
    };

    this.authService.login(loginRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loginAttempts = 0;
          this.clearLockoutStatus();
          
          this.messageService.add({
            severity: 'success',
            summary: this.languageService.translate('auth.login.success'),
            detail: this.languageService.translate('auth.login.welcomeBack', { 
              name: response.user.firstName 
            })
          });

          // Navigate to return URL or dashboard
          this.router.navigate([this.returnUrl]);
        },
        error: (error) => {
          this.handleLoginError(error);
        }
      });
  }

  private handleLoginError(error: any): void {
    this.loading = false;
    this.loginAttempts++;

    let errorMessage = this.languageService.translate('auth.login.error.generic');
    
    if (error?.status === 401) {
      errorMessage = this.languageService.translate('auth.login.error.invalidCredentials');
    } else if (error?.status === 423) {
      errorMessage = this.languageService.translate('auth.login.error.accountLocked');
      this.handleAccountLockout();
    } else if (error?.status === 429) {
      errorMessage = this.languageService.translate('auth.login.error.tooManyAttempts');
      this.handleRateLimitExceeded();
    } else if (error?.message) {
      errorMessage = error.message;
    }

    this.messageService.add({
      severity: 'error',
      summary: this.languageService.translate('auth.login.error.title'),
      detail: errorMessage
    });

    // Check if we should lock out the user
    if (this.loginAttempts >= this.maxLoginAttempts) {
      this.handleAccountLockout();
    }

    // Clear password field on error
    this.loginForm.patchValue({ password: '' });
  }

  private handleAccountLockout(): void {
    this.isLockedOut = true;
    this.lockoutEndTime = new Date(Date.now() + this.lockoutTime * 60 * 1000);
    
    // Store lockout info in localStorage
    localStorage.setItem('lockoutEndTime', this.lockoutEndTime.toISOString());
    localStorage.setItem('loginAttempts', this.loginAttempts.toString());

    this.messageService.add({
      severity: 'warn',
      summary: this.languageService.translate('auth.login.lockout.title'),
      detail: this.languageService.translate('auth.login.lockout.message', { 
        minutes: this.lockoutTime 
      }),
      life: 10000
    });

    // Start countdown timer
    this.startLockoutTimer();
  }

  private handleRateLimitExceeded(): void {
    this.messageService.add({
      severity: 'warn',
      summary: this.languageService.translate('auth.login.rateLimit.title'),
      detail: this.languageService.translate('auth.login.rateLimit.message'),
      life: 8000
    });
  }

  private checkLockoutStatus(): void {
    const lockoutEndTimeStr = localStorage.getItem('lockoutEndTime');
    const storedAttempts = localStorage.getItem('loginAttempts');

    if (lockoutEndTimeStr && storedAttempts) {
      const lockoutEndTime = new Date(lockoutEndTimeStr);
      const now = new Date();

      if (now < lockoutEndTime) {
        this.isLockedOut = true;
        this.lockoutEndTime = lockoutEndTime;
        this.loginAttempts = parseInt(storedAttempts, 10);
        this.startLockoutTimer();
      } else {
        this.clearLockoutStatus();
      }
    }
  }

  private startLockoutTimer(): void {
    if (!this.lockoutEndTime) return;

    const checkLockout = () => {
      const now = new Date();
      if (this.lockoutEndTime && now >= this.lockoutEndTime) {
        this.clearLockoutStatus();
        this.messageService.add({
          severity: 'info',
          summary: this.languageService.translate('auth.login.lockout.expired'),
          detail: this.languageService.translate('auth.login.lockout.canTryAgain')
        });
      } else {
        setTimeout(checkLockout, 1000);
      }
    };

    setTimeout(checkLockout, 1000);
  }

  private clearLockoutStatus(): void {
    this.isLockedOut = false;
    this.lockoutEndTime = undefined;
    this.loginAttempts = 0;
    localStorage.removeItem('lockoutEndTime');
    localStorage.removeItem('loginAttempts');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  switchLanguage(): void {
    this.languageService.toggleLanguage();
  }

  // Getter methods for template
  get username() { return this.loginForm.get('username'); }
  get password() { return this.loginForm.get('password'); }
  get rememberMe() { return this.loginForm.get('rememberMe'); }

  get remainingLockoutTime(): string {
    if (!this.lockoutEndTime) return '';
    
    const now = new Date();
    const remaining = Math.max(0, this.lockoutEndTime.getTime() - now.getTime());
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  get remainingAttempts(): number {
    return Math.max(0, this.maxLoginAttempts - this.loginAttempts);
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (!field || !field.errors) return '';

    const errors = field.errors;
    
    if (errors['required']) {
      return this.languageService.translate(`auth.login.validation.${fieldName}.required`);
    }
    if (errors['minlength']) {
      return this.languageService.translate(`auth.login.validation.${fieldName}.minLength`, {
        min: errors['minlength'].requiredLength
      });
    }
    if (errors['maxlength']) {
      return this.languageService.translate(`auth.login.validation.${fieldName}.maxLength`, {
        max: errors['maxlength'].requiredLength
      });
    }

    return this.languageService.translate('auth.login.validation.invalid');
  }
}
