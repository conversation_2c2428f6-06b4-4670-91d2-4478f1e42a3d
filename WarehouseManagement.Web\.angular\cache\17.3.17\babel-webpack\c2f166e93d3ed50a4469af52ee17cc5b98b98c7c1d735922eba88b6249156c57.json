{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nconst _c0 = [\"element\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"*\"];\nconst _c3 = (a0, a1, a2) => ({\n  \"p-scroller\": true,\n  \"p-scroller-inline\": a0,\n  \"p-both-scroll\": a1,\n  \"p-horizontal-scroll\": a2\n});\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c5 = a0 => ({\n  \"p-scroller-loading\": a0\n});\nconst _c6 = a0 => ({\n  \"p-component-overlay\": a0\n});\nconst _c7 = a0 => ({\n  numCols: a0\n});\nconst _c8 = a0 => ({\n  options: a0\n});\nconst _c9 = () => ({\n  styleClass: \"p-scroller-loading-icon\"\n});\nconst _c10 = (a0, a1) => ({\n  rows: a0,\n  columns: a1\n});\nfunction Scroller_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, ctx_r1.loadedItems, ctx_r1.getContentOptions()));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const index_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, item_r3, ctx_r1.getOptions(index_r4)));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_ng_template_4_ng_container_2_Template, 2, 5, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c5, ctx_r1.d_loading))(\"ngStyle\", ctx_r1.contentStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loadedItems)(\"ngForTrackBy\", ctx_r1._trackBy || ctx_r1.index);\n  }\n}\nfunction Scroller_ng_container_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.spacerStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"spacer\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c8, ctx_r1.getLoaderOptions(index_r5, ctx_r1.both && i0.ɵɵpureFunction1(2, _c7, ctx_r1._numItemsInViewport.cols))));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template, 2, 6, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loaderArr);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c8, i0.ɵɵpureFunction0(2, _c9)));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-scroller-loading-icon pi-spin\");\n    i0.ɵɵattribute(\"data-pc-section\", \"loadingIcon\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template, 2, 5, \"ng-container\", 6)(1, Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template, 1, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const buildInLoaderIcon_r6 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderIconTemplate)(\"ngIfElse\", buildInLoaderIcon_r6);\n  }\n}\nfunction Scroller_ng_container_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_Template, 2, 1, \"ng-container\", 6)(2, Scroller_ng_container_0_div_7_ng_template_2_Template, 3, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildInLoader_r7 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c6, !ctx_r1.loaderTemplate));\n    i0.ɵɵattribute(\"data-pc-section\", \"loader\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderTemplate)(\"ngIfElse\", buildInLoader_r7);\n  }\n}\nfunction Scroller_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7, 1);\n    i0.ɵɵlistener(\"scroll\", function Scroller_ng_container_0_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onContainerScroll($event));\n    });\n    i0.ɵɵtemplate(3, Scroller_ng_container_0_ng_container_3_Template, 2, 5, \"ng-container\", 6)(4, Scroller_ng_container_0_ng_template_4_Template, 3, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, Scroller_ng_container_0_div_6_Template, 1, 2, \"div\", 8)(7, Scroller_ng_container_0_div_7_Template, 4, 6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const buildInContent_r8 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1._styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1._style)(\"ngClass\", i0.ɵɵpureFunction3(12, _c3, ctx_r1.inline, ctx_r1.both, ctx_r1.horizontal));\n    i0.ɵɵattribute(\"id\", ctx_r1._id)(\"tabindex\", ctx_r1.tabindex)(\"data-pc-name\", \"scroller\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate)(\"ngIfElse\", buildInContent_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._showSpacer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loaderDisabled && ctx_r1._showLoader && ctx_r1.d_loading);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(5, _c4, ctx_r1.items, i0.ɵɵpureFunction2(2, _c10, ctx_r1._items, ctx_r1.loadedColumns)));\n  }\n}\nfunction Scroller_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_Template, 2, 8, \"ng-container\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate);\n  }\n}\nclass Scroller {\n  document;\n  platformId;\n  renderer;\n  cd;\n  zone;\n  /**\n   * Unique identifier of the element.\n   * @group Props\n   */\n  get id() {\n    return this._id;\n  }\n  set id(val) {\n    this._id = val;\n  }\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  get style() {\n    return this._style;\n  }\n  set style(val) {\n    this._style = val;\n  }\n  /**\n   * Style class of the element.\n   * @group Props\n   */\n  get styleClass() {\n    return this._styleClass;\n  }\n  set styleClass(val) {\n    this._styleClass = val;\n  }\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  get tabindex() {\n    return this._tabindex;\n  }\n  set tabindex(val) {\n    this._tabindex = val;\n  }\n  /**\n   * An array of objects to display.\n   * @group Props\n   */\n  get items() {\n    return this._items;\n  }\n  set items(val) {\n    this._items = val;\n  }\n  /**\n   * The height/width of item according to orientation.\n   * @group Props\n   */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(val) {\n    this._itemSize = val;\n  }\n  /**\n   * Height of the scroll viewport.\n   * @group Props\n   */\n  get scrollHeight() {\n    return this._scrollHeight;\n  }\n  set scrollHeight(val) {\n    this._scrollHeight = val;\n  }\n  /**\n   * Width of the scroll viewport.\n   * @group Props\n   */\n  get scrollWidth() {\n    return this._scrollWidth;\n  }\n  set scrollWidth(val) {\n    this._scrollWidth = val;\n  }\n  /**\n   * The orientation of scrollbar.\n   * @group Props\n   */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(val) {\n    this._orientation = val;\n  }\n  /**\n   * Used to specify how many items to load in each load method in lazy mode.\n   * @group Props\n   */\n  get step() {\n    return this._step;\n  }\n  set step(val) {\n    this._step = val;\n  }\n  /**\n   * Delay in scroll before new data is loaded.\n   * @group Props\n   */\n  get delay() {\n    return this._delay;\n  }\n  set delay(val) {\n    this._delay = val;\n  }\n  /**\n   * Delay after window's resize finishes.\n   * @group Props\n   */\n  get resizeDelay() {\n    return this._resizeDelay;\n  }\n  set resizeDelay(val) {\n    this._resizeDelay = val;\n  }\n  /**\n   * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n   * @group Props\n   */\n  get appendOnly() {\n    return this._appendOnly;\n  }\n  set appendOnly(val) {\n    this._appendOnly = val;\n  }\n  /**\n   * Specifies whether the scroller should be displayed inline or not.\n   * @group Props\n   */\n  get inline() {\n    return this._inline;\n  }\n  set inline(val) {\n    this._inline = val;\n  }\n  /**\n   * Defines if data is loaded and interacted with in lazy manner.\n   * @group Props\n   */\n  get lazy() {\n    return this._lazy;\n  }\n  set lazy(val) {\n    this._lazy = val;\n  }\n  /**\n   * If disabled, the scroller feature is eliminated and the content is displayed directly.\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(val) {\n    this._disabled = val;\n  }\n  /**\n   * Used to implement a custom loader instead of using the loader feature in the scroller.\n   * @group Props\n   */\n  get loaderDisabled() {\n    return this._loaderDisabled;\n  }\n  set loaderDisabled(val) {\n    this._loaderDisabled = val;\n  }\n  /**\n   * Columns to display.\n   * @group Props\n   */\n  get columns() {\n    return this._columns;\n  }\n  set columns(val) {\n    this._columns = val;\n  }\n  /**\n   * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n   * @group Props\n   */\n  get showSpacer() {\n    return this._showSpacer;\n  }\n  set showSpacer(val) {\n    this._showSpacer = val;\n  }\n  /**\n   * Defines whether to show loader.\n   * @group Props\n   */\n  get showLoader() {\n    return this._showLoader;\n  }\n  set showLoader(val) {\n    this._showLoader = val;\n  }\n  /**\n   * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n   * @group Props\n   */\n  get numToleratedItems() {\n    return this._numToleratedItems;\n  }\n  set numToleratedItems(val) {\n    this._numToleratedItems = val;\n  }\n  /**\n   * Defines whether the data is loaded.\n   * @group Props\n   */\n  get loading() {\n    return this._loading;\n  }\n  set loading(val) {\n    this._loading = val;\n  }\n  /**\n   * Defines whether to dynamically change the height or width of scrollable container.\n   * @group Props\n   */\n  get autoSize() {\n    return this._autoSize;\n  }\n  set autoSize(val) {\n    this._autoSize = val;\n  }\n  /**\n   * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n   * @group Props\n   */\n  get trackBy() {\n    return this._trackBy;\n  }\n  set trackBy(val) {\n    this._trackBy = val;\n  }\n  /**\n   * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n   * @group Props\n   */\n  get options() {\n    return this._options;\n  }\n  set options(val) {\n    this._options = val;\n    if (val && typeof val === 'object') {\n      //@ts-ignore\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n    }\n  }\n  /**\n   * Callback to invoke in lazy mode to load new data.\n   * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n   * @group Emits\n   */\n  onLazyLoad = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll event.\n   * @group Emits\n   */\n  onScroll = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position and item's range in view changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n   * @group Emits\n   */\n  onScrollIndexChange = new EventEmitter();\n  elementViewChild;\n  contentViewChild;\n  templates;\n  _id;\n  _style;\n  _styleClass;\n  _tabindex = 0;\n  _items;\n  _itemSize = 0;\n  _scrollHeight;\n  _scrollWidth;\n  _orientation = 'vertical';\n  _step = 0;\n  _delay = 0;\n  _resizeDelay = 10;\n  _appendOnly = false;\n  _inline = false;\n  _lazy = false;\n  _disabled = false;\n  _loaderDisabled = false;\n  _columns;\n  _showSpacer = true;\n  _showLoader = false;\n  _numToleratedItems;\n  _loading;\n  _autoSize = false;\n  _trackBy;\n  _options;\n  d_loading = false;\n  d_numToleratedItems;\n  contentEl;\n  contentTemplate;\n  itemTemplate;\n  loaderTemplate;\n  loaderIconTemplate;\n  first = 0;\n  last = 0;\n  page = 0;\n  isRangeChanged = false;\n  numItemsInViewport = 0;\n  lastScrollPos = 0;\n  lazyLoadState = {};\n  loaderArr = [];\n  spacerStyle = {};\n  contentStyle = {};\n  scrollTimeout;\n  resizeTimeout;\n  initialized = false;\n  resizeObserver;\n  defaultWidth;\n  defaultHeight;\n  defaultContentWidth;\n  defaultContentHeight;\n  get vertical() {\n    return this._orientation === 'vertical';\n  }\n  get horizontal() {\n    return this._orientation === 'horizontal';\n  }\n  get both() {\n    return this._orientation === 'both';\n  }\n  get loadedItems() {\n    if (this._items && !this.d_loading) {\n      if (this.both) return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map(item => this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols));else if (this.horizontal && this._columns) return this._items;else return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n    }\n    return [];\n  }\n  get loadedRows() {\n    return this.d_loading ? this._loaderDisabled ? this.loaderArr : [] : this.loadedItems;\n  }\n  get loadedColumns() {\n    if (this._columns && (this.both || this.horizontal)) {\n      return this.d_loading && this._loaderDisabled ? this.both ? this.loaderArr[0] : this.loaderArr : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n    }\n    return this._columns;\n  }\n  constructor(document, platformId, renderer, cd, zone) {\n    this.document = document;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.zone = zone;\n  }\n  ngOnInit() {\n    this.setInitialState();\n  }\n  ngOnChanges(simpleChanges) {\n    let isLoadingChanged = false;\n    if (simpleChanges.loading) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.loading;\n      if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n        this.d_loading = currentValue;\n        isLoadingChanged = true;\n      }\n    }\n    if (simpleChanges.orientation) {\n      this.lastScrollPos = this.both ? {\n        top: 0,\n        left: 0\n      } : 0;\n    }\n    if (simpleChanges.numToleratedItems) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.numToleratedItems;\n      if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue;\n      }\n    }\n    if (simpleChanges.options) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.options;\n      if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n        this.d_loading = currentValue.loading;\n        isLoadingChanged = true;\n      }\n      if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue.numToleratedItems;\n      }\n    }\n    if (this.initialized) {\n      const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n      if (isChanged) {\n        this.init();\n        this.calculateAutoSize();\n      }\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'loader':\n          this.loaderTemplate = item.template;\n          break;\n        case 'loadericon':\n          this.loaderIconTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    Promise.resolve().then(() => {\n      this.viewInit();\n    });\n  }\n  ngAfterViewChecked() {\n    if (!this.initialized) {\n      this.viewInit();\n    }\n  }\n  ngOnDestroy() {\n    this.unbindResizeListener();\n    this.contentEl = null;\n    this.initialized = false;\n  }\n  viewInit() {\n    if (isPlatformBrowser(this.platformId) && !this.initialized) {\n      if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n        this.setInitialState();\n        this.setContentEl(this.contentEl);\n        this.init();\n        this.calculateAutoSize();\n        this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n        this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n        this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n        this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n        this.resizeObserver = new ResizeObserver(() => this.onResize());\n        this.resizeObserver.observe(this.elementViewChild?.nativeElement);\n        this.initialized = true;\n      }\n    }\n  }\n  init() {\n    if (!this._disabled) {\n      this.setSize();\n      this.calculateOptions();\n      this.setSpacerSize();\n      this.cd.detectChanges();\n    }\n  }\n  setContentEl(el) {\n    this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n  }\n  setInitialState() {\n    this.first = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.last = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.numItemsInViewport = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    this.d_loading = this._loading || false;\n    this.d_numToleratedItems = this._numToleratedItems;\n    this.loaderArr = [];\n    this.spacerStyle = {};\n    this.contentStyle = {};\n  }\n  getElementRef() {\n    return this.elementViewChild;\n  }\n  getPageByFirst(first) {\n    return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this._step || 1));\n  }\n  isPageChanged(first) {\n    return this._step ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n  }\n  scrollTo(options) {\n    // this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n    this.elementViewChild?.nativeElement?.scrollTo(options);\n  }\n  scrollToIndex(index, behavior = 'auto') {\n    const valid = this.both ? index.every(i => i > -1) : index > -1;\n    if (valid) {\n      const first = this.first;\n      const {\n        scrollTop = 0,\n        scrollLeft = 0\n      } = this.elementViewChild?.nativeElement;\n      const {\n        numToleratedItems\n      } = this.calculateNumItems();\n      const contentPos = this.getContentPosition();\n      const itemSize = this.itemSize;\n      const calculateFirst = (_index = 0, _numT) => _index <= _numT ? 0 : _index;\n      const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n      let newFirst = this.both ? {\n        rows: 0,\n        cols: 0\n      } : 0;\n      let isRangeChanged = false,\n        isScrollChanged = false;\n      if (this.both) {\n        newFirst = {\n          rows: calculateFirst(index[0], numToleratedItems[0]),\n          cols: calculateFirst(index[1], numToleratedItems[1])\n        };\n        scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n        isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n        isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n      } else {\n        newFirst = calculateFirst(index, numToleratedItems);\n        this.horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n        isScrollChanged = this.lastScrollPos !== (this.horizontal ? scrollLeft : scrollTop);\n        isRangeChanged = newFirst !== first;\n      }\n      this.isRangeChanged = isRangeChanged;\n      isScrollChanged && (this.first = newFirst);\n    }\n  }\n  scrollInView(index, to, behavior = 'auto') {\n    if (to) {\n      const {\n        first,\n        viewport\n      } = this.getRenderedRange();\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n      const isToStart = to === 'to-start';\n      const isToEnd = to === 'to-end';\n      if (isToStart) {\n        if (this.both) {\n          if (viewport.first.rows - first.rows > index[0]) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n          } else if (viewport.first.cols - first.cols > index[1]) {\n            scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.first - first > index) {\n            const pos = (viewport.first - 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      } else if (isToEnd) {\n        if (this.both) {\n          if (viewport.last.rows - first.rows <= index[0] + 1) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n          } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n            scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.last - first <= index + 1) {\n            const pos = (viewport.first + 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      }\n    } else {\n      this.scrollToIndex(index, behavior);\n    }\n  }\n  getRenderedRange() {\n    const calculateFirstInViewport = (_pos, _size) => _size || _pos ? Math.floor(_pos / (_size || _pos)) : 0;\n    let firstInViewport = this.first;\n    let lastInViewport = 0;\n    if (this.elementViewChild?.nativeElement) {\n      const {\n        scrollTop,\n        scrollLeft\n      } = this.elementViewChild.nativeElement;\n      if (this.both) {\n        firstInViewport = {\n          rows: calculateFirstInViewport(scrollTop, this._itemSize[0]),\n          cols: calculateFirstInViewport(scrollLeft, this._itemSize[1])\n        };\n        lastInViewport = {\n          rows: firstInViewport.rows + this.numItemsInViewport.rows,\n          cols: firstInViewport.cols + this.numItemsInViewport.cols\n        };\n      } else {\n        const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n        firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n        lastInViewport = firstInViewport + this.numItemsInViewport;\n      }\n    }\n    return {\n      first: this.first,\n      last: this.last,\n      viewport: {\n        first: firstInViewport,\n        last: lastInViewport\n      }\n    };\n  }\n  calculateNumItems() {\n    const contentPos = this.getContentPosition();\n    const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n    const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n    const calculateNumItemsInViewport = (_contentSize, _itemSize) => _itemSize || _contentSize ? Math.ceil(_contentSize / (_itemSize || _contentSize)) : 0;\n    const calculateNumToleratedItems = _numItems => Math.ceil(_numItems / 2);\n    const numItemsInViewport = this.both ? {\n      rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]),\n      cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1])\n    } : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n    const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n    return {\n      numItemsInViewport,\n      numToleratedItems\n    };\n  }\n  calculateOptions() {\n    const {\n      numItemsInViewport,\n      numToleratedItems\n    } = this.calculateNumItems();\n    const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n    const first = this.first;\n    const last = this.both ? {\n      rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]),\n      cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n    } : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n    this.last = last;\n    this.numItemsInViewport = numItemsInViewport;\n    this.d_numToleratedItems = numToleratedItems;\n    if (this.showLoader) {\n      this.loaderArr = this.both ? Array.from({\n        length: numItemsInViewport.rows\n      }).map(() => Array.from({\n        length: numItemsInViewport.cols\n      })) : Array.from({\n        length: numItemsInViewport\n      });\n    }\n    if (this._lazy) {\n      Promise.resolve().then(() => {\n        this.lazyLoadState = {\n          first: this._step ? this.both ? {\n            rows: 0,\n            cols: first.cols\n          } : 0 : first,\n          last: Math.min(this._step ? this._step : this.last, this.items.length)\n        };\n        this.handleEvents('onLazyLoad', this.lazyLoadState);\n      });\n    }\n  }\n  calculateAutoSize() {\n    if (this._autoSize && !this.d_loading) {\n      Promise.resolve().then(() => {\n        if (this.contentEl) {\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n          this.contentEl.style.position = 'relative';\n          this.elementViewChild.nativeElement.style.contain = 'none';\n          const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n          contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n          contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n          const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n          this.contentEl.style.position = '';\n          this.elementViewChild.nativeElement.style.contain = '';\n          this.defaultWidth = width;\n          this.defaultHeight = height;\n          this.defaultContentWidth = contentWidth;\n          this.defaultContentHeight = contentHeight;\n          (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n          (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n        }\n      });\n    }\n  }\n  getLast(last = 0, isCols = false) {\n    return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n  }\n  getContentPosition() {\n    if (this.contentEl) {\n      const style = getComputedStyle(this.contentEl);\n      const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n      const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n      const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n      const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n      return {\n        left,\n        right,\n        top,\n        bottom,\n        x: left + right,\n        y: top + bottom\n      };\n    }\n    return {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      x: 0,\n      y: 0\n    };\n  }\n  setSize() {\n    if (this.elementViewChild?.nativeElement) {\n      const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n      const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n      const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n      const setProp = (_name, _value) => this.elementViewChild.nativeElement.style[_name] = _value;\n      if (this.both || this.horizontal) {\n        setProp('height', height);\n        setProp('width', width);\n      } else {\n        setProp('height', height);\n      }\n    }\n  }\n  setSpacerSize() {\n    if (this._items) {\n      const setProp = (_name, _count, _size) => this.spacerStyle = {\n        ...this.spacerStyle,\n        ...{\n          [`${_name}`]: _count * _size + 'px'\n        }\n      };\n      const numItems = this._items.length;\n      if (this.both) {\n        setProp('height', numItems, this._itemSize[0]);\n        setProp('width', this._columns?.length || this._items[1]?.length, this._itemSize[1]);\n      } else {\n        this.horizontal ? setProp('width', this._columns?.length || this._items.length, this._itemSize) : setProp('height', numItems, this._itemSize);\n      }\n    }\n  }\n  setContentPosition(pos) {\n    if (this.contentEl && !this._appendOnly) {\n      const first = pos ? pos.first : this.first;\n      const calculateTranslateVal = (_first, _size) => _first * _size;\n      const setTransform = (_x = 0, _y = 0) => this.contentStyle = {\n        ...this.contentStyle,\n        ...{\n          transform: `translate3d(${_x}px, ${_y}px, 0)`\n        }\n      };\n      if (this.both) {\n        setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n      } else {\n        const translateVal = calculateTranslateVal(first, this._itemSize);\n        this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n      }\n    }\n  }\n  onScrollPositionChange(event) {\n    const target = event.target;\n    const contentPos = this.getContentPosition();\n    const calculateScrollPos = (_pos, _cpos) => _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n    const calculateCurrentIndex = (_pos, _size) => _size || _pos ? Math.floor(_pos / (_size || _pos)) : 0;\n    const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n    };\n    const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      if (_currentIndex <= _numT) return 0;else return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n    };\n    const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n      let lastValue = _first + _num + 2 * _numT;\n      if (_currentIndex >= _numT) {\n        lastValue += _numT + 1;\n      }\n      return this.getLast(lastValue, _isCols);\n    };\n    const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n    const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n    let newFirst = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    let newLast = this.last;\n    let isRangeChanged = false;\n    let newScrollPos = this.lastScrollPos;\n    if (this.both) {\n      const isScrollDown = this.lastScrollPos.top <= scrollTop;\n      const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n      if (!this._appendOnly || this._appendOnly && (isScrollDown || isScrollRight)) {\n        const currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, this._itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, this._itemSize[1])\n        };\n        const triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n        };\n        isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n        newScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      }\n    } else {\n      const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n      const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n      if (!this._appendOnly || this._appendOnly && isScrollDownOrRight) {\n        const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n        const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n        isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n        newScrollPos = scrollPos;\n      }\n    }\n    return {\n      first: newFirst,\n      last: newLast,\n      isRangeChanged,\n      scrollPos: newScrollPos\n    };\n  }\n  onScrollChange(event) {\n    const {\n      first,\n      last,\n      isRangeChanged,\n      scrollPos\n    } = this.onScrollPositionChange(event);\n    if (isRangeChanged) {\n      const newState = {\n        first,\n        last\n      };\n      this.setContentPosition(newState);\n      this.first = first;\n      this.last = last;\n      this.lastScrollPos = scrollPos;\n      this.handleEvents('onScrollIndexChange', newState);\n      if (this._lazy && this.isPageChanged(first)) {\n        const lazyLoadState = {\n          first: this._step ? Math.min(this.getPageByFirst(first) * this._step, this.items.length - this._step) : first,\n          last: Math.min(this._step ? (this.getPageByFirst(first) + 1) * this._step : last, this.items.length)\n        };\n        const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n        isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n        this.lazyLoadState = lazyLoadState;\n      }\n    }\n  }\n  onContainerScroll(event) {\n    this.handleEvents('onScroll', {\n      originalEvent: event\n    });\n    if (this._delay && this.isPageChanged()) {\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n      if (!this.d_loading && this.showLoader) {\n        const {\n          isRangeChanged\n        } = this.onScrollPositionChange(event);\n        const changed = isRangeChanged || (this._step ? this.isPageChanged() : false);\n        if (changed) {\n          this.d_loading = true;\n          this.cd.detectChanges();\n        }\n      }\n      this.scrollTimeout = setTimeout(() => {\n        this.onScrollChange(event);\n        if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n          this.d_loading = false;\n          this.page = this.getPageByFirst();\n          this.cd.detectChanges();\n        }\n      }, this._delay);\n    } else {\n      !this.d_loading && this.onScrollChange(event);\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeObserver) {\n      this.resizeObserver.unobserve(this.elementViewChild?.nativeElement);\n      this.resizeObserver = null;\n    }\n  }\n  onResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n    }\n    this.resizeTimeout = setTimeout(() => {\n      if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n        const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n        const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n        const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n        reinit && this.zone.run(() => {\n          this.d_numToleratedItems = this._numToleratedItems;\n          this.init();\n          this.calculateAutoSize();\n        });\n      }\n    }, this._resizeDelay);\n  }\n  handleEvents(name, params) {\n    //@ts-ignore\n    return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n  }\n  getContentOptions() {\n    return {\n      contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n      items: this.loadedItems,\n      getItemOptions: index => this.getOptions(index),\n      loading: this.d_loading,\n      getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n      itemSize: this._itemSize,\n      rows: this.loadedRows,\n      columns: this.loadedColumns,\n      spacerStyle: this.spacerStyle,\n      contentStyle: this.contentStyle,\n      vertical: this.vertical,\n      horizontal: this.horizontal,\n      both: this.both\n    };\n  }\n  getOptions(renderedIndex) {\n    const count = (this._items || []).length;\n    const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0\n    };\n  }\n  getLoaderOptions(index, extOptions) {\n    const count = this.loaderArr.length;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      ...extOptions\n    };\n  }\n  static ɵfac = function Scroller_Factory(t) {\n    return new (t || Scroller)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Scroller,\n    selectors: [[\"p-scroller\"]],\n    contentQueries: function Scroller_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Scroller_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-scroller-viewport\", \"p-element\"],\n    inputs: {\n      id: \"id\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      tabindex: \"tabindex\",\n      items: \"items\",\n      itemSize: \"itemSize\",\n      scrollHeight: \"scrollHeight\",\n      scrollWidth: \"scrollWidth\",\n      orientation: \"orientation\",\n      step: \"step\",\n      delay: \"delay\",\n      resizeDelay: \"resizeDelay\",\n      appendOnly: \"appendOnly\",\n      inline: \"inline\",\n      lazy: \"lazy\",\n      disabled: \"disabled\",\n      loaderDisabled: \"loaderDisabled\",\n      columns: \"columns\",\n      showSpacer: \"showSpacer\",\n      showLoader: \"showLoader\",\n      numToleratedItems: \"numToleratedItems\",\n      loading: \"loading\",\n      autoSize: \"autoSize\",\n      trackBy: \"trackBy\",\n      options: \"options\"\n    },\n    outputs: {\n      onLazyLoad: \"onLazyLoad\",\n      onScroll: \"onScroll\",\n      onScrollIndexChange: \"onScrollIndexChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 3,\n    vars: 2,\n    consts: [[\"disabledContainer\", \"\"], [\"element\", \"\"], [\"buildInContent\", \"\"], [\"content\", \"\"], [\"buildInLoader\", \"\"], [\"buildInLoaderIcon\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"scroll\", \"ngStyle\", \"ngClass\"], [\"class\", \"p-scroller-spacer\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-scroller-loader\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-scroller-content\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"p-scroller-spacer\", 3, \"ngStyle\"], [1, \"p-scroller-loader\", 3, \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [3, \"styleClass\"], [4, \"ngIf\"]],\n    template: function Scroller_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Scroller_ng_container_0_Template, 8, 16, \"ng-container\", 6)(1, Scroller_ng_template_1_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const disabledContainer_r9 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx._disabled)(\"ngIfElse\", disabledContainer_r9);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SpinnerIcon],\n    styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Scroller, [{\n    type: Component,\n    args: [{\n      selector: 'p-scroller',\n      template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `,\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-scroller-viewport p-element'\n      },\n      styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }], {\n    id: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    scrollWidth: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    resizeDelay: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    loaderDisabled: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    showSpacer: [{\n      type: Input\n    }],\n    showLoader: [{\n      type: Input\n    }],\n    numToleratedItems: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    autoSize: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    onScroll: [{\n      type: Output\n    }],\n    onScrollIndexChange: [{\n      type: Output\n    }],\n    elementViewChild: [{\n      type: ViewChild,\n      args: ['element']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ScrollerModule {\n  static ɵfac = function ScrollerModule_Factory(t) {\n    return new (t || ScrollerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ScrollerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, SpinnerIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, SpinnerIcon],\n      exports: [Scroller, SharedModule],\n      declarations: [Scroller]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };", "map": {"version": 3, "names": ["i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChildren", "NgModule", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "SpinnerIcon", "_c0", "_c1", "_c2", "_c3", "a0", "a1", "a2", "_c4", "$implicit", "options", "_c5", "_c6", "_c7", "numCols", "_c8", "_c9", "styleClass", "_c10", "rows", "columns", "Scroller_ng_container_0_ng_container_3_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainer", "Scroller_ng_container_0_ng_container_3_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ctx_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "contentTemplate", "ɵɵpureFunction2", "loadedItems", "getContentOptions", "Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template", "Scroller_ng_container_0_ng_template_4_ng_container_2_Template", "item_r3", "index_r4", "index", "itemTemplate", "getOptions", "Scroller_ng_container_0_ng_template_4_Template", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵpureFunction1", "d_loading", "contentStyle", "ɵɵattribute", "_trackBy", "Scroller_ng_container_0_div_6_Template", "ɵɵelement", "spacerStyle", "Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template", "Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template", "index_r5", "loaderTemplate", "getLoaderOptions", "both", "_numItemsInViewport", "cols", "Scroller_ng_container_0_div_7_ng_container_1_Template", "loaderArr", "Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template", "Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template", "loaderIconTemplate", "ɵɵpureFunction0", "Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template", "Scroller_ng_container_0_div_7_ng_template_2_Template", "ɵɵtemplateRefExtractor", "buildInLoaderIcon_r6", "ɵɵreference", "Scroller_ng_container_0_div_7_Template", "buildInLoader_r7", "Scroller_ng_container_0_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "Scroller_ng_container_0_Template_div_scroll_1_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onContainerScroll", "buildInContent_r8", "ɵɵclassMap", "_styleClass", "_style", "ɵɵpureFunction3", "inline", "horizontal", "_id", "tabindex", "_showSpacer", "loaderDisabled", "_showLoader", "Scroller_ng_template_1_ng_container_1_ng_container_1_Template", "Scroller_ng_template_1_ng_container_1_Template", "items", "_items", "loadedColumns", "Scroller_ng_template_1_Template", "ɵɵprojection", "<PERSON><PERSON><PERSON>", "document", "platformId", "renderer", "cd", "zone", "id", "val", "style", "_tabindex", "itemSize", "_itemSize", "scrollHeight", "_scrollHeight", "scrollWidth", "_scrollWidth", "orientation", "_orientation", "step", "_step", "delay", "_delay", "resizeDelay", "_resizeDelay", "appendOnly", "_appendOnly", "_inline", "lazy", "_lazy", "disabled", "_disabled", "_loaderDisabled", "_columns", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "numToleratedItems", "_numToleratedItems", "loading", "_loading", "autoSize", "_autoSize", "trackBy", "_options", "Object", "entries", "for<PERSON>ach", "k", "v", "onLazyLoad", "onScroll", "onScrollIndexChange", "elementViewChild", "contentViewChild", "templates", "d_numToleratedItems", "contentEl", "first", "last", "page", "isRangeChanged", "numItemsInViewport", "lastScrollPos", "lazyLoadState", "scrollTimeout", "resizeTimeout", "initialized", "resizeObserver", "defaultWidth", "defaultHeight", "defaultContentWidth", "defaultContentHeight", "vertical", "slice", "map", "item", "loadedRows", "constructor", "ngOnInit", "setInitialState", "ngOnChanges", "simpleChanges", "isLoadingChanged", "previousValue", "currentValue", "top", "left", "isChanged", "length", "init", "calculateAutoSize", "ngAfterContentInit", "getType", "template", "ngAfterViewInit", "Promise", "resolve", "then", "viewInit", "ngAfterViewChecked", "ngOnDestroy", "unbindResizeListener", "isVisible", "nativeElement", "setContentEl", "getWidth", "getHeight", "ResizeObserver", "onResize", "observe", "setSize", "calculateOptions", "setSpacerSize", "detectChanges", "el", "findSingle", "getElementRef", "getPageByFirst", "Math", "floor", "isPageChanged", "scrollTo", "scrollToIndex", "behavior", "valid", "every", "i", "scrollTop", "scrollLeft", "calculateNumItems", "contentPos", "getContentPosition", "calculateFirst", "_index", "_numT", "calculateCoord", "_first", "_size", "_cpos", "newFirst", "isScrollChanged", "scrollInView", "to", "viewport", "getRenderedRange", "isToStart", "isToEnd", "pos", "calculateFirstInViewport", "_pos", "firstInViewport", "lastInViewport", "scrollPos", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "ceil", "calculateNumToleratedItems", "_numItems", "calculateLast", "_num", "_isCols", "getLast", "Array", "from", "min", "handleEvents", "minHeight", "min<PERSON><PERSON><PERSON>", "position", "contain", "width", "height", "isCols", "getComputedStyle", "parseFloat", "paddingLeft", "max", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "parentElement", "setProp", "_name", "_value", "_count", "numItems", "setContentPosition", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "event", "target", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "lastValue", "newLast", "newScrollPos", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "onScrollChange", "newState", "isLazyStateChanged", "originalEvent", "clearTimeout", "changed", "setTimeout", "undefined", "unobserve", "isDiffWidth", "isDiffHeight", "reinit", "run", "name", "params", "emit", "contentStyleClass", "getItemOptions", "renderedIndex", "count", "even", "odd", "extOptions", "ɵfac", "Scroller_Factory", "t", "ɵɵdirectiveInject", "Renderer2", "ChangeDetectorRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Scroller_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "Scroller_Query", "ɵɵviewQuery", "hostAttrs", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "consts", "Scroller_Template", "ɵɵprojectionDef", "disabledContainer_r9", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "host", "class", "Document", "decorators", "ScrollerModule", "ScrollerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-scroller.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nclass Scroller {\n    document;\n    platformId;\n    renderer;\n    cd;\n    zone;\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    get id() {\n        return this._id;\n    }\n    set id(val) {\n        this._id = val;\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    get style() {\n        return this._style;\n    }\n    set style(val) {\n        this._style = val;\n    }\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    get styleClass() {\n        return this._styleClass;\n    }\n    set styleClass(val) {\n        this._styleClass = val;\n    }\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    get tabindex() {\n        return this._tabindex;\n    }\n    set tabindex(val) {\n        this._tabindex = val;\n    }\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    get items() {\n        return this._items;\n    }\n    set items(val) {\n        this._items = val;\n    }\n    /**\n     * The height/width of item according to orientation.\n     * @group Props\n     */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(val) {\n        this._itemSize = val;\n    }\n    /**\n     * Height of the scroll viewport.\n     * @group Props\n     */\n    get scrollHeight() {\n        return this._scrollHeight;\n    }\n    set scrollHeight(val) {\n        this._scrollHeight = val;\n    }\n    /**\n     * Width of the scroll viewport.\n     * @group Props\n     */\n    get scrollWidth() {\n        return this._scrollWidth;\n    }\n    set scrollWidth(val) {\n        this._scrollWidth = val;\n    }\n    /**\n     * The orientation of scrollbar.\n     * @group Props\n     */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(val) {\n        this._orientation = val;\n    }\n    /**\n     * Used to specify how many items to load in each load method in lazy mode.\n     * @group Props\n     */\n    get step() {\n        return this._step;\n    }\n    set step(val) {\n        this._step = val;\n    }\n    /**\n     * Delay in scroll before new data is loaded.\n     * @group Props\n     */\n    get delay() {\n        return this._delay;\n    }\n    set delay(val) {\n        this._delay = val;\n    }\n    /**\n     * Delay after window's resize finishes.\n     * @group Props\n     */\n    get resizeDelay() {\n        return this._resizeDelay;\n    }\n    set resizeDelay(val) {\n        this._resizeDelay = val;\n    }\n    /**\n     * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n     * @group Props\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(val) {\n        this._appendOnly = val;\n    }\n    /**\n     * Specifies whether the scroller should be displayed inline or not.\n     * @group Props\n     */\n    get inline() {\n        return this._inline;\n    }\n    set inline(val) {\n        this._inline = val;\n    }\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    get lazy() {\n        return this._lazy;\n    }\n    set lazy(val) {\n        this._lazy = val;\n    }\n    /**\n     * If disabled, the scroller feature is eliminated and the content is displayed directly.\n     * @group Props\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(val) {\n        this._disabled = val;\n    }\n    /**\n     * Used to implement a custom loader instead of using the loader feature in the scroller.\n     * @group Props\n     */\n    get loaderDisabled() {\n        return this._loaderDisabled;\n    }\n    set loaderDisabled(val) {\n        this._loaderDisabled = val;\n    }\n    /**\n     * Columns to display.\n     * @group Props\n     */\n    get columns() {\n        return this._columns;\n    }\n    set columns(val) {\n        this._columns = val;\n    }\n    /**\n     * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n     * @group Props\n     */\n    get showSpacer() {\n        return this._showSpacer;\n    }\n    set showSpacer(val) {\n        this._showSpacer = val;\n    }\n    /**\n     * Defines whether to show loader.\n     * @group Props\n     */\n    get showLoader() {\n        return this._showLoader;\n    }\n    set showLoader(val) {\n        this._showLoader = val;\n    }\n    /**\n     * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n     * @group Props\n     */\n    get numToleratedItems() {\n        return this._numToleratedItems;\n    }\n    set numToleratedItems(val) {\n        this._numToleratedItems = val;\n    }\n    /**\n     * Defines whether the data is loaded.\n     * @group Props\n     */\n    get loading() {\n        return this._loading;\n    }\n    set loading(val) {\n        this._loading = val;\n    }\n    /**\n     * Defines whether to dynamically change the height or width of scrollable container.\n     * @group Props\n     */\n    get autoSize() {\n        return this._autoSize;\n    }\n    set autoSize(val) {\n        this._autoSize = val;\n    }\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n     * @group Props\n     */\n    get trackBy() {\n        return this._trackBy;\n    }\n    set trackBy(val) {\n        this._trackBy = val;\n    }\n    /**\n     * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n        if (val && typeof val === 'object') {\n            //@ts-ignore\n            Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n        }\n    }\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll event.\n     * @group Emits\n     */\n    onScroll = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position and item's range in view changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n     * @group Emits\n     */\n    onScrollIndexChange = new EventEmitter();\n    elementViewChild;\n    contentViewChild;\n    templates;\n    _id;\n    _style;\n    _styleClass;\n    _tabindex = 0;\n    _items;\n    _itemSize = 0;\n    _scrollHeight;\n    _scrollWidth;\n    _orientation = 'vertical';\n    _step = 0;\n    _delay = 0;\n    _resizeDelay = 10;\n    _appendOnly = false;\n    _inline = false;\n    _lazy = false;\n    _disabled = false;\n    _loaderDisabled = false;\n    _columns;\n    _showSpacer = true;\n    _showLoader = false;\n    _numToleratedItems;\n    _loading;\n    _autoSize = false;\n    _trackBy;\n    _options;\n    d_loading = false;\n    d_numToleratedItems;\n    contentEl;\n    contentTemplate;\n    itemTemplate;\n    loaderTemplate;\n    loaderIconTemplate;\n    first = 0;\n    last = 0;\n    page = 0;\n    isRangeChanged = false;\n    numItemsInViewport = 0;\n    lastScrollPos = 0;\n    lazyLoadState = {};\n    loaderArr = [];\n    spacerStyle = {};\n    contentStyle = {};\n    scrollTimeout;\n    resizeTimeout;\n    initialized = false;\n    resizeObserver;\n    defaultWidth;\n    defaultHeight;\n    defaultContentWidth;\n    defaultContentHeight;\n    get vertical() {\n        return this._orientation === 'vertical';\n    }\n    get horizontal() {\n        return this._orientation === 'horizontal';\n    }\n    get both() {\n        return this._orientation === 'both';\n    }\n    get loadedItems() {\n        if (this._items && !this.d_loading) {\n            if (this.both)\n                return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols)));\n            else if (this.horizontal && this._columns)\n                return this._items;\n            else\n                return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n        }\n        return [];\n    }\n    get loadedRows() {\n        return this.d_loading ? (this._loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n    }\n    get loadedColumns() {\n        if (this._columns && (this.both || this.horizontal)) {\n            return this.d_loading && this._loaderDisabled ? (this.both ? this.loaderArr[0] : this.loaderArr) : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n        }\n        return this._columns;\n    }\n    constructor(document, platformId, renderer, cd, zone) {\n        this.document = document;\n        this.platformId = platformId;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.zone = zone;\n    }\n    ngOnInit() {\n        this.setInitialState();\n    }\n    ngOnChanges(simpleChanges) {\n        let isLoadingChanged = false;\n        if (simpleChanges.loading) {\n            const { previousValue, currentValue } = simpleChanges.loading;\n            if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n                this.d_loading = currentValue;\n                isLoadingChanged = true;\n            }\n        }\n        if (simpleChanges.orientation) {\n            this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        }\n        if (simpleChanges.numToleratedItems) {\n            const { previousValue, currentValue } = simpleChanges.numToleratedItems;\n            if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue;\n            }\n        }\n        if (simpleChanges.options) {\n            const { previousValue, currentValue } = simpleChanges.options;\n            if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n                this.d_loading = currentValue.loading;\n                isLoadingChanged = true;\n            }\n            if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue.numToleratedItems;\n            }\n        }\n        if (this.initialized) {\n            const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n            if (isChanged) {\n                this.init();\n                this.calculateAutoSize();\n            }\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n                case 'loadericon':\n                    this.loaderIconTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        Promise.resolve().then(() => {\n            this.viewInit();\n        });\n    }\n    ngAfterViewChecked() {\n        if (!this.initialized) {\n            this.viewInit();\n        }\n    }\n    ngOnDestroy() {\n        this.unbindResizeListener();\n        this.contentEl = null;\n        this.initialized = false;\n    }\n    viewInit() {\n        if (isPlatformBrowser(this.platformId) && !this.initialized) {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                this.setInitialState();\n                this.setContentEl(this.contentEl);\n                this.init();\n                this.calculateAutoSize();\n                this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n                this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n                this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n                this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n                this.resizeObserver = new ResizeObserver(() => this.onResize());\n                this.resizeObserver.observe(this.elementViewChild?.nativeElement);\n                this.initialized = true;\n            }\n        }\n    }\n    init() {\n        if (!this._disabled) {\n            this.setSize();\n            this.calculateOptions();\n            this.setSpacerSize();\n            this.cd.detectChanges();\n        }\n    }\n    setContentEl(el) {\n        this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n    }\n    setInitialState() {\n        this.first = this.both ? { rows: 0, cols: 0 } : 0;\n        this.last = this.both ? { rows: 0, cols: 0 } : 0;\n        this.numItemsInViewport = this.both ? { rows: 0, cols: 0 } : 0;\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.d_loading = this._loading || false;\n        this.d_numToleratedItems = this._numToleratedItems;\n        this.loaderArr = [];\n        this.spacerStyle = {};\n        this.contentStyle = {};\n    }\n    getElementRef() {\n        return this.elementViewChild;\n    }\n    getPageByFirst(first) {\n        return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this._step || 1));\n    }\n    isPageChanged(first) {\n        return this._step ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n    }\n    scrollTo(options) {\n        // this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.elementViewChild?.nativeElement?.scrollTo(options);\n    }\n    scrollToIndex(index, behavior = 'auto') {\n        const valid = this.both ? index.every((i) => i > -1) : index > -1;\n        if (valid) {\n            const first = this.first;\n            const { scrollTop = 0, scrollLeft = 0 } = this.elementViewChild?.nativeElement;\n            const { numToleratedItems } = this.calculateNumItems();\n            const contentPos = this.getContentPosition();\n            const itemSize = this.itemSize;\n            const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n            const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n            const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n            let newFirst = this.both ? { rows: 0, cols: 0 } : 0;\n            let isRangeChanged = false, isScrollChanged = false;\n            if (this.both) {\n                newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n                scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n                isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n                isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n            }\n            else {\n                newFirst = calculateFirst(index, numToleratedItems);\n                this.horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n                isScrollChanged = this.lastScrollPos !== (this.horizontal ? scrollLeft : scrollTop);\n                isRangeChanged = newFirst !== first;\n            }\n            this.isRangeChanged = isRangeChanged;\n            isScrollChanged && (this.first = newFirst);\n        }\n    }\n    scrollInView(index, to, behavior = 'auto') {\n        if (to) {\n            const { first, viewport } = this.getRenderedRange();\n            const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n            const isToStart = to === 'to-start';\n            const isToEnd = to === 'to-end';\n            if (isToStart) {\n                if (this.both) {\n                    if (viewport.first.rows - first.rows > index[0]) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.first.cols - first.cols > index[1]) {\n                        scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.first - first > index) {\n                        const pos = (viewport.first - 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n            else if (isToEnd) {\n                if (this.both) {\n                    if (viewport.last.rows - first.rows <= index[0] + 1) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                        scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.last - first <= index + 1) {\n                        const pos = (viewport.first + 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n        }\n        else {\n            this.scrollToIndex(index, behavior);\n        }\n    }\n    getRenderedRange() {\n        const calculateFirstInViewport = (_pos, _size) => (_size || _pos ? Math.floor(_pos / (_size || _pos)) : 0);\n        let firstInViewport = this.first;\n        let lastInViewport = 0;\n        if (this.elementViewChild?.nativeElement) {\n            const { scrollTop, scrollLeft } = this.elementViewChild.nativeElement;\n            if (this.both) {\n                firstInViewport = { rows: calculateFirstInViewport(scrollTop, this._itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this._itemSize[1]) };\n                lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n            }\n            else {\n                const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n                firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n                lastInViewport = firstInViewport + this.numItemsInViewport;\n            }\n        }\n        return {\n            first: this.first,\n            last: this.last,\n            viewport: {\n                first: firstInViewport,\n                last: lastInViewport\n            }\n        };\n    }\n    calculateNumItems() {\n        const contentPos = this.getContentPosition();\n        const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n        const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n        const calculateNumItemsInViewport = (_contentSize, _itemSize) => (_itemSize || _contentSize ? Math.ceil(_contentSize / (_itemSize || _contentSize)) : 0);\n        const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n        const numItemsInViewport = this.both\n            ? { rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1]) }\n            : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n        const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n        return { numItemsInViewport, numToleratedItems };\n    }\n    calculateOptions() {\n        const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n        const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n        const first = this.first;\n        const last = this.both\n            ? { rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n            : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n        this.last = last;\n        this.numItemsInViewport = numItemsInViewport;\n        this.d_numToleratedItems = numToleratedItems;\n        if (this.showLoader) {\n            this.loaderArr = this.both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n        }\n        if (this._lazy) {\n            Promise.resolve().then(() => {\n                this.lazyLoadState = {\n                    first: this._step ? (this.both ? { rows: 0, cols: first.cols } : 0) : first,\n                    last: Math.min(this._step ? this._step : this.last, this.items.length)\n                };\n                this.handleEvents('onLazyLoad', this.lazyLoadState);\n            });\n        }\n    }\n    calculateAutoSize() {\n        if (this._autoSize && !this.d_loading) {\n            Promise.resolve().then(() => {\n                if (this.contentEl) {\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n                    this.contentEl.style.position = 'relative';\n                    this.elementViewChild.nativeElement.style.contain = 'none';\n                    const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n                    contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n                    contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n                    const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n                    this.contentEl.style.position = '';\n                    this.elementViewChild.nativeElement.style.contain = '';\n                    this.defaultWidth = width;\n                    this.defaultHeight = height;\n                    this.defaultContentWidth = contentWidth;\n                    this.defaultContentHeight = contentHeight;\n                    (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n                    (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n                }\n            });\n        }\n    }\n    getLast(last = 0, isCols = false) {\n        return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n    }\n    getContentPosition() {\n        if (this.contentEl) {\n            const style = getComputedStyle(this.contentEl);\n            const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n            const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n            const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n            const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n            return { left, right, top, bottom, x: left + right, y: top + bottom };\n        }\n        return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n    }\n    setSize() {\n        if (this.elementViewChild?.nativeElement) {\n            const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n            const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n            const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n            const setProp = (_name, _value) => (this.elementViewChild.nativeElement.style[_name] = _value);\n            if (this.both || this.horizontal) {\n                setProp('height', height);\n                setProp('width', width);\n            }\n            else {\n                setProp('height', height);\n            }\n        }\n    }\n    setSpacerSize() {\n        if (this._items) {\n            const setProp = (_name, _count, _size) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: _count * _size + 'px' } });\n            const numItems = this._items.length;\n            if (this.both) {\n                setProp('height', numItems, this._itemSize[0]);\n                setProp('width', this._columns?.length || this._items[1]?.length, this._itemSize[1]);\n            }\n            else {\n                this.horizontal ? setProp('width', this._columns?.length || this._items.length, this._itemSize) : setProp('height', numItems, this._itemSize);\n            }\n        }\n    }\n    setContentPosition(pos) {\n        if (this.contentEl && !this._appendOnly) {\n            const first = pos ? pos.first : this.first;\n            const calculateTranslateVal = (_first, _size) => _first * _size;\n            const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n            if (this.both) {\n                setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n            }\n            else {\n                const translateVal = calculateTranslateVal(first, this._itemSize);\n                this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n            }\n        }\n    }\n    onScrollPositionChange(event) {\n        const target = event.target;\n        const contentPos = this.getContentPosition();\n        const calculateScrollPos = (_pos, _cpos) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n        const calculateCurrentIndex = (_pos, _size) => (_size || _pos ? Math.floor(_pos / (_size || _pos)) : 0);\n        const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n        };\n        const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            if (_currentIndex <= _numT)\n                return 0;\n            else\n                return Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n        };\n        const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n            let lastValue = _first + _num + 2 * _numT;\n            if (_currentIndex >= _numT) {\n                lastValue += _numT + 1;\n            }\n            return this.getLast(lastValue, _isCols);\n        };\n        const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n        const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n        let newFirst = this.both ? { rows: 0, cols: 0 } : 0;\n        let newLast = this.last;\n        let isRangeChanged = false;\n        let newScrollPos = this.lastScrollPos;\n        if (this.both) {\n            const isScrollDown = this.lastScrollPos.top <= scrollTop;\n            const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n            if (!this._appendOnly || (this._appendOnly && (isScrollDown || isScrollRight))) {\n                const currentIndex = { rows: calculateCurrentIndex(scrollTop, this._itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this._itemSize[1]) };\n                const triggerIndex = {\n                    rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n                newFirst = {\n                    rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n                newLast = {\n                    rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                    cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                };\n                isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                newScrollPos = { top: scrollTop, left: scrollLeft };\n            }\n        }\n        else {\n            const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n            const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n            if (!this._appendOnly || (this._appendOnly && isScrollDownOrRight)) {\n                const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n                const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                newScrollPos = scrollPos;\n            }\n        }\n        return {\n            first: newFirst,\n            last: newLast,\n            isRangeChanged,\n            scrollPos: newScrollPos\n        };\n    }\n    onScrollChange(event) {\n        const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n        if (isRangeChanged) {\n            const newState = { first, last };\n            this.setContentPosition(newState);\n            this.first = first;\n            this.last = last;\n            this.lastScrollPos = scrollPos;\n            this.handleEvents('onScrollIndexChange', newState);\n            if (this._lazy && this.isPageChanged(first)) {\n                const lazyLoadState = {\n                    first: this._step ? Math.min(this.getPageByFirst(first) * this._step, this.items.length - this._step) : first,\n                    last: Math.min(this._step ? (this.getPageByFirst(first) + 1) * this._step : last, this.items.length)\n                };\n                const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n                isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n                this.lazyLoadState = lazyLoadState;\n            }\n        }\n    }\n    onContainerScroll(event) {\n        this.handleEvents('onScroll', { originalEvent: event });\n        if (this._delay && this.isPageChanged()) {\n            if (this.scrollTimeout) {\n                clearTimeout(this.scrollTimeout);\n            }\n            if (!this.d_loading && this.showLoader) {\n                const { isRangeChanged } = this.onScrollPositionChange(event);\n                const changed = isRangeChanged || (this._step ? this.isPageChanged() : false);\n                if (changed) {\n                    this.d_loading = true;\n                    this.cd.detectChanges();\n                }\n            }\n            this.scrollTimeout = setTimeout(() => {\n                this.onScrollChange(event);\n                if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n                    this.d_loading = false;\n                    this.page = this.getPageByFirst();\n                    this.cd.detectChanges();\n                }\n            }, this._delay);\n        }\n        else {\n            !this.d_loading && this.onScrollChange(event);\n        }\n    }\n    unbindResizeListener() {\n        if (this.resizeObserver) {\n            this.resizeObserver.unobserve(this.elementViewChild?.nativeElement);\n            this.resizeObserver = null;\n        }\n    }\n    onResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n        }\n        this.resizeTimeout = setTimeout(() => {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n                const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n                reinit &&\n                    this.zone.run(() => {\n                        this.d_numToleratedItems = this._numToleratedItems;\n                        this.init();\n                        this.calculateAutoSize();\n                    });\n            }\n        }, this._resizeDelay);\n    }\n    handleEvents(name, params) {\n        //@ts-ignore\n        return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n    }\n    getContentOptions() {\n        return {\n            contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n            items: this.loadedItems,\n            getItemOptions: (index) => this.getOptions(index),\n            loading: this.d_loading,\n            getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n            itemSize: this._itemSize,\n            rows: this.loadedRows,\n            columns: this.loadedColumns,\n            spacerStyle: this.spacerStyle,\n            contentStyle: this.contentStyle,\n            vertical: this.vertical,\n            horizontal: this.horizontal,\n            both: this.both\n        };\n    }\n    getOptions(renderedIndex) {\n        const count = (this._items || []).length;\n        const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0\n        };\n    }\n    getLoaderOptions(index, extOptions) {\n        const count = this.loaderArr.length;\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0,\n            ...extOptions\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: Scroller, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.0.1\", type: Scroller, selector: \"p-scroller\", inputs: { id: \"id\", style: \"style\", styleClass: \"styleClass\", tabindex: \"tabindex\", items: \"items\", itemSize: \"itemSize\", scrollHeight: \"scrollHeight\", scrollWidth: \"scrollWidth\", orientation: \"orientation\", step: \"step\", delay: \"delay\", resizeDelay: \"resizeDelay\", appendOnly: \"appendOnly\", inline: \"inline\", lazy: \"lazy\", disabled: \"disabled\", loaderDisabled: \"loaderDisabled\", columns: \"columns\", showSpacer: \"showSpacer\", showLoader: \"showLoader\", numToleratedItems: \"numToleratedItems\", loading: \"loading\", autoSize: \"autoSize\", trackBy: \"trackBy\", options: \"options\" }, outputs: { onLazyLoad: \"onLazyLoad\", onScroll: \"onScroll\", onScrollIndexChange: \"onScrollIndexChange\" }, host: { classAttribute: \"p-scroller-viewport p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"elementViewChild\", first: true, predicate: [\"element\"], descendants: true }, { propertyName: \"contentViewChild\", first: true, predicate: [\"content\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, isInline: true, styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => SpinnerIcon), selector: \"SpinnerIcon\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: Scroller, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-scroller', template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-scroller-viewport p-element'\n                    }, styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }], propDecorators: { id: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], items: [{\n                type: Input\n            }], itemSize: [{\n                type: Input\n            }], scrollHeight: [{\n                type: Input\n            }], scrollWidth: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], delay: [{\n                type: Input\n            }], resizeDelay: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], inline: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], loaderDisabled: [{\n                type: Input\n            }], columns: [{\n                type: Input\n            }], showSpacer: [{\n                type: Input\n            }], showLoader: [{\n                type: Input\n            }], numToleratedItems: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], autoSize: [{\n                type: Input\n            }], trackBy: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], onLazyLoad: [{\n                type: Output\n            }], onScroll: [{\n                type: Output\n            }], onScrollIndexChange: [{\n                type: Output\n            }], elementViewChild: [{\n                type: ViewChild,\n                args: ['element']\n            }], contentViewChild: [{\n                type: ViewChild,\n                args: ['content']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass ScrollerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: ScrollerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: ScrollerModule, declarations: [Scroller], imports: [CommonModule, SharedModule, SpinnerIcon], exports: [Scroller, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: ScrollerModule, imports: [CommonModule, SharedModule, SpinnerIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: ScrollerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, SpinnerIcon],\n                    exports: [Scroller, SharedModule],\n                    declarations: [Scroller]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7K,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,WAAW,QAAQ,uBAAuB;;AAEnD;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;EAAA,qBAAAF,EAAA;EAAA,iBAAAC,EAAA;EAAA,uBAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAH,EAAA,EAAAC,EAAA;EAAAG,SAAA,EAAAJ,EAAA;EAAAK,OAAA,EAAAJ;AAAA;AAAA,MAAAK,GAAA,GAAAN,EAAA;EAAA,sBAAAA;AAAA;AAAA,MAAAO,GAAA,GAAAP,EAAA;EAAA,uBAAAA;AAAA;AAAA,MAAAQ,GAAA,GAAAR,EAAA;EAAAS,OAAA,EAAAT;AAAA;AAAA,MAAAU,GAAA,GAAAV,EAAA;EAAAK,OAAA,EAAAL;AAAA;AAAA,MAAAW,GAAA,GAAAA,CAAA;EAAAC,UAAA;AAAA;AAAA,MAAAC,IAAA,GAAAA,CAAAb,EAAA,EAAAC,EAAA;EAAAa,IAAA,EAAAd,EAAA;EAAAe,OAAA,EAAAd;AAAA;AAAA,SAAAe,+DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA63B6FrC,EAAE,CAAAuC,kBAAA,EAewD,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAf3DrC,EAAE,CAAAyC,uBAAA,EAcrB,CAAC;IAdkBzC,EAAE,CAAA0C,UAAA,IAAAN,8DAAA,0BAeyC,CAAC;IAf5CpC,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAe1B,CAAC;IAfuB9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAI,eAe1B,CAAC,4BAfuBhD,EAAE,CAAAiD,eAAA,IAAA1B,GAAA,EAAAqB,MAAA,CAAAM,WAAA,EAAAN,MAAA,CAAAO,iBAAA,GAeuC,CAAC;EAAA;AAAA;AAAA,SAAAC,6EAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAf1CrC,EAAE,CAAAuC,kBAAA,EAoBoD,CAAC;EAAA;AAAA;AAAA,SAAAc,8DAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBvDrC,EAAE,CAAAyC,uBAAA,EAmBsB,CAAC;IAnBzBzC,EAAE,CAAA0C,UAAA,IAAAU,4EAAA,0BAoBqC,CAAC;IApBxCpD,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAiB,OAAA,GAAAhB,GAAA,CAAAd,SAAA;IAAA,MAAA+B,QAAA,GAAAjB,GAAA,CAAAkB,KAAA;IAAA,MAAAZ,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAoBrB,CAAC;IApBkB9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAa,YAoBrB,CAAC,4BApBkBzD,EAAE,CAAAiD,eAAA,IAAA1B,GAAA,EAAA+B,OAAA,EAAAV,MAAA,CAAAc,UAAA,CAAAH,QAAA,EAoBmC,CAAC;EAAA;AAAA;AAAA,SAAAI,+CAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBtCrC,EAAE,CAAA4D,cAAA,gBAkByE,CAAC;IAlB5E5D,EAAE,CAAA0C,UAAA,IAAAW,6DAAA,0BAmBsB,CAAC;IAnBzBrD,EAAE,CAAA6D,YAAA,CAsBtE,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GAtBmE5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,YAAF/C,EAAE,CAAA8D,eAAA,IAAApC,GAAA,EAAAkB,MAAA,CAAAmB,SAAA,CAkBY,CAAC,YAAAnB,MAAA,CAAAoB,YAAwB,CAAC;IAlBxChE,EAAE,CAAAiE,WAAA;IAAFjE,EAAE,CAAA8C,SAAA,EAmBzB,CAAC;IAnBsB9C,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAAM,WAmBzB,CAAC,iBAAAN,MAAA,CAAAsB,QAAA,IAAAtB,MAAA,CAAAY,KAA4C,CAAC;EAAA;AAAA;AAAA,SAAAW,uCAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnBvBrC,EAAE,CAAAoE,SAAA,aAwBmC,CAAC;EAAA;EAAA,IAAA/B,EAAA;IAAA,MAAAO,MAAA,GAxBtC5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAAyB,WAwBN,CAAC;IAxBGrE,EAAE,CAAAiE,WAAA;EAAA;AAAA;AAAA,SAAAK,oFAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAuC,kBAAA,EA4B0F,CAAC;EAAA;AAAA;AAAA,SAAAgC,qEAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5B7FrC,EAAE,CAAAyC,uBAAA,EA2BR,CAAC;IA3BKzC,EAAE,CAAA0C,UAAA,IAAA4B,mFAAA,0BA4B2E,CAAC;IA5B9EtE,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAmC,QAAA,GAAAlC,GAAA,CAAAkB,KAAA;IAAA,MAAAZ,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA4BnB,CAAC;IA5BgB9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAA6B,cA4BnB,CAAC,4BA5BgBzE,EAAE,CAAA8D,eAAA,IAAAhC,GAAA,EAAAc,MAAA,CAAA8B,gBAAA,CAAAF,QAAA,EAAA5B,MAAA,CAAA+B,IAAA,IAAF3E,EAAE,CAAA8D,eAAA,IAAAlC,GAAA,EAAAgB,MAAA,CAAAgC,mBAAA,CAAAC,IAAA,GA4ByE,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5B5ErC,EAAE,CAAAyC,uBAAA,EA0BnB,CAAC;IA1BgBzC,EAAE,CAAA0C,UAAA,IAAA6B,oEAAA,0BA2BR,CAAC;IA3BKvE,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA2B3B,CAAC;IA3BwB9C,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAAmC,SA2B3B,CAAC;EAAA;AAAA;AAAA,SAAAC,mFAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3BwBrC,EAAE,CAAAuC,kBAAA,EAiCiE,CAAC;EAAA;AAAA;AAAA,SAAA0C,oEAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCpErC,EAAE,CAAAyC,uBAAA,EAgCP,CAAC;IAhCIzC,EAAE,CAAA0C,UAAA,IAAAsC,kFAAA,0BAiCkD,CAAC;IAjCrDhF,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAiCf,CAAC;IAjCY9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAsC,kBAiCf,CAAC,4BAjCYlF,EAAE,CAAA8D,eAAA,IAAAhC,GAAA,EAAF9B,EAAE,CAAAmF,eAAA,IAAApD,GAAA,EAiCgD,CAAC;EAAA;AAAA;AAAA,SAAAqD,mEAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCnDrC,EAAE,CAAAoE,SAAA,qBAoCmC,CAAC;EAAA;EAAA,IAAA/B,EAAA;IApCtCrC,EAAE,CAAA+C,UAAA,gDAoCP,CAAC;IApCI/C,EAAE,CAAAiE,WAAA;EAAA;AAAA;AAAA,SAAAoB,qDAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAA0C,UAAA,IAAAuC,mEAAA,yBAgCP,CAAC,IAAAG,kEAAA,gCAhCIpF,EAAE,CAAAsF,sBAmCxC,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAkD,oBAAA,GAnCqCvF,EAAE,CAAAwF,WAAA;IAAA,MAAA5C,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAsC,kBAgC/B,CAAC,aAAAK,oBAAqB,CAAC;EAAA;AAAA;AAAA,SAAAE,uCAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhCMrC,EAAE,CAAA4D,cAAA,aAyB4F,CAAC;IAzB/F5D,EAAE,CAAA0C,UAAA,IAAAoC,qDAAA,yBA0BnB,CAAC,IAAAO,oDAAA,gCA1BgBrF,EAAE,CAAAsF,sBA+BhD,CAAC;IA/B6CtF,EAAE,CAAA6D,YAAA,CAuC1E,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAqD,gBAAA,GAvCuE1F,EAAE,CAAAwF,WAAA;IAAA,MAAA5C,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,YAAF/C,EAAE,CAAA8D,eAAA,IAAAnC,GAAA,GAAAiB,MAAA,CAAA6B,cAAA,CAyByD,CAAC;IAzB5DzE,EAAE,CAAAiE,WAAA;IAAFjE,EAAE,CAAA8C,SAAA,CA0BvC,CAAC;IA1BoC9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAA6B,cA0BvC,CAAC,aAAAiB,gBAAiB,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuD,GAAA,GA1BkB5F,EAAE,CAAA6F,gBAAA;IAAF7F,EAAE,CAAAyC,uBAAA,EAE/B,CAAC;IAF4BzC,EAAE,CAAA4D,cAAA,eAanF,CAAC;IAbgF5D,EAAE,CAAA8F,UAAA,oBAAAC,uDAAAC,MAAA;MAAFhG,EAAE,CAAAiG,aAAA,CAAAL,GAAA;MAAA,MAAAhD,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;MAAA,OAAF7C,EAAE,CAAAkG,WAAA,CAUrEtD,MAAA,CAAAuD,iBAAA,CAAAH,MAAwB,CAAC;IAAA,EAAC;IAVyChG,EAAE,CAAA0C,UAAA,IAAAF,+CAAA,yBAcrB,CAAC,IAAAmB,8CAAA,gCAdkB3D,EAAE,CAAAsF,sBAiBnD,CAAC,IAAAnB,sCAAA,gBAO+E,CAAC,IAAAsB,sCAAA,gBAC8D,CAAC;IAzB/FzF,EAAE,CAAA6D,YAAA,CAwC9E,CAAC;IAxC2E7D,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAA+D,iBAAA,GAAFpG,EAAE,CAAAwF,WAAA;IAAA,MAAA5C,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAQ3D,CAAC;IARwD9C,EAAE,CAAAqG,UAAA,CAAAzD,MAAA,CAAA0D,WAQ3D,CAAC;IARwDtG,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAA2D,MAO9D,CAAC,YAP2DvG,EAAE,CAAAwG,eAAA,KAAArF,GAAA,EAAAyB,MAAA,CAAA6D,MAAA,EAAA7D,MAAA,CAAA+B,IAAA,EAAA/B,MAAA,CAAA8D,UAAA,CASyC,CAAC;IAT5C1G,EAAE,CAAAiE,WAAA,OAAArB,MAAA,CAAA+D,GAAA,cAAA/D,MAAA,CAAAgE,QAAA;IAAF5G,EAAE,CAAA8C,SAAA,EAc1C,CAAC;IAduC9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAI,eAc1C,CAAC,aAAAoD,iBAAkB,CAAC;IAdoBpG,EAAE,CAAA8C,SAAA,EAwBzD,CAAC;IAxBsD9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAiE,WAwBzD,CAAC;IAxBsD7G,EAAE,CAAA8C,SAAA,CAyBzB,CAAC;IAzBsB9C,EAAE,CAAA+C,UAAA,UAAAH,MAAA,CAAAkE,cAAA,IAAAlE,MAAA,CAAAmE,WAAA,IAAAnE,MAAA,CAAAmB,SAyBzB,CAAC;EAAA;AAAA;AAAA,SAAAiD,8DAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzBsBrC,EAAE,CAAAuC,kBAAA,EA6CmE,CAAC;EAAA;AAAA;AAAA,SAAA0E,+CAAA5E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CtErC,EAAE,CAAAyC,uBAAA,EA4C9C,CAAC;IA5C2CzC,EAAE,CAAA0C,UAAA,IAAAsE,6DAAA,0BA6CoD,CAAC;IA7CvDhH,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA6C9B,CAAC;IA7C2B9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAI,eA6C9B,CAAC,4BA7C2BhD,EAAE,CAAAiD,eAAA,IAAA1B,GAAA,EAAAqB,MAAA,CAAAsE,KAAA,EAAFlH,EAAE,CAAAiD,eAAA,IAAAhB,IAAA,EAAAW,MAAA,CAAAuE,MAAA,EAAAvE,MAAA,CAAAwE,aAAA,EA6CkD,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CrDrC,EAAE,CAAAsH,YAAA,EA2C3D,CAAC;IA3CwDtH,EAAE,CAAA0C,UAAA,IAAAuE,8CAAA,0BA4C9C,CAAC;EAAA;EAAA,IAAA5E,EAAA;IAAA,MAAAO,MAAA,GA5C2C5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA4ChD,CAAC;IA5C6C9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAI,eA4ChD,CAAC;EAAA;AAAA;AAr6BhD,MAAMuE,QAAQ,CAAC;EACXC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,EAAE;EACFC,IAAI;EACJ;AACJ;AACA;AACA;EACI,IAAIC,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAAClB,GAAG;EACnB;EACA,IAAIkB,EAAEA,CAACC,GAAG,EAAE;IACR,IAAI,CAACnB,GAAG,GAAGmB,GAAG;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxB,MAAM;EACtB;EACA,IAAIwB,KAAKA,CAACD,GAAG,EAAE;IACX,IAAI,CAACvB,MAAM,GAAGuB,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAI9F,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACsE,WAAW;EAC3B;EACA,IAAItE,UAAUA,CAAC8F,GAAG,EAAE;IAChB,IAAI,CAACxB,WAAW,GAAGwB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAIlB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoB,SAAS;EACzB;EACA,IAAIpB,QAAQA,CAACkB,GAAG,EAAE;IACd,IAAI,CAACE,SAAS,GAAGF,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIZ,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACY,GAAG,EAAE;IACX,IAAI,CAACX,MAAM,GAAGW,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAIG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,GAAG,EAAE;IACd,IAAI,CAACI,SAAS,GAAGJ,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACL,GAAG,EAAE;IAClB,IAAI,CAACM,aAAa,GAAGN,GAAG;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIO,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACP,GAAG,EAAE;IACjB,IAAI,CAACQ,YAAY,GAAGR,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIS,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACT,GAAG,EAAE;IACjB,IAAI,CAACU,YAAY,GAAGV,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIW,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACX,GAAG,EAAE;IACV,IAAI,CAACY,KAAK,GAAGZ,GAAG;EACpB;EACA;AACJ;AACA;AACA;EACI,IAAIa,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACb,GAAG,EAAE;IACX,IAAI,CAACc,MAAM,GAAGd,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAIe,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACf,GAAG,EAAE;IACjB,IAAI,CAACgB,YAAY,GAAGhB,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIiB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACjB,GAAG,EAAE;IAChB,IAAI,CAACkB,WAAW,GAAGlB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAIrB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACwC,OAAO;EACvB;EACA,IAAIxC,MAAMA,CAACqB,GAAG,EAAE;IACZ,IAAI,CAACmB,OAAO,GAAGnB,GAAG;EACtB;EACA;AACJ;AACA;AACA;EACI,IAAIoB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACpB,GAAG,EAAE;IACV,IAAI,CAACqB,KAAK,GAAGrB,GAAG;EACpB;EACA;AACJ;AACA;AACA;EACI,IAAIsB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACtB,GAAG,EAAE;IACd,IAAI,CAACuB,SAAS,GAAGvB,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIhB,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACwC,eAAe;EAC/B;EACA,IAAIxC,cAAcA,CAACgB,GAAG,EAAE;IACpB,IAAI,CAACwB,eAAe,GAAGxB,GAAG;EAC9B;EACA;AACJ;AACA;AACA;EACI,IAAI3F,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoH,QAAQ;EACxB;EACA,IAAIpH,OAAOA,CAAC2F,GAAG,EAAE;IACb,IAAI,CAACyB,QAAQ,GAAGzB,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAI0B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC3C,WAAW;EAC3B;EACA,IAAI2C,UAAUA,CAAC1B,GAAG,EAAE;IAChB,IAAI,CAACjB,WAAW,GAAGiB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI2B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1C,WAAW;EAC3B;EACA,IAAI0C,UAAUA,CAAC3B,GAAG,EAAE;IAChB,IAAI,CAACf,WAAW,GAAGe,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI4B,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiBA,CAAC5B,GAAG,EAAE;IACvB,IAAI,CAAC6B,kBAAkB,GAAG7B,GAAG;EACjC;EACA;AACJ;AACA;AACA;EACI,IAAI8B,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAAC9B,GAAG,EAAE;IACb,IAAI,CAAC+B,QAAQ,GAAG/B,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIgC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAChC,GAAG,EAAE;IACd,IAAI,CAACiC,SAAS,GAAGjC,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIkC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9F,QAAQ;EACxB;EACA,IAAI8F,OAAOA,CAAClC,GAAG,EAAE;IACb,IAAI,CAAC5D,QAAQ,GAAG4D,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIrG,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACwI,QAAQ;EACxB;EACA,IAAIxI,OAAOA,CAACqG,GAAG,EAAE;IACb,IAAI,CAACmC,QAAQ,GAAGnC,GAAG;IACnB,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAChC;MACAoC,MAAM,CAACC,OAAO,CAACrC,GAAG,CAAC,CAACsC,OAAO,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAID,CAAC,EAAE,CAAC,KAAKC,CAAC,KAAK,IAAI,CAAC,IAAID,CAAC,EAAE,CAAC,GAAGC,CAAC,CAAC,CAAC;IACvF;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAU,GAAG,IAAItK,YAAY,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;AACA;EACIuK,QAAQ,GAAG,IAAIvK,YAAY,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;AACA;EACIwK,mBAAmB,GAAG,IAAIxK,YAAY,CAAC,CAAC;EACxCyK,gBAAgB;EAChBC,gBAAgB;EAChBC,SAAS;EACTjE,GAAG;EACHJ,MAAM;EACND,WAAW;EACX0B,SAAS,GAAG,CAAC;EACbb,MAAM;EACNe,SAAS,GAAG,CAAC;EACbE,aAAa;EACbE,YAAY;EACZE,YAAY,GAAG,UAAU;EACzBE,KAAK,GAAG,CAAC;EACTE,MAAM,GAAG,CAAC;EACVE,YAAY,GAAG,EAAE;EACjBE,WAAW,GAAG,KAAK;EACnBC,OAAO,GAAG,KAAK;EACfE,KAAK,GAAG,KAAK;EACbE,SAAS,GAAG,KAAK;EACjBC,eAAe,GAAG,KAAK;EACvBC,QAAQ;EACR1C,WAAW,GAAG,IAAI;EAClBE,WAAW,GAAG,KAAK;EACnB4C,kBAAkB;EAClBE,QAAQ;EACRE,SAAS,GAAG,KAAK;EACjB7F,QAAQ;EACR+F,QAAQ;EACRlG,SAAS,GAAG,KAAK;EACjB8G,mBAAmB;EACnBC,SAAS;EACT9H,eAAe;EACfS,YAAY;EACZgB,cAAc;EACdS,kBAAkB;EAClB6F,KAAK,GAAG,CAAC;EACTC,IAAI,GAAG,CAAC;EACRC,IAAI,GAAG,CAAC;EACRC,cAAc,GAAG,KAAK;EACtBC,kBAAkB,GAAG,CAAC;EACtBC,aAAa,GAAG,CAAC;EACjBC,aAAa,GAAG,CAAC,CAAC;EAClBtG,SAAS,GAAG,EAAE;EACdV,WAAW,GAAG,CAAC,CAAC;EAChBL,YAAY,GAAG,CAAC,CAAC;EACjBsH,aAAa;EACbC,aAAa;EACbC,WAAW,GAAG,KAAK;EACnBC,cAAc;EACdC,YAAY;EACZC,aAAa;EACbC,mBAAmB;EACnBC,oBAAoB;EACpB,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtD,YAAY,KAAK,UAAU;EAC3C;EACA,IAAI9B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC8B,YAAY,KAAK,YAAY;EAC7C;EACA,IAAI7D,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC6D,YAAY,KAAK,MAAM;EACvC;EACA,IAAItF,WAAWA,CAAA,EAAG;IACd,IAAI,IAAI,CAACiE,MAAM,IAAI,CAAC,IAAI,CAACpD,SAAS,EAAE;MAChC,IAAI,IAAI,CAACY,IAAI,EACT,OAAO,IAAI,CAACwC,MAAM,CAAC4E,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,CAAC,CAAC8J,GAAG,CAAEC,IAAI,IAAM,IAAI,CAAC1C,QAAQ,GAAG0C,IAAI,GAAGA,IAAI,CAACF,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,CAAE,CAAC,CAAC,KAC3L,IAAI,IAAI,CAAC6B,UAAU,IAAI,IAAI,CAAC6C,QAAQ,EACrC,OAAO,IAAI,CAACpC,MAAM,CAAC,KAEnB,OAAO,IAAI,CAACA,MAAM,CAAC4E,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,EAAE,IAAI,CAACC,IAAI,CAAC;IAC9E;IACA,OAAO,EAAE;EACb;EACA,IAAIkB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnI,SAAS,GAAI,IAAI,CAACuF,eAAe,GAAG,IAAI,CAACvE,SAAS,GAAG,EAAE,GAAI,IAAI,CAAC7B,WAAW;EAC3F;EACA,IAAIkE,aAAaA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACmC,QAAQ,KAAK,IAAI,CAAC5E,IAAI,IAAI,IAAI,CAAC+B,UAAU,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC3C,SAAS,IAAI,IAAI,CAACuF,eAAe,GAAI,IAAI,CAAC3E,IAAI,GAAG,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,SAAS,GAAI,IAAI,CAACwE,QAAQ,CAACwC,KAAK,CAAC,IAAI,CAACpH,IAAI,GAAG,IAAI,CAACoG,KAAK,CAAClG,IAAI,GAAG,IAAI,CAACkG,KAAK,EAAE,IAAI,CAACpG,IAAI,GAAG,IAAI,CAACqG,IAAI,CAACnG,IAAI,GAAG,IAAI,CAACmG,IAAI,CAAC;IAC5M;IACA,OAAO,IAAI,CAACzB,QAAQ;EACxB;EACA4C,WAAWA,CAAC3E,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,IAAI,EAAE;IAClD,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAwE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAID,aAAa,CAAC3C,OAAO,EAAE;MACvB,MAAM;QAAE6C,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC3C,OAAO;MAC7D,IAAI,IAAI,CAACV,IAAI,IAAIuD,aAAa,KAAKC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAAC3I,SAAS,EAAE;QAChF,IAAI,CAACA,SAAS,GAAG2I,YAAY;QAC7BF,gBAAgB,GAAG,IAAI;MAC3B;IACJ;IACA,IAAID,aAAa,CAAChE,WAAW,EAAE;MAC3B,IAAI,CAAC6C,aAAa,GAAG,IAAI,CAACzG,IAAI,GAAG;QAAEgI,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAC,GAAG,CAAC;IAC5D;IACA,IAAIL,aAAa,CAAC7C,iBAAiB,EAAE;MACjC,MAAM;QAAE+C,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC7C,iBAAiB;MACvE,IAAI+C,aAAa,KAAKC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAAC7B,mBAAmB,EAAE;QAC7E,IAAI,CAACA,mBAAmB,GAAG6B,YAAY;MAC3C;IACJ;IACA,IAAIH,aAAa,CAAC9K,OAAO,EAAE;MACvB,MAAM;QAAEgL,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC9K,OAAO;MAC7D,IAAI,IAAI,CAACyH,IAAI,IAAIuD,aAAa,EAAE7C,OAAO,KAAK8C,YAAY,EAAE9C,OAAO,IAAI8C,YAAY,EAAE9C,OAAO,KAAK,IAAI,CAAC7F,SAAS,EAAE;QAC3G,IAAI,CAACA,SAAS,GAAG2I,YAAY,CAAC9C,OAAO;QACrC4C,gBAAgB,GAAG,IAAI;MAC3B;MACA,IAAIC,aAAa,EAAE/C,iBAAiB,KAAKgD,YAAY,EAAEhD,iBAAiB,IAAIgD,YAAY,EAAEhD,iBAAiB,KAAK,IAAI,CAACmB,mBAAmB,EAAE;QACtI,IAAI,CAACA,mBAAmB,GAAG6B,YAAY,CAAChD,iBAAiB;MAC7D;IACJ;IACA,IAAI,IAAI,CAAC8B,WAAW,EAAE;MAClB,MAAMqB,SAAS,GAAG,CAACL,gBAAgB,KAAKD,aAAa,CAACrF,KAAK,EAAEuF,aAAa,EAAEK,MAAM,KAAKP,aAAa,CAACrF,KAAK,EAAEwF,YAAY,EAAEI,MAAM,IAAIP,aAAa,CAACtE,QAAQ,IAAIsE,aAAa,CAACpE,YAAY,IAAIoE,aAAa,CAAClE,WAAW,CAAC;MACtN,IAAIwE,SAAS,EAAE;QACX,IAAI,CAACE,IAAI,CAAC,CAAC;QACX,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC5B;IACJ;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACrC,SAAS,CAACR,OAAO,CAAE6B,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACiB,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAAClK,eAAe,GAAGiJ,IAAI,CAACkB,QAAQ;UACpC;QACJ,KAAK,MAAM;UACP,IAAI,CAAC1J,YAAY,GAAGwI,IAAI,CAACkB,QAAQ;UACjC;QACJ,KAAK,QAAQ;UACT,IAAI,CAAC1I,cAAc,GAAGwH,IAAI,CAACkB,QAAQ;UACnC;QACJ,KAAK,YAAY;UACb,IAAI,CAACjI,kBAAkB,GAAG+G,IAAI,CAACkB,QAAQ;UACvC;QACJ;UACI,IAAI,CAAC1J,YAAY,GAAGwI,IAAI,CAACkB,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACdC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACC,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACjC,WAAW,EAAE;MACnB,IAAI,CAACgC,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC7C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACU,WAAW,GAAG,KAAK;EAC5B;EACAgC,QAAQA,CAAA,EAAG;IACP,IAAI3N,iBAAiB,CAAC,IAAI,CAAC4H,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC+D,WAAW,EAAE;MACzD,IAAI1K,UAAU,CAAC8M,SAAS,CAAC,IAAI,CAAClD,gBAAgB,EAAEmD,aAAa,CAAC,EAAE;QAC5D,IAAI,CAACxB,eAAe,CAAC,CAAC;QACtB,IAAI,CAACyB,YAAY,CAAC,IAAI,CAAChD,SAAS,CAAC;QACjC,IAAI,CAACiC,IAAI,CAAC,CAAC;QACX,IAAI,CAACC,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACtB,YAAY,GAAG5K,UAAU,CAACiN,QAAQ,CAAC,IAAI,CAACrD,gBAAgB,EAAEmD,aAAa,CAAC;QAC7E,IAAI,CAAClC,aAAa,GAAG7K,UAAU,CAACkN,SAAS,CAAC,IAAI,CAACtD,gBAAgB,EAAEmD,aAAa,CAAC;QAC/E,IAAI,CAACjC,mBAAmB,GAAG9K,UAAU,CAACiN,QAAQ,CAAC,IAAI,CAACjD,SAAS,CAAC;QAC9D,IAAI,CAACe,oBAAoB,GAAG/K,UAAU,CAACkN,SAAS,CAAC,IAAI,CAAClD,SAAS,CAAC;QAChE,IAAI,CAACW,cAAc,GAAG,IAAIwC,cAAc,CAAC,MAAM,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;QAC/D,IAAI,CAACzC,cAAc,CAAC0C,OAAO,CAAC,IAAI,CAACzD,gBAAgB,EAAEmD,aAAa,CAAC;QACjE,IAAI,CAACrC,WAAW,GAAG,IAAI;MAC3B;IACJ;EACJ;EACAuB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC1D,SAAS,EAAE;MACjB,IAAI,CAAC+E,OAAO,CAAC,CAAC;MACd,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAAC3G,EAAE,CAAC4G,aAAa,CAAC,CAAC;IAC3B;EACJ;EACAT,YAAYA,CAACU,EAAE,EAAE;IACb,IAAI,CAAC1D,SAAS,GAAG0D,EAAE,IAAI,IAAI,CAAC7D,gBAAgB,EAAEkD,aAAa,IAAI/M,UAAU,CAAC2N,UAAU,CAAC,IAAI,CAAC/D,gBAAgB,EAAEmD,aAAa,EAAE,qBAAqB,CAAC;EACrJ;EACAxB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACtB,KAAK,GAAG,IAAI,CAACpG,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACjD,IAAI,CAACmG,IAAI,GAAG,IAAI,CAACrG,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IAChD,IAAI,CAACsG,kBAAkB,GAAG,IAAI,CAACxG,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IAC9D,IAAI,CAACuG,aAAa,GAAG,IAAI,CAACzG,IAAI,GAAG;MAAEgI,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACxD,IAAI,CAAC7I,SAAS,GAAG,IAAI,CAAC8F,QAAQ,IAAI,KAAK;IACvC,IAAI,CAACgB,mBAAmB,GAAG,IAAI,CAAClB,kBAAkB;IAClD,IAAI,CAAC5E,SAAS,GAAG,EAAE;IACnB,IAAI,CAACV,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACL,YAAY,GAAG,CAAC,CAAC;EAC1B;EACA0K,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChE,gBAAgB;EAChC;EACAiE,cAAcA,CAAC5D,KAAK,EAAE;IAClB,OAAO6D,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC9D,KAAK,IAAI,IAAI,CAACA,KAAK,IAAI,IAAI,CAACF,mBAAmB,GAAG,CAAC,KAAK,IAAI,CAACnC,KAAK,IAAI,CAAC,CAAC,CAAC;EACjG;EACAoG,aAAaA,CAAC/D,KAAK,EAAE;IACjB,OAAO,IAAI,CAACrC,KAAK,GAAG,IAAI,CAACuC,IAAI,KAAK,IAAI,CAAC0D,cAAc,CAAC5D,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC,GAAG,IAAI;EACrF;EACAgE,QAAQA,CAACtN,OAAO,EAAE;IACd;IACA,IAAI,CAACiJ,gBAAgB,EAAEmD,aAAa,EAAEkB,QAAQ,CAACtN,OAAO,CAAC;EAC3D;EACAuN,aAAaA,CAACxL,KAAK,EAAEyL,QAAQ,GAAG,MAAM,EAAE;IACpC,MAAMC,KAAK,GAAG,IAAI,CAACvK,IAAI,GAAGnB,KAAK,CAAC2L,KAAK,CAAEC,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG5L,KAAK,GAAG,CAAC,CAAC;IACjE,IAAI0L,KAAK,EAAE;MACP,MAAMnE,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAM;QAAEsE,SAAS,GAAG,CAAC;QAAEC,UAAU,GAAG;MAAE,CAAC,GAAG,IAAI,CAAC5E,gBAAgB,EAAEmD,aAAa;MAC9E,MAAM;QAAEnE;MAAkB,CAAC,GAAG,IAAI,CAAC6F,iBAAiB,CAAC,CAAC;MACtD,MAAMC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC5C,MAAMxH,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAMyH,cAAc,GAAGA,CAACC,MAAM,GAAG,CAAC,EAAEC,KAAK,KAAMD,MAAM,IAAIC,KAAK,GAAG,CAAC,GAAGD,MAAO;MAC5E,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,KAAKF,MAAM,GAAGC,KAAK,GAAGC,KAAK;MACvE,MAAMjB,QAAQ,GAAGA,CAACnC,IAAI,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,KAAK,IAAI,CAACoC,QAAQ,CAAC;QAAEnC,IAAI;QAAED,GAAG;QAAEsC;MAAS,CAAC,CAAC;MAC9E,IAAIgB,QAAQ,GAAG,IAAI,CAACtL,IAAI,GAAG;QAAEzC,IAAI,EAAE,CAAC;QAAE2C,IAAI,EAAE;MAAE,CAAC,GAAG,CAAC;MACnD,IAAIqG,cAAc,GAAG,KAAK;QAAEgF,eAAe,GAAG,KAAK;MACnD,IAAI,IAAI,CAACvL,IAAI,EAAE;QACXsL,QAAQ,GAAG;UAAE/N,IAAI,EAAEwN,cAAc,CAAClM,KAAK,CAAC,CAAC,CAAC,EAAEkG,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAAE7E,IAAI,EAAE6K,cAAc,CAAClM,KAAK,CAAC,CAAC,CAAC,EAAEkG,iBAAiB,CAAC,CAAC,CAAC;QAAE,CAAC;QACzHqF,QAAQ,CAACc,cAAc,CAACI,QAAQ,CAACpL,IAAI,EAAEoD,QAAQ,CAAC,CAAC,CAAC,EAAEuH,UAAU,CAAC5C,IAAI,CAAC,EAAEiD,cAAc,CAACI,QAAQ,CAAC/N,IAAI,EAAE+F,QAAQ,CAAC,CAAC,CAAC,EAAEuH,UAAU,CAAC7C,GAAG,CAAC,CAAC;QACjIuD,eAAe,GAAG,IAAI,CAAC9E,aAAa,CAACuB,GAAG,KAAK0C,SAAS,IAAI,IAAI,CAACjE,aAAa,CAACwB,IAAI,KAAK0C,UAAU;QAChGpE,cAAc,GAAG+E,QAAQ,CAAC/N,IAAI,KAAK6I,KAAK,CAAC7I,IAAI,IAAI+N,QAAQ,CAACpL,IAAI,KAAKkG,KAAK,CAAClG,IAAI;MACjF,CAAC,MACI;QACDoL,QAAQ,GAAGP,cAAc,CAAClM,KAAK,EAAEkG,iBAAiB,CAAC;QACnD,IAAI,CAAChD,UAAU,GAAGqI,QAAQ,CAACc,cAAc,CAACI,QAAQ,EAAEhI,QAAQ,EAAEuH,UAAU,CAAC5C,IAAI,CAAC,EAAEyC,SAAS,CAAC,GAAGN,QAAQ,CAACO,UAAU,EAAEO,cAAc,CAACI,QAAQ,EAAEhI,QAAQ,EAAEuH,UAAU,CAAC7C,GAAG,CAAC,CAAC;QACrKuD,eAAe,GAAG,IAAI,CAAC9E,aAAa,MAAM,IAAI,CAAC1E,UAAU,GAAG4I,UAAU,GAAGD,SAAS,CAAC;QACnFnE,cAAc,GAAG+E,QAAQ,KAAKlF,KAAK;MACvC;MACA,IAAI,CAACG,cAAc,GAAGA,cAAc;MACpCgF,eAAe,KAAK,IAAI,CAACnF,KAAK,GAAGkF,QAAQ,CAAC;IAC9C;EACJ;EACAE,YAAYA,CAAC3M,KAAK,EAAE4M,EAAE,EAAEnB,QAAQ,GAAG,MAAM,EAAE;IACvC,IAAImB,EAAE,EAAE;MACJ,MAAM;QAAErF,KAAK;QAAEsF;MAAS,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACnD,MAAMvB,QAAQ,GAAGA,CAACnC,IAAI,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,KAAK,IAAI,CAACoC,QAAQ,CAAC;QAAEnC,IAAI;QAAED,GAAG;QAAEsC;MAAS,CAAC,CAAC;MAC9E,MAAMsB,SAAS,GAAGH,EAAE,KAAK,UAAU;MACnC,MAAMI,OAAO,GAAGJ,EAAE,KAAK,QAAQ;MAC/B,IAAIG,SAAS,EAAE;QACX,IAAI,IAAI,CAAC5L,IAAI,EAAE;UACX,IAAI0L,QAAQ,CAACtF,KAAK,CAAC7I,IAAI,GAAG6I,KAAK,CAAC7I,IAAI,GAAGsB,KAAK,CAAC,CAAC,CAAC,EAAE;YAC7CuL,QAAQ,CAACsB,QAAQ,CAACtF,KAAK,CAAClG,IAAI,GAAG,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAE,CAACmI,QAAQ,CAACtF,KAAK,CAAC7I,IAAI,GAAG,CAAC,IAAI,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG,CAAC,MACI,IAAImI,QAAQ,CAACtF,KAAK,CAAClG,IAAI,GAAGkG,KAAK,CAAClG,IAAI,GAAGrB,KAAK,CAAC,CAAC,CAAC,EAAE;YAClDuL,QAAQ,CAAC,CAACsB,QAAQ,CAACtF,KAAK,CAAClG,IAAI,GAAG,CAAC,IAAI,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAEmI,QAAQ,CAACtF,KAAK,CAAC7I,IAAI,GAAG,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG;QACJ,CAAC,MACI;UACD,IAAImI,QAAQ,CAACtF,KAAK,GAAGA,KAAK,GAAGvH,KAAK,EAAE;YAChC,MAAMiN,GAAG,GAAG,CAACJ,QAAQ,CAACtF,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC7C,SAAS;YACjD,IAAI,CAACxB,UAAU,GAAGqI,QAAQ,CAAC0B,GAAG,EAAE,CAAC,CAAC,GAAG1B,QAAQ,CAAC,CAAC,EAAE0B,GAAG,CAAC;UACzD;QACJ;MACJ,CAAC,MACI,IAAID,OAAO,EAAE;QACd,IAAI,IAAI,CAAC7L,IAAI,EAAE;UACX,IAAI0L,QAAQ,CAACrF,IAAI,CAAC9I,IAAI,GAAG6I,KAAK,CAAC7I,IAAI,IAAIsB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACjDuL,QAAQ,CAACsB,QAAQ,CAACtF,KAAK,CAAClG,IAAI,GAAG,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAE,CAACmI,QAAQ,CAACtF,KAAK,CAAC7I,IAAI,GAAG,CAAC,IAAI,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG,CAAC,MACI,IAAImI,QAAQ,CAACrF,IAAI,CAACnG,IAAI,GAAGkG,KAAK,CAAClG,IAAI,IAAIrB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACtDuL,QAAQ,CAAC,CAACsB,QAAQ,CAACtF,KAAK,CAAClG,IAAI,GAAG,CAAC,IAAI,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAEmI,QAAQ,CAACtF,KAAK,CAAC7I,IAAI,GAAG,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG;QACJ,CAAC,MACI;UACD,IAAImI,QAAQ,CAACrF,IAAI,GAAGD,KAAK,IAAIvH,KAAK,GAAG,CAAC,EAAE;YACpC,MAAMiN,GAAG,GAAG,CAACJ,QAAQ,CAACtF,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC7C,SAAS;YACjD,IAAI,CAACxB,UAAU,GAAGqI,QAAQ,CAAC0B,GAAG,EAAE,CAAC,CAAC,GAAG1B,QAAQ,CAAC,CAAC,EAAE0B,GAAG,CAAC;UACzD;QACJ;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACzB,aAAa,CAACxL,KAAK,EAAEyL,QAAQ,CAAC;IACvC;EACJ;EACAqB,gBAAgBA,CAAA,EAAG;IACf,MAAMI,wBAAwB,GAAGA,CAACC,IAAI,EAAEZ,KAAK,KAAMA,KAAK,IAAIY,IAAI,GAAG/B,IAAI,CAACC,KAAK,CAAC8B,IAAI,IAAIZ,KAAK,IAAIY,IAAI,CAAC,CAAC,GAAG,CAAE;IAC1G,IAAIC,eAAe,GAAG,IAAI,CAAC7F,KAAK;IAChC,IAAI8F,cAAc,GAAG,CAAC;IACtB,IAAI,IAAI,CAACnG,gBAAgB,EAAEmD,aAAa,EAAE;MACtC,MAAM;QAAEwB,SAAS;QAAEC;MAAW,CAAC,GAAG,IAAI,CAAC5E,gBAAgB,CAACmD,aAAa;MACrE,IAAI,IAAI,CAAClJ,IAAI,EAAE;QACXiM,eAAe,GAAG;UAAE1O,IAAI,EAAEwO,wBAAwB,CAACrB,SAAS,EAAE,IAAI,CAACnH,SAAS,CAAC,CAAC,CAAC,CAAC;UAAErD,IAAI,EAAE6L,wBAAwB,CAACpB,UAAU,EAAE,IAAI,CAACpH,SAAS,CAAC,CAAC,CAAC;QAAE,CAAC;QACjJ2I,cAAc,GAAG;UAAE3O,IAAI,EAAE0O,eAAe,CAAC1O,IAAI,GAAG,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI;UAAE2C,IAAI,EAAE+L,eAAe,CAAC/L,IAAI,GAAG,IAAI,CAACsG,kBAAkB,CAACtG;QAAK,CAAC;MAC7I,CAAC,MACI;QACD,MAAMiM,SAAS,GAAG,IAAI,CAACpK,UAAU,GAAG4I,UAAU,GAAGD,SAAS;QAC1DuB,eAAe,GAAGF,wBAAwB,CAACI,SAAS,EAAE,IAAI,CAAC5I,SAAS,CAAC;QACrE2I,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACzF,kBAAkB;MAC9D;IACJ;IACA,OAAO;MACHJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfqF,QAAQ,EAAE;QACNtF,KAAK,EAAE6F,eAAe;QACtB5F,IAAI,EAAE6F;MACV;IACJ,CAAC;EACL;EACAtB,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMsB,YAAY,GAAG,CAAC,IAAI,CAACrG,gBAAgB,EAAEmD,aAAa,GAAG,IAAI,CAACnD,gBAAgB,CAACmD,aAAa,CAACmD,WAAW,GAAGxB,UAAU,CAAC5C,IAAI,GAAG,CAAC,KAAK,CAAC;IACxI,MAAMqE,aAAa,GAAG,CAAC,IAAI,CAACvG,gBAAgB,EAAEmD,aAAa,GAAG,IAAI,CAACnD,gBAAgB,CAACmD,aAAa,CAACqD,YAAY,GAAG1B,UAAU,CAAC7C,GAAG,GAAG,CAAC,KAAK,CAAC;IACzI,MAAMwE,2BAA2B,GAAGA,CAACC,YAAY,EAAElJ,SAAS,KAAMA,SAAS,IAAIkJ,YAAY,GAAGxC,IAAI,CAACyC,IAAI,CAACD,YAAY,IAAIlJ,SAAS,IAAIkJ,YAAY,CAAC,CAAC,GAAG,CAAE;IACxJ,MAAME,0BAA0B,GAAIC,SAAS,IAAK3C,IAAI,CAACyC,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC;IAC1E,MAAMpG,kBAAkB,GAAG,IAAI,CAACxG,IAAI,GAC9B;MAAEzC,IAAI,EAAEiP,2BAA2B,CAACF,aAAa,EAAE,IAAI,CAAC/I,SAAS,CAAC,CAAC,CAAC,CAAC;MAAErD,IAAI,EAAEsM,2BAA2B,CAACJ,YAAY,EAAE,IAAI,CAAC7I,SAAS,CAAC,CAAC,CAAC;IAAE,CAAC,GAC3IiJ,2BAA2B,CAAC,IAAI,CAACzK,UAAU,GAAGqK,YAAY,GAAGE,aAAa,EAAE,IAAI,CAAC/I,SAAS,CAAC;IACjG,MAAMwB,iBAAiB,GAAG,IAAI,CAACmB,mBAAmB,KAAK,IAAI,CAAClG,IAAI,GAAG,CAAC2M,0BAA0B,CAACnG,kBAAkB,CAACjJ,IAAI,CAAC,EAAEoP,0BAA0B,CAACnG,kBAAkB,CAACtG,IAAI,CAAC,CAAC,GAAGyM,0BAA0B,CAACnG,kBAAkB,CAAC,CAAC;IAC/N,OAAO;MAAEA,kBAAkB;MAAEzB;IAAkB,CAAC;EACpD;EACA2E,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAElD,kBAAkB;MAAEzB;IAAkB,CAAC,GAAG,IAAI,CAAC6F,iBAAiB,CAAC,CAAC;IAC1E,MAAMiC,aAAa,GAAGA,CAAC1B,MAAM,EAAE2B,IAAI,EAAE7B,KAAK,EAAE8B,OAAO,GAAG,KAAK,KAAK,IAAI,CAACC,OAAO,CAAC7B,MAAM,GAAG2B,IAAI,GAAG,CAAC3B,MAAM,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC,IAAIA,KAAK,EAAE8B,OAAO,CAAC;IACvI,MAAM3G,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMC,IAAI,GAAG,IAAI,CAACrG,IAAI,GAChB;MAAEzC,IAAI,EAAEsP,aAAa,CAAC,IAAI,CAACzG,KAAK,CAAC7I,IAAI,EAAEiJ,kBAAkB,CAACjJ,IAAI,EAAEwH,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAAE7E,IAAI,EAAE2M,aAAa,CAAC,IAAI,CAACzG,KAAK,CAAClG,IAAI,EAAEsG,kBAAkB,CAACtG,IAAI,EAAE6E,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI;IAAE,CAAC,GAClL8H,aAAa,CAAC,IAAI,CAACzG,KAAK,EAAEI,kBAAkB,EAAEzB,iBAAiB,CAAC;IACtE,IAAI,CAACsB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACN,mBAAmB,GAAGnB,iBAAiB;IAC5C,IAAI,IAAI,CAACD,UAAU,EAAE;MACjB,IAAI,CAAC1E,SAAS,GAAG,IAAI,CAACJ,IAAI,GAAGiN,KAAK,CAACC,IAAI,CAAC;QAAE/E,MAAM,EAAE3B,kBAAkB,CAACjJ;MAAK,CAAC,CAAC,CAAC8J,GAAG,CAAC,MAAM4F,KAAK,CAACC,IAAI,CAAC;QAAE/E,MAAM,EAAE3B,kBAAkB,CAACtG;MAAK,CAAC,CAAC,CAAC,GAAG+M,KAAK,CAACC,IAAI,CAAC;QAAE/E,MAAM,EAAE3B;MAAmB,CAAC,CAAC;IACxL;IACA,IAAI,IAAI,CAAChC,KAAK,EAAE;MACZkE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAClC,aAAa,GAAG;UACjBN,KAAK,EAAE,IAAI,CAACrC,KAAK,GAAI,IAAI,CAAC/D,IAAI,GAAG;YAAEzC,IAAI,EAAE,CAAC;YAAE2C,IAAI,EAAEkG,KAAK,CAAClG;UAAK,CAAC,GAAG,CAAC,GAAIkG,KAAK;UAC3EC,IAAI,EAAE4D,IAAI,CAACkD,GAAG,CAAC,IAAI,CAACpJ,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACsC,IAAI,EAAE,IAAI,CAAC9D,KAAK,CAAC4F,MAAM;QACzE,CAAC;QACD,IAAI,CAACiF,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC1G,aAAa,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA2B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACjD,SAAS,IAAI,CAAC,IAAI,CAAChG,SAAS,EAAE;MACnCsJ,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,IAAI,CAACzC,SAAS,EAAE;UAChB,IAAI,CAACA,SAAS,CAAC/C,KAAK,CAACiK,SAAS,GAAG,IAAI,CAAClH,SAAS,CAAC/C,KAAK,CAACkK,QAAQ,GAAG,MAAM;UACvE,IAAI,CAACnH,SAAS,CAAC/C,KAAK,CAACmK,QAAQ,GAAG,UAAU;UAC1C,IAAI,CAACxH,gBAAgB,CAACmD,aAAa,CAAC9F,KAAK,CAACoK,OAAO,GAAG,MAAM;UAC1D,MAAM,CAACpB,YAAY,EAAEE,aAAa,CAAC,GAAG,CAACnQ,UAAU,CAACiN,QAAQ,CAAC,IAAI,CAACjD,SAAS,CAAC,EAAEhK,UAAU,CAACkN,SAAS,CAAC,IAAI,CAAClD,SAAS,CAAC,CAAC;UACjHiG,YAAY,KAAK,IAAI,CAACnF,mBAAmB,KAAK,IAAI,CAAClB,gBAAgB,CAACmD,aAAa,CAAC9F,KAAK,CAACqK,KAAK,GAAG,EAAE,CAAC;UACnGnB,aAAa,KAAK,IAAI,CAACpF,oBAAoB,KAAK,IAAI,CAACnB,gBAAgB,CAACmD,aAAa,CAAC9F,KAAK,CAACsK,MAAM,GAAG,EAAE,CAAC;UACtG,MAAM,CAACD,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACvR,UAAU,CAACiN,QAAQ,CAAC,IAAI,CAACrD,gBAAgB,CAACmD,aAAa,CAAC,EAAE/M,UAAU,CAACkN,SAAS,CAAC,IAAI,CAACtD,gBAAgB,CAACmD,aAAa,CAAC,CAAC;UAC7I,IAAI,CAAC/C,SAAS,CAAC/C,KAAK,CAACiK,SAAS,GAAG,IAAI,CAAClH,SAAS,CAAC/C,KAAK,CAACkK,QAAQ,GAAG,EAAE;UACnE,IAAI,CAACnH,SAAS,CAAC/C,KAAK,CAACmK,QAAQ,GAAG,EAAE;UAClC,IAAI,CAACxH,gBAAgB,CAACmD,aAAa,CAAC9F,KAAK,CAACoK,OAAO,GAAG,EAAE;UACtD,IAAI,CAACzG,YAAY,GAAG0G,KAAK;UACzB,IAAI,CAACzG,aAAa,GAAG0G,MAAM;UAC3B,IAAI,CAACzG,mBAAmB,GAAGmF,YAAY;UACvC,IAAI,CAAClF,oBAAoB,GAAGoF,aAAa;UACzC,CAAC,IAAI,CAACtM,IAAI,IAAI,IAAI,CAAC+B,UAAU,MAAM,IAAI,CAACgE,gBAAgB,CAACmD,aAAa,CAAC9F,KAAK,CAACqK,KAAK,GAAGA,KAAK,GAAG,IAAI,CAAC1G,YAAY,GAAG0G,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC9J,YAAY,IAAI,IAAI,CAACoD,YAAY,GAAG,IAAI,CAAC;UAC9K,CAAC,IAAI,CAAC/G,IAAI,IAAI,IAAI,CAACmH,QAAQ,MAAM,IAAI,CAACpB,gBAAgB,CAACmD,aAAa,CAAC9F,KAAK,CAACsK,MAAM,GAAGA,MAAM,GAAG,IAAI,CAAC1G,aAAa,GAAG0G,MAAM,GAAG,IAAI,GAAG,IAAI,CAACjK,aAAa,IAAI,IAAI,CAACuD,aAAa,GAAG,IAAI,CAAC;QACtL;MACJ,CAAC,CAAC;IACN;EACJ;EACAgG,OAAOA,CAAC3G,IAAI,GAAG,CAAC,EAAEsH,MAAM,GAAG,KAAK,EAAE;IAC9B,OAAO,IAAI,CAACnL,MAAM,GAAGyH,IAAI,CAACkD,GAAG,CAACQ,MAAM,GAAG,CAAC,IAAI,CAAC/I,QAAQ,IAAI,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC,EAAE2F,MAAM,GAAG,IAAI,CAAC3F,MAAM,CAAC2F,MAAM,EAAE9B,IAAI,CAAC,GAAG,CAAC;EACnH;EACAyE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC3E,SAAS,EAAE;MAChB,MAAM/C,KAAK,GAAGwK,gBAAgB,CAAC,IAAI,CAACzH,SAAS,CAAC;MAC9C,MAAM8B,IAAI,GAAG4F,UAAU,CAACzK,KAAK,CAAC0K,WAAW,CAAC,GAAG7D,IAAI,CAAC8D,GAAG,CAACF,UAAU,CAACzK,KAAK,CAAC6E,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACrF,MAAM+F,KAAK,GAAGH,UAAU,CAACzK,KAAK,CAAC6K,YAAY,CAAC,GAAGhE,IAAI,CAAC8D,GAAG,CAACF,UAAU,CAACzK,KAAK,CAAC4K,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACxF,MAAMhG,GAAG,GAAG6F,UAAU,CAACzK,KAAK,CAAC8K,UAAU,CAAC,GAAGjE,IAAI,CAAC8D,GAAG,CAACF,UAAU,CAACzK,KAAK,CAAC4E,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAClF,MAAMmG,MAAM,GAAGN,UAAU,CAACzK,KAAK,CAACgL,aAAa,CAAC,GAAGnE,IAAI,CAAC8D,GAAG,CAACF,UAAU,CAACzK,KAAK,CAAC+K,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAC3F,OAAO;QAAElG,IAAI;QAAE+F,KAAK;QAAEhG,GAAG;QAAEmG,MAAM;QAAEE,CAAC,EAAEpG,IAAI,GAAG+F,KAAK;QAAEM,CAAC,EAAEtG,GAAG,GAAGmG;MAAO,CAAC;IACzE;IACA,OAAO;MAAElG,IAAI,EAAE,CAAC;MAAE+F,KAAK,EAAE,CAAC;MAAEhG,GAAG,EAAE,CAAC;MAAEmG,MAAM,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAC/D;EACA7E,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC1D,gBAAgB,EAAEmD,aAAa,EAAE;MACtC,MAAMqF,aAAa,GAAG,IAAI,CAACxI,gBAAgB,CAACmD,aAAa,CAACqF,aAAa,CAACA,aAAa;MACrF,MAAMd,KAAK,GAAG,IAAI,CAAC9J,YAAY,IAAI,GAAG,IAAI,CAACoC,gBAAgB,CAACmD,aAAa,CAACmD,WAAW,IAAIkC,aAAa,CAAClC,WAAW,IAAI;MACtH,MAAMqB,MAAM,GAAG,IAAI,CAACjK,aAAa,IAAI,GAAG,IAAI,CAACsC,gBAAgB,CAACmD,aAAa,CAACqD,YAAY,IAAIgC,aAAa,CAAChC,YAAY,IAAI;MAC1H,MAAMiC,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAM,IAAI,CAAC3I,gBAAgB,CAACmD,aAAa,CAAC9F,KAAK,CAACqL,KAAK,CAAC,GAAGC,MAAO;MAC9F,IAAI,IAAI,CAAC1O,IAAI,IAAI,IAAI,CAAC+B,UAAU,EAAE;QAC9ByM,OAAO,CAAC,QAAQ,EAAEd,MAAM,CAAC;QACzBc,OAAO,CAAC,OAAO,EAAEf,KAAK,CAAC;MAC3B,CAAC,MACI;QACDe,OAAO,CAAC,QAAQ,EAAEd,MAAM,CAAC;MAC7B;IACJ;EACJ;EACA/D,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACnH,MAAM,EAAE;MACb,MAAMgM,OAAO,GAAGA,CAACC,KAAK,EAAEE,MAAM,EAAEvD,KAAK,KAAM,IAAI,CAAC1L,WAAW,GAAG;QAAE,GAAG,IAAI,CAACA,WAAW;QAAE,GAAG;UAAE,CAAC,GAAG+O,KAAK,EAAE,GAAGE,MAAM,GAAGvD,KAAK,GAAG;QAAK;MAAE,CAAE;MAClI,MAAMwD,QAAQ,GAAG,IAAI,CAACpM,MAAM,CAAC2F,MAAM;MACnC,IAAI,IAAI,CAACnI,IAAI,EAAE;QACXwO,OAAO,CAAC,QAAQ,EAAEI,QAAQ,EAAE,IAAI,CAACrL,SAAS,CAAC,CAAC,CAAC,CAAC;QAC9CiL,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC5J,QAAQ,EAAEuD,MAAM,IAAI,IAAI,CAAC3F,MAAM,CAAC,CAAC,CAAC,EAAE2F,MAAM,EAAE,IAAI,CAAC5E,SAAS,CAAC,CAAC,CAAC,CAAC;MACxF,CAAC,MACI;QACD,IAAI,CAACxB,UAAU,GAAGyM,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC5J,QAAQ,EAAEuD,MAAM,IAAI,IAAI,CAAC3F,MAAM,CAAC2F,MAAM,EAAE,IAAI,CAAC5E,SAAS,CAAC,GAAGiL,OAAO,CAAC,QAAQ,EAAEI,QAAQ,EAAE,IAAI,CAACrL,SAAS,CAAC;MACjJ;IACJ;EACJ;EACAsL,kBAAkBA,CAAC/C,GAAG,EAAE;IACpB,IAAI,IAAI,CAAC3F,SAAS,IAAI,CAAC,IAAI,CAAC9B,WAAW,EAAE;MACrC,MAAM+B,KAAK,GAAG0F,GAAG,GAAGA,GAAG,CAAC1F,KAAK,GAAG,IAAI,CAACA,KAAK;MAC1C,MAAM0I,qBAAqB,GAAGA,CAAC3D,MAAM,EAAEC,KAAK,KAAKD,MAAM,GAAGC,KAAK;MAC/D,MAAM2D,YAAY,GAAGA,CAACC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,KAAM,IAAI,CAAC5P,YAAY,GAAG;QAAE,GAAG,IAAI,CAACA,YAAY;QAAE,GAAG;UAAE6P,SAAS,EAAE,eAAeF,EAAE,OAAOC,EAAE;QAAS;MAAE,CAAE;MAC7I,IAAI,IAAI,CAACjP,IAAI,EAAE;QACX+O,YAAY,CAACD,qBAAqB,CAAC1I,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEuL,qBAAqB,CAAC1I,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5H,CAAC,MACI;QACD,MAAM4L,YAAY,GAAGL,qBAAqB,CAAC1I,KAAK,EAAE,IAAI,CAAC7C,SAAS,CAAC;QACjE,IAAI,CAACxB,UAAU,GAAGgN,YAAY,CAACI,YAAY,EAAE,CAAC,CAAC,GAAGJ,YAAY,CAAC,CAAC,EAAEI,YAAY,CAAC;MACnF;IACJ;EACJ;EACAC,sBAAsBA,CAACC,KAAK,EAAE;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,MAAMzE,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMyE,kBAAkB,GAAGA,CAACvD,IAAI,EAAEX,KAAK,KAAMW,IAAI,GAAIA,IAAI,GAAGX,KAAK,GAAGW,IAAI,GAAGX,KAAK,GAAGW,IAAI,GAAI,CAAE;IAC7F,MAAMwD,qBAAqB,GAAGA,CAACxD,IAAI,EAAEZ,KAAK,KAAMA,KAAK,IAAIY,IAAI,GAAG/B,IAAI,CAACC,KAAK,CAAC8B,IAAI,IAAIZ,KAAK,IAAIY,IAAI,CAAC,CAAC,GAAG,CAAE;IACvG,MAAMyD,qBAAqB,GAAGA,CAACC,aAAa,EAAEvE,MAAM,EAAEwE,KAAK,EAAE7C,IAAI,EAAE7B,KAAK,EAAE2E,oBAAoB,KAAK;MAC/F,OAAOF,aAAa,IAAIzE,KAAK,GAAGA,KAAK,GAAG2E,oBAAoB,GAAGD,KAAK,GAAG7C,IAAI,GAAG7B,KAAK,GAAGE,MAAM,GAAGF,KAAK,GAAG,CAAC;IAC5G,CAAC;IACD,MAAMF,cAAc,GAAGA,CAAC2E,aAAa,EAAEG,aAAa,EAAE1E,MAAM,EAAEwE,KAAK,EAAE7C,IAAI,EAAE7B,KAAK,EAAE2E,oBAAoB,KAAK;MACvG,IAAIF,aAAa,IAAIzE,KAAK,EACtB,OAAO,CAAC,CAAC,KAET,OAAOhB,IAAI,CAAC8D,GAAG,CAAC,CAAC,EAAE6B,oBAAoB,GAAIF,aAAa,GAAGG,aAAa,GAAG1E,MAAM,GAAGuE,aAAa,GAAGzE,KAAK,GAAIyE,aAAa,GAAGG,aAAa,GAAG1E,MAAM,GAAGuE,aAAa,GAAG,CAAC,GAAGzE,KAAK,CAAC;IACxL,CAAC;IACD,MAAM4B,aAAa,GAAGA,CAAC6C,aAAa,EAAEvE,MAAM,EAAEwE,KAAK,EAAE7C,IAAI,EAAE7B,KAAK,EAAE8B,OAAO,GAAG,KAAK,KAAK;MAClF,IAAI+C,SAAS,GAAG3E,MAAM,GAAG2B,IAAI,GAAG,CAAC,GAAG7B,KAAK;MACzC,IAAIyE,aAAa,IAAIzE,KAAK,EAAE;QACxB6E,SAAS,IAAI7E,KAAK,GAAG,CAAC;MAC1B;MACA,OAAO,IAAI,CAAC+B,OAAO,CAAC8C,SAAS,EAAE/C,OAAO,CAAC;IAC3C,CAAC;IACD,MAAMrC,SAAS,GAAG6E,kBAAkB,CAACD,MAAM,CAAC5E,SAAS,EAAEG,UAAU,CAAC7C,GAAG,CAAC;IACtE,MAAM2C,UAAU,GAAG4E,kBAAkB,CAACD,MAAM,CAAC3E,UAAU,EAAEE,UAAU,CAAC5C,IAAI,CAAC;IACzE,IAAIqD,QAAQ,GAAG,IAAI,CAACtL,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACnD,IAAI6P,OAAO,GAAG,IAAI,CAAC1J,IAAI;IACvB,IAAIE,cAAc,GAAG,KAAK;IAC1B,IAAIyJ,YAAY,GAAG,IAAI,CAACvJ,aAAa;IACrC,IAAI,IAAI,CAACzG,IAAI,EAAE;MACX,MAAMiQ,YAAY,GAAG,IAAI,CAACxJ,aAAa,CAACuB,GAAG,IAAI0C,SAAS;MACxD,MAAMwF,aAAa,GAAG,IAAI,CAACzJ,aAAa,CAACwB,IAAI,IAAI0C,UAAU;MAC3D,IAAI,CAAC,IAAI,CAACtG,WAAW,IAAK,IAAI,CAACA,WAAW,KAAK4L,YAAY,IAAIC,aAAa,CAAE,EAAE;QAC5E,MAAMC,YAAY,GAAG;UAAE5S,IAAI,EAAEiS,qBAAqB,CAAC9E,SAAS,EAAE,IAAI,CAACnH,SAAS,CAAC,CAAC,CAAC,CAAC;UAAErD,IAAI,EAAEsP,qBAAqB,CAAC7E,UAAU,EAAE,IAAI,CAACpH,SAAS,CAAC,CAAC,CAAC;QAAE,CAAC;QAC9I,MAAM6M,YAAY,GAAG;UACjB7S,IAAI,EAAEkS,qBAAqB,CAACU,YAAY,CAAC5S,IAAI,EAAE,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,EAAE,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI,EAAE,IAAI,CAAC2I,mBAAmB,CAAC,CAAC,CAAC,EAAE+J,YAAY,CAAC;UACxJ/P,IAAI,EAAEuP,qBAAqB,CAACU,YAAY,CAACjQ,IAAI,EAAE,IAAI,CAACkG,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,EAAE,IAAI,CAACsG,kBAAkB,CAACtG,IAAI,EAAE,IAAI,CAACgG,mBAAmB,CAAC,CAAC,CAAC,EAAEgK,aAAa;QAC5J,CAAC;QACD5E,QAAQ,GAAG;UACP/N,IAAI,EAAEwN,cAAc,CAACoF,YAAY,CAAC5S,IAAI,EAAE6S,YAAY,CAAC7S,IAAI,EAAE,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,EAAE,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI,EAAE,IAAI,CAAC2I,mBAAmB,CAAC,CAAC,CAAC,EAAE+J,YAAY,CAAC;UACpK/P,IAAI,EAAE6K,cAAc,CAACoF,YAAY,CAACjQ,IAAI,EAAEkQ,YAAY,CAAClQ,IAAI,EAAE,IAAI,CAACkG,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,EAAE,IAAI,CAACsG,kBAAkB,CAACtG,IAAI,EAAE,IAAI,CAACgG,mBAAmB,CAAC,CAAC,CAAC,EAAEgK,aAAa;QACxK,CAAC;QACDH,OAAO,GAAG;UACNxS,IAAI,EAAEsP,aAAa,CAACsD,YAAY,CAAC5S,IAAI,EAAE+N,QAAQ,CAAC/N,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,EAAE,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI,EAAE,IAAI,CAAC2I,mBAAmB,CAAC,CAAC,CAAC,CAAC;UAChIhG,IAAI,EAAE2M,aAAa,CAACsD,YAAY,CAACjQ,IAAI,EAAEoL,QAAQ,CAACpL,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,EAAE,IAAI,CAACsG,kBAAkB,CAACtG,IAAI,EAAE,IAAI,CAACgG,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI;QACzI,CAAC;QACDK,cAAc,GAAG+E,QAAQ,CAAC/N,IAAI,KAAK,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,IAAIwS,OAAO,CAACxS,IAAI,KAAK,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,IAAI+N,QAAQ,CAACpL,IAAI,KAAK,IAAI,CAACkG,KAAK,CAAClG,IAAI,IAAI6P,OAAO,CAAC7P,IAAI,KAAK,IAAI,CAACmG,IAAI,CAACnG,IAAI,IAAI,IAAI,CAACqG,cAAc;QACpLyJ,YAAY,GAAG;UAAEhI,GAAG,EAAE0C,SAAS;UAAEzC,IAAI,EAAE0C;QAAW,CAAC;MACvD;IACJ,CAAC,MACI;MACD,MAAMwB,SAAS,GAAG,IAAI,CAACpK,UAAU,GAAG4I,UAAU,GAAGD,SAAS;MAC1D,MAAM2F,mBAAmB,GAAG,IAAI,CAAC5J,aAAa,IAAI0F,SAAS;MAC3D,IAAI,CAAC,IAAI,CAAC9H,WAAW,IAAK,IAAI,CAACA,WAAW,IAAIgM,mBAAoB,EAAE;QAChE,MAAMF,YAAY,GAAGX,qBAAqB,CAACrD,SAAS,EAAE,IAAI,CAAC5I,SAAS,CAAC;QACrE,MAAM6M,YAAY,GAAGX,qBAAqB,CAACU,YAAY,EAAE,IAAI,CAAC/J,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,EAAEmK,mBAAmB,CAAC;QACvJ/E,QAAQ,GAAGP,cAAc,CAACoF,YAAY,EAAEC,YAAY,EAAE,IAAI,CAAChK,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,EAAEmK,mBAAmB,CAAC;QACpJN,OAAO,GAAGlD,aAAa,CAACsD,YAAY,EAAE7E,QAAQ,EAAE,IAAI,CAACjF,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,CAAC;QAC7GK,cAAc,GAAG+E,QAAQ,KAAK,IAAI,CAAClF,KAAK,IAAI2J,OAAO,KAAK,IAAI,CAAC1J,IAAI,IAAI,IAAI,CAACE,cAAc;QACxFyJ,YAAY,GAAG7D,SAAS;MAC5B;IACJ;IACA,OAAO;MACH/F,KAAK,EAAEkF,QAAQ;MACfjF,IAAI,EAAE0J,OAAO;MACbxJ,cAAc;MACd4F,SAAS,EAAE6D;IACf,CAAC;EACL;EACAM,cAAcA,CAACjB,KAAK,EAAE;IAClB,MAAM;MAAEjJ,KAAK;MAAEC,IAAI;MAAEE,cAAc;MAAE4F;IAAU,CAAC,GAAG,IAAI,CAACiD,sBAAsB,CAACC,KAAK,CAAC;IACrF,IAAI9I,cAAc,EAAE;MAChB,MAAMgK,QAAQ,GAAG;QAAEnK,KAAK;QAAEC;MAAK,CAAC;MAChC,IAAI,CAACwI,kBAAkB,CAAC0B,QAAQ,CAAC;MACjC,IAAI,CAACnK,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACI,aAAa,GAAG0F,SAAS;MAC9B,IAAI,CAACiB,YAAY,CAAC,qBAAqB,EAAEmD,QAAQ,CAAC;MAClD,IAAI,IAAI,CAAC/L,KAAK,IAAI,IAAI,CAAC2F,aAAa,CAAC/D,KAAK,CAAC,EAAE;QACzC,MAAMM,aAAa,GAAG;UAClBN,KAAK,EAAE,IAAI,CAACrC,KAAK,GAAGkG,IAAI,CAACkD,GAAG,CAAC,IAAI,CAACnD,cAAc,CAAC5D,KAAK,CAAC,GAAG,IAAI,CAACrC,KAAK,EAAE,IAAI,CAACxB,KAAK,CAAC4F,MAAM,GAAG,IAAI,CAACpE,KAAK,CAAC,GAAGqC,KAAK;UAC7GC,IAAI,EAAE4D,IAAI,CAACkD,GAAG,CAAC,IAAI,CAACpJ,KAAK,GAAG,CAAC,IAAI,CAACiG,cAAc,CAAC5D,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAACrC,KAAK,GAAGsC,IAAI,EAAE,IAAI,CAAC9D,KAAK,CAAC4F,MAAM;QACvG,CAAC;QACD,MAAMqI,kBAAkB,GAAG,IAAI,CAAC9J,aAAa,CAACN,KAAK,KAAKM,aAAa,CAACN,KAAK,IAAI,IAAI,CAACM,aAAa,CAACL,IAAI,KAAKK,aAAa,CAACL,IAAI;QAC7HmK,kBAAkB,IAAI,IAAI,CAACpD,YAAY,CAAC,YAAY,EAAE1G,aAAa,CAAC;QACpE,IAAI,CAACA,aAAa,GAAGA,aAAa;MACtC;IACJ;EACJ;EACAlF,iBAAiBA,CAAC6N,KAAK,EAAE;IACrB,IAAI,CAACjC,YAAY,CAAC,UAAU,EAAE;MAAEqD,aAAa,EAAEpB;IAAM,CAAC,CAAC;IACvD,IAAI,IAAI,CAACpL,MAAM,IAAI,IAAI,CAACkG,aAAa,CAAC,CAAC,EAAE;MACrC,IAAI,IAAI,CAACxD,aAAa,EAAE;QACpB+J,YAAY,CAAC,IAAI,CAAC/J,aAAa,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAACvH,SAAS,IAAI,IAAI,CAAC0F,UAAU,EAAE;QACpC,MAAM;UAAEyB;QAAe,CAAC,GAAG,IAAI,CAAC6I,sBAAsB,CAACC,KAAK,CAAC;QAC7D,MAAMsB,OAAO,GAAGpK,cAAc,KAAK,IAAI,CAACxC,KAAK,GAAG,IAAI,CAACoG,aAAa,CAAC,CAAC,GAAG,KAAK,CAAC;QAC7E,IAAIwG,OAAO,EAAE;UACT,IAAI,CAACvR,SAAS,GAAG,IAAI;UACrB,IAAI,CAAC4D,EAAE,CAAC4G,aAAa,CAAC,CAAC;QAC3B;MACJ;MACA,IAAI,CAACjD,aAAa,GAAGiK,UAAU,CAAC,MAAM;QAClC,IAAI,CAACN,cAAc,CAACjB,KAAK,CAAC;QAC1B,IAAI,IAAI,CAACjQ,SAAS,IAAI,IAAI,CAAC0F,UAAU,KAAK,CAAC,IAAI,CAACN,KAAK,IAAI,IAAI,CAACU,QAAQ,KAAK2L,SAAS,CAAC,EAAE;UACnF,IAAI,CAACzR,SAAS,GAAG,KAAK;UACtB,IAAI,CAACkH,IAAI,GAAG,IAAI,CAAC0D,cAAc,CAAC,CAAC;UACjC,IAAI,CAAChH,EAAE,CAAC4G,aAAa,CAAC,CAAC;QAC3B;MACJ,CAAC,EAAE,IAAI,CAAC3F,MAAM,CAAC;IACnB,CAAC,MACI;MACD,CAAC,IAAI,CAAC7E,SAAS,IAAI,IAAI,CAACkR,cAAc,CAACjB,KAAK,CAAC;IACjD;EACJ;EACArG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAClC,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACgK,SAAS,CAAC,IAAI,CAAC/K,gBAAgB,EAAEmD,aAAa,CAAC;MACnE,IAAI,CAACpC,cAAc,GAAG,IAAI;IAC9B;EACJ;EACAyC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC3C,aAAa,EAAE;MACpB8J,YAAY,CAAC,IAAI,CAAC9J,aAAa,CAAC;IACpC;IACA,IAAI,CAACA,aAAa,GAAGgK,UAAU,CAAC,MAAM;MAClC,IAAIzU,UAAU,CAAC8M,SAAS,CAAC,IAAI,CAAClD,gBAAgB,EAAEmD,aAAa,CAAC,EAAE;QAC5D,MAAM,CAACuE,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACvR,UAAU,CAACiN,QAAQ,CAAC,IAAI,CAACrD,gBAAgB,EAAEmD,aAAa,CAAC,EAAE/M,UAAU,CAACkN,SAAS,CAAC,IAAI,CAACtD,gBAAgB,EAAEmD,aAAa,CAAC,CAAC;QAC/I,MAAM,CAAC6H,WAAW,EAAEC,YAAY,CAAC,GAAG,CAACvD,KAAK,KAAK,IAAI,CAAC1G,YAAY,EAAE2G,MAAM,KAAK,IAAI,CAAC1G,aAAa,CAAC;QAChG,MAAMiK,MAAM,GAAG,IAAI,CAACjR,IAAI,GAAG+Q,WAAW,IAAIC,YAAY,GAAG,IAAI,CAACjP,UAAU,GAAGgP,WAAW,GAAG,IAAI,CAAC5J,QAAQ,GAAG6J,YAAY,GAAG,KAAK;QAC7HC,MAAM,IACF,IAAI,CAAChO,IAAI,CAACiO,GAAG,CAAC,MAAM;UAChB,IAAI,CAAChL,mBAAmB,GAAG,IAAI,CAAClB,kBAAkB;UAClD,IAAI,CAACoD,IAAI,CAAC,CAAC;UACX,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC5B,CAAC,CAAC;MACV;IACJ,CAAC,EAAE,IAAI,CAAClE,YAAY,CAAC;EACzB;EACAiJ,YAAYA,CAAC+D,IAAI,EAAEC,MAAM,EAAE;IACvB;IACA,OAAO,IAAI,CAACtU,OAAO,IAAI,IAAI,CAACA,OAAO,CAACqU,IAAI,CAAC,GAAG,IAAI,CAACrU,OAAO,CAACqU,IAAI,CAAC,CAACC,MAAM,CAAC,GAAG,IAAI,CAACD,IAAI,CAAC,CAACE,IAAI,CAACD,MAAM,CAAC;EACpG;EACA5S,iBAAiBA,CAAA,EAAG;IAChB,OAAO;MACH8S,iBAAiB,EAAE,sBAAsB,IAAI,CAAClS,SAAS,GAAG,oBAAoB,GAAG,EAAE,EAAE;MACrFmD,KAAK,EAAE,IAAI,CAAChE,WAAW;MACvBgT,cAAc,EAAG1S,KAAK,IAAK,IAAI,CAACE,UAAU,CAACF,KAAK,CAAC;MACjDoG,OAAO,EAAE,IAAI,CAAC7F,SAAS;MACvBW,gBAAgB,EAAEA,CAAClB,KAAK,EAAE/B,OAAO,KAAK,IAAI,CAACiD,gBAAgB,CAAClB,KAAK,EAAE/B,OAAO,CAAC;MAC3EwG,QAAQ,EAAE,IAAI,CAACC,SAAS;MACxBhG,IAAI,EAAE,IAAI,CAACgK,UAAU;MACrB/J,OAAO,EAAE,IAAI,CAACiF,aAAa;MAC3B/C,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/B8H,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBpF,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B/B,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;EACL;EACAjB,UAAUA,CAACyS,aAAa,EAAE;IACtB,MAAMC,KAAK,GAAG,CAAC,IAAI,CAACjP,MAAM,IAAI,EAAE,EAAE2F,MAAM;IACxC,MAAMtJ,KAAK,GAAG,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACoG,KAAK,CAAC7I,IAAI,GAAGiU,aAAa,GAAG,IAAI,CAACpL,KAAK,GAAGoL,aAAa;IACtF,OAAO;MACH3S,KAAK;MACL4S,KAAK;MACLrL,KAAK,EAAEvH,KAAK,KAAK,CAAC;MAClBwH,IAAI,EAAExH,KAAK,KAAK4S,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAE7S,KAAK,GAAG,CAAC,KAAK,CAAC;MACrB8S,GAAG,EAAE9S,KAAK,GAAG,CAAC,KAAK;IACvB,CAAC;EACL;EACAkB,gBAAgBA,CAAClB,KAAK,EAAE+S,UAAU,EAAE;IAChC,MAAMH,KAAK,GAAG,IAAI,CAACrR,SAAS,CAAC+H,MAAM;IACnC,OAAO;MACHtJ,KAAK;MACL4S,KAAK;MACLrL,KAAK,EAAEvH,KAAK,KAAK,CAAC;MAClBwH,IAAI,EAAExH,KAAK,KAAK4S,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAE7S,KAAK,GAAG,CAAC,KAAK,CAAC;MACrB8S,GAAG,EAAE9S,KAAK,GAAG,CAAC,KAAK,CAAC;MACpB,GAAG+S;IACP,CAAC;EACL;EACA,OAAOC,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFnP,QAAQ,EAAlBvH,EAAE,CAAA2W,iBAAA,CAAkC7W,QAAQ,GAA5CE,EAAE,CAAA2W,iBAAA,CAAuDzW,WAAW,GAApEF,EAAE,CAAA2W,iBAAA,CAA+E3W,EAAE,CAAC4W,SAAS,GAA7F5W,EAAE,CAAA2W,iBAAA,CAAwG3W,EAAE,CAAC6W,iBAAiB,GAA9H7W,EAAE,CAAA2W,iBAAA,CAAyI3W,EAAE,CAAC8W,MAAM;EAAA;EAC7O,OAAOC,IAAI,kBAD8E/W,EAAE,CAAAgX,iBAAA;IAAAC,IAAA,EACJ1P,QAAQ;IAAA2P,SAAA;IAAAC,cAAA,WAAAC,wBAAA/U,EAAA,EAAAC,GAAA,EAAA+U,QAAA;MAAA,IAAAhV,EAAA;QADNrC,EAAE,CAAAsX,cAAA,CAAAD,QAAA,EACozBzW,aAAa;MAAA;MAAA,IAAAyB,EAAA;QAAA,IAAAkV,EAAA;QADn0BvX,EAAE,CAAAwX,cAAA,CAAAD,EAAA,GAAFvX,EAAE,CAAAyX,WAAA,QAAAnV,GAAA,CAAAsI,SAAA,GAAA2M,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,eAAAtV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAA4X,WAAA,CAAA5W,GAAA;QAAFhB,EAAE,CAAA4X,WAAA,CAAA3W,GAAA;MAAA;MAAA,IAAAoB,EAAA;QAAA,IAAAkV,EAAA;QAAFvX,EAAE,CAAAwX,cAAA,CAAAD,EAAA,GAAFvX,EAAE,CAAAyX,WAAA,QAAAnV,GAAA,CAAAoI,gBAAA,GAAA6M,EAAA,CAAAxM,KAAA;QAAF/K,EAAE,CAAAwX,cAAA,CAAAD,EAAA,GAAFvX,EAAE,CAAAyX,WAAA,QAAAnV,GAAA,CAAAqI,gBAAA,GAAA4M,EAAA,CAAAxM,KAAA;MAAA;IAAA;IAAA8M,SAAA;IAAAC,MAAA;MAAAjQ,EAAA;MAAAE,KAAA;MAAA/F,UAAA;MAAA4E,QAAA;MAAAM,KAAA;MAAAe,QAAA;MAAAE,YAAA;MAAAE,WAAA;MAAAE,WAAA;MAAAE,IAAA;MAAAE,KAAA;MAAAE,WAAA;MAAAE,UAAA;MAAAtC,MAAA;MAAAyC,IAAA;MAAAE,QAAA;MAAAtC,cAAA;MAAA3E,OAAA;MAAAqH,UAAA;MAAAC,UAAA;MAAAC,iBAAA;MAAAE,OAAA;MAAAE,QAAA;MAAAE,OAAA;MAAAvI,OAAA;IAAA;IAAAsW,OAAA;MAAAxN,UAAA;MAAAC,QAAA;MAAAC,mBAAA;IAAA;IAAAuN,QAAA,GAAFhY,EAAE,CAAAiY,oBAAA;IAAAC,kBAAA,EAAAhX,GAAA;IAAAiX,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAlL,QAAA,WAAAmL,kBAAAjW,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAAuY,eAAA;QAAFvY,EAAE,CAAA0C,UAAA,IAAAiD,gCAAA,0BAE/B,CAAC,IAAA0B,+BAAA,gCAF4BrH,EAAE,CAAAsF,sBA0CxD,CAAC;MAAA;MAAA,IAAAjD,EAAA;QAAA,MAAAmW,oBAAA,GA1CqDxY,EAAE,CAAAwF,WAAA;QAAFxF,EAAE,CAAA+C,UAAA,UAAAT,GAAA,CAAA+G,SAEvD,CAAC,aAAAmP,oBAAqB,CAAC;MAAA;IAAA;IAAAC,YAAA,EAAAA,CAAA,MA8C0qB7Y,EAAE,CAAC8Y,OAAO,EAAyG9Y,EAAE,CAAC+Y,OAAO,EAAwI/Y,EAAE,CAACgZ,IAAI,EAAkHhZ,EAAE,CAACiZ,gBAAgB,EAAyKjZ,EAAE,CAACkZ,OAAO,EAAgG/X,WAAW;IAAAgY,MAAA;IAAAC,aAAA;EAAA;AACx5C;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlD6FjZ,EAAE,CAAAkZ,iBAAA,CAkDJ3R,QAAQ,EAAc,CAAC;IACtG0P,IAAI,EAAE9W,SAAS;IACfgZ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEjM,QAAQ,EAAE;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEkM,eAAe,EAAEjZ,uBAAuB,CAACkZ,OAAO;MAAEN,aAAa,EAAE3Y,iBAAiB,CAACkZ,IAAI;MAAEC,IAAI,EAAE;QAC9EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,woBAAwoB;IAAE,CAAC;EACnqB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9B,IAAI,EAAEyC,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9C1C,IAAI,EAAE3W,MAAM;MACZ6Y,IAAI,EAAE,CAACrZ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEmX,IAAI,EAAEzB,SAAS;IAAEmE,UAAU,EAAE,CAAC;MAClC1C,IAAI,EAAE3W,MAAM;MACZ6Y,IAAI,EAAE,CAACjZ,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE+W,IAAI,EAAEjX,EAAE,CAAC4W;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAEjX,EAAE,CAAC6W;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAEjX,EAAE,CAAC8W;EAAO,CAAC,CAAC,EAAkB;IAAEjP,EAAE,EAAE,CAAC;MAC3GoP,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEwH,KAAK,EAAE,CAAC;MACRkP,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEyB,UAAU,EAAE,CAAC;MACbiV,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEqG,QAAQ,EAAE,CAAC;MACXqQ,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAE2G,KAAK,EAAE,CAAC;MACR+P,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAE0H,QAAQ,EAAE,CAAC;MACXgP,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAE4H,YAAY,EAAE,CAAC;MACf8O,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAE8H,WAAW,EAAE,CAAC;MACd4O,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEgI,WAAW,EAAE,CAAC;MACd0O,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEkI,IAAI,EAAE,CAAC;MACPwO,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEoI,KAAK,EAAE,CAAC;MACRsO,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEsI,WAAW,EAAE,CAAC;MACdoO,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEwI,UAAU,EAAE,CAAC;MACbkO,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEkG,MAAM,EAAE,CAAC;MACTwQ,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAE2I,IAAI,EAAE,CAAC;MACP+N,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAE6I,QAAQ,EAAE,CAAC;MACX6N,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEuG,cAAc,EAAE,CAAC;MACjBmQ,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAE4B,OAAO,EAAE,CAAC;MACV8U,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEiJ,UAAU,EAAE,CAAC;MACbyN,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEkJ,UAAU,EAAE,CAAC;MACbwN,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEmJ,iBAAiB,EAAE,CAAC;MACpBuN,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVqN,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEuJ,QAAQ,EAAE,CAAC;MACXmN,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEyJ,OAAO,EAAE,CAAC;MACViN,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEkB,OAAO,EAAE,CAAC;MACVwV,IAAI,EAAE1W;IACV,CAAC,CAAC;IAAEgK,UAAU,EAAE,CAAC;MACb0M,IAAI,EAAEzW;IACV,CAAC,CAAC;IAAEgK,QAAQ,EAAE,CAAC;MACXyM,IAAI,EAAEzW;IACV,CAAC,CAAC;IAAEiK,mBAAmB,EAAE,CAAC;MACtBwM,IAAI,EAAEzW;IACV,CAAC,CAAC;IAAEkK,gBAAgB,EAAE,CAAC;MACnBuM,IAAI,EAAExW,SAAS;MACf0Y,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAExO,gBAAgB,EAAE,CAAC;MACnBsM,IAAI,EAAExW,SAAS;MACf0Y,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEvO,SAAS,EAAE,CAAC;MACZqM,IAAI,EAAEvW,eAAe;MACrByY,IAAI,EAAE,CAACvY,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMgZ,cAAc,CAAC;EACjB,OAAOpD,IAAI,YAAAqD,uBAAAnD,CAAA;IAAA,YAAAA,CAAA,IAAwFkD,cAAc;EAAA;EACjH,OAAOE,IAAI,kBAhL8E9Z,EAAE,CAAA+Z,gBAAA;IAAA9C,IAAA,EAgLS2C;EAAc;EAClH,OAAOI,IAAI,kBAjL8Eha,EAAE,CAAAia,gBAAA;IAAAC,OAAA,GAiLmCna,YAAY,EAAEc,YAAY,EAAEE,WAAW,EAAEF,YAAY;EAAA;AACvL;AACA;EAAA,QAAAoY,SAAA,oBAAAA,SAAA,KAnL6FjZ,EAAE,CAAAkZ,iBAAA,CAmLJU,cAAc,EAAc,CAAC;IAC5G3C,IAAI,EAAEtW,QAAQ;IACdwY,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAACna,YAAY,EAAEc,YAAY,EAAEE,WAAW,CAAC;MAClDoZ,OAAO,EAAE,CAAC5S,QAAQ,EAAE1G,YAAY,CAAC;MACjCuZ,YAAY,EAAE,CAAC7S,QAAQ;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEqS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}