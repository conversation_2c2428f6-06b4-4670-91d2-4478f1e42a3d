{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/language.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/tooltip\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/sidebar\";\nimport * as i6 from \"primeng/panelmenu\";\nimport * as i7 from \"primeng/button\";\nconst _c0 = [\"*\"];\nfunction LayoutComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"\\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 24);\n    i0.ɵɵtext(4, \"\\u0645\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LayoutComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"Ahmed Mohamed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 24);\n    i0.ɵɵtext(4, \"System Admin\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LayoutComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"i\", 5);\n    i0.ɵɵelementStart(2, \"span\", 26);\n    i0.ɵɵtext(3, \"Menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class LayoutComponent {\n  constructor(languageService) {\n    this.languageService = languageService;\n    this.sidebarVisible = false;\n    this.menuItems = [];\n  }\n  ngOnInit() {\n    this.initializeMenuItems();\n    // Subscribe to language changes to update menu items\n    this.languageService.currentLanguage$.subscribe(() => {\n      this.initializeMenuItems();\n    });\n  }\n  initializeMenuItems() {\n    this.menuItems = [{\n      label: this.languageService.translate('nav.dashboard'),\n      icon: 'pi pi-home',\n      routerLink: '/dashboard'\n    }, {\n      label: this.languageService.translate('nav.inventoryManagement'),\n      icon: 'pi pi-box',\n      expanded: true,\n      items: [{\n        label: this.languageService.translate('nav.items'),\n        icon: 'pi pi-list',\n        routerLink: '/items'\n      }, {\n        label: this.languageService.translate('nav.categories'),\n        icon: 'pi pi-sitemap',\n        routerLink: '/categories'\n      }, {\n        label: this.languageService.translate('nav.warehouses'),\n        icon: 'pi pi-building',\n        routerLink: '/warehouses'\n      }, {\n        label: this.languageService.translate('nav.stockMovements'),\n        icon: 'pi pi-arrows-h',\n        routerLink: '/inventory/movements'\n      }, {\n        label: this.languageService.translate('nav.stockAdjustments'),\n        icon: 'pi pi-pencil',\n        routerLink: '/inventory/adjustments'\n      }, {\n        label: this.languageService.translate('nav.transfers'),\n        icon: 'pi pi-send',\n        routerLink: '/inventory/transfers'\n      }]\n    }, {\n      label: this.languageService.translate('nav.salesPurchases'),\n      icon: 'pi pi-shopping-cart',\n      items: [{\n        label: this.languageService.translate('nav.salesInvoices'),\n        icon: 'pi pi-file',\n        routerLink: '/invoices/sales'\n      }, {\n        label: this.languageService.translate('nav.purchaseInvoices'),\n        icon: 'pi pi-file-import',\n        routerLink: '/invoices/purchases'\n      }, {\n        label: this.languageService.translate('nav.salesReturns'),\n        icon: 'pi pi-undo',\n        routerLink: '/invoices/sales-returns'\n      }, {\n        label: this.languageService.translate('nav.purchaseReturns'),\n        icon: 'pi pi-replay',\n        routerLink: '/invoices/purchase-returns'\n      }]\n    }, {\n      label: this.languageService.translate('nav.customersSuppliers'),\n      icon: 'pi pi-users',\n      items: [{\n        label: this.languageService.translate('nav.customers'),\n        icon: 'pi pi-user',\n        routerLink: '/customers'\n      }, {\n        label: this.languageService.translate('nav.suppliers'),\n        icon: 'pi pi-user-plus',\n        routerLink: '/suppliers'\n      }]\n    }, {\n      label: this.languageService.translate('nav.financialManagement'),\n      icon: 'pi pi-dollar',\n      items: [{\n        label: this.languageService.translate('nav.payments'),\n        icon: 'pi pi-credit-card',\n        routerLink: '/payments'\n      }, {\n        label: this.languageService.translate('nav.accountStatements'),\n        icon: 'pi pi-file-pdf',\n        routerLink: '/reports/statements'\n      }, {\n        label: this.languageService.translate('nav.cashRegister'),\n        icon: 'pi pi-wallet',\n        routerLink: '/cash-register'\n      }]\n    }, {\n      label: this.languageService.translate('nav.reports'),\n      icon: 'pi pi-chart-bar',\n      items: [{\n        label: this.languageService.translate('nav.inventoryReports'),\n        icon: 'pi pi-chart-line',\n        routerLink: '/reports/inventory'\n      }, {\n        label: this.languageService.translate('nav.financialReports'),\n        icon: 'pi pi-chart-pie',\n        routerLink: '/reports/financial'\n      }, {\n        label: this.languageService.translate('nav.salesReports'),\n        icon: 'pi pi-trending-up',\n        routerLink: '/reports/sales'\n      }, {\n        label: this.languageService.translate('nav.purchaseReports'),\n        icon: 'pi pi-trending-down',\n        routerLink: '/reports/purchases'\n      }]\n    }];\n  }\n  toggleSidebar() {\n    this.sidebarVisible = !this.sidebarVisible;\n  }\n  static {\n    this.ɵfac = function LayoutComponent_Factory(t) {\n      return new (t || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LanguageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutComponent,\n      selectors: [[\"app-layout\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 28,\n      vars: 13,\n      consts: [[1, \"layout-wrapper\"], [1, \"layout-topbar\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bars\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"pi\", \"pi-box\", \"text-2xl\", \"text-primary\"], [1, \"m-0\", \"text-xl\", \"font-semibold\"], [1, \"relative\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bell\", \"tooltipPosition\", \"bottom\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pTooltip\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"bg-red-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"w-5\", \"h-5\", \"flex\", \"align-items-center\", \"justify-content-center\", 2, \"font-size\", \"0.7rem\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"p-2\", \"border-round\", \"cursor-pointer\", \"hover:surface-hover\", 2, \"border\", \"1px solid var(--surface-border)\"], [1, \"w-2rem\", \"h-2rem\", \"border-circle\", \"bg-primary\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"pi\", \"pi-user\", \"text-white\"], [\"class\", \"flex flex-column text-right\", 4, \"ngIf\"], [\"class\", \"flex flex-column\", 4, \"ngIf\"], [1, \"pi\", \"pi-chevron-down\", \"text-500\"], [1, \"layout-main\"], [1, \"layout-sidebar\", \"hidden-mobile\"], [3, \"model\", \"multiple\"], [\"position\", \"left\", \"styleClass\", \"layout-sidebar-mobile\", 3, \"visibleChange\", \"visible\", \"modal\", \"dismissible\"], [\"pTemplate\", \"header\"], [1, \"layout-content\"], [1, \"flex\", \"flex-column\", \"text-right\"], [1, \"text-sm\", \"font-medium\"], [1, \"text-xs\", \"text-500\"], [1, \"flex\", \"flex-column\"], [1, \"font-semibold\"]],\n      template: function LayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LayoutComponent_Template_button_click_3_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementStart(6, \"h2\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 2);\n          i0.ɵɵelement(9, \"app-language-switcher\");\n          i0.ɵɵelementStart(10, \"div\", 7);\n          i0.ɵɵelement(11, \"button\", 8);\n          i0.ɵɵelementStart(12, \"span\", 9);\n          i0.ɵɵtext(13, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11);\n          i0.ɵɵelement(16, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, LayoutComponent_div_17_Template, 5, 0, \"div\", 13)(18, LayoutComponent_div_18_Template, 5, 0, \"div\", 14);\n          i0.ɵɵelement(19, \"i\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"div\", 17);\n          i0.ɵɵelement(22, \"p-panelMenu\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p-sidebar\", 19);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LayoutComponent_Template_p_sidebar_visibleChange_23_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sidebarVisible, $event) || (ctx.sidebarVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(24, LayoutComponent_ng_template_24_Template, 4, 0, \"ng-template\", 20);\n          i0.ɵɵelement(25, \"p-panelMenu\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 21);\n          i0.ɵɵprojection(27);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"hidden-desktop\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"app.title\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"pTooltip\", ctx.languageService.translate(\"app.notifications\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.languageService.isArabic());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.languageService.isArabic());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.sidebarVisible);\n          i0.ɵɵproperty(\"modal\", true)(\"dismissible\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"model\", ctx.menuItems)(\"multiple\", false);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, RouterModule, MenubarModule, i3.Tooltip, i4.PrimeTemplate, SidebarModule, i5.Sidebar, PanelMenuModule, i6.PanelMenu, ButtonModule, i7.ButtonDirective, LanguageSwitcherComponent],\n      styles: [\".layout-wrapper[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.layout-topbar[_ngcontent-%COMP%] {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-200);\\n  padding: 1rem 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: sticky;\\n  top: 0;\\n  z-index: 100;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.layout-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n}\\n\\n.layout-sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: var(--surface-0);\\n  border-right: 1px solid var(--surface-200);\\n  overflow-y: auto;\\n  height: calc(100vh - 73px);\\n  position: sticky;\\n  top: 73px;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu {\\n  border: none;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-panel {\\n  border: none;\\n  margin-bottom: 0;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header {\\n  border: none;\\n  border-radius: 0;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link {\\n  border: none;\\n  border-radius: 0;\\n  padding: 1rem 1.5rem;\\n  background: transparent;\\n  color: var(--text-color);\\n  transition: all 0.2s;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:hover {\\n  background: var(--surface-100);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-header .p-panelmenu-header-link:focus {\\n  box-shadow: none;\\n  background: var(--surface-100);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content {\\n  border: none;\\n  background: var(--surface-50);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link {\\n  padding: 0.75rem 1.5rem 0.75rem 3rem;\\n  color: var(--text-color-secondary);\\n  border: none;\\n  border-radius: 0;\\n  transition: all 0.2s;\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link:hover {\\n  background: var(--surface-100);\\n  color: var(--text-color);\\n}\\n.layout-sidebar[_ngcontent-%COMP%]     .p-panelmenu .p-panelmenu-content .p-panelmenu-root-submenu .p-menuitem-link.router-link-active {\\n  background: var(--primary-color);\\n  color: var(--primary-color-text);\\n}\\n\\n.layout-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1.5rem;\\n  overflow-x: auto;\\n  background: var(--surface-50);\\n}\\n\\n@media (max-width: 768px) {\\n  .hidden-mobile[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .layout-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n    .layout-sidebar-mobile {\\n    width: 280px !important;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .hidden-desktop[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .layout-content[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.75rem;\\n  }\\n  .layout-topbar[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n    .layout-sidebar-mobile {\\n    width: 100% !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MenubarModule", "SidebarModule", "PanelMenuModule", "ButtonModule", "LanguageSwitcherComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LayoutComponent", "constructor", "languageService", "sidebarVisible", "menuItems", "ngOnInit", "initializeMenuItems", "currentLanguage$", "subscribe", "label", "translate", "icon", "routerLink", "expanded", "items", "toggleSidebar", "ɵɵdirectiveInject", "i1", "LanguageService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "LayoutComponent_Template", "rf", "ctx", "ɵɵlistener", "LayoutComponent_Template_button_click_3_listener", "ɵɵtemplate", "LayoutComponent_div_17_Template", "LayoutComponent_div_18_Template", "ɵɵtwoWayListener", "LayoutComponent_Template_p_sidebar_visibleChange_23_listener", "$event", "ɵɵtwoWayBindingSet", "LayoutComponent_ng_template_24_Template", "ɵɵprojection", "ɵɵadvance", "ɵɵclassProp", "ɵɵtextInterpolate", "ɵɵproperty", "isArabic", "ɵɵtwoWayProperty", "i2", "NgIf", "i3", "<PERSON><PERSON><PERSON>", "i4", "PrimeTemplate", "i5", "Sidebar", "i6", "PanelMenu", "i7", "ButtonDirective", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\shared\\components\\layout\\layout.component.ts", "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\shared\\components\\layout\\layout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { MenuItem } from 'primeng/api';\nimport { LanguageService } from '@core/services/language.service';\nimport { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';\n\n@Component({\n  selector: 'app-layout',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MenubarModule,\n    SidebarModule,\n    PanelMenuModule,\n    ButtonModule,\n    LanguageSwitcherComponent\n  ],\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.scss']\n})\nexport class LayoutComponent implements OnInit {\n  sidebarVisible = false;\n  menuItems: MenuItem[] = [];\n\n  constructor(public languageService: LanguageService) {}\n\n  ngOnInit() {\n    this.initializeMenuItems();\n\n    // Subscribe to language changes to update menu items\n    this.languageService.currentLanguage$.subscribe(() => {\n      this.initializeMenuItems();\n    });\n  }\n\n  private initializeMenuItems() {\n    this.menuItems = [\n      {\n        label: this.languageService.translate('nav.dashboard'),\n        icon: 'pi pi-home',\n        routerLink: '/dashboard'\n      },\n      {\n        label: this.languageService.translate('nav.inventoryManagement'),\n        icon: 'pi pi-box',\n        expanded: true,\n        items: [\n          {\n            label: this.languageService.translate('nav.items'),\n            icon: 'pi pi-list',\n            routerLink: '/items'\n          },\n          {\n            label: this.languageService.translate('nav.categories'),\n            icon: 'pi pi-sitemap',\n            routerLink: '/categories'\n          },\n          {\n            label: this.languageService.translate('nav.warehouses'),\n            icon: 'pi pi-building',\n            routerLink: '/warehouses'\n          },\n          {\n            label: this.languageService.translate('nav.stockMovements'),\n            icon: 'pi pi-arrows-h',\n            routerLink: '/inventory/movements'\n          },\n          {\n            label: this.languageService.translate('nav.stockAdjustments'),\n            icon: 'pi pi-pencil',\n            routerLink: '/inventory/adjustments'\n          },\n          {\n            label: this.languageService.translate('nav.transfers'),\n            icon: 'pi pi-send',\n            routerLink: '/inventory/transfers'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.salesPurchases'),\n        icon: 'pi pi-shopping-cart',\n        items: [\n          {\n            label: this.languageService.translate('nav.salesInvoices'),\n            icon: 'pi pi-file',\n            routerLink: '/invoices/sales'\n          },\n          {\n            label: this.languageService.translate('nav.purchaseInvoices'),\n            icon: 'pi pi-file-import',\n            routerLink: '/invoices/purchases'\n          },\n          {\n            label: this.languageService.translate('nav.salesReturns'),\n            icon: 'pi pi-undo',\n            routerLink: '/invoices/sales-returns'\n          },\n          {\n            label: this.languageService.translate('nav.purchaseReturns'),\n            icon: 'pi pi-replay',\n            routerLink: '/invoices/purchase-returns'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.customersSuppliers'),\n        icon: 'pi pi-users',\n        items: [\n          {\n            label: this.languageService.translate('nav.customers'),\n            icon: 'pi pi-user',\n            routerLink: '/customers'\n          },\n          {\n            label: this.languageService.translate('nav.suppliers'),\n            icon: 'pi pi-user-plus',\n            routerLink: '/suppliers'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.financialManagement'),\n        icon: 'pi pi-dollar',\n        items: [\n          {\n            label: this.languageService.translate('nav.payments'),\n            icon: 'pi pi-credit-card',\n            routerLink: '/payments'\n          },\n          {\n            label: this.languageService.translate('nav.accountStatements'),\n            icon: 'pi pi-file-pdf',\n            routerLink: '/reports/statements'\n          },\n          {\n            label: this.languageService.translate('nav.cashRegister'),\n            icon: 'pi pi-wallet',\n            routerLink: '/cash-register'\n          }\n        ]\n      },\n      {\n        label: this.languageService.translate('nav.reports'),\n        icon: 'pi pi-chart-bar',\n        items: [\n          {\n            label: this.languageService.translate('nav.inventoryReports'),\n            icon: 'pi pi-chart-line',\n            routerLink: '/reports/inventory'\n          },\n          {\n            label: this.languageService.translate('nav.financialReports'),\n            icon: 'pi pi-chart-pie',\n            routerLink: '/reports/financial'\n          },\n          {\n            label: this.languageService.translate('nav.salesReports'),\n            icon: 'pi pi-trending-up',\n            routerLink: '/reports/sales'\n          },\n          {\n            label: this.languageService.translate('nav.purchaseReports'),\n            icon: 'pi pi-trending-down',\n            routerLink: '/reports/purchases'\n          }\n        ]\n      }\n    ];\n  }\n\n  toggleSidebar() {\n    this.sidebarVisible = !this.sidebarVisible;\n  }\n}\n", "<div class=\"layout-wrapper\">\n  <!-- Top Navigation Bar -->\n  <div class=\"layout-topbar\">\n    <div class=\"flex align-items-center gap-3\">\n      <button\n        pButton\n        type=\"button\"\n        icon=\"pi pi-bars\"\n        class=\"p-button-text p-button-rounded p-button-plain\"\n        (click)=\"toggleSidebar()\"\n        [class.hidden-desktop]=\"true\">\n      </button>\n\n      <div class=\"flex align-items-center gap-2\">\n        <i class=\"pi pi-box text-2xl text-primary\"></i>\n        <h2 class=\"m-0 text-xl font-semibold\">{{ languageService.translate('app.title') }}</h2>\n      </div>\n    </div>\n\n    <div class=\"flex align-items-center gap-3\">\n      <!-- Language Switcher -->\n      <app-language-switcher></app-language-switcher>\n\n      <!-- Notifications -->\n      <div class=\"relative\">\n        <button\n          pButton\n          type=\"button\"\n          icon=\"pi pi-bell\"\n          class=\"p-button-text p-button-rounded p-button-plain\"\n          [pTooltip]=\"languageService.translate('app.notifications')\"\n          tooltipPosition=\"bottom\">\n        </button>\n        <span class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex align-items-center justify-content-center\" style=\"font-size: 0.7rem;\">3</span>\n      </div>\n\n      <!-- User Profile -->\n      <div class=\"flex align-items-center gap-2 p-2 border-round cursor-pointer hover:surface-hover\"\n           style=\"border: 1px solid var(--surface-border);\">\n        <div class=\"w-2rem h-2rem border-circle bg-primary flex align-items-center justify-content-center\">\n          <i class=\"pi pi-user text-white\"></i>\n        </div>\n\n        <div class=\"flex flex-column text-right\" *ngIf=\"languageService.isArabic()\">\n          <span class=\"text-sm font-medium\">أحمد محمد</span>\n          <span class=\"text-xs text-500\">مدير النظام</span>\n        </div>\n\n        <div class=\"flex flex-column\" *ngIf=\"!languageService.isArabic()\">\n          <span class=\"text-sm font-medium\">Ahmed Mohamed</span>\n          <span class=\"text-xs text-500\">System Admin</span>\n        </div>\n\n        <i class=\"pi pi-chevron-down text-500\"></i>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"layout-main\">\n    <!-- Desktop Sidebar -->\n    <div class=\"layout-sidebar hidden-mobile\">\n      <p-panelMenu [model]=\"menuItems\" [multiple]=\"false\"></p-panelMenu>\n    </div>\n\n    <!-- Mobile Sidebar -->\n    <p-sidebar \n      [(visible)]=\"sidebarVisible\" \n      position=\"left\" \n      [modal]=\"true\"\n      [dismissible]=\"true\"\n      styleClass=\"layout-sidebar-mobile\">\n      <ng-template pTemplate=\"header\">\n        <div class=\"flex align-items-center gap-2\">\n          <i class=\"pi pi-box text-2xl text-primary\"></i>\n          <span class=\"font-semibold\">Menu</span>\n        </div>\n      </ng-template>\n      \n      <p-panelMenu [model]=\"menuItems\" [multiple]=\"false\"></p-panelMenu>\n    </p-sidebar>\n\n    <!-- Main Content Area -->\n    <div class=\"layout-content\">\n      <ng-content></ng-content>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAI7C,SAASC,yBAAyB,QAAQ,kDAAkD;;;;;;;;;;;;ICkClFC,EADF,CAAAC,cAAA,cAA4E,eACxC;IAAAD,EAAA,CAAAE,MAAA,wDAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,oEAAW;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;;;;;IAGJH,EADF,CAAAC,cAAA,cAAkE,eAC9B;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;;;;;IAqBNH,EAAA,CAAAC,cAAA,aAA2C;IACzCD,EAAA,CAAAI,SAAA,WAA+C;IAC/CJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;;;ADhDd,OAAM,MAAOE,eAAe;EAI1BC,YAAmBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAHlC,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,SAAS,GAAe,EAAE;EAE4B;EAEtDC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACJ,eAAe,CAACK,gBAAgB,CAACC,SAAS,CAAC,MAAK;MACnD,IAAI,CAACF,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACF,SAAS,GAAG,CACf;MACEK,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;MACtDC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE;KACb,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,yBAAyB,CAAC;MAChEC,IAAI,EAAE,WAAW;MACjBE,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,WAAW,CAAC;QAClDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,gBAAgB,CAAC;QACvDC,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,gBAAgB,CAAC;QACvDC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,oBAAoB,CAAC;QAC3DC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;QACtDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,oBAAoB,CAAC;MAC3DC,IAAI,EAAE,qBAAqB;MAC3BG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,mBAAmB,CAAC;QAC1DC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,kBAAkB,CAAC;QACzDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,qBAAqB,CAAC;QAC5DC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,wBAAwB,CAAC;MAC/DC,IAAI,EAAE,aAAa;MACnBG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;QACtDC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,eAAe,CAAC;QACtDC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,yBAAyB,CAAC;MAChEC,IAAI,EAAE,cAAc;MACpBG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,cAAc,CAAC;QACrDC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,uBAAuB,CAAC;QAC9DC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,kBAAkB,CAAC;QACzDC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb;KAEJ,EACD;MACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,aAAa,CAAC;MACpDC,IAAI,EAAE,iBAAiB;MACvBG,KAAK,EAAE,CACL;QACEL,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,kBAAkB;QACxBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,sBAAsB,CAAC;QAC7DC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,kBAAkB,CAAC;QACzDC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE;OACb,EACD;QACEH,KAAK,EAAE,IAAI,CAACP,eAAe,CAACQ,SAAS,CAAC,qBAAqB,CAAC;QAC5DC,IAAI,EAAE,qBAAqB;QAC3BC,UAAU,EAAE;OACb;KAEJ,CACF;EACH;EAEAG,aAAaA,CAAA;IACX,IAAI,CAACZ,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;;;uBAzJWH,eAAe,EAAAL,EAAA,CAAAqB,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAflB,eAAe;MAAAmB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1B,EAAA,CAAA2B,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCvBtBnC,EAJN,CAAAC,cAAA,aAA4B,aAEC,aACkB,gBAOT;UAD9BD,EAAA,CAAAqC,UAAA,mBAAAC,iDAAA;YAAA,OAASF,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAE3BpB,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,aAA2C;UACzCD,EAAA,CAAAI,SAAA,WAA+C;UAC/CJ,EAAA,CAAAC,cAAA,YAAsC;UAAAD,EAAA,CAAAE,MAAA,GAA4C;UAEtFF,EAFsF,CAAAG,YAAA,EAAK,EACnF,EACF;UAENH,EAAA,CAAAC,cAAA,aAA2C;UAEzCD,EAAA,CAAAI,SAAA,4BAA+C;UAG/CJ,EAAA,CAAAC,cAAA,cAAsB;UACpBD,EAAA,CAAAI,SAAA,iBAOS;UACTJ,EAAA,CAAAC,cAAA,eAAoK;UAAAD,EAAA,CAAAE,MAAA,SAAC;UACvKF,EADuK,CAAAG,YAAA,EAAO,EACxK;UAKJH,EAFF,CAAAC,cAAA,eACsD,eAC+C;UACjGD,EAAA,CAAAI,SAAA,aAAqC;UACvCJ,EAAA,CAAAG,YAAA,EAAM;UAONH,EALA,CAAAuC,UAAA,KAAAC,+BAAA,kBAA4E,KAAAC,+BAAA,kBAKV;UAKlEzC,EAAA,CAAAI,SAAA,aAA2C;UAGjDJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAIJH,EAFF,CAAAC,cAAA,eAAyB,eAEmB;UACxCD,EAAA,CAAAI,SAAA,uBAAkE;UACpEJ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,qBAKqC;UAJnCD,EAAA,CAAA0C,gBAAA,2BAAAC,6DAAAC,MAAA;YAAA5C,EAAA,CAAA6C,kBAAA,CAAAT,GAAA,CAAA5B,cAAA,EAAAoC,MAAA,MAAAR,GAAA,CAAA5B,cAAA,GAAAoC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAK5B5C,EAAA,CAAAuC,UAAA,KAAAO,uCAAA,0BAAgC;UAOhC9C,EAAA,CAAAI,SAAA,uBAAkE;UACpEJ,EAAA,CAAAG,YAAA,EAAY;UAGZH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAA+C,YAAA,IAAyB;UAG/B/C,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;UA5EEH,EAAA,CAAAgD,SAAA,GAA6B;UAA7BhD,EAAA,CAAAiD,WAAA,wBAA6B;UAKSjD,EAAA,CAAAgD,SAAA,GAA4C;UAA5ChD,EAAA,CAAAkD,iBAAA,CAAAd,GAAA,CAAA7B,eAAA,CAAAQ,SAAA,cAA4C;UAehFf,EAAA,CAAAgD,SAAA,GAA2D;UAA3DhD,EAAA,CAAAmD,UAAA,aAAAf,GAAA,CAAA7B,eAAA,CAAAQ,SAAA,sBAA2D;UAanBf,EAAA,CAAAgD,SAAA,GAAgC;UAAhChD,EAAA,CAAAmD,UAAA,SAAAf,GAAA,CAAA7B,eAAA,CAAA6C,QAAA,GAAgC;UAK3CpD,EAAA,CAAAgD,SAAA,EAAiC;UAAjChD,EAAA,CAAAmD,UAAA,UAAAf,GAAA,CAAA7B,eAAA,CAAA6C,QAAA,GAAiC;UAarDpD,EAAA,CAAAgD,SAAA,GAAmB;UAAChD,EAApB,CAAAmD,UAAA,UAAAf,GAAA,CAAA3B,SAAA,CAAmB,mBAAmB;UAKnDT,EAAA,CAAAgD,SAAA,EAA4B;UAA5BhD,EAAA,CAAAqD,gBAAA,YAAAjB,GAAA,CAAA5B,cAAA,CAA4B;UAG5BR,EADA,CAAAmD,UAAA,eAAc,qBACM;UASPnD,EAAA,CAAAgD,SAAA,GAAmB;UAAChD,EAApB,CAAAmD,UAAA,UAAAf,GAAA,CAAA3B,SAAA,CAAmB,mBAAmB;;;qBD9DrDhB,YAAY,EAAA6D,EAAA,CAAAC,IAAA,EACZ7D,YAAY,EACZC,aAAa,EAAA6D,EAAA,CAAAC,OAAA,EAAAC,EAAA,CAAAC,aAAA,EACb/D,aAAa,EAAAgE,EAAA,CAAAC,OAAA,EACbhE,eAAe,EAAAiE,EAAA,CAAAC,SAAA,EACfjE,YAAY,EAAAkE,EAAA,CAAAC,eAAA,EACZlE,yBAAyB;MAAAmE,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}