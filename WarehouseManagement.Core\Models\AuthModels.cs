using System.ComponentModel.DataAnnotations;

namespace WarehouseManagement.Core.Models;

// Request Models
public class LoginRequest
{
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Password { get; set; } = string.Empty;

    public bool RememberMe { get; set; } = false;
}

public class RefreshTokenRequest
{
    [Required]
    public string RefreshToken { get; set; } = string.Empty;
}

public class ChangePasswordRequest
{
    [Required]
    public string CurrentPassword { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 8)]
    public string NewPassword { get; set; } = string.Empty;

    [Required]
    [Compare("NewPassword")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

public class ResetPasswordRequest
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
}

public class ConfirmResetPasswordRequest
{
    [Required]
    public string Token { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 8)]
    public string NewPassword { get; set; } = string.Empty;

    [Required]
    [Compare("NewPassword")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

// Response Models
public class LoginResponse
{
    public UserDto User { get; set; } = null!;
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public string TokenType { get; set; } = "Bearer";
}

public class RefreshTokenResponse
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
}

public class UserDto
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<RoleDto> Roles { get; set; } = new();
    public List<PermissionDto> Permissions { get; set; } = new();
}

public class RoleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public List<PermissionDto> Permissions { get; set; } = new();
}

public class PermissionDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Resource { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string? Description { get; set; }
}

// Security Models
public class SecurityEventDto
{
    public int Id { get; set; }
    public int? UserId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string? UserAgent { get; set; }
    public string Severity { get; set; } = string.Empty;
    public DateTime OccurredAt { get; set; }
    public string? AdditionalData { get; set; }
}

public class LoginAttemptDto
{
    public int Id { get; set; }
    public int? UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string? UserAgent { get; set; }
    public bool IsSuccessful { get; set; }
    public string? FailureReason { get; set; }
    public DateTime AttemptedAt { get; set; }
}

public class CsrfTokenResponse
{
    public string Token { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
}

public class RateLimitInfo
{
    public int Limit { get; set; }
    public int Remaining { get; set; }
    public DateTime ResetTime { get; set; }
}

public class SecurityConfigDto
{
    public int MaxLoginAttempts { get; set; }
    public int LockoutDurationMinutes { get; set; }
    public int TokenExpirationMinutes { get; set; }
    public int RefreshTokenExpirationDays { get; set; }
    public PasswordPolicyDto PasswordPolicy { get; set; } = new();
    public Dictionary<string, RateLimitConfigDto> RateLimits { get; set; } = new();
}

public class PasswordPolicyDto
{
    public int MinLength { get; set; }
    public bool RequireUppercase { get; set; }
    public bool RequireLowercase { get; set; }
    public bool RequireNumbers { get; set; }
    public bool RequireSpecialChars { get; set; }
    public bool PreventCommonPasswords { get; set; }
}

public class RateLimitConfigDto
{
    public int MaxAttempts { get; set; }
    public int WindowMinutes { get; set; }
}

// JWT Claims
public class JwtClaims
{
    public const string UserId = "user_id";
    public const string Username = "username";
    public const string Email = "email";
    public const string Roles = "roles";
    public const string Permissions = "permissions";
    public const string SessionId = "session_id";
}

// Enums
public enum SecurityEventType
{
    LoginSuccess,
    LoginFailure,
    Logout,
    TokenRefresh,
    PasswordChange,
    AccountLocked,
    SuspiciousActivity,
    PermissionDenied,
    DataAccess,
    ConfigurationChange
}

public enum SecurityEventSeverity
{
    Low,
    Medium,
    High,
    Critical
}

public enum PermissionAction
{
    Create,
    Read,
    Update,
    Delete,
    Manage
}

public enum UserRoleType
{
    Admin,
    Manager,
    Employee,
    Viewer
}

// Constants
public static class Resources
{
    public const string Dashboard = "dashboard";
    public const string Inventory = "inventory";
    public const string Products = "products";
    public const string Categories = "categories";
    public const string Suppliers = "suppliers";
    public const string Customers = "customers";
    public const string Orders = "orders";
    public const string Reports = "reports";
    public const string Users = "users";
    public const string Roles = "roles";
    public const string Settings = "settings";
    public const string AuditLogs = "audit_logs";
}

public static class Actions
{
    public const string Create = "create";
    public const string Read = "read";
    public const string Update = "update";
    public const string Delete = "delete";
    public const string Manage = "manage";
}

// Validation Attributes
public class StrongPasswordAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value is not string password)
            return false;

        if (password.Length < 8)
            return false;

        bool hasUpper = password.Any(char.IsUpper);
        bool hasLower = password.Any(char.IsLower);
        bool hasDigit = password.Any(char.IsDigit);
        bool hasSpecial = password.Any(ch => !char.IsLetterOrDigit(ch));

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"{name} must be at least 8 characters long and contain uppercase, lowercase, number, and special character.";
    }
}
