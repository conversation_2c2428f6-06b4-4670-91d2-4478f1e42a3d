using Microsoft.AspNetCore.Mvc;
using WarehouseManagement.Core.Common;

namespace WarehouseManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public abstract class BaseController : ControllerBase
{
    protected IActionResult HandleResult<T>(Result<T> result)
    {
        if (result.IsSuccess)
        {
            if (result.Value == null)
                return NotFound();
            
            return Ok(result.Value);
        }

        if (result.Errors.Any(e => e.Contains("not found", StringComparison.OrdinalIgnoreCase)))
            return NotFound(result.Error);

        if (result.Errors.Any(e => e.Contains("already exists", StringComparison.OrdinalIgnoreCase)))
            return Conflict(result.Error);

        return BadRequest(result.Error);
    }

    protected IActionResult HandleResult(Result result)
    {
        if (result.IsSuccess)
            return Ok();

        if (result.Errors.Any(e => e.Contains("not found", StringComparison.OrdinalIgnoreCase)))
            return NotFound(result.Error);

        if (result.Errors.Any(e => e.Contains("already exists", StringComparison.OrdinalIgnoreCase)))
            return Conflict(result.Error);

        return BadRequest(result.Error);
    }

    protected IActionResult HandlePagedResult<T>(Result<PagedResult<T>> result)
    {
        if (result.IsSuccess && result.Value != null)
        {
            Response.Headers.Add("X-Total-Count", result.Value.TotalCount.ToString());
            Response.Headers.Add("X-Page-Number", result.Value.PageNumber.ToString());
            Response.Headers.Add("X-Page-Size", result.Value.PageSize.ToString());
            Response.Headers.Add("X-Total-Pages", result.Value.TotalPages.ToString());
            
            return Ok(result.Value.Data);
        }

        return HandleResult(Result.Failure(result.Error));
    }

    // API Response helpers for consistent response format
    protected object CreateSuccessResponse<T>(T data, string? message = null)
    {
        return new
        {
            success = true,
            data = data,
            message = message
        };
    }

    protected object CreateSuccessResponse(string message)
    {
        return new
        {
            success = true,
            message = message
        };
    }

    protected object CreateErrorResponse(string error, string[]? errors = null)
    {
        return new
        {
            success = false,
            message = error,
            errors = errors
        };
    }

    protected object CreateErrorResponse(string[] errors)
    {
        return new
        {
            success = false,
            message = "Validation failed",
            errors = errors
        };
    }
}
