{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:7001/api',\n  appName: 'Warehouse Management System',\n  version: '1.0.0'\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "appName", "version"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:7001/api',\n  appName: 'Warehouse Management System',\n  version: '1.0.0'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,4BAA4B;EACpCC,OAAO,EAAE,6BAA6B;EACtCC,OAAO,EAAE;CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}