{"version": 3, "sources": ["../../../../../../../../../packages/localize/tools/src/extract/cli.ts", "../../../../../../../../../packages/localize/tools/src/extract/index.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;AASA,SAAQ,eAAe,UAAU,kBAAkB,qBAAoB;AACvE,OAAO,UAAU;AACjB,OAAO,WAAW;;;AC+DZ,SAAU,oBAAoB,EAClC,UAAAA,WACA,iBAAAC,kBACA,cACA,QAAAC,SACA,YAAY,QACZ,QAAAC,SACA,eACA,cACA,0BAAAC,2BACA,eAAAC,iBAAgB,CAAA,GAChB,YAAY,GAAE,GACa;AAC3B,QAAM,WAAW,GAAG,QAAQL,SAAQ;AACpC,QAAM,YAAY,IAAI,iBAAiB,IAAIG,SAAQ,EAAC,UAAU,cAAa,CAAC;AAE5E,QAAM,WAA6B,CAAA;AACnC,aAAW,QAAQF,kBAAiB;AAClC,aAAS,KAAK,GAAG,UAAU,gBAAgB,IAAI,CAAC;EAClD;AAEA,QAAM,cAAc,uBAAuB,IAAI,UAAUG,2BAA0B,QAAQ;AAC3F,MAAI,YAAY,WAAW;AACzB,UAAM,IAAI,MAAM,YAAY,kBAAkB,4BAA4B,CAAC;EAC7E;AAEA,QAAM,aAAa,GAAG,QAAQJ,WAAU,MAAM;AAC9C,QAAM,aAAa,cACfE,SAAQ,cAAc,GAAG,QAAQ,UAAU,GAAG,cAAcG,gBAAe,IAAI,WAAW;AAC9F,QAAM,kBAAkB,WAAW,UAAU,QAAQ;AACrD,KAAG,UAAU,GAAG,QAAQ,UAAU,CAAC;AACnC,KAAG,UAAU,YAAY,eAAe;AAExC,MAAI,YAAY,SAAS,QAAQ;AAC/B,IAAAF,QAAO,KAAK,YAAY,kBAAkB,kCAAkC,CAAC;EAC/E;AACF;AAEA,SAAS,cACLD,SAAgB,cAAsBF,WAA0B,cAChEK,iBAA+B,CAAA,GAAI,IACnC,aAAwB;AAC1B,UAAQH,SAAQ;IACd,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO,IAAI,4BACP,cAAcF,WAAU,cAAcK,gBAAe,EAAE;IAC7D,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO,IAAI,4BACP,cAAcL,WAAU,cAAcK,gBAAe,EAAE;IAC7D,KAAK;AACH,aAAO,IAAI,yBAAyBL,WAAU,cAAc,EAAE;IAChE,KAAK;AACH,aAAO,IAAI,gCAAgC,YAAY;IACzD,KAAK;AACH,aAAO,IAAI,yBAAyB,cAAcA,WAAU,EAAE;IAChE,KAAK;AACH,aAAO,IAAI,mCAAmC,WAAW;EAC7D;AACA,QAAM,IAAI,MAAM,6DAA6DE,SAAQ;AACvF;;;ADxHA,QAAQ,QAAQ;AAChB,IAAM,OAAO,QAAQ,KAAK,MAAM,CAAC;AACjC,IAAM,UACF,MAAM,IAAI,EACL,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,SAAS;EACT,MAAM;CACP,EACA,OAAO,KAAK;EACX,OAAO;EACP,SAAS;EACT,UAAU;EAEV,MAAM;CACP,EACA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,UACI;EAEJ,MAAM;CACP,EACA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,SACI,CAAC,OAAO,OAAO,QAAQ,SAAS,QAAQ,SAAS,UAAU,QAAQ,gBAAgB;EACvF,UAAU;EACV,MAAM;CACP,EACA,OAAO,iBAAiB;EACvB,UACI;EAGJ,MAAM;CACP,EACA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,UACI;EACJ,MAAM;CACP,EACA,OAAO,YAAY;EAClB,UAAU;EACV,SAAS,CAAC,SAAS,QAAQ,QAAQ,OAAO;EAC1C,MAAM;CACP,EACA,OAAO,iBAAiB;EACvB,MAAM;EACN,SAAS;EACT,UACI;CACL,EACA,OAAO,gBAAgB;EACtB,MAAM;EACN,SAAS;EACT,UACI;CACL,EACA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,SAAS,CAAC,SAAS,WAAW,QAAQ;EACtC,SAAS;EACT,MAAM;CACP,EACA,OAAM,EACN,KAAI,EACJ,UAAS;AAElB,IAAM,aAAa,IAAI,iBAAgB;AACvC,cAAc,UAAU;AAExB,IAAM,WAAW,QAAQ;AACzB,IAAM,kBAAkB,KAAK,KAAK,QAAQ,GAAG,EAAC,KAAK,SAAQ,CAAC;AAC5D,IAAM,WAAW,QAAQ;AACzB,IAAM,SAAS,IAAI,cAAc,WAAW,SAAS,YAAY,SAAS,IAAI;AAC9E,IAAM,2BAA2B,QAAQ;AACzC,IAAM,gBAAgB,mBAAmB,QAAQ,aAAa;AAC9D,IAAM,SAAS,QAAQ;AAEvB,oBAAoB;EAClB;EACA;EACA,cAAc,QAAQ;EACtB;EACA,YAAY,QAAQ;EACpB;EACA,eAAe,QAAQ;EACvB,cAAc,WAAW,oBAAoB,QAAQ;EACrD;EACA;EACA;CACD;", "names": ["rootPath", "sourceFilePaths", "format", "logger", "duplicateMessageHandling", "formatOptions"]}