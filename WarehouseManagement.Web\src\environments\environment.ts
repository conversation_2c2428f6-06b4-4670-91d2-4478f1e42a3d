export const environment = {
  production: false,
  apiUrl: 'https://localhost:7001/api',
  appName: 'Warehouse Management System',
  version: '1.0.0',

  // Security Configuration
  security: {
    enableCSRF: true,
    enableRateLimiting: true,
    enableSecurityHeaders: true,
    enableXSSProtection: true,
    sessionTimeout: 15, // minutes
    maxLoginAttempts: 5,
    lockoutDuration: 15, // minutes
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      preventCommonPasswords: true
    },
    rateLimits: {
      login: { maxAttempts: 5, windowMinutes: 15 },
      api: { maxAttempts: 100, windowMinutes: 1 },
      passwordReset: { maxAttempts: 3, windowMinutes: 60 }
    }
  },

  // Feature Flags
  features: {
    enableBiometricAuth: false,
    enableTwoFactorAuth: false,
    enableAuditLogging: true,
    enableSecurityMonitoring: true
  }
};
