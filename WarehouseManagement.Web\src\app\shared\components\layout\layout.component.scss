.layout-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  // RTL Support
  :host-context(.rtl) & {
    direction: rtl;
  }
}

.layout-topbar {
  background: var(--surface-0);
  border-bottom: 1px solid var(--surface-200);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  // RTL Support
  :host-context(.rtl) & {
    .flex {
      flex-direction: row-reverse;
    }
  }

  // User profile styling
  .user-profile {
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-hover);
      transform: translateY(-1px);
    }
  }

  // Notification badge
  .notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #ef4444;
    color: white;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;

    :host-context(.rtl) & {
      right: auto;
      left: -4px;
    }
  }
}

.layout-main {
  flex: 1;
  display: flex;
}

.layout-sidebar {
  width: 280px;
  background: var(--surface-0);
  border-right: 1px solid var(--surface-200);
  overflow-y: auto;
  height: calc(100vh - 73px); // Subtract topbar height
  position: sticky;
  top: 73px;

  // RTL Support
  :host-context(.rtl) & {
    border-right: none;
    border-left: 1px solid var(--surface-200);
  }

  ::ng-deep {
    .p-panelmenu {
      border: none;
      
      .p-panelmenu-panel {
        border: none;
        margin-bottom: 0;
      }

      .p-panelmenu-header {
        border: none;
        border-radius: 0;
        
        .p-panelmenu-header-link {
          border: none;
          border-radius: 0;
          padding: 1rem 1.5rem;
          background: transparent;
          color: var(--text-color);
          transition: all 0.2s;

          &:hover {
            background: var(--surface-100);
          }

          &:focus {
            box-shadow: none;
            background: var(--surface-100);
          }

          // RTL Support for icons
          .p-panelmenu-icon {
            :host-context(.rtl) & {
              margin-right: 0;
              margin-left: 0.5rem;
            }
          }
        }
      }

      .p-panelmenu-content {
        border: none;
        background: var(--surface-50);
        
        .p-panelmenu-root-submenu {
          .p-menuitem-link {
            padding: 0.75rem 1.5rem 0.75rem 3rem;
            color: var(--text-color-secondary);
            border: none;
            border-radius: 0;
            transition: all 0.2s;

            &:hover {
              background: var(--surface-100);
              color: var(--text-color);
            }

            &.router-link-active {
              background: var(--primary-color);
              color: var(--primary-color-text);
            }
          }
        }
      }
    }
  }
}

.layout-content {
  flex: 1;
  padding: 1.5rem;
  overflow-x: auto;
  background: var(--surface-50);
}

// Mobile styles
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }

  .layout-content {
    padding: 1rem;
  }

  .layout-topbar {
    padding: 0.75rem 1rem;
  }

  ::ng-deep .layout-sidebar-mobile {
    width: 280px !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .layout-content {
    padding: 0.75rem;
  }

  .layout-topbar {
    padding: 0.5rem 0.75rem;
    
    h2 {
      font-size: 1rem !important;
    }
  }

  ::ng-deep .layout-sidebar-mobile {
    width: 100% !important;
  }
}
