{"ast": null, "code": "/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nclass DomHandler {\n  static zindex = 1000;\n  static calculatedScrollbarWidth = null;\n  static calculatedScrollbarHeight = null;\n  static browser;\n  static addClass(element, className) {\n    if (element && className) {\n      if (element.classList) element.classList.add(className);else element.className += ' ' + className;\n    }\n  }\n  static addMultipleClasses(element, className) {\n    if (element && className) {\n      if (element.classList) {\n        let styles = className.trim().split(' ');\n        for (let i = 0; i < styles.length; i++) {\n          element.classList.add(styles[i]);\n        }\n      } else {\n        let styles = className.split(' ');\n        for (let i = 0; i < styles.length; i++) {\n          element.className += ' ' + styles[i];\n        }\n      }\n    }\n  }\n  static removeClass(element, className) {\n    if (element && className) {\n      if (element.classList) element.classList.remove(className);else element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n    }\n  }\n  static removeMultipleClasses(element, classNames) {\n    if (element && classNames) {\n      [classNames].flat().filter(Boolean).forEach(cNames => cNames.split(' ').forEach(className => this.removeClass(element, className)));\n    }\n  }\n  static hasClass(element, className) {\n    if (element && className) {\n      if (element.classList) return element.classList.contains(className);else return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n    }\n    return false;\n  }\n  static siblings(element) {\n    return Array.prototype.filter.call(element.parentNode.children, function (child) {\n      return child !== element;\n    });\n  }\n  static find(element, selector) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n  static findSingle(element, selector) {\n    return this.isElement(element) ? element.querySelector(selector) : null;\n  }\n  static index(element) {\n    let children = element.parentNode.childNodes;\n    let num = 0;\n    for (var i = 0; i < children.length; i++) {\n      if (children[i] == element) return num;\n      if (children[i].nodeType == 1) num++;\n    }\n    return -1;\n  }\n  static indexWithinGroup(element, attributeName) {\n    let children = element.parentNode ? element.parentNode.childNodes : [];\n    let num = 0;\n    for (var i = 0; i < children.length; i++) {\n      if (children[i] == element) return num;\n      if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1) num++;\n    }\n    return -1;\n  }\n  static appendOverlay(overlay, target, appendTo = 'self') {\n    if (appendTo !== 'self' && overlay && target) {\n      this.appendChild(overlay, target);\n    }\n  }\n  static alignOverlay(overlay, target, appendTo = 'self', calculateMinWidth = true) {\n    if (overlay && target) {\n      if (calculateMinWidth) {\n        overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n      }\n      if (appendTo === 'self') {\n        this.relativePosition(overlay, target);\n      } else {\n        this.absolutePosition(overlay, target);\n      }\n    }\n  }\n  static relativePosition(element, target, gutter = true) {\n    const getClosestRelativeElement = el => {\n      if (!el) return;\n      return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n    };\n    const elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : this.getHiddenElementDimensions(element);\n    const targetHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n    const targetOffset = target.getBoundingClientRect();\n    const windowScrollTop = this.getWindowScrollTop();\n    const windowScrollLeft = this.getWindowScrollLeft();\n    const viewport = this.getViewport();\n    const relativeElement = getClosestRelativeElement(element);\n    const relativeElementOffset = relativeElement?.getBoundingClientRect() || {\n      top: -1 * windowScrollTop,\n      left: -1 * windowScrollLeft\n    };\n    let top, left;\n    if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n      top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n      element.style.transformOrigin = 'bottom';\n      if (targetOffset.top + top < 0) {\n        top = -1 * targetOffset.top;\n      }\n    } else {\n      top = targetHeight + targetOffset.top - relativeElementOffset.top;\n      element.style.transformOrigin = 'top';\n    }\n    const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n    const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n    if (elementDimensions.width > viewport.width) {\n      // element wider then viewport and cannot fit on screen (align at left side of viewport)\n      left = (targetOffset.left - relativeElementOffset.left) * -1;\n    } else if (horizontalOverflow > 0) {\n      // element wider then viewport but can be fit on screen (align at right side of viewport)\n      left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n    } else {\n      // element fits on screen (align with target)\n      left = targetOffset.left - relativeElementOffset.left;\n    }\n    element.style.top = top + 'px';\n    element.style.left = left + 'px';\n    gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n  }\n  static absolutePosition(element, target, gutter = true) {\n    const elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : this.getHiddenElementDimensions(element);\n    const elementOuterHeight = elementDimensions.height;\n    const elementOuterWidth = elementDimensions.width;\n    const targetOuterHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n    const targetOuterWidth = target.offsetWidth ?? target.getBoundingClientRect().width;\n    const targetOffset = target.getBoundingClientRect();\n    const windowScrollTop = this.getWindowScrollTop();\n    const windowScrollLeft = this.getWindowScrollLeft();\n    const viewport = this.getViewport();\n    let top, left;\n    if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n      top = targetOffset.top + windowScrollTop - elementOuterHeight;\n      element.style.transformOrigin = 'bottom';\n      if (top < 0) {\n        top = windowScrollTop;\n      }\n    } else {\n      top = targetOuterHeight + targetOffset.top + windowScrollTop;\n      element.style.transformOrigin = 'top';\n    }\n    if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);else left = targetOffset.left + windowScrollLeft;\n    element.style.top = top + 'px';\n    element.style.left = left + 'px';\n    gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n  }\n  static getParents(element, parents = []) {\n    return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n  }\n  static getScrollableParents(element) {\n    let scrollableParents = [];\n    if (element) {\n      let parents = this.getParents(element);\n      const overflowRegex = /(auto|scroll)/;\n      const overflowCheck = node => {\n        let styleDeclaration = window['getComputedStyle'](node, null);\n        return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n      };\n      for (let parent of parents) {\n        let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n        if (scrollSelectors) {\n          let selectors = scrollSelectors.split(',');\n          for (let selector of selectors) {\n            let el = this.findSingle(parent, selector);\n            if (el && overflowCheck(el)) {\n              scrollableParents.push(el);\n            }\n          }\n        }\n        if (parent.nodeType !== 9 && overflowCheck(parent)) {\n          scrollableParents.push(parent);\n        }\n      }\n    }\n    return scrollableParents;\n  }\n  static getHiddenElementOuterHeight(element) {\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    let elementHeight = element.offsetHeight;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return elementHeight;\n  }\n  static getHiddenElementOuterWidth(element) {\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    let elementWidth = element.offsetWidth;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return elementWidth;\n  }\n  static getHiddenElementDimensions(element) {\n    let dimensions = {};\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    dimensions.width = element.offsetWidth;\n    dimensions.height = element.offsetHeight;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return dimensions;\n  }\n  static scrollInView(container, item) {\n    let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n    let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n    let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n    let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n    let containerRect = container.getBoundingClientRect();\n    let itemRect = item.getBoundingClientRect();\n    let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n    let scroll = container.scrollTop;\n    let elementHeight = container.clientHeight;\n    let itemHeight = this.getOuterHeight(item);\n    if (offset < 0) {\n      container.scrollTop = scroll + offset;\n    } else if (offset + itemHeight > elementHeight) {\n      container.scrollTop = scroll + offset - elementHeight + itemHeight;\n    }\n  }\n  static fadeIn(element, duration) {\n    element.style.opacity = 0;\n    let last = +new Date();\n    let opacity = 0;\n    let tick = function () {\n      opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n      element.style.opacity = opacity;\n      last = +new Date();\n      if (+opacity < 1) {\n        window.requestAnimationFrame && requestAnimationFrame(tick) || setTimeout(tick, 16);\n      }\n    };\n    tick();\n  }\n  static fadeOut(element, ms) {\n    var opacity = 1,\n      interval = 50,\n      duration = ms,\n      gap = interval / duration;\n    let fading = setInterval(() => {\n      opacity = opacity - gap;\n      if (opacity <= 0) {\n        opacity = 0;\n        clearInterval(fading);\n      }\n      element.style.opacity = opacity;\n    }, interval);\n  }\n  static getWindowScrollTop() {\n    let doc = document.documentElement;\n    return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n  }\n  static getWindowScrollLeft() {\n    let doc = document.documentElement;\n    return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n  }\n  static matches(element, selector) {\n    var p = Element.prototype;\n    var f = p['matches'] || p.webkitMatchesSelector || p['mozMatchesSelector'] || p['msMatchesSelector'] || function (s) {\n      return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n    };\n    return f.call(element, selector);\n  }\n  static getOuterWidth(el, margin) {\n    let width = el.offsetWidth;\n    if (margin) {\n      let style = getComputedStyle(el);\n      width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    return width;\n  }\n  static getHorizontalPadding(el) {\n    let style = getComputedStyle(el);\n    return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n  }\n  static getHorizontalMargin(el) {\n    let style = getComputedStyle(el);\n    return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n  }\n  static innerWidth(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    return width;\n  }\n  static width(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    return width;\n  }\n  static getInnerHeight(el) {\n    let height = el.offsetHeight;\n    let style = getComputedStyle(el);\n    height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n    return height;\n  }\n  static getOuterHeight(el, margin) {\n    let height = el.offsetHeight;\n    if (margin) {\n      let style = getComputedStyle(el);\n      height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n    }\n    return height;\n  }\n  static getHeight(el) {\n    let height = el.offsetHeight;\n    let style = getComputedStyle(el);\n    height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n    return height;\n  }\n  static getWidth(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n    return width;\n  }\n  static getViewport() {\n    let win = window,\n      d = document,\n      e = d.documentElement,\n      g = d.getElementsByTagName('body')[0],\n      w = win.innerWidth || e.clientWidth || g.clientWidth,\n      h = win.innerHeight || e.clientHeight || g.clientHeight;\n    return {\n      width: w,\n      height: h\n    };\n  }\n  static getOffset(el) {\n    var rect = el.getBoundingClientRect();\n    return {\n      top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n      left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n    };\n  }\n  static replaceElementWith(element, replacementElement) {\n    let parentNode = element.parentNode;\n    if (!parentNode) throw `Can't replace element`;\n    return parentNode.replaceChild(replacementElement, element);\n  }\n  static getUserAgent() {\n    if (navigator && this.isClient()) {\n      return navigator.userAgent;\n    }\n  }\n  static isIE() {\n    var ua = window.navigator.userAgent;\n    var msie = ua.indexOf('MSIE ');\n    if (msie > 0) {\n      // IE 10 or older => return version number\n      return true;\n    }\n    var trident = ua.indexOf('Trident/');\n    if (trident > 0) {\n      // IE 11 => return version number\n      var rv = ua.indexOf('rv:');\n      return true;\n    }\n    var edge = ua.indexOf('Edge/');\n    if (edge > 0) {\n      // Edge (IE 12+) => return version number\n      return true;\n    }\n    // other browser\n    return false;\n  }\n  static isIOS() {\n    return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n  }\n  static isAndroid() {\n    return /(android)/i.test(navigator.userAgent);\n  }\n  static isTouchDevice() {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n  }\n  static appendChild(element, target) {\n    if (this.isElement(target)) target.appendChild(element);else if (target && target.el && target.el.nativeElement) target.el.nativeElement.appendChild(element);else throw 'Cannot append ' + target + ' to ' + element;\n  }\n  static removeChild(element, target) {\n    if (this.isElement(target)) target.removeChild(element);else if (target.el && target.el.nativeElement) target.el.nativeElement.removeChild(element);else throw 'Cannot remove ' + element + ' from ' + target;\n  }\n  static removeElement(element) {\n    if (!('remove' in Element.prototype)) element.parentNode.removeChild(element);else element.remove();\n  }\n  static isElement(obj) {\n    return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n  }\n  static calculateScrollbarWidth(el) {\n    if (el) {\n      let style = getComputedStyle(el);\n      return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n    } else {\n      if (this.calculatedScrollbarWidth !== null) return this.calculatedScrollbarWidth;\n      let scrollDiv = document.createElement('div');\n      scrollDiv.className = 'p-scrollbar-measure';\n      document.body.appendChild(scrollDiv);\n      let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n      this.calculatedScrollbarWidth = scrollbarWidth;\n      return scrollbarWidth;\n    }\n  }\n  static calculateScrollbarHeight() {\n    if (this.calculatedScrollbarHeight !== null) return this.calculatedScrollbarHeight;\n    let scrollDiv = document.createElement('div');\n    scrollDiv.className = 'p-scrollbar-measure';\n    document.body.appendChild(scrollDiv);\n    let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    document.body.removeChild(scrollDiv);\n    this.calculatedScrollbarWidth = scrollbarHeight;\n    return scrollbarHeight;\n  }\n  static invokeElementMethod(element, methodName, args) {\n    element[methodName].apply(element, args);\n  }\n  static clearSelection() {\n    if (window.getSelection) {\n      if (window.getSelection().empty) {\n        window.getSelection().empty();\n      } else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n        window.getSelection().removeAllRanges();\n      }\n    } else if (document['selection'] && document['selection'].empty) {\n      try {\n        document['selection'].empty();\n      } catch (error) {\n        //ignore IE bug\n      }\n    }\n  }\n  static getBrowser() {\n    if (!this.browser) {\n      let matched = this.resolveUserAgent();\n      this.browser = {};\n      if (matched.browser) {\n        this.browser[matched.browser] = true;\n        this.browser['version'] = matched.version;\n      }\n      if (this.browser['chrome']) {\n        this.browser['webkit'] = true;\n      } else if (this.browser['webkit']) {\n        this.browser['safari'] = true;\n      }\n    }\n    return this.browser;\n  }\n  static resolveUserAgent() {\n    let ua = navigator.userAgent.toLowerCase();\n    let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) || [];\n    return {\n      browser: match[1] || '',\n      version: match[2] || '0'\n    };\n  }\n  static isInteger(value) {\n    if (Number.isInteger) {\n      return Number.isInteger(value);\n    } else {\n      return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n    }\n  }\n  static isHidden(element) {\n    return !element || element.offsetParent === null;\n  }\n  static isVisible(element) {\n    return element && element.offsetParent != null;\n  }\n  static isExist(element) {\n    return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n  }\n  static focus(element, options) {\n    element && document.activeElement !== element && element.focus(options);\n  }\n  static getFocusableSelectorString(selector = '') {\n    return `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-inputtext:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`;\n  }\n  static getFocusableElements(element, selector = '') {\n    let focusableElements = this.find(element, this.getFocusableSelectorString(selector));\n    let visibleFocusableElements = [];\n    for (let focusableElement of focusableElements) {\n      const computedStyle = getComputedStyle(focusableElement);\n      if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') visibleFocusableElements.push(focusableElement);\n    }\n    return visibleFocusableElements;\n  }\n  static getFocusableElement(element, selector = '') {\n    let focusableElement = this.findSingle(element, this.getFocusableSelectorString(selector));\n    if (focusableElement) {\n      const computedStyle = getComputedStyle(focusableElement);\n      if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') return focusableElement;\n    }\n    return null;\n  }\n  static getFirstFocusableElement(element, selector = '') {\n    const focusableElements = this.getFocusableElements(element, selector);\n    return focusableElements.length > 0 ? focusableElements[0] : null;\n  }\n  static getLastFocusableElement(element, selector) {\n    const focusableElements = this.getFocusableElements(element, selector);\n    return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n  }\n  static getNextFocusableElement(element, reverse = false) {\n    const focusableElements = DomHandler.getFocusableElements(element);\n    let index = 0;\n    if (focusableElements && focusableElements.length > 0) {\n      const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n      if (reverse) {\n        if (focusedIndex == -1 || focusedIndex === 0) {\n          index = focusableElements.length - 1;\n        } else {\n          index = focusedIndex - 1;\n        }\n      } else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n        index = focusedIndex + 1;\n      }\n    }\n    return focusableElements[index];\n  }\n  static generateZIndex() {\n    this.zindex = this.zindex || 999;\n    return ++this.zindex;\n  }\n  static getSelection() {\n    if (window.getSelection) return window.getSelection().toString();else if (document.getSelection) return document.getSelection().toString();else if (document['selection']) return document['selection'].createRange().text;\n    return null;\n  }\n  static getTargetElement(target, el) {\n    if (!target) return null;\n    switch (target) {\n      case 'document':\n        return document;\n      case 'window':\n        return window;\n      case '@next':\n        return el?.nextElementSibling;\n      case '@prev':\n        return el?.previousElementSibling;\n      case '@parent':\n        return el?.parentElement;\n      case '@grandparent':\n        return el?.parentElement.parentElement;\n      default:\n        const type = typeof target;\n        if (type === 'string') {\n          return document.querySelector(target);\n        } else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n          return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n        }\n        const isFunction = obj => !!(obj && obj.constructor && obj.call && obj.apply);\n        const element = isFunction(target) ? target() : target;\n        return element && element.nodeType === 9 || this.isExist(element) ? element : null;\n    }\n  }\n  static isClient() {\n    return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n  }\n  static getAttribute(element, name) {\n    if (element) {\n      const value = element.getAttribute(name);\n      if (!isNaN(value)) {\n        return +value;\n      }\n      if (value === 'true' || value === 'false') {\n        return value === 'true';\n      }\n      return value;\n    }\n    return undefined;\n  }\n  static calculateBodyScrollbarWidth() {\n    return window.innerWidth - document.documentElement.offsetWidth;\n  }\n  static blockBodyScroll(className = 'p-overflow-hidden') {\n    document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n    this.addClass(document.body, className);\n  }\n  static unblockBodyScroll(className = 'p-overflow-hidden') {\n    document.body.style.removeProperty('--scrollbar-width');\n    this.removeClass(document.body, className);\n  }\n  static createElement(type, attributes = {}, ...children) {\n    if (type) {\n      const element = document.createElement(type);\n      this.setAttributes(element, attributes);\n      element.append(...children);\n      return element;\n    }\n    return undefined;\n  }\n  static setAttribute(element, attribute = '', value) {\n    if (this.isElement(element) && value !== null && value !== undefined) {\n      element.setAttribute(attribute, value);\n    }\n  }\n  static setAttributes(element, attributes = {}) {\n    if (this.isElement(element)) {\n      const computedStyles = (rule, value) => {\n        const styles = element?.$attrs?.[rule] ? [element?.$attrs?.[rule]] : [];\n        return [value].flat().reduce((cv, v) => {\n          if (v !== null && v !== undefined) {\n            const type = typeof v;\n            if (type === 'string' || type === 'number') {\n              cv.push(v);\n            } else if (type === 'object') {\n              const _cv = Array.isArray(v) ? computedStyles(rule, v) : Object.entries(v).map(([_k, _v]) => rule === 'style' && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()}:${_v}` : !!_v ? _k : undefined);\n              cv = _cv.length ? cv.concat(_cv.filter(c => !!c)) : cv;\n            }\n          }\n          return cv;\n        }, styles);\n      };\n      Object.entries(attributes).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          const matchedEvent = key.match(/^on(.+)/);\n          if (matchedEvent) {\n            element.addEventListener(matchedEvent[1].toLowerCase(), value);\n          } else if (key === 'pBind') {\n            this.setAttributes(element, value);\n          } else {\n            value = key === 'class' ? [...new Set(computedStyles('class', value))].join(' ').trim() : key === 'style' ? computedStyles('style', value).join(';').trim() : value;\n            (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n            element.setAttribute(key, value);\n          }\n        }\n      });\n    }\n  }\n  static isFocusableElement(element, selector = '') {\n    return this.isElement(element) ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`) : false;\n  }\n}\nclass ConnectedOverlayScrollHandler {\n  element;\n  listener;\n  scrollableParents;\n  constructor(element, listener = () => {}) {\n    this.element = element;\n    this.listener = listener;\n  }\n  bindScrollListener() {\n    this.scrollableParents = DomHandler.getScrollableParents(this.element);\n    for (let i = 0; i < this.scrollableParents.length; i++) {\n      this.scrollableParents[i].addEventListener('scroll', this.listener);\n    }\n  }\n  unbindScrollListener() {\n    if (this.scrollableParents) {\n      for (let i = 0; i < this.scrollableParents.length; i++) {\n        this.scrollableParents[i].removeEventListener('scroll', this.listener);\n      }\n    }\n  }\n  destroy() {\n    this.unbindScrollListener();\n    this.element = null;\n    this.listener = null;\n    this.scrollableParents = null;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "zindex", "calculatedScrollbarWidth", "calculatedScrollbarHeight", "browser", "addClass", "element", "className", "classList", "add", "addMultipleClasses", "styles", "trim", "split", "i", "length", "removeClass", "remove", "replace", "RegExp", "join", "removeMultipleClasses", "classNames", "flat", "filter", "Boolean", "for<PERSON>ach", "cNames", "hasClass", "contains", "test", "siblings", "Array", "prototype", "call", "parentNode", "children", "child", "find", "selector", "from", "querySelectorAll", "findSingle", "isElement", "querySelector", "index", "childNodes", "num", "nodeType", "indexWithinGroup", "attributeName", "attributes", "appendOverlay", "overlay", "target", "appendTo", "append<PERSON><PERSON><PERSON>", "alignOverlay", "calculateMinWidth", "style", "min<PERSON><PERSON><PERSON>", "getOuterWidth", "relativePosition", "absolutePosition", "gutter", "getClosestRelativeElement", "el", "getComputedStyle", "getPropertyValue", "parentElement", "elementDimensions", "offsetParent", "width", "offsetWidth", "height", "offsetHeight", "getHiddenElementDimensions", "targetHeight", "getBoundingClientRect", "targetOffset", "windowScrollTop", "getWindowScrollTop", "windowScrollLeft", "getWindowScrollLeft", "viewport", "getViewport", "relativeElement", "relativeElementOffset", "top", "left", "transform<PERSON><PERSON>in", "horizontalOverflow", "targetLeftOffsetInSpaceOfRelativeElement", "marginTop", "origin", "elementOuterHeight", "elementOuterWidth", "targetOuterHeight", "targetOuterWidth", "Math", "max", "getParents", "parents", "concat", "getScrollableParents", "scrollableParents", "overflowRegex", "overflowCheck", "node", "styleDeclaration", "window", "parent", "scrollSelectors", "dataset", "selectors", "push", "getHiddenElementOuterHeight", "visibility", "display", "elementHeight", "getHiddenElementOuterWidth", "elementWidth", "dimensions", "scrollInView", "container", "item", "borderTopValue", "borderTop", "parseFloat", "paddingTopValue", "paddingTop", "containerRect", "itemRect", "offset", "document", "body", "scrollTop", "scroll", "clientHeight", "itemHeight", "getOuterHeight", "fadeIn", "duration", "opacity", "last", "Date", "tick", "getTime", "requestAnimationFrame", "setTimeout", "fadeOut", "ms", "interval", "gap", "fading", "setInterval", "clearInterval", "doc", "documentElement", "pageYOffset", "clientTop", "pageXOffset", "scrollLeft", "clientLeft", "matches", "p", "Element", "f", "webkitMatchesSelector", "s", "indexOf", "margin", "marginLeft", "marginRight", "getHorizontalPadding", "paddingLeft", "paddingRight", "getHorizontalMargin", "innerWidth", "getInnerHeight", "paddingBottom", "marginBottom", "getHeight", "borderTopWidth", "borderBottomWidth", "getWidth", "borderLeftWidth", "borderRightWidth", "win", "d", "e", "g", "getElementsByTagName", "w", "clientWidth", "h", "innerHeight", "getOffset", "rect", "replaceElementWith", "replacementElement", "<PERSON><PERSON><PERSON><PERSON>", "getUserAgent", "navigator", "isClient", "userAgent", "isIE", "ua", "msie", "trident", "rv", "edge", "isIOS", "isAndroid", "isTouchDevice", "maxTouchPoints", "nativeElement", "<PERSON><PERSON><PERSON><PERSON>", "removeElement", "obj", "HTMLElement", "nodeName", "calculateScrollbarWidth", "scrollDiv", "createElement", "scrollbarWidth", "calculateScrollbarHeight", "scrollbarHeight", "invokeElementMethod", "methodName", "args", "apply", "clearSelection", "getSelection", "empty", "removeAllRanges", "rangeCount", "getRangeAt", "getClientRects", "error", "<PERSON><PERSON><PERSON><PERSON>", "matched", "resolveUserAgent", "version", "toLowerCase", "match", "exec", "isInteger", "value", "Number", "isFinite", "floor", "isHidden", "isVisible", "isExist", "focus", "options", "activeElement", "getFocusableSelectorString", "getFocusableElements", "focusableElements", "visibleFocusableElements", "focusableElement", "computedStyle", "getFocusableElement", "getFirstFocusableElement", "getLastFocusableElement", "getNextFocusableElement", "reverse", "focusedIndex", "ownerDocument", "generateZIndex", "toString", "createRange", "text", "getTargetElement", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "type", "hasOwnProperty", "undefined", "isFunction", "constructor", "getAttribute", "name", "isNaN", "calculateBodyScrollbarWidth", "blockBodyScroll", "setProperty", "unblockBodyScroll", "removeProperty", "setAttributes", "append", "setAttribute", "attribute", "computedStyles", "rule", "$attrs", "reduce", "cv", "v", "_cv", "isArray", "Object", "entries", "map", "_k", "_v", "c", "key", "matchedEvent", "addEventListener", "Set", "isFocusableElement", "ConnectedOverlayScrollHandler", "listener", "bindScrollListener", "unbindScrollListener", "removeEventListener", "destroy"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-dom.mjs"], "sourcesContent": ["/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nclass DomHandler {\n    static zindex = 1000;\n    static calculatedScrollbarWidth = null;\n    static calculatedScrollbarHeight = null;\n    static browser;\n    static addClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                element.classList.add(className);\n            else\n                element.className += ' ' + className;\n        }\n    }\n    static addMultipleClasses(element, className) {\n        if (element && className) {\n            if (element.classList) {\n                let styles = className.trim().split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.classList.add(styles[i]);\n                }\n            }\n            else {\n                let styles = className.split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.className += ' ' + styles[i];\n                }\n            }\n        }\n    }\n    static removeClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                element.classList.remove(className);\n            else\n                element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n        }\n    }\n    static removeMultipleClasses(element, classNames) {\n        if (element && classNames) {\n            [classNames]\n                .flat()\n                .filter(Boolean)\n                .forEach((cNames) => cNames.split(' ').forEach((className) => this.removeClass(element, className)));\n        }\n    }\n    static hasClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                return element.classList.contains(className);\n            else\n                return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n        }\n        return false;\n    }\n    static siblings(element) {\n        return Array.prototype.filter.call(element.parentNode.children, function (child) {\n            return child !== element;\n        });\n    }\n    static find(element, selector) {\n        return Array.from(element.querySelectorAll(selector));\n    }\n    static findSingle(element, selector) {\n        return this.isElement(element) ? element.querySelector(selector) : null;\n    }\n    static index(element) {\n        let children = element.parentNode.childNodes;\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static indexWithinGroup(element, attributeName) {\n        let children = element.parentNode ? element.parentNode.childNodes : [];\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static appendOverlay(overlay, target, appendTo = 'self') {\n        if (appendTo !== 'self' && overlay && target) {\n            this.appendChild(overlay, target);\n        }\n    }\n    static alignOverlay(overlay, target, appendTo = 'self', calculateMinWidth = true) {\n        if (overlay && target) {\n            if (calculateMinWidth) {\n                overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n            }\n            if (appendTo === 'self') {\n                this.relativePosition(overlay, target);\n            }\n            else {\n                this.absolutePosition(overlay, target);\n            }\n        }\n    }\n    static relativePosition(element, target, gutter = true) {\n        const getClosestRelativeElement = (el) => {\n            if (!el)\n                return;\n            return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n        };\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const targetHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        const relativeElement = getClosestRelativeElement(element);\n        const relativeElementOffset = relativeElement?.getBoundingClientRect() || { top: -1 * windowScrollTop, left: -1 * windowScrollLeft };\n        let top, left;\n        if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n            top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n            element.style.transformOrigin = 'bottom';\n            if (targetOffset.top + top < 0) {\n                top = -1 * targetOffset.top;\n            }\n        }\n        else {\n            top = targetHeight + targetOffset.top - relativeElementOffset.top;\n            element.style.transformOrigin = 'top';\n        }\n        const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n        const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n        if (elementDimensions.width > viewport.width) {\n            // element wider then viewport and cannot fit on screen (align at left side of viewport)\n            left = (targetOffset.left - relativeElementOffset.left) * -1;\n        }\n        else if (horizontalOverflow > 0) {\n            // element wider then viewport but can be fit on screen (align at right side of viewport)\n            left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n        }\n        else {\n            // element fits on screen (align with target)\n            left = targetOffset.left - relativeElementOffset.left;\n        }\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n        gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static absolutePosition(element, target, gutter = true) {\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const elementOuterHeight = elementDimensions.height;\n        const elementOuterWidth = elementDimensions.width;\n        const targetOuterHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n        const targetOuterWidth = target.offsetWidth ?? target.getBoundingClientRect().width;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        let top, left;\n        if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n            top = targetOffset.top + windowScrollTop - elementOuterHeight;\n            element.style.transformOrigin = 'bottom';\n            if (top < 0) {\n                top = windowScrollTop;\n            }\n        }\n        else {\n            top = targetOuterHeight + targetOffset.top + windowScrollTop;\n            element.style.transformOrigin = 'top';\n        }\n        if (targetOffset.left + elementOuterWidth > viewport.width)\n            left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);\n        else\n            left = targetOffset.left + windowScrollLeft;\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n        gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static getParents(element, parents = []) {\n        return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n    }\n    static getScrollableParents(element) {\n        let scrollableParents = [];\n        if (element) {\n            let parents = this.getParents(element);\n            const overflowRegex = /(auto|scroll)/;\n            const overflowCheck = (node) => {\n                let styleDeclaration = window['getComputedStyle'](node, null);\n                return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n            };\n            for (let parent of parents) {\n                let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n                if (scrollSelectors) {\n                    let selectors = scrollSelectors.split(',');\n                    for (let selector of selectors) {\n                        let el = this.findSingle(parent, selector);\n                        if (el && overflowCheck(el)) {\n                            scrollableParents.push(el);\n                        }\n                    }\n                }\n                if (parent.nodeType !== 9 && overflowCheck(parent)) {\n                    scrollableParents.push(parent);\n                }\n            }\n        }\n        return scrollableParents;\n    }\n    static getHiddenElementOuterHeight(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementHeight = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementHeight;\n    }\n    static getHiddenElementOuterWidth(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementWidth = element.offsetWidth;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementWidth;\n    }\n    static getHiddenElementDimensions(element) {\n        let dimensions = {};\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        dimensions.width = element.offsetWidth;\n        dimensions.height = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return dimensions;\n    }\n    static scrollInView(container, item) {\n        let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n        let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n        let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n        let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n        let containerRect = container.getBoundingClientRect();\n        let itemRect = item.getBoundingClientRect();\n        let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n        let scroll = container.scrollTop;\n        let elementHeight = container.clientHeight;\n        let itemHeight = this.getOuterHeight(item);\n        if (offset < 0) {\n            container.scrollTop = scroll + offset;\n        }\n        else if (offset + itemHeight > elementHeight) {\n            container.scrollTop = scroll + offset - elementHeight + itemHeight;\n        }\n    }\n    static fadeIn(element, duration) {\n        element.style.opacity = 0;\n        let last = +new Date();\n        let opacity = 0;\n        let tick = function () {\n            opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n            element.style.opacity = opacity;\n            last = +new Date();\n            if (+opacity < 1) {\n                (window.requestAnimationFrame && requestAnimationFrame(tick)) || setTimeout(tick, 16);\n            }\n        };\n        tick();\n    }\n    static fadeOut(element, ms) {\n        var opacity = 1, interval = 50, duration = ms, gap = interval / duration;\n        let fading = setInterval(() => {\n            opacity = opacity - gap;\n            if (opacity <= 0) {\n                opacity = 0;\n                clearInterval(fading);\n            }\n            element.style.opacity = opacity;\n        }, interval);\n    }\n    static getWindowScrollTop() {\n        let doc = document.documentElement;\n        return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n    }\n    static getWindowScrollLeft() {\n        let doc = document.documentElement;\n        return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n    }\n    static matches(element, selector) {\n        var p = Element.prototype;\n        var f = p['matches'] ||\n            p.webkitMatchesSelector ||\n            p['mozMatchesSelector'] ||\n            p['msMatchesSelector'] ||\n            function (s) {\n                return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n            };\n        return f.call(element, selector);\n    }\n    static getOuterWidth(el, margin) {\n        let width = el.offsetWidth;\n        if (margin) {\n            let style = getComputedStyle(el);\n            width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n        }\n        return width;\n    }\n    static getHorizontalPadding(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    }\n    static getHorizontalMargin(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    static innerWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static width(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static getInnerHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n        return height;\n    }\n    static getOuterHeight(el, margin) {\n        let height = el.offsetHeight;\n        if (margin) {\n            let style = getComputedStyle(el);\n            height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n        }\n        return height;\n    }\n    static getHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n        return height;\n    }\n    static getWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n        return width;\n    }\n    static getViewport() {\n        let win = window, d = document, e = d.documentElement, g = d.getElementsByTagName('body')[0], w = win.innerWidth || e.clientWidth || g.clientWidth, h = win.innerHeight || e.clientHeight || g.clientHeight;\n        return { width: w, height: h };\n    }\n    static getOffset(el) {\n        var rect = el.getBoundingClientRect();\n        return {\n            top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n            left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n        };\n    }\n    static replaceElementWith(element, replacementElement) {\n        let parentNode = element.parentNode;\n        if (!parentNode)\n            throw `Can't replace element`;\n        return parentNode.replaceChild(replacementElement, element);\n    }\n    static getUserAgent() {\n        if (navigator && this.isClient()) {\n            return navigator.userAgent;\n        }\n    }\n    static isIE() {\n        var ua = window.navigator.userAgent;\n        var msie = ua.indexOf('MSIE ');\n        if (msie > 0) {\n            // IE 10 or older => return version number\n            return true;\n        }\n        var trident = ua.indexOf('Trident/');\n        if (trident > 0) {\n            // IE 11 => return version number\n            var rv = ua.indexOf('rv:');\n            return true;\n        }\n        var edge = ua.indexOf('Edge/');\n        if (edge > 0) {\n            // Edge (IE 12+) => return version number\n            return true;\n        }\n        // other browser\n        return false;\n    }\n    static isIOS() {\n        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n    }\n    static isAndroid() {\n        return /(android)/i.test(navigator.userAgent);\n    }\n    static isTouchDevice() {\n        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n    static appendChild(element, target) {\n        if (this.isElement(target))\n            target.appendChild(element);\n        else if (target && target.el && target.el.nativeElement)\n            target.el.nativeElement.appendChild(element);\n        else\n            throw 'Cannot append ' + target + ' to ' + element;\n    }\n    static removeChild(element, target) {\n        if (this.isElement(target))\n            target.removeChild(element);\n        else if (target.el && target.el.nativeElement)\n            target.el.nativeElement.removeChild(element);\n        else\n            throw 'Cannot remove ' + element + ' from ' + target;\n    }\n    static removeElement(element) {\n        if (!('remove' in Element.prototype))\n            element.parentNode.removeChild(element);\n        else\n            element.remove();\n    }\n    static isElement(obj) {\n        return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n    }\n    static calculateScrollbarWidth(el) {\n        if (el) {\n            let style = getComputedStyle(el);\n            return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n        }\n        else {\n            if (this.calculatedScrollbarWidth !== null)\n                return this.calculatedScrollbarWidth;\n            let scrollDiv = document.createElement('div');\n            scrollDiv.className = 'p-scrollbar-measure';\n            document.body.appendChild(scrollDiv);\n            let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n            document.body.removeChild(scrollDiv);\n            this.calculatedScrollbarWidth = scrollbarWidth;\n            return scrollbarWidth;\n        }\n    }\n    static calculateScrollbarHeight() {\n        if (this.calculatedScrollbarHeight !== null)\n            return this.calculatedScrollbarHeight;\n        let scrollDiv = document.createElement('div');\n        scrollDiv.className = 'p-scrollbar-measure';\n        document.body.appendChild(scrollDiv);\n        let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n        document.body.removeChild(scrollDiv);\n        this.calculatedScrollbarWidth = scrollbarHeight;\n        return scrollbarHeight;\n    }\n    static invokeElementMethod(element, methodName, args) {\n        element[methodName].apply(element, args);\n    }\n    static clearSelection() {\n        if (window.getSelection) {\n            if (window.getSelection().empty) {\n                window.getSelection().empty();\n            }\n            else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n                window.getSelection().removeAllRanges();\n            }\n        }\n        else if (document['selection'] && document['selection'].empty) {\n            try {\n                document['selection'].empty();\n            }\n            catch (error) {\n                //ignore IE bug\n            }\n        }\n    }\n    static getBrowser() {\n        if (!this.browser) {\n            let matched = this.resolveUserAgent();\n            this.browser = {};\n            if (matched.browser) {\n                this.browser[matched.browser] = true;\n                this.browser['version'] = matched.version;\n            }\n            if (this.browser['chrome']) {\n                this.browser['webkit'] = true;\n            }\n            else if (this.browser['webkit']) {\n                this.browser['safari'] = true;\n            }\n        }\n        return this.browser;\n    }\n    static resolveUserAgent() {\n        let ua = navigator.userAgent.toLowerCase();\n        let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || (ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua)) || [];\n        return {\n            browser: match[1] || '',\n            version: match[2] || '0'\n        };\n    }\n    static isInteger(value) {\n        if (Number.isInteger) {\n            return Number.isInteger(value);\n        }\n        else {\n            return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n        }\n    }\n    static isHidden(element) {\n        return !element || element.offsetParent === null;\n    }\n    static isVisible(element) {\n        return element && element.offsetParent != null;\n    }\n    static isExist(element) {\n        return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n    }\n    static focus(element, options) {\n        element && document.activeElement !== element && element.focus(options);\n    }\n    static getFocusableSelectorString(selector = '') {\n        return `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-inputtext:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`;\n    }\n    static getFocusableElements(element, selector = '') {\n        let focusableElements = this.find(element, this.getFocusableSelectorString(selector));\n        let visibleFocusableElements = [];\n        for (let focusableElement of focusableElements) {\n            const computedStyle = getComputedStyle(focusableElement);\n            if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden')\n                visibleFocusableElements.push(focusableElement);\n        }\n        return visibleFocusableElements;\n    }\n    static getFocusableElement(element, selector = '') {\n        let focusableElement = this.findSingle(element, this.getFocusableSelectorString(selector));\n        if (focusableElement) {\n            const computedStyle = getComputedStyle(focusableElement);\n            if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden')\n                return focusableElement;\n        }\n        return null;\n    }\n    static getFirstFocusableElement(element, selector = '') {\n        const focusableElements = this.getFocusableElements(element, selector);\n        return focusableElements.length > 0 ? focusableElements[0] : null;\n    }\n    static getLastFocusableElement(element, selector) {\n        const focusableElements = this.getFocusableElements(element, selector);\n        return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n    }\n    static getNextFocusableElement(element, reverse = false) {\n        const focusableElements = DomHandler.getFocusableElements(element);\n        let index = 0;\n        if (focusableElements && focusableElements.length > 0) {\n            const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n            if (reverse) {\n                if (focusedIndex == -1 || focusedIndex === 0) {\n                    index = focusableElements.length - 1;\n                }\n                else {\n                    index = focusedIndex - 1;\n                }\n            }\n            else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n                index = focusedIndex + 1;\n            }\n        }\n        return focusableElements[index];\n    }\n    static generateZIndex() {\n        this.zindex = this.zindex || 999;\n        return ++this.zindex;\n    }\n    static getSelection() {\n        if (window.getSelection)\n            return window.getSelection().toString();\n        else if (document.getSelection)\n            return document.getSelection().toString();\n        else if (document['selection'])\n            return document['selection'].createRange().text;\n        return null;\n    }\n    static getTargetElement(target, el) {\n        if (!target)\n            return null;\n        switch (target) {\n            case 'document':\n                return document;\n            case 'window':\n                return window;\n            case '@next':\n                return el?.nextElementSibling;\n            case '@prev':\n                return el?.previousElementSibling;\n            case '@parent':\n                return el?.parentElement;\n            case '@grandparent':\n                return el?.parentElement.parentElement;\n            default:\n                const type = typeof target;\n                if (type === 'string') {\n                    return document.querySelector(target);\n                }\n                else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n                    return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n                }\n                const isFunction = (obj) => !!(obj && obj.constructor && obj.call && obj.apply);\n                const element = isFunction(target) ? target() : target;\n                return (element && element.nodeType === 9) || this.isExist(element) ? element : null;\n        }\n    }\n    static isClient() {\n        return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n    }\n    static getAttribute(element, name) {\n        if (element) {\n            const value = element.getAttribute(name);\n            if (!isNaN(value)) {\n                return +value;\n            }\n            if (value === 'true' || value === 'false') {\n                return value === 'true';\n            }\n            return value;\n        }\n        return undefined;\n    }\n    static calculateBodyScrollbarWidth() {\n        return window.innerWidth - document.documentElement.offsetWidth;\n    }\n    static blockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n        this.addClass(document.body, className);\n    }\n    static unblockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.removeProperty('--scrollbar-width');\n        this.removeClass(document.body, className);\n    }\n    static createElement(type, attributes = {}, ...children) {\n        if (type) {\n            const element = document.createElement(type);\n            this.setAttributes(element, attributes);\n            element.append(...children);\n            return element;\n        }\n        return undefined;\n    }\n    static setAttribute(element, attribute = '', value) {\n        if (this.isElement(element) && value !== null && value !== undefined) {\n            element.setAttribute(attribute, value);\n        }\n    }\n    static setAttributes(element, attributes = {}) {\n        if (this.isElement(element)) {\n            const computedStyles = (rule, value) => {\n                const styles = element?.$attrs?.[rule] ? [element?.$attrs?.[rule]] : [];\n                return [value].flat().reduce((cv, v) => {\n                    if (v !== null && v !== undefined) {\n                        const type = typeof v;\n                        if (type === 'string' || type === 'number') {\n                            cv.push(v);\n                        }\n                        else if (type === 'object') {\n                            const _cv = Array.isArray(v)\n                                ? computedStyles(rule, v)\n                                : Object.entries(v).map(([_k, _v]) => (rule === 'style' && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()}:${_v}` : !!_v ? _k : undefined));\n                            cv = _cv.length ? cv.concat(_cv.filter((c) => !!c)) : cv;\n                        }\n                    }\n                    return cv;\n                }, styles);\n            };\n            Object.entries(attributes).forEach(([key, value]) => {\n                if (value !== undefined && value !== null) {\n                    const matchedEvent = key.match(/^on(.+)/);\n                    if (matchedEvent) {\n                        element.addEventListener(matchedEvent[1].toLowerCase(), value);\n                    }\n                    else if (key === 'pBind') {\n                        this.setAttributes(element, value);\n                    }\n                    else {\n                        value = key === 'class' ? [...new Set(computedStyles('class', value))].join(' ').trim() : key === 'style' ? computedStyles('style', value).join(';').trim() : value;\n                        (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n                        element.setAttribute(key, value);\n                    }\n                }\n            });\n        }\n    }\n    static isFocusableElement(element, selector = '') {\n        return this.isElement(element)\n            ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`)\n            : false;\n    }\n}\n\nclass ConnectedOverlayScrollHandler {\n    element;\n    listener;\n    scrollableParents;\n    constructor(element, listener = () => { }) {\n        this.element = element;\n        this.listener = listener;\n    }\n    bindScrollListener() {\n        this.scrollableParents = DomHandler.getScrollableParents(this.element);\n        for (let i = 0; i < this.scrollableParents.length; i++) {\n            this.scrollableParents[i].addEventListener('scroll', this.listener);\n        }\n    }\n    unbindScrollListener() {\n        if (this.scrollableParents) {\n            for (let i = 0; i < this.scrollableParents.length; i++) {\n                this.scrollableParents[i].removeEventListener('scroll', this.listener);\n            }\n        }\n    }\n    destroy() {\n        this.unbindScrollListener();\n        this.element = null;\n        this.listener = null;\n        this.scrollableParents = null;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,UAAU,CAAC;EACb,OAAOC,MAAM,GAAG,IAAI;EACpB,OAAOC,wBAAwB,GAAG,IAAI;EACtC,OAAOC,yBAAyB,GAAG,IAAI;EACvC,OAAOC,OAAO;EACd,OAAOC,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;IAChC,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EACjBF,OAAO,CAACE,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC,CAAC,KAEjCD,OAAO,CAACC,SAAS,IAAI,GAAG,GAAGA,SAAS;IAC5C;EACJ;EACA,OAAOG,kBAAkBA,CAACJ,OAAO,EAAEC,SAAS,EAAE;IAC1C,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EAAE;QACnB,IAAIG,MAAM,GAAGJ,SAAS,CAACK,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;QACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;UACpCR,OAAO,CAACE,SAAS,CAACC,GAAG,CAACE,MAAM,CAACG,CAAC,CAAC,CAAC;QACpC;MACJ,CAAC,MACI;QACD,IAAIH,MAAM,GAAGJ,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC;QACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;UACpCR,OAAO,CAACC,SAAS,IAAI,GAAG,GAAGI,MAAM,CAACG,CAAC,CAAC;QACxC;MACJ;IACJ;EACJ;EACA,OAAOE,WAAWA,CAACV,OAAO,EAAEC,SAAS,EAAE;IACnC,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EACjBF,OAAO,CAACE,SAAS,CAACS,MAAM,CAACV,SAAS,CAAC,CAAC,KAEpCD,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS,CAACW,OAAO,CAAC,IAAIC,MAAM,CAAC,SAAS,GAAGZ,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IACpI;EACJ;EACA,OAAOC,qBAAqBA,CAACf,OAAO,EAAEgB,UAAU,EAAE;IAC9C,IAAIhB,OAAO,IAAIgB,UAAU,EAAE;MACvB,CAACA,UAAU,CAAC,CACPC,IAAI,CAAC,CAAC,CACNC,MAAM,CAACC,OAAO,CAAC,CACfC,OAAO,CAAEC,MAAM,IAAKA,MAAM,CAACd,KAAK,CAAC,GAAG,CAAC,CAACa,OAAO,CAAEnB,SAAS,IAAK,IAAI,CAACS,WAAW,CAACV,OAAO,EAAEC,SAAS,CAAC,CAAC,CAAC;IAC5G;EACJ;EACA,OAAOqB,QAAQA,CAACtB,OAAO,EAAEC,SAAS,EAAE;IAChC,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EACjB,OAAOF,OAAO,CAACE,SAAS,CAACqB,QAAQ,CAACtB,SAAS,CAAC,CAAC,KAE7C,OAAO,IAAIY,MAAM,CAAC,OAAO,GAAGZ,SAAS,GAAG,OAAO,EAAE,IAAI,CAAC,CAACuB,IAAI,CAACxB,OAAO,CAACC,SAAS,CAAC;IACtF;IACA,OAAO,KAAK;EAChB;EACA,OAAOwB,QAAQA,CAACzB,OAAO,EAAE;IACrB,OAAO0B,KAAK,CAACC,SAAS,CAACT,MAAM,CAACU,IAAI,CAAC5B,OAAO,CAAC6B,UAAU,CAACC,QAAQ,EAAE,UAAUC,KAAK,EAAE;MAC7E,OAAOA,KAAK,KAAK/B,OAAO;IAC5B,CAAC,CAAC;EACN;EACA,OAAOgC,IAAIA,CAAChC,OAAO,EAAEiC,QAAQ,EAAE;IAC3B,OAAOP,KAAK,CAACQ,IAAI,CAAClC,OAAO,CAACmC,gBAAgB,CAACF,QAAQ,CAAC,CAAC;EACzD;EACA,OAAOG,UAAUA,CAACpC,OAAO,EAAEiC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAACI,SAAS,CAACrC,OAAO,CAAC,GAAGA,OAAO,CAACsC,aAAa,CAACL,QAAQ,CAAC,GAAG,IAAI;EAC3E;EACA,OAAOM,KAAKA,CAACvC,OAAO,EAAE;IAClB,IAAI8B,QAAQ,GAAG9B,OAAO,CAAC6B,UAAU,CAACW,UAAU;IAC5C,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,QAAQ,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIsB,QAAQ,CAACtB,CAAC,CAAC,IAAIR,OAAO,EACtB,OAAOyC,GAAG;MACd,IAAIX,QAAQ,CAACtB,CAAC,CAAC,CAACkC,QAAQ,IAAI,CAAC,EACzBD,GAAG,EAAE;IACb;IACA,OAAO,CAAC,CAAC;EACb;EACA,OAAOE,gBAAgBA,CAAC3C,OAAO,EAAE4C,aAAa,EAAE;IAC5C,IAAId,QAAQ,GAAG9B,OAAO,CAAC6B,UAAU,GAAG7B,OAAO,CAAC6B,UAAU,CAACW,UAAU,GAAG,EAAE;IACtE,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,QAAQ,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIsB,QAAQ,CAACtB,CAAC,CAAC,IAAIR,OAAO,EACtB,OAAOyC,GAAG;MACd,IAAIX,QAAQ,CAACtB,CAAC,CAAC,CAACqC,UAAU,IAAIf,QAAQ,CAACtB,CAAC,CAAC,CAACqC,UAAU,CAACD,aAAa,CAAC,IAAId,QAAQ,CAACtB,CAAC,CAAC,CAACkC,QAAQ,IAAI,CAAC,EAC5FD,GAAG,EAAE;IACb;IACA,OAAO,CAAC,CAAC;EACb;EACA,OAAOK,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,GAAG,MAAM,EAAE;IACrD,IAAIA,QAAQ,KAAK,MAAM,IAAIF,OAAO,IAAIC,MAAM,EAAE;MAC1C,IAAI,CAACE,WAAW,CAACH,OAAO,EAAEC,MAAM,CAAC;IACrC;EACJ;EACA,OAAOG,YAAYA,CAACJ,OAAO,EAAEC,MAAM,EAAEC,QAAQ,GAAG,MAAM,EAAEG,iBAAiB,GAAG,IAAI,EAAE;IAC9E,IAAIL,OAAO,IAAIC,MAAM,EAAE;MACnB,IAAII,iBAAiB,EAAE;QACnBL,OAAO,CAACM,KAAK,CAACC,QAAQ,GAAG,GAAG5D,UAAU,CAAC6D,aAAa,CAACP,MAAM,CAAC,IAAI;MACpE;MACA,IAAIC,QAAQ,KAAK,MAAM,EAAE;QACrB,IAAI,CAACO,gBAAgB,CAACT,OAAO,EAAEC,MAAM,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAACS,gBAAgB,CAACV,OAAO,EAAEC,MAAM,CAAC;MAC1C;IACJ;EACJ;EACA,OAAOQ,gBAAgBA,CAACxD,OAAO,EAAEgD,MAAM,EAAEU,MAAM,GAAG,IAAI,EAAE;IACpD,MAAMC,yBAAyB,GAAIC,EAAE,IAAK;MACtC,IAAI,CAACA,EAAE,EACH;MACJ,OAAOC,gBAAgB,CAACD,EAAE,CAAC,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU,GAAGF,EAAE,GAAGD,yBAAyB,CAACC,EAAE,CAACG,aAAa,CAAC;IAC9H,CAAC;IACD,MAAMC,iBAAiB,GAAGhE,OAAO,CAACiE,YAAY,GAAG;MAAEC,KAAK,EAAElE,OAAO,CAACmE,WAAW;MAAEC,MAAM,EAAEpE,OAAO,CAACqE;IAAa,CAAC,GAAG,IAAI,CAACC,0BAA0B,CAACtE,OAAO,CAAC;IACxJ,MAAMuE,YAAY,GAAGvB,MAAM,CAACqB,YAAY,IAAIrB,MAAM,CAACwB,qBAAqB,CAAC,CAAC,CAACJ,MAAM;IACjF,MAAMK,YAAY,GAAGzB,MAAM,CAACwB,qBAAqB,CAAC,CAAC;IACnD,MAAME,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IACnD,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,MAAMC,eAAe,GAAGrB,yBAAyB,CAAC3D,OAAO,CAAC;IAC1D,MAAMiF,qBAAqB,GAAGD,eAAe,EAAER,qBAAqB,CAAC,CAAC,IAAI;MAAEU,GAAG,EAAE,CAAC,CAAC,GAAGR,eAAe;MAAES,IAAI,EAAE,CAAC,CAAC,GAAGP;IAAiB,CAAC;IACpI,IAAIM,GAAG,EAAEC,IAAI;IACb,IAAIV,YAAY,CAACS,GAAG,GAAGX,YAAY,GAAGP,iBAAiB,CAACI,MAAM,GAAGU,QAAQ,CAACV,MAAM,EAAE;MAC9Ec,GAAG,GAAGT,YAAY,CAACS,GAAG,GAAGD,qBAAqB,CAACC,GAAG,GAAGlB,iBAAiB,CAACI,MAAM;MAC7EpE,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,QAAQ;MACxC,IAAIX,YAAY,CAACS,GAAG,GAAGA,GAAG,GAAG,CAAC,EAAE;QAC5BA,GAAG,GAAG,CAAC,CAAC,GAAGT,YAAY,CAACS,GAAG;MAC/B;IACJ,CAAC,MACI;MACDA,GAAG,GAAGX,YAAY,GAAGE,YAAY,CAACS,GAAG,GAAGD,qBAAqB,CAACC,GAAG;MACjElF,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,KAAK;IACzC;IACA,MAAMC,kBAAkB,GAAGZ,YAAY,CAACU,IAAI,GAAGnB,iBAAiB,CAACE,KAAK,GAAGY,QAAQ,CAACZ,KAAK;IACvF,MAAMoB,wCAAwC,GAAGb,YAAY,CAACU,IAAI,GAAGF,qBAAqB,CAACE,IAAI;IAC/F,IAAInB,iBAAiB,CAACE,KAAK,GAAGY,QAAQ,CAACZ,KAAK,EAAE;MAC1C;MACAiB,IAAI,GAAG,CAACV,YAAY,CAACU,IAAI,GAAGF,qBAAqB,CAACE,IAAI,IAAI,CAAC,CAAC;IAChE,CAAC,MACI,IAAIE,kBAAkB,GAAG,CAAC,EAAE;MAC7B;MACAF,IAAI,GAAGG,wCAAwC,GAAGD,kBAAkB;IACxE,CAAC,MACI;MACD;MACAF,IAAI,GAAGV,YAAY,CAACU,IAAI,GAAGF,qBAAqB,CAACE,IAAI;IACzD;IACAnF,OAAO,CAACqD,KAAK,CAAC6B,GAAG,GAAGA,GAAG,GAAG,IAAI;IAC9BlF,OAAO,CAACqD,KAAK,CAAC8B,IAAI,GAAGA,IAAI,GAAG,IAAI;IAChCzB,MAAM,KAAK1D,OAAO,CAACqD,KAAK,CAACkC,SAAS,GAAGC,MAAM,KAAK,QAAQ,GAAG,mCAAmC,GAAG,8BAA8B,CAAC;EACpI;EACA,OAAO/B,gBAAgBA,CAACzD,OAAO,EAAEgD,MAAM,EAAEU,MAAM,GAAG,IAAI,EAAE;IACpD,MAAMM,iBAAiB,GAAGhE,OAAO,CAACiE,YAAY,GAAG;MAAEC,KAAK,EAAElE,OAAO,CAACmE,WAAW;MAAEC,MAAM,EAAEpE,OAAO,CAACqE;IAAa,CAAC,GAAG,IAAI,CAACC,0BAA0B,CAACtE,OAAO,CAAC;IACxJ,MAAMyF,kBAAkB,GAAGzB,iBAAiB,CAACI,MAAM;IACnD,MAAMsB,iBAAiB,GAAG1B,iBAAiB,CAACE,KAAK;IACjD,MAAMyB,iBAAiB,GAAG3C,MAAM,CAACqB,YAAY,IAAIrB,MAAM,CAACwB,qBAAqB,CAAC,CAAC,CAACJ,MAAM;IACtF,MAAMwB,gBAAgB,GAAG5C,MAAM,CAACmB,WAAW,IAAInB,MAAM,CAACwB,qBAAqB,CAAC,CAAC,CAACN,KAAK;IACnF,MAAMO,YAAY,GAAGzB,MAAM,CAACwB,qBAAqB,CAAC,CAAC;IACnD,MAAME,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IACnD,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,IAAIG,GAAG,EAAEC,IAAI;IACb,IAAIV,YAAY,CAACS,GAAG,GAAGS,iBAAiB,GAAGF,kBAAkB,GAAGX,QAAQ,CAACV,MAAM,EAAE;MAC7Ec,GAAG,GAAGT,YAAY,CAACS,GAAG,GAAGR,eAAe,GAAGe,kBAAkB;MAC7DzF,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,QAAQ;MACxC,IAAIF,GAAG,GAAG,CAAC,EAAE;QACTA,GAAG,GAAGR,eAAe;MACzB;IACJ,CAAC,MACI;MACDQ,GAAG,GAAGS,iBAAiB,GAAGlB,YAAY,CAACS,GAAG,GAAGR,eAAe;MAC5D1E,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,KAAK;IACzC;IACA,IAAIX,YAAY,CAACU,IAAI,GAAGO,iBAAiB,GAAGZ,QAAQ,CAACZ,KAAK,EACtDiB,IAAI,GAAGU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErB,YAAY,CAACU,IAAI,GAAGP,gBAAgB,GAAGgB,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,KAEhGP,IAAI,GAAGV,YAAY,CAACU,IAAI,GAAGP,gBAAgB;IAC/C5E,OAAO,CAACqD,KAAK,CAAC6B,GAAG,GAAGA,GAAG,GAAG,IAAI;IAC9BlF,OAAO,CAACqD,KAAK,CAAC8B,IAAI,GAAGA,IAAI,GAAG,IAAI;IAChCzB,MAAM,KAAK1D,OAAO,CAACqD,KAAK,CAACkC,SAAS,GAAGC,MAAM,KAAK,QAAQ,GAAG,mCAAmC,GAAG,8BAA8B,CAAC;EACpI;EACA,OAAOO,UAAUA,CAAC/F,OAAO,EAAEgG,OAAO,GAAG,EAAE,EAAE;IACrC,OAAOhG,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,GAAGgG,OAAO,GAAG,IAAI,CAACD,UAAU,CAAC/F,OAAO,CAAC6B,UAAU,EAAEmE,OAAO,CAACC,MAAM,CAAC,CAACjG,OAAO,CAAC6B,UAAU,CAAC,CAAC,CAAC;EAC/H;EACA,OAAOqE,oBAAoBA,CAAClG,OAAO,EAAE;IACjC,IAAImG,iBAAiB,GAAG,EAAE;IAC1B,IAAInG,OAAO,EAAE;MACT,IAAIgG,OAAO,GAAG,IAAI,CAACD,UAAU,CAAC/F,OAAO,CAAC;MACtC,MAAMoG,aAAa,GAAG,eAAe;MACrC,MAAMC,aAAa,GAAIC,IAAI,IAAK;QAC5B,IAAIC,gBAAgB,GAAGC,MAAM,CAAC,kBAAkB,CAAC,CAACF,IAAI,EAAE,IAAI,CAAC;QAC7D,OAAOF,aAAa,CAAC5E,IAAI,CAAC+E,gBAAgB,CAACzC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAIsC,aAAa,CAAC5E,IAAI,CAAC+E,gBAAgB,CAACzC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAIsC,aAAa,CAAC5E,IAAI,CAAC+E,gBAAgB,CAACzC,gBAAgB,CAAC,WAAW,CAAC,CAAC;MACxN,CAAC;MACD,KAAK,IAAI2C,MAAM,IAAIT,OAAO,EAAE;QACxB,IAAIU,eAAe,GAAGD,MAAM,CAAC/D,QAAQ,KAAK,CAAC,IAAI+D,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;QAChF,IAAID,eAAe,EAAE;UACjB,IAAIE,SAAS,GAAGF,eAAe,CAACnG,KAAK,CAAC,GAAG,CAAC;UAC1C,KAAK,IAAI0B,QAAQ,IAAI2E,SAAS,EAAE;YAC5B,IAAIhD,EAAE,GAAG,IAAI,CAACxB,UAAU,CAACqE,MAAM,EAAExE,QAAQ,CAAC;YAC1C,IAAI2B,EAAE,IAAIyC,aAAa,CAACzC,EAAE,CAAC,EAAE;cACzBuC,iBAAiB,CAACU,IAAI,CAACjD,EAAE,CAAC;YAC9B;UACJ;QACJ;QACA,IAAI6C,MAAM,CAAC/D,QAAQ,KAAK,CAAC,IAAI2D,aAAa,CAACI,MAAM,CAAC,EAAE;UAChDN,iBAAiB,CAACU,IAAI,CAACJ,MAAM,CAAC;QAClC;MACJ;IACJ;IACA,OAAON,iBAAiB;EAC5B;EACA,OAAOW,2BAA2BA,CAAC9G,OAAO,EAAE;IACxCA,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,QAAQ;IACnC/G,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,OAAO;IAC/B,IAAIC,aAAa,GAAGjH,OAAO,CAACqE,YAAY;IACxCrE,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,MAAM;IAC9BhH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,SAAS;IACpC,OAAOE,aAAa;EACxB;EACA,OAAOC,0BAA0BA,CAAClH,OAAO,EAAE;IACvCA,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,QAAQ;IACnC/G,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,OAAO;IAC/B,IAAIG,YAAY,GAAGnH,OAAO,CAACmE,WAAW;IACtCnE,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,MAAM;IAC9BhH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,SAAS;IACpC,OAAOI,YAAY;EACvB;EACA,OAAO7C,0BAA0BA,CAACtE,OAAO,EAAE;IACvC,IAAIoH,UAAU,GAAG,CAAC,CAAC;IACnBpH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,QAAQ;IACnC/G,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,OAAO;IAC/BI,UAAU,CAAClD,KAAK,GAAGlE,OAAO,CAACmE,WAAW;IACtCiD,UAAU,CAAChD,MAAM,GAAGpE,OAAO,CAACqE,YAAY;IACxCrE,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,MAAM;IAC9BhH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,SAAS;IACpC,OAAOK,UAAU;EACrB;EACA,OAAOC,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACjC,IAAIC,cAAc,GAAG3D,gBAAgB,CAACyD,SAAS,CAAC,CAACxD,gBAAgB,CAAC,gBAAgB,CAAC;IACnF,IAAI2D,SAAS,GAAGD,cAAc,GAAGE,UAAU,CAACF,cAAc,CAAC,GAAG,CAAC;IAC/D,IAAIG,eAAe,GAAG9D,gBAAgB,CAACyD,SAAS,CAAC,CAACxD,gBAAgB,CAAC,YAAY,CAAC;IAChF,IAAI8D,UAAU,GAAGD,eAAe,GAAGD,UAAU,CAACC,eAAe,CAAC,GAAG,CAAC;IAClE,IAAIE,aAAa,GAAGP,SAAS,CAAC9C,qBAAqB,CAAC,CAAC;IACrD,IAAIsD,QAAQ,GAAGP,IAAI,CAAC/C,qBAAqB,CAAC,CAAC;IAC3C,IAAIuD,MAAM,GAAGD,QAAQ,CAAC5C,GAAG,GAAG8C,QAAQ,CAACC,IAAI,CAACC,SAAS,IAAIL,aAAa,CAAC3C,GAAG,GAAG8C,QAAQ,CAACC,IAAI,CAACC,SAAS,CAAC,GAAGT,SAAS,GAAGG,UAAU;IAC5H,IAAIO,MAAM,GAAGb,SAAS,CAACY,SAAS;IAChC,IAAIjB,aAAa,GAAGK,SAAS,CAACc,YAAY;IAC1C,IAAIC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACf,IAAI,CAAC;IAC1C,IAAIQ,MAAM,GAAG,CAAC,EAAE;MACZT,SAAS,CAACY,SAAS,GAAGC,MAAM,GAAGJ,MAAM;IACzC,CAAC,MACI,IAAIA,MAAM,GAAGM,UAAU,GAAGpB,aAAa,EAAE;MAC1CK,SAAS,CAACY,SAAS,GAAGC,MAAM,GAAGJ,MAAM,GAAGd,aAAa,GAAGoB,UAAU;IACtE;EACJ;EACA,OAAOE,MAAMA,CAACvI,OAAO,EAAEwI,QAAQ,EAAE;IAC7BxI,OAAO,CAACqD,KAAK,CAACoF,OAAO,GAAG,CAAC;IACzB,IAAIC,IAAI,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAIF,OAAO,GAAG,CAAC;IACf,IAAIG,IAAI,GAAG,SAAAA,CAAA,EAAY;MACnBH,OAAO,GAAG,CAACzI,OAAO,CAACqD,KAAK,CAACoF,OAAO,CAAC7H,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI+H,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,IAAI,IAAIF,QAAQ;MAC7FxI,OAAO,CAACqD,KAAK,CAACoF,OAAO,GAAGA,OAAO;MAC/BC,IAAI,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;MAClB,IAAI,CAACF,OAAO,GAAG,CAAC,EAAE;QACbjC,MAAM,CAACsC,qBAAqB,IAAIA,qBAAqB,CAACF,IAAI,CAAC,IAAKG,UAAU,CAACH,IAAI,EAAE,EAAE,CAAC;MACzF;IACJ,CAAC;IACDA,IAAI,CAAC,CAAC;EACV;EACA,OAAOI,OAAOA,CAAChJ,OAAO,EAAEiJ,EAAE,EAAE;IACxB,IAAIR,OAAO,GAAG,CAAC;MAAES,QAAQ,GAAG,EAAE;MAAEV,QAAQ,GAAGS,EAAE;MAAEE,GAAG,GAAGD,QAAQ,GAAGV,QAAQ;IACxE,IAAIY,MAAM,GAAGC,WAAW,CAAC,MAAM;MAC3BZ,OAAO,GAAGA,OAAO,GAAGU,GAAG;MACvB,IAAIV,OAAO,IAAI,CAAC,EAAE;QACdA,OAAO,GAAG,CAAC;QACXa,aAAa,CAACF,MAAM,CAAC;MACzB;MACApJ,OAAO,CAACqD,KAAK,CAACoF,OAAO,GAAGA,OAAO;IACnC,CAAC,EAAES,QAAQ,CAAC;EAChB;EACA,OAAOvE,kBAAkBA,CAAA,EAAG;IACxB,IAAI4E,GAAG,GAAGvB,QAAQ,CAACwB,eAAe;IAClC,OAAO,CAAChD,MAAM,CAACiD,WAAW,IAAIF,GAAG,CAACrB,SAAS,KAAKqB,GAAG,CAACG,SAAS,IAAI,CAAC,CAAC;EACvE;EACA,OAAO7E,mBAAmBA,CAAA,EAAG;IACzB,IAAI0E,GAAG,GAAGvB,QAAQ,CAACwB,eAAe;IAClC,OAAO,CAAChD,MAAM,CAACmD,WAAW,IAAIJ,GAAG,CAACK,UAAU,KAAKL,GAAG,CAACM,UAAU,IAAI,CAAC,CAAC;EACzE;EACA,OAAOC,OAAOA,CAAC9J,OAAO,EAAEiC,QAAQ,EAAE;IAC9B,IAAI8H,CAAC,GAAGC,OAAO,CAACrI,SAAS;IACzB,IAAIsI,CAAC,GAAGF,CAAC,CAAC,SAAS,CAAC,IAChBA,CAAC,CAACG,qBAAqB,IACvBH,CAAC,CAAC,oBAAoB,CAAC,IACvBA,CAAC,CAAC,mBAAmB,CAAC,IACtB,UAAUI,CAAC,EAAE;MACT,OAAO,EAAE,CAACC,OAAO,CAACxI,IAAI,CAACoG,QAAQ,CAAC7F,gBAAgB,CAACgI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACrE,CAAC;IACL,OAAOF,CAAC,CAACrI,IAAI,CAAC5B,OAAO,EAAEiC,QAAQ,CAAC;EACpC;EACA,OAAOsB,aAAaA,CAACK,EAAE,EAAEyG,MAAM,EAAE;IAC7B,IAAInG,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAIkG,MAAM,EAAE;MACR,IAAIhH,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACiH,UAAU,CAAC,GAAG5C,UAAU,CAACrE,KAAK,CAACkH,WAAW,CAAC;IACzE;IACA,OAAOrG,KAAK;EAChB;EACA,OAAOsG,oBAAoBA,CAAC5G,EAAE,EAAE;IAC5B,IAAIP,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;IAChC,OAAO8D,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC;EACzE;EACA,OAAOC,mBAAmBA,CAAC/G,EAAE,EAAE;IAC3B,IAAIP,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;IAChC,OAAO8D,UAAU,CAACrE,KAAK,CAACiH,UAAU,CAAC,GAAG5C,UAAU,CAACrE,KAAK,CAACkH,WAAW,CAAC;EACvE;EACA,OAAOK,UAAUA,CAAChH,EAAE,EAAE;IAClB,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAId,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;IAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC;IACvE,OAAOxG,KAAK;EAChB;EACA,OAAOA,KAAKA,CAACN,EAAE,EAAE;IACb,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAId,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;IAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC;IACvE,OAAOxG,KAAK;EAChB;EACA,OAAO2G,cAAcA,CAACjH,EAAE,EAAE;IACtB,IAAIQ,MAAM,GAAGR,EAAE,CAACS,YAAY;IAC5B,IAAIhB,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;IAChCQ,MAAM,IAAIsD,UAAU,CAACrE,KAAK,CAACuE,UAAU,CAAC,GAAGF,UAAU,CAACrE,KAAK,CAACyH,aAAa,CAAC;IACxE,OAAO1G,MAAM;EACjB;EACA,OAAOkE,cAAcA,CAAC1E,EAAE,EAAEyG,MAAM,EAAE;IAC9B,IAAIjG,MAAM,GAAGR,EAAE,CAACS,YAAY;IAC5B,IAAIgG,MAAM,EAAE;MACR,IAAIhH,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChCQ,MAAM,IAAIsD,UAAU,CAACrE,KAAK,CAACkC,SAAS,CAAC,GAAGmC,UAAU,CAACrE,KAAK,CAAC0H,YAAY,CAAC;IAC1E;IACA,OAAO3G,MAAM;EACjB;EACA,OAAO4G,SAASA,CAACpH,EAAE,EAAE;IACjB,IAAIQ,MAAM,GAAGR,EAAE,CAACS,YAAY;IAC5B,IAAIhB,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;IAChCQ,MAAM,IAAIsD,UAAU,CAACrE,KAAK,CAACuE,UAAU,CAAC,GAAGF,UAAU,CAACrE,KAAK,CAACyH,aAAa,CAAC,GAAGpD,UAAU,CAACrE,KAAK,CAAC4H,cAAc,CAAC,GAAGvD,UAAU,CAACrE,KAAK,CAAC6H,iBAAiB,CAAC;IACjJ,OAAO9G,MAAM;EACjB;EACA,OAAO+G,QAAQA,CAACvH,EAAE,EAAE;IAChB,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAId,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;IAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC,GAAGhD,UAAU,CAACrE,KAAK,CAAC+H,eAAe,CAAC,GAAG1D,UAAU,CAACrE,KAAK,CAACgI,gBAAgB,CAAC;IAChJ,OAAOnH,KAAK;EAChB;EACA,OAAOa,WAAWA,CAAA,EAAG;IACjB,IAAIuG,GAAG,GAAG9E,MAAM;MAAE+E,CAAC,GAAGvD,QAAQ;MAAEwD,CAAC,GAAGD,CAAC,CAAC/B,eAAe;MAAEiC,CAAC,GAAGF,CAAC,CAACG,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;MAAEC,CAAC,GAAGL,GAAG,CAACV,UAAU,IAAIY,CAAC,CAACI,WAAW,IAAIH,CAAC,CAACG,WAAW;MAAEC,CAAC,GAAGP,GAAG,CAACQ,WAAW,IAAIN,CAAC,CAACpD,YAAY,IAAIqD,CAAC,CAACrD,YAAY;IAC3M,OAAO;MAAElE,KAAK,EAAEyH,CAAC;MAAEvH,MAAM,EAAEyH;IAAE,CAAC;EAClC;EACA,OAAOE,SAASA,CAACnI,EAAE,EAAE;IACjB,IAAIoI,IAAI,GAAGpI,EAAE,CAACY,qBAAqB,CAAC,CAAC;IACrC,OAAO;MACHU,GAAG,EAAE8G,IAAI,CAAC9G,GAAG,IAAIsB,MAAM,CAACiD,WAAW,IAAIzB,QAAQ,CAACwB,eAAe,CAACtB,SAAS,IAAIF,QAAQ,CAACC,IAAI,CAACC,SAAS,IAAI,CAAC,CAAC;MAC1G/C,IAAI,EAAE6G,IAAI,CAAC7G,IAAI,IAAIqB,MAAM,CAACmD,WAAW,IAAI3B,QAAQ,CAACwB,eAAe,CAACI,UAAU,IAAI5B,QAAQ,CAACC,IAAI,CAAC2B,UAAU,IAAI,CAAC;IACjH,CAAC;EACL;EACA,OAAOqC,kBAAkBA,CAACjM,OAAO,EAAEkM,kBAAkB,EAAE;IACnD,IAAIrK,UAAU,GAAG7B,OAAO,CAAC6B,UAAU;IACnC,IAAI,CAACA,UAAU,EACX,MAAM,uBAAuB;IACjC,OAAOA,UAAU,CAACsK,YAAY,CAACD,kBAAkB,EAAElM,OAAO,CAAC;EAC/D;EACA,OAAOoM,YAAYA,CAAA,EAAG;IAClB,IAAIC,SAAS,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MAC9B,OAAOD,SAAS,CAACE,SAAS;IAC9B;EACJ;EACA,OAAOC,IAAIA,CAAA,EAAG;IACV,IAAIC,EAAE,GAAGjG,MAAM,CAAC6F,SAAS,CAACE,SAAS;IACnC,IAAIG,IAAI,GAAGD,EAAE,CAACrC,OAAO,CAAC,OAAO,CAAC;IAC9B,IAAIsC,IAAI,GAAG,CAAC,EAAE;MACV;MACA,OAAO,IAAI;IACf;IACA,IAAIC,OAAO,GAAGF,EAAE,CAACrC,OAAO,CAAC,UAAU,CAAC;IACpC,IAAIuC,OAAO,GAAG,CAAC,EAAE;MACb;MACA,IAAIC,EAAE,GAAGH,EAAE,CAACrC,OAAO,CAAC,KAAK,CAAC;MAC1B,OAAO,IAAI;IACf;IACA,IAAIyC,IAAI,GAAGJ,EAAE,CAACrC,OAAO,CAAC,OAAO,CAAC;IAC9B,IAAIyC,IAAI,GAAG,CAAC,EAAE;MACV;MACA,OAAO,IAAI;IACf;IACA;IACA,OAAO,KAAK;EAChB;EACA,OAAOC,KAAKA,CAAA,EAAG;IACX,OAAO,kBAAkB,CAACtL,IAAI,CAAC6K,SAAS,CAACE,SAAS,CAAC,IAAI,CAAC/F,MAAM,CAAC,UAAU,CAAC;EAC9E;EACA,OAAOuG,SAASA,CAAA,EAAG;IACf,OAAO,YAAY,CAACvL,IAAI,CAAC6K,SAAS,CAACE,SAAS,CAAC;EACjD;EACA,OAAOS,aAAaA,CAAA,EAAG;IACnB,OAAO,cAAc,IAAIxG,MAAM,IAAI6F,SAAS,CAACY,cAAc,GAAG,CAAC;EACnE;EACA,OAAO/J,WAAWA,CAAClD,OAAO,EAAEgD,MAAM,EAAE;IAChC,IAAI,IAAI,CAACX,SAAS,CAACW,MAAM,CAAC,EACtBA,MAAM,CAACE,WAAW,CAAClD,OAAO,CAAC,CAAC,KAC3B,IAAIgD,MAAM,IAAIA,MAAM,CAACY,EAAE,IAAIZ,MAAM,CAACY,EAAE,CAACsJ,aAAa,EACnDlK,MAAM,CAACY,EAAE,CAACsJ,aAAa,CAAChK,WAAW,CAAClD,OAAO,CAAC,CAAC,KAE7C,MAAM,gBAAgB,GAAGgD,MAAM,GAAG,MAAM,GAAGhD,OAAO;EAC1D;EACA,OAAOmN,WAAWA,CAACnN,OAAO,EAAEgD,MAAM,EAAE;IAChC,IAAI,IAAI,CAACX,SAAS,CAACW,MAAM,CAAC,EACtBA,MAAM,CAACmK,WAAW,CAACnN,OAAO,CAAC,CAAC,KAC3B,IAAIgD,MAAM,CAACY,EAAE,IAAIZ,MAAM,CAACY,EAAE,CAACsJ,aAAa,EACzClK,MAAM,CAACY,EAAE,CAACsJ,aAAa,CAACC,WAAW,CAACnN,OAAO,CAAC,CAAC,KAE7C,MAAM,gBAAgB,GAAGA,OAAO,GAAG,QAAQ,GAAGgD,MAAM;EAC5D;EACA,OAAOoK,aAAaA,CAACpN,OAAO,EAAE;IAC1B,IAAI,EAAE,QAAQ,IAAIgK,OAAO,CAACrI,SAAS,CAAC,EAChC3B,OAAO,CAAC6B,UAAU,CAACsL,WAAW,CAACnN,OAAO,CAAC,CAAC,KAExCA,OAAO,CAACW,MAAM,CAAC,CAAC;EACxB;EACA,OAAO0B,SAASA,CAACgL,GAAG,EAAE;IAClB,OAAO,OAAOC,WAAW,KAAK,QAAQ,GAAGD,GAAG,YAAYC,WAAW,GAAGD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,CAAC3K,QAAQ,KAAK,CAAC,IAAI,OAAO2K,GAAG,CAACE,QAAQ,KAAK,QAAQ;EAClL;EACA,OAAOC,uBAAuBA,CAAC5J,EAAE,EAAE;IAC/B,IAAIA,EAAE,EAAE;MACJ,IAAIP,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChC,OAAOA,EAAE,CAACO,WAAW,GAAGP,EAAE,CAACgI,WAAW,GAAGlE,UAAU,CAACrE,KAAK,CAAC+H,eAAe,CAAC,GAAG1D,UAAU,CAACrE,KAAK,CAACgI,gBAAgB,CAAC;IACnH,CAAC,MACI;MACD,IAAI,IAAI,CAACzL,wBAAwB,KAAK,IAAI,EACtC,OAAO,IAAI,CAACA,wBAAwB;MACxC,IAAI6N,SAAS,GAAGzF,QAAQ,CAAC0F,aAAa,CAAC,KAAK,CAAC;MAC7CD,SAAS,CAACxN,SAAS,GAAG,qBAAqB;MAC3C+H,QAAQ,CAACC,IAAI,CAAC/E,WAAW,CAACuK,SAAS,CAAC;MACpC,IAAIE,cAAc,GAAGF,SAAS,CAACtJ,WAAW,GAAGsJ,SAAS,CAAC7B,WAAW;MAClE5D,QAAQ,CAACC,IAAI,CAACkF,WAAW,CAACM,SAAS,CAAC;MACpC,IAAI,CAAC7N,wBAAwB,GAAG+N,cAAc;MAC9C,OAAOA,cAAc;IACzB;EACJ;EACA,OAAOC,wBAAwBA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAAC/N,yBAAyB,KAAK,IAAI,EACvC,OAAO,IAAI,CAACA,yBAAyB;IACzC,IAAI4N,SAAS,GAAGzF,QAAQ,CAAC0F,aAAa,CAAC,KAAK,CAAC;IAC7CD,SAAS,CAACxN,SAAS,GAAG,qBAAqB;IAC3C+H,QAAQ,CAACC,IAAI,CAAC/E,WAAW,CAACuK,SAAS,CAAC;IACpC,IAAII,eAAe,GAAGJ,SAAS,CAACpJ,YAAY,GAAGoJ,SAAS,CAACrF,YAAY;IACrEJ,QAAQ,CAACC,IAAI,CAACkF,WAAW,CAACM,SAAS,CAAC;IACpC,IAAI,CAAC7N,wBAAwB,GAAGiO,eAAe;IAC/C,OAAOA,eAAe;EAC1B;EACA,OAAOC,mBAAmBA,CAAC9N,OAAO,EAAE+N,UAAU,EAAEC,IAAI,EAAE;IAClDhO,OAAO,CAAC+N,UAAU,CAAC,CAACE,KAAK,CAACjO,OAAO,EAAEgO,IAAI,CAAC;EAC5C;EACA,OAAOE,cAAcA,CAAA,EAAG;IACpB,IAAI1H,MAAM,CAAC2H,YAAY,EAAE;MACrB,IAAI3H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACC,KAAK,EAAE;QAC7B5H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACjC,CAAC,MACI,IAAI5H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACE,eAAe,IAAI7H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACG,UAAU,GAAG,CAAC,IAAI9H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACI,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC/N,MAAM,GAAG,CAAC,EAAE;QACvJ+F,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACE,eAAe,CAAC,CAAC;MAC3C;IACJ,CAAC,MACI,IAAIrG,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,WAAW,CAAC,CAACoG,KAAK,EAAE;MAC3D,IAAI;QACApG,QAAQ,CAAC,WAAW,CAAC,CAACoG,KAAK,CAAC,CAAC;MACjC,CAAC,CACD,OAAOK,KAAK,EAAE;QACV;MAAA;IAER;EACJ;EACA,OAAOC,UAAUA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC5O,OAAO,EAAE;MACf,IAAI6O,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACrC,IAAI,CAAC9O,OAAO,GAAG,CAAC,CAAC;MACjB,IAAI6O,OAAO,CAAC7O,OAAO,EAAE;QACjB,IAAI,CAACA,OAAO,CAAC6O,OAAO,CAAC7O,OAAO,CAAC,GAAG,IAAI;QACpC,IAAI,CAACA,OAAO,CAAC,SAAS,CAAC,GAAG6O,OAAO,CAACE,OAAO;MAC7C;MACA,IAAI,IAAI,CAAC/O,OAAO,CAAC,QAAQ,CAAC,EAAE;QACxB,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;MACjC,CAAC,MACI,IAAI,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC7B,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;MACjC;IACJ;IACA,OAAO,IAAI,CAACA,OAAO;EACvB;EACA,OAAO8O,gBAAgBA,CAAA,EAAG;IACtB,IAAInC,EAAE,GAAGJ,SAAS,CAACE,SAAS,CAACuC,WAAW,CAAC,CAAC;IAC1C,IAAIC,KAAK,GAAG,uBAAuB,CAACC,IAAI,CAACvC,EAAE,CAAC,IAAI,uBAAuB,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAI,oCAAoC,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAI,iBAAiB,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAKA,EAAE,CAACrC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,+BAA+B,CAAC4E,IAAI,CAACvC,EAAE,CAAE,IAAI,EAAE;IACnP,OAAO;MACH3M,OAAO,EAAEiP,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MACvBF,OAAO,EAAEE,KAAK,CAAC,CAAC,CAAC,IAAI;IACzB,CAAC;EACL;EACA,OAAOE,SAASA,CAACC,KAAK,EAAE;IACpB,IAAIC,MAAM,CAACF,SAAS,EAAE;MAClB,OAAOE,MAAM,CAACF,SAAS,CAACC,KAAK,CAAC;IAClC,CAAC,MACI;MACD,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIE,QAAQ,CAACF,KAAK,CAAC,IAAIrJ,IAAI,CAACwJ,KAAK,CAACH,KAAK,CAAC,KAAKA,KAAK;IACtF;EACJ;EACA,OAAOI,QAAQA,CAACtP,OAAO,EAAE;IACrB,OAAO,CAACA,OAAO,IAAIA,OAAO,CAACiE,YAAY,KAAK,IAAI;EACpD;EACA,OAAOsL,SAASA,CAACvP,OAAO,EAAE;IACtB,OAAOA,OAAO,IAAIA,OAAO,CAACiE,YAAY,IAAI,IAAI;EAClD;EACA,OAAOuL,OAAOA,CAACxP,OAAO,EAAE;IACpB,OAAOA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACuN,QAAQ,IAAIvN,OAAO,CAAC6B,UAAU;EACvG;EACA,OAAO4N,KAAKA,CAACzP,OAAO,EAAE0P,OAAO,EAAE;IAC3B1P,OAAO,IAAIgI,QAAQ,CAAC2H,aAAa,KAAK3P,OAAO,IAAIA,OAAO,CAACyP,KAAK,CAACC,OAAO,CAAC;EAC3E;EACA,OAAOE,0BAA0BA,CAAC3N,QAAQ,GAAG,EAAE,EAAE;IAC7C,OAAO,2FAA2FA,QAAQ;AAClH,6HAA6HA,QAAQ;AACrI,iGAAiGA,QAAQ;AACzG,kGAAkGA,QAAQ;AAC1G,oGAAoGA,QAAQ;AAC5G,sGAAsGA,QAAQ;AAC9G,6GAA6GA,QAAQ;AACrH,wGAAwGA,QAAQ;AAChH,qGAAqGA,QAAQ,EAAE;EAC3G;EACA,OAAO4N,oBAAoBA,CAAC7P,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;IAChD,IAAI6N,iBAAiB,GAAG,IAAI,CAAC9N,IAAI,CAAChC,OAAO,EAAE,IAAI,CAAC4P,0BAA0B,CAAC3N,QAAQ,CAAC,CAAC;IACrF,IAAI8N,wBAAwB,GAAG,EAAE;IACjC,KAAK,IAAIC,gBAAgB,IAAIF,iBAAiB,EAAE;MAC5C,MAAMG,aAAa,GAAGpM,gBAAgB,CAACmM,gBAAgB,CAAC;MACxD,IAAI,IAAI,CAACT,SAAS,CAACS,gBAAgB,CAAC,IAAIC,aAAa,CAACjJ,OAAO,IAAI,MAAM,IAAIiJ,aAAa,CAAClJ,UAAU,IAAI,QAAQ,EAC3GgJ,wBAAwB,CAAClJ,IAAI,CAACmJ,gBAAgB,CAAC;IACvD;IACA,OAAOD,wBAAwB;EACnC;EACA,OAAOG,mBAAmBA,CAAClQ,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;IAC/C,IAAI+N,gBAAgB,GAAG,IAAI,CAAC5N,UAAU,CAACpC,OAAO,EAAE,IAAI,CAAC4P,0BAA0B,CAAC3N,QAAQ,CAAC,CAAC;IAC1F,IAAI+N,gBAAgB,EAAE;MAClB,MAAMC,aAAa,GAAGpM,gBAAgB,CAACmM,gBAAgB,CAAC;MACxD,IAAI,IAAI,CAACT,SAAS,CAACS,gBAAgB,CAAC,IAAIC,aAAa,CAACjJ,OAAO,IAAI,MAAM,IAAIiJ,aAAa,CAAClJ,UAAU,IAAI,QAAQ,EAC3G,OAAOiJ,gBAAgB;IAC/B;IACA,OAAO,IAAI;EACf;EACA,OAAOG,wBAAwBA,CAACnQ,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;IACpD,MAAM6N,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC7P,OAAO,EAAEiC,QAAQ,CAAC;IACtE,OAAO6N,iBAAiB,CAACrP,MAAM,GAAG,CAAC,GAAGqP,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;EACrE;EACA,OAAOM,uBAAuBA,CAACpQ,OAAO,EAAEiC,QAAQ,EAAE;IAC9C,MAAM6N,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC7P,OAAO,EAAEiC,QAAQ,CAAC;IACtE,OAAO6N,iBAAiB,CAACrP,MAAM,GAAG,CAAC,GAAGqP,iBAAiB,CAACA,iBAAiB,CAACrP,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAChG;EACA,OAAO4P,uBAAuBA,CAACrQ,OAAO,EAAEsQ,OAAO,GAAG,KAAK,EAAE;IACrD,MAAMR,iBAAiB,GAAGpQ,UAAU,CAACmQ,oBAAoB,CAAC7P,OAAO,CAAC;IAClE,IAAIuC,KAAK,GAAG,CAAC;IACb,IAAIuN,iBAAiB,IAAIA,iBAAiB,CAACrP,MAAM,GAAG,CAAC,EAAE;MACnD,MAAM8P,YAAY,GAAGT,iBAAiB,CAAC1F,OAAO,CAAC0F,iBAAiB,CAAC,CAAC,CAAC,CAACU,aAAa,CAACb,aAAa,CAAC;MAChG,IAAIW,OAAO,EAAE;QACT,IAAIC,YAAY,IAAI,CAAC,CAAC,IAAIA,YAAY,KAAK,CAAC,EAAE;UAC1ChO,KAAK,GAAGuN,iBAAiB,CAACrP,MAAM,GAAG,CAAC;QACxC,CAAC,MACI;UACD8B,KAAK,GAAGgO,YAAY,GAAG,CAAC;QAC5B;MACJ,CAAC,MACI,IAAIA,YAAY,IAAI,CAAC,CAAC,IAAIA,YAAY,KAAKT,iBAAiB,CAACrP,MAAM,GAAG,CAAC,EAAE;QAC1E8B,KAAK,GAAGgO,YAAY,GAAG,CAAC;MAC5B;IACJ;IACA,OAAOT,iBAAiB,CAACvN,KAAK,CAAC;EACnC;EACA,OAAOkO,cAAcA,CAAA,EAAG;IACpB,IAAI,CAAC9Q,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,GAAG;IAChC,OAAO,EAAE,IAAI,CAACA,MAAM;EACxB;EACA,OAAOwO,YAAYA,CAAA,EAAG;IAClB,IAAI3H,MAAM,CAAC2H,YAAY,EACnB,OAAO3H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC,CAAC,KACvC,IAAI1I,QAAQ,CAACmG,YAAY,EAC1B,OAAOnG,QAAQ,CAACmG,YAAY,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC,CAAC,KACzC,IAAI1I,QAAQ,CAAC,WAAW,CAAC,EAC1B,OAAOA,QAAQ,CAAC,WAAW,CAAC,CAAC2I,WAAW,CAAC,CAAC,CAACC,IAAI;IACnD,OAAO,IAAI;EACf;EACA,OAAOC,gBAAgBA,CAAC7N,MAAM,EAAEY,EAAE,EAAE;IAChC,IAAI,CAACZ,MAAM,EACP,OAAO,IAAI;IACf,QAAQA,MAAM;MACV,KAAK,UAAU;QACX,OAAOgF,QAAQ;MACnB,KAAK,QAAQ;QACT,OAAOxB,MAAM;MACjB,KAAK,OAAO;QACR,OAAO5C,EAAE,EAAEkN,kBAAkB;MACjC,KAAK,OAAO;QACR,OAAOlN,EAAE,EAAEmN,sBAAsB;MACrC,KAAK,SAAS;QACV,OAAOnN,EAAE,EAAEG,aAAa;MAC5B,KAAK,cAAc;QACf,OAAOH,EAAE,EAAEG,aAAa,CAACA,aAAa;MAC1C;QACI,MAAMiN,IAAI,GAAG,OAAOhO,MAAM;QAC1B,IAAIgO,IAAI,KAAK,QAAQ,EAAE;UACnB,OAAOhJ,QAAQ,CAAC1F,aAAa,CAACU,MAAM,CAAC;QACzC,CAAC,MACI,IAAIgO,IAAI,KAAK,QAAQ,IAAIhO,MAAM,CAACiO,cAAc,CAAC,eAAe,CAAC,EAAE;UAClE,OAAO,IAAI,CAACzB,OAAO,CAACxM,MAAM,CAACkK,aAAa,CAAC,GAAGlK,MAAM,CAACkK,aAAa,GAAGgE,SAAS;QAChF;QACA,MAAMC,UAAU,GAAI9D,GAAG,IAAK,CAAC,EAAEA,GAAG,IAAIA,GAAG,CAAC+D,WAAW,IAAI/D,GAAG,CAACzL,IAAI,IAAIyL,GAAG,CAACY,KAAK,CAAC;QAC/E,MAAMjO,OAAO,GAAGmR,UAAU,CAACnO,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM;QACtD,OAAQhD,OAAO,IAAIA,OAAO,CAAC0C,QAAQ,KAAK,CAAC,IAAK,IAAI,CAAC8M,OAAO,CAACxP,OAAO,CAAC,GAAGA,OAAO,GAAG,IAAI;IAC5F;EACJ;EACA,OAAOsM,QAAQA,CAAA,EAAG;IACd,OAAO,CAAC,EAAE,OAAO9F,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACwB,QAAQ,IAAIxB,MAAM,CAACwB,QAAQ,CAAC0F,aAAa,CAAC;EAChG;EACA,OAAO2D,YAAYA,CAACrR,OAAO,EAAEsR,IAAI,EAAE;IAC/B,IAAItR,OAAO,EAAE;MACT,MAAMkP,KAAK,GAAGlP,OAAO,CAACqR,YAAY,CAACC,IAAI,CAAC;MACxC,IAAI,CAACC,KAAK,CAACrC,KAAK,CAAC,EAAE;QACf,OAAO,CAACA,KAAK;MACjB;MACA,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAE;QACvC,OAAOA,KAAK,KAAK,MAAM;MAC3B;MACA,OAAOA,KAAK;IAChB;IACA,OAAOgC,SAAS;EACpB;EACA,OAAOM,2BAA2BA,CAAA,EAAG;IACjC,OAAOhL,MAAM,CAACoE,UAAU,GAAG5C,QAAQ,CAACwB,eAAe,CAACrF,WAAW;EACnE;EACA,OAAOsN,eAAeA,CAACxR,SAAS,GAAG,mBAAmB,EAAE;IACpD+H,QAAQ,CAACC,IAAI,CAAC5E,KAAK,CAACqO,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAACF,2BAA2B,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/F,IAAI,CAACzR,QAAQ,CAACiI,QAAQ,CAACC,IAAI,EAAEhI,SAAS,CAAC;EAC3C;EACA,OAAO0R,iBAAiBA,CAAC1R,SAAS,GAAG,mBAAmB,EAAE;IACtD+H,QAAQ,CAACC,IAAI,CAAC5E,KAAK,CAACuO,cAAc,CAAC,mBAAmB,CAAC;IACvD,IAAI,CAAClR,WAAW,CAACsH,QAAQ,CAACC,IAAI,EAAEhI,SAAS,CAAC;EAC9C;EACA,OAAOyN,aAAaA,CAACsD,IAAI,EAAEnO,UAAU,GAAG,CAAC,CAAC,EAAE,GAAGf,QAAQ,EAAE;IACrD,IAAIkP,IAAI,EAAE;MACN,MAAMhR,OAAO,GAAGgI,QAAQ,CAAC0F,aAAa,CAACsD,IAAI,CAAC;MAC5C,IAAI,CAACa,aAAa,CAAC7R,OAAO,EAAE6C,UAAU,CAAC;MACvC7C,OAAO,CAAC8R,MAAM,CAAC,GAAGhQ,QAAQ,CAAC;MAC3B,OAAO9B,OAAO;IAClB;IACA,OAAOkR,SAAS;EACpB;EACA,OAAOa,YAAYA,CAAC/R,OAAO,EAAEgS,SAAS,GAAG,EAAE,EAAE9C,KAAK,EAAE;IAChD,IAAI,IAAI,CAAC7M,SAAS,CAACrC,OAAO,CAAC,IAAIkP,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKgC,SAAS,EAAE;MAClElR,OAAO,CAAC+R,YAAY,CAACC,SAAS,EAAE9C,KAAK,CAAC;IAC1C;EACJ;EACA,OAAO2C,aAAaA,CAAC7R,OAAO,EAAE6C,UAAU,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,IAAI,CAACR,SAAS,CAACrC,OAAO,CAAC,EAAE;MACzB,MAAMiS,cAAc,GAAGA,CAACC,IAAI,EAAEhD,KAAK,KAAK;QACpC,MAAM7O,MAAM,GAAGL,OAAO,EAAEmS,MAAM,GAAGD,IAAI,CAAC,GAAG,CAAClS,OAAO,EAAEmS,MAAM,GAAGD,IAAI,CAAC,CAAC,GAAG,EAAE;QACvE,OAAO,CAAChD,KAAK,CAAC,CAACjO,IAAI,CAAC,CAAC,CAACmR,MAAM,CAAC,CAACC,EAAE,EAAEC,CAAC,KAAK;UACpC,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKpB,SAAS,EAAE;YAC/B,MAAMF,IAAI,GAAG,OAAOsB,CAAC;YACrB,IAAItB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;cACxCqB,EAAE,CAACxL,IAAI,CAACyL,CAAC,CAAC;YACd,CAAC,MACI,IAAItB,IAAI,KAAK,QAAQ,EAAE;cACxB,MAAMuB,GAAG,GAAG7Q,KAAK,CAAC8Q,OAAO,CAACF,CAAC,CAAC,GACtBL,cAAc,CAACC,IAAI,EAAEI,CAAC,CAAC,GACvBG,MAAM,CAACC,OAAO,CAACJ,CAAC,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,KAAMX,IAAI,KAAK,OAAO,KAAK,CAAC,CAACW,EAAE,IAAIA,EAAE,KAAK,CAAC,CAAC,GAAG,GAAGD,EAAE,CAAChS,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACkO,WAAW,CAAC,CAAC,IAAI+D,EAAE,EAAE,GAAG,CAAC,CAACA,EAAE,GAAGD,EAAE,GAAG1B,SAAU,CAAC;cAC7KmB,EAAE,GAAGE,GAAG,CAAC9R,MAAM,GAAG4R,EAAE,CAACpM,MAAM,CAACsM,GAAG,CAACrR,MAAM,CAAE4R,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAAC,GAAGT,EAAE;YAC5D;UACJ;UACA,OAAOA,EAAE;QACb,CAAC,EAAEhS,MAAM,CAAC;MACd,CAAC;MACDoS,MAAM,CAACC,OAAO,CAAC7P,UAAU,CAAC,CAACzB,OAAO,CAAC,CAAC,CAAC2R,GAAG,EAAE7D,KAAK,CAAC,KAAK;QACjD,IAAIA,KAAK,KAAKgC,SAAS,IAAIhC,KAAK,KAAK,IAAI,EAAE;UACvC,MAAM8D,YAAY,GAAGD,GAAG,CAAChE,KAAK,CAAC,SAAS,CAAC;UACzC,IAAIiE,YAAY,EAAE;YACdhT,OAAO,CAACiT,gBAAgB,CAACD,YAAY,CAAC,CAAC,CAAC,CAAClE,WAAW,CAAC,CAAC,EAAEI,KAAK,CAAC;UAClE,CAAC,MACI,IAAI6D,GAAG,KAAK,OAAO,EAAE;YACtB,IAAI,CAAClB,aAAa,CAAC7R,OAAO,EAAEkP,KAAK,CAAC;UACtC,CAAC,MACI;YACDA,KAAK,GAAG6D,GAAG,KAAK,OAAO,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACjB,cAAc,CAAC,OAAO,EAAE/C,KAAK,CAAC,CAAC,CAAC,CAACpO,IAAI,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC,CAAC,GAAGyS,GAAG,KAAK,OAAO,GAAGd,cAAc,CAAC,OAAO,EAAE/C,KAAK,CAAC,CAACpO,IAAI,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC,CAAC,GAAG4O,KAAK;YACnK,CAAClP,OAAO,CAACmS,MAAM,GAAGnS,OAAO,CAACmS,MAAM,IAAI,CAAC,CAAC,MAAMnS,OAAO,CAACmS,MAAM,CAACY,GAAG,CAAC,GAAG7D,KAAK,CAAC;YACxElP,OAAO,CAAC+R,YAAY,CAACgB,GAAG,EAAE7D,KAAK,CAAC;UACpC;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA,OAAOiE,kBAAkBA,CAACnT,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;IAC9C,OAAO,IAAI,CAACI,SAAS,CAACrC,OAAO,CAAC,GACxBA,OAAO,CAAC8J,OAAO,CAAC,2FAA2F7H,QAAQ;AACjI,qIAAqIA,QAAQ;AAC7I,yGAAyGA,QAAQ;AACjH,0GAA0GA,QAAQ;AAClH,4GAA4GA,QAAQ;AACpH,8GAA8GA,QAAQ;AACtH,qHAAqHA,QAAQ,EAAE,CAAC,GAClH,KAAK;EACf;AACJ;AAEA,MAAMmR,6BAA6B,CAAC;EAChCpT,OAAO;EACPqT,QAAQ;EACRlN,iBAAiB;EACjBiL,WAAWA,CAACpR,OAAO,EAAEqT,QAAQ,GAAGA,CAAA,KAAM,CAAE,CAAC,EAAE;IACvC,IAAI,CAACrT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqT,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACnN,iBAAiB,GAAGzG,UAAU,CAACwG,oBAAoB,CAAC,IAAI,CAAClG,OAAO,CAAC;IACtE,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2F,iBAAiB,CAAC1F,MAAM,EAAED,CAAC,EAAE,EAAE;MACpD,IAAI,CAAC2F,iBAAiB,CAAC3F,CAAC,CAAC,CAACyS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACI,QAAQ,CAAC;IACvE;EACJ;EACAE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpN,iBAAiB,EAAE;MACxB,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2F,iBAAiB,CAAC1F,MAAM,EAAED,CAAC,EAAE,EAAE;QACpD,IAAI,CAAC2F,iBAAiB,CAAC3F,CAAC,CAAC,CAACgT,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACH,QAAQ,CAAC;MAC1E;IACJ;EACJ;EACAI,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACvT,OAAO,GAAG,IAAI;IACnB,IAAI,CAACqT,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAClN,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;;AAEA,SAASiN,6BAA6B,EAAE1T,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}