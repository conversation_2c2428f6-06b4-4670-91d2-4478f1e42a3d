{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { LayoutComponent } from './shared/components/layout/layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@core/services/language.service\";\nimport * as i3 from \"primeng/toast\";\nimport * as i4 from \"primeng/confirmdialog\";\nexport class AppComponent {\n  constructor(primengConfig, languageService) {\n    this.primengConfig = primengConfig;\n    this.languageService = languageService;\n    this.title = 'Warehouse Management System';\n  }\n  ngOnInit() {\n    this.primengConfig.ripple = true;\n    // Initialize language service and subscribe to language changes\n    this.languageService.currentLanguage$.subscribe(language => {\n      // Update PrimeNG configuration for RTL\n      this.primengConfig.ripple = true;\n      // Set document title based on language\n      document.title = this.languageService.translate('app.title') || this.title;\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i2.LanguageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MessageService, ConfirmationService]), i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-layout\");\n          i0.ɵɵelement(1, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(2, \"p-toast\")(3, \"p-confirmDialog\");\n        }\n      },\n      dependencies: [CommonModule, RouterOutlet, ToastModule, i3.Toast, ConfirmDialogModule, i4.ConfirmDialog, LayoutComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  height: 100vh;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0UsY0FBQTtFQUNBLGFBQUE7QUFBRiIsInNvdXJjZXNDb250ZW50IjpbIi8vIEFwcCBjb21wb25lbnQgc3BlY2lmaWMgc3R5bGVzXG46aG9zdCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xuICBoZWlnaHQ6IDEwMHZoO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "ToastModule", "ConfirmDialogModule", "MessageService", "ConfirmationService", "LayoutComponent", "AppComponent", "constructor", "primengConfig", "languageService", "title", "ngOnInit", "ripple", "currentLanguage$", "subscribe", "language", "document", "translate", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "i2", "LanguageService", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "i3", "Toast", "i4", "ConfirmDialog", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { PrimeNGConfig } from 'primeng/api';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService, ConfirmationService } from 'primeng/api';\n\nimport { LayoutComponent } from './shared/components/layout/layout.component';\nimport { LanguageService } from '@core/services/language.service';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    ToastModule,\n    ConfirmDialogModule,\n    LayoutComponent\n  ],\n  providers: [\n    MessageService,\n    ConfirmationService\n  ],\n  template: `\n    <app-layout>\n      <router-outlet></router-outlet>\n    </app-layout>\n    <p-toast></p-toast>\n    <p-confirmDialog></p-confirmDialog>\n  `,\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'Warehouse Management System';\n\n  constructor(\n    private primengConfig: PrimeNGConfig,\n    private languageService: LanguageService\n  ) {}\n\n  ngOnInit() {\n    this.primengConfig.ripple = true;\n\n    // Initialize language service and subscribe to language changes\n    this.languageService.currentLanguage$.subscribe(language => {\n      // Update PrimeNG configuration for RTL\n      this.primengConfig.ripple = true;\n\n      // Set document title based on language\n      document.title = this.languageService.translate('app.title') || this.title;\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AAEjE,SAASC,eAAe,QAAQ,6CAA6C;;;;;;AA0B7E,OAAM,MAAOC,YAAY;EAGvBC,YACUC,aAA4B,EAC5BC,eAAgC;IADhC,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IAJzB,KAAAC,KAAK,GAAG,6BAA6B;EAKlC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACH,aAAa,CAACI,MAAM,GAAG,IAAI;IAEhC;IACA,IAAI,CAACH,eAAe,CAACI,gBAAgB,CAACC,SAAS,CAACC,QAAQ,IAAG;MACzD;MACA,IAAI,CAACP,aAAa,CAACI,MAAM,GAAG,IAAI;MAEhC;MACAI,QAAQ,CAACN,KAAK,GAAG,IAAI,CAACD,eAAe,CAACQ,SAAS,CAAC,WAAW,CAAC,IAAI,IAAI,CAACP,KAAK;IAC5E,CAAC,CAAC;EACJ;;;uBAnBWJ,YAAY,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAZjB,YAAY;MAAAkB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAR,EAAA,CAAAS,kBAAA,CAbZ,CACTxB,cAAc,EACdC,mBAAmB,CACpB,GAAAc,EAAA,CAAAU,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAECf,EAAA,CAAAiB,cAAA,iBAAY;UACVjB,EAAA,CAAAkB,SAAA,oBAA+B;UACjClB,EAAA,CAAAmB,YAAA,EAAa;UAEbnB,EADA,CAAAkB,SAAA,cAAmB,sBACgB;;;qBAfnCrC,YAAY,EACZC,YAAY,EACZC,WAAW,EAAAqC,EAAA,CAAAC,KAAA,EACXrC,mBAAmB,EAAAsC,EAAA,CAAAC,aAAA,EACnBpC,eAAe;MAAAqC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}