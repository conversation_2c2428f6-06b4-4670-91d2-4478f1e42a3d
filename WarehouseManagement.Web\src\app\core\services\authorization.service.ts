import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AuthService } from './auth.service';
import { 
  User, 
  Permission, 
  UserRole, 
  PermissionAction, 
  UserRoleType,
  SecurityEventType,
  SecurityEventSeverity 
} from '../models/auth.models';

export interface ResourcePermissions {
  [resource: string]: {
    [action: string]: boolean;
  };
}

export interface PermissionCheck {
  resource: string;
  action: PermissionAction | string;
  requireAll?: boolean; // If true, user must have ALL specified permissions
}

@Injectable({
  providedIn: 'root'
})
export class AuthorizationService {
  
  // Define resource constants
  public static readonly RESOURCES = {
    DASHBOARD: 'dashboard',
    INVENTORY: 'inventory',
    PRODUCTS: 'products',
    CATEGORIES: 'categories',
    SUPPLIERS: 'suppliers',
    CUSTOMERS: 'customers',
    ORDERS: 'orders',
    REPORTS: 'reports',
    USERS: 'users',
    ROLES: 'roles',
    SETTINGS: 'settings',
    AUDIT_LOGS: 'audit_logs'
  } as const;

  // Define default role permissions
  private readonly DEFAULT_ROLE_PERMISSIONS: Record<UserRoleType, PermissionCheck[]> = {
    [UserRoleType.ADMIN]: [
      // Admin has full access to everything
      { resource: '*', action: '*' }
    ],
    [UserRoleType.MANAGER]: [
      // Dashboard access
      { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ },
      
      // Inventory management
      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.UPDATE },
      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.CREATE },
      
      // Product management
      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.MANAGE },
      
      // Category management
      { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.MANAGE },
      
      // Supplier management
      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.UPDATE },
      
      // Customer management
      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.UPDATE },
      
      // Order management
      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.MANAGE },
      
      // Reports access
      { resource: AuthorizationService.RESOURCES.REPORTS, action: PermissionAction.READ },
      
      // User management (limited)
      { resource: AuthorizationService.RESOURCES.USERS, action: PermissionAction.READ },
      
      // Settings access (limited)
      { resource: AuthorizationService.RESOURCES.SETTINGS, action: PermissionAction.READ }
    ],
    [UserRoleType.EMPLOYEE]: [
      // Dashboard access
      { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ },
      
      // Inventory access
      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.UPDATE },
      
      // Product access
      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.UPDATE },
      
      // Category access
      { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.READ },
      
      // Supplier access
      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ },
      
      // Customer access
      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.CREATE },
      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.UPDATE },
      
      // Order access
      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.CREATE },
      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.UPDATE }
    ],
    [UserRoleType.VIEWER]: [
      // Dashboard access
      { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ },
      
      // Read-only access to most resources
      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.READ },
      { resource: AuthorizationService.RESOURCES.REPORTS, action: PermissionAction.READ }
    ]
  };

  constructor(private authService: AuthService) {}

  /**
   * Check if current user has permission for a specific resource and action
   */
  hasPermission(resource: string, action: PermissionAction | string): boolean {
    const user = this.authService.getCurrentUserSync();
    if (!user) {
      return false;
    }

    return this.userHasPermission(user, resource, action);
  }

  /**
   * Check if user has permission for a specific resource and action
   */
  userHasPermission(user: User, resource: string, action: PermissionAction | string): boolean {
    // Admin role has all permissions
    if (this.userHasRole(user, UserRoleType.ADMIN)) {
      return true;
    }

    // Check explicit permissions first
    const hasExplicitPermission = user.permissions.some(permission => 
      (permission.resource === resource || permission.resource === '*') &&
      (permission.action === action || permission.action === '*' as PermissionAction)
    );

    if (hasExplicitPermission) {
      return true;
    }

    // Check role-based permissions
    return user.roles.some(role => 
      this.roleHasPermission(role, resource, action)
    );
  }

  /**
   * Check if current user has specific role
   */
  hasRole(roleName: UserRoleType | string): boolean {
    const user = this.authService.getCurrentUserSync();
    if (!user) {
      return false;
    }

    return this.userHasRole(user, roleName);
  }

  /**
   * Check if user has specific role
   */
  userHasRole(user: User, roleName: UserRoleType | string): boolean {
    return user.roles.some(role => role.name === roleName);
  }

  /**
   * Check if role has permission for a specific resource and action
   */
  roleHasPermission(role: UserRole, resource: string, action: PermissionAction | string): boolean {
    // Check explicit role permissions
    const hasExplicitPermission = role.permissions.some(permission => 
      (permission.resource === resource || permission.resource === '*') &&
      (permission.action === action || permission.action === '*' as PermissionAction)
    );

    if (hasExplicitPermission) {
      return true;
    }

    // Check default role permissions
    const roleType = role.name as UserRoleType;
    const defaultPermissions = this.DEFAULT_ROLE_PERMISSIONS[roleType];
    
    if (!defaultPermissions) {
      return false;
    }

    return defaultPermissions.some(permission => 
      (permission.resource === resource || permission.resource === '*') &&
      (permission.action === action || permission.action === '*')
    );
  }

  /**
   * Check multiple permissions (AND logic)
   */
  hasAllPermissions(permissions: PermissionCheck[]): boolean {
    return permissions.every(permission => 
      this.hasPermission(permission.resource, permission.action)
    );
  }

  /**
   * Check multiple permissions (OR logic)
   */
  hasAnyPermission(permissions: PermissionCheck[]): boolean {
    return permissions.some(permission => 
      this.hasPermission(permission.resource, permission.action)
    );
  }

  /**
   * Get all permissions for current user
   */
  getCurrentUserPermissions(): ResourcePermissions {
    const user = this.authService.getCurrentUserSync();
    if (!user) {
      return {};
    }

    return this.getUserPermissions(user);
  }

  /**
   * Get all permissions for a specific user
   */
  getUserPermissions(user: User): ResourcePermissions {
    const permissions: ResourcePermissions = {};
    const resources = Object.values(AuthorizationService.RESOURCES);
    const actions = Object.values(PermissionAction);

    resources.forEach(resource => {
      permissions[resource] = {};
      actions.forEach(action => {
        permissions[resource][action] = this.userHasPermission(user, resource, action);
      });
    });

    return permissions;
  }

  /**
   * Check if current user can access a specific route
   */
  canAccessRoute(route: string): boolean {
    // Map routes to required permissions
    const routePermissions: Record<string, PermissionCheck[]> = {
      '/dashboard': [
        { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ }
      ],
      '/inventory': [
        { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ }
      ],
      '/products': [
        { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.READ }
      ],
      '/categories': [
        { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.READ }
      ],
      '/suppliers': [
        { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ }
      ],
      '/customers': [
        { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ }
      ],
      '/orders': [
        { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.READ }
      ],
      '/reports': [
        { resource: AuthorizationService.RESOURCES.REPORTS, action: PermissionAction.READ }
      ],
      '/users': [
        { resource: AuthorizationService.RESOURCES.USERS, action: PermissionAction.READ }
      ],
      '/settings': [
        { resource: AuthorizationService.RESOURCES.SETTINGS, action: PermissionAction.READ }
      ]
    };

    const requiredPermissions = routePermissions[route];
    if (!requiredPermissions) {
      return true; // Allow access to routes without specific permissions
    }

    return this.hasAllPermissions(requiredPermissions);
  }

  /**
   * Log permission denied event
   */
  logPermissionDenied(resource: string, action: string, route?: string): void {
    const user = this.authService.getCurrentUserSync();
    const description = `Permission denied for ${action} on ${resource}${route ? ` (route: ${route})` : ''}`;
    
    console.warn(`Authorization: ${description}`, {
      user: user?.username,
      resource,
      action,
      route
    });

    // This would typically send to a logging service
    // For now, we'll just log to console
  }

  /**
   * Get user-friendly permission description
   */
  getPermissionDescription(resource: string, action: PermissionAction): string {
    const actionDescriptions = {
      [PermissionAction.CREATE]: 'create',
      [PermissionAction.READ]: 'view',
      [PermissionAction.UPDATE]: 'edit',
      [PermissionAction.DELETE]: 'delete',
      [PermissionAction.MANAGE]: 'manage'
    };

    const resourceDescriptions = {
      [AuthorizationService.RESOURCES.DASHBOARD]: 'Dashboard',
      [AuthorizationService.RESOURCES.INVENTORY]: 'Inventory',
      [AuthorizationService.RESOURCES.PRODUCTS]: 'Products',
      [AuthorizationService.RESOURCES.CATEGORIES]: 'Categories',
      [AuthorizationService.RESOURCES.SUPPLIERS]: 'Suppliers',
      [AuthorizationService.RESOURCES.CUSTOMERS]: 'Customers',
      [AuthorizationService.RESOURCES.ORDERS]: 'Orders',
      [AuthorizationService.RESOURCES.REPORTS]: 'Reports',
      [AuthorizationService.RESOURCES.USERS]: 'Users',
      [AuthorizationService.RESOURCES.ROLES]: 'Roles',
      [AuthorizationService.RESOURCES.SETTINGS]: 'Settings',
      [AuthorizationService.RESOURCES.AUDIT_LOGS]: 'Audit Logs'
    };

    const actionDesc = actionDescriptions[action as PermissionAction] || action;
    const resourceDesc = resourceDescriptions[resource as keyof typeof resourceDescriptions] || resource;

    return `${actionDesc} ${resourceDesc}`;
  }
}
