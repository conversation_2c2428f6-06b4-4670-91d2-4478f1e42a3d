{"ast": null, "code": "import { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/api.service\";\nexport class DashboardService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  getDashboardData() {\n    // For now, return mock data. In production, this would call the API\n    return of(this.getMockDashboardData());\n    // Uncomment this line when the API endpoint is ready:\n    // return this.apiService.get<DashboardData>('dashboard');\n  }\n  getMockDashboardData() {\n    return {\n      totalItems: 1247,\n      totalRevenue: 125000,\n      pendingOrders: 23,\n      lowStockItems: [{\n        id: 1,\n        name: 'Steel Rods',\n        code: 'STL001',\n        currentStock: 15,\n        minimumLevel: 50,\n        warehouseName: 'Raw Materials Warehouse'\n      }, {\n        id: 2,\n        name: 'Plastic Sheets',\n        code: 'PLS002',\n        currentStock: 8,\n        minimumLevel: 25,\n        warehouseName: 'Raw Materials Warehouse'\n      }, {\n        id: 3,\n        name: 'Finished Product A',\n        code: 'FPA001',\n        currentStock: 12,\n        minimumLevel: 30,\n        warehouseName: 'Finished Products Warehouse'\n      }, {\n        id: 4,\n        name: 'Consumable Item B',\n        code: 'CON002',\n        currentStock: 5,\n        minimumLevel: 20,\n        warehouseName: 'Raw Materials Warehouse'\n      }, {\n        id: 5,\n        name: 'Semi-Finished C',\n        code: 'SFC003',\n        currentStock: 18,\n        minimumLevel: 40,\n        warehouseName: 'Finished Products Warehouse'\n      }],\n      recentTransactions: [{\n        id: 1,\n        date: new Date('2024-01-15T10:30:00'),\n        type: 'Sale',\n        amount: 2500,\n        status: 'Completed',\n        reference: 'INV-2024-001'\n      }, {\n        id: 2,\n        date: new Date('2024-01-15T09:15:00'),\n        type: 'Purchase',\n        amount: 1800,\n        status: 'Pending',\n        reference: 'PO-2024-045'\n      }, {\n        id: 3,\n        date: new Date('2024-01-14T16:45:00'),\n        type: 'Return',\n        amount: 350,\n        status: 'Completed',\n        reference: 'RET-2024-012'\n      }, {\n        id: 4,\n        date: new Date('2024-01-14T14:20:00'),\n        type: 'Adjustment',\n        amount: 120,\n        status: 'Completed',\n        reference: 'ADJ-2024-008'\n      }, {\n        id: 5,\n        date: new Date('2024-01-14T11:30:00'),\n        type: 'Sale',\n        amount: 3200,\n        status: 'Completed',\n        reference: 'INV-2024-002'\n      }],\n      salesTrend: [{\n        period: 'Jan',\n        amount: 45000,\n        quantity: 120\n      }, {\n        period: 'Feb',\n        amount: 52000,\n        quantity: 135\n      }, {\n        period: 'Mar',\n        amount: 48000,\n        quantity: 128\n      }, {\n        period: 'Apr',\n        amount: 61000,\n        quantity: 155\n      }, {\n        period: 'May',\n        amount: 55000,\n        quantity: 142\n      }, {\n        period: 'Jun',\n        amount: 67000,\n        quantity: 168\n      }, {\n        period: 'Jul',\n        amount: 71000,\n        quantity: 178\n      }, {\n        period: 'Aug',\n        amount: 69000,\n        quantity: 172\n      }, {\n        period: 'Sep',\n        amount: 75000,\n        quantity: 185\n      }, {\n        period: 'Oct',\n        amount: 82000,\n        quantity: 198\n      }, {\n        period: 'Nov',\n        amount: 78000,\n        quantity: 189\n      }, {\n        period: 'Dec',\n        amount: 85000,\n        quantity: 205\n      }],\n      inventorySummary: {\n        rawMaterials: 450,\n        semiFinished: 280,\n        finishedProducts: 380,\n        consumables: 137,\n        totalValue: 2850000\n      },\n      monthlyRevenue: [{\n        month: 'Jan',\n        revenue: 45000,\n        profit: 12000,\n        expenses: 33000\n      }, {\n        month: 'Feb',\n        revenue: 52000,\n        profit: 15000,\n        expenses: 37000\n      }, {\n        month: 'Mar',\n        revenue: 48000,\n        profit: 13000,\n        expenses: 35000\n      }, {\n        month: 'Apr',\n        revenue: 61000,\n        profit: 18000,\n        expenses: 43000\n      }, {\n        month: 'May',\n        revenue: 55000,\n        profit: 16000,\n        expenses: 39000\n      }, {\n        month: 'Jun',\n        revenue: 67000,\n        profit: 20000,\n        expenses: 47000\n      }, {\n        month: 'Jul',\n        revenue: 71000,\n        profit: 22000,\n        expenses: 49000\n      }, {\n        month: 'Aug',\n        revenue: 69000,\n        profit: 21000,\n        expenses: 48000\n      }, {\n        month: 'Sep',\n        revenue: 75000,\n        profit: 24000,\n        expenses: 51000\n      }, {\n        month: 'Oct',\n        revenue: 82000,\n        profit: 27000,\n        expenses: 55000\n      }, {\n        month: 'Nov',\n        revenue: 78000,\n        profit: 25000,\n        expenses: 53000\n      }, {\n        month: 'Dec',\n        revenue: 85000,\n        profit: 28000,\n        expenses: 57000\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function DashboardService_Factory(t) {\n      return new (t || DashboardService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DashboardService,\n      factory: DashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "DashboardService", "constructor", "apiService", "getDashboardData", "getMockDashboardData", "totalItems", "totalRevenue", "pendingOrders", "lowStockItems", "id", "name", "code", "currentStock", "minimumLevel", "warehouseName", "recentTransactions", "date", "Date", "type", "amount", "status", "reference", "salesTrend", "period", "quantity", "inventorySummary", "rawMaterials", "semiFinished", "finishedProducts", "consumables", "totalValue", "monthlyRevenue", "month", "revenue", "profit", "expenses", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\features\\dashboard\\dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of } from 'rxjs';\nimport { ApiService } from '@core/services/api.service';\nimport { DashboardData } from './dashboard.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DashboardService {\n  constructor(private apiService: ApiService) {}\n\n  getDashboardData(): Observable<DashboardData> {\n    // For now, return mock data. In production, this would call the API\n    return of(this.getMockDashboardData());\n    \n    // Uncomment this line when the API endpoint is ready:\n    // return this.apiService.get<DashboardData>('dashboard');\n  }\n\n  private getMockDashboardData(): DashboardData {\n    return {\n      totalItems: 1247,\n      totalRevenue: 125000,\n      pendingOrders: 23,\n      lowStockItems: [\n        {\n          id: 1,\n          name: 'Steel Rods',\n          code: 'STL001',\n          currentStock: 15,\n          minimumLevel: 50,\n          warehouseName: 'Raw Materials Warehouse'\n        },\n        {\n          id: 2,\n          name: 'Plastic Sheets',\n          code: 'PLS002',\n          currentStock: 8,\n          minimumLevel: 25,\n          warehouseName: 'Raw Materials Warehouse'\n        },\n        {\n          id: 3,\n          name: 'Finished Product A',\n          code: 'FPA001',\n          currentStock: 12,\n          minimumLevel: 30,\n          warehouseName: 'Finished Products Warehouse'\n        },\n        {\n          id: 4,\n          name: 'Consumable Item B',\n          code: 'CON002',\n          currentStock: 5,\n          minimumLevel: 20,\n          warehouseName: 'Raw Materials Warehouse'\n        },\n        {\n          id: 5,\n          name: 'Semi-Finished C',\n          code: 'SFC003',\n          currentStock: 18,\n          minimumLevel: 40,\n          warehouseName: 'Finished Products Warehouse'\n        }\n      ],\n      recentTransactions: [\n        {\n          id: 1,\n          date: new Date('2024-01-15T10:30:00'),\n          type: 'Sale',\n          amount: 2500,\n          status: 'Completed',\n          reference: 'INV-2024-001'\n        },\n        {\n          id: 2,\n          date: new Date('2024-01-15T09:15:00'),\n          type: 'Purchase',\n          amount: 1800,\n          status: 'Pending',\n          reference: 'PO-2024-045'\n        },\n        {\n          id: 3,\n          date: new Date('2024-01-14T16:45:00'),\n          type: 'Return',\n          amount: 350,\n          status: 'Completed',\n          reference: 'RET-2024-012'\n        },\n        {\n          id: 4,\n          date: new Date('2024-01-14T14:20:00'),\n          type: 'Adjustment',\n          amount: 120,\n          status: 'Completed',\n          reference: 'ADJ-2024-008'\n        },\n        {\n          id: 5,\n          date: new Date('2024-01-14T11:30:00'),\n          type: 'Sale',\n          amount: 3200,\n          status: 'Completed',\n          reference: 'INV-2024-002'\n        }\n      ],\n      salesTrend: [\n        { period: 'Jan', amount: 45000, quantity: 120 },\n        { period: 'Feb', amount: 52000, quantity: 135 },\n        { period: 'Mar', amount: 48000, quantity: 128 },\n        { period: 'Apr', amount: 61000, quantity: 155 },\n        { period: 'May', amount: 55000, quantity: 142 },\n        { period: 'Jun', amount: 67000, quantity: 168 },\n        { period: 'Jul', amount: 71000, quantity: 178 },\n        { period: 'Aug', amount: 69000, quantity: 172 },\n        { period: 'Sep', amount: 75000, quantity: 185 },\n        { period: 'Oct', amount: 82000, quantity: 198 },\n        { period: 'Nov', amount: 78000, quantity: 189 },\n        { period: 'Dec', amount: 85000, quantity: 205 }\n      ],\n      inventorySummary: {\n        rawMaterials: 450,\n        semiFinished: 280,\n        finishedProducts: 380,\n        consumables: 137,\n        totalValue: 2850000\n      },\n      monthlyRevenue: [\n        { month: 'Jan', revenue: 45000, profit: 12000, expenses: 33000 },\n        { month: 'Feb', revenue: 52000, profit: 15000, expenses: 37000 },\n        { month: 'Mar', revenue: 48000, profit: 13000, expenses: 35000 },\n        { month: 'Apr', revenue: 61000, profit: 18000, expenses: 43000 },\n        { month: 'May', revenue: 55000, profit: 16000, expenses: 39000 },\n        { month: 'Jun', revenue: 67000, profit: 20000, expenses: 47000 },\n        { month: 'Jul', revenue: 71000, profit: 22000, expenses: 49000 },\n        { month: 'Aug', revenue: 69000, profit: 21000, expenses: 48000 },\n        { month: 'Sep', revenue: 75000, profit: 24000, expenses: 51000 },\n        { month: 'Oct', revenue: 82000, profit: 27000, expenses: 55000 },\n        { month: 'Nov', revenue: 78000, profit: 25000, expenses: 53000 },\n        { month: 'Dec', revenue: 85000, profit: 28000, expenses: 57000 }\n      ]\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,EAAE,QAAQ,MAAM;;;AAOrC,OAAM,MAAOC,gBAAgB;EAC3BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAe;EAE7CC,gBAAgBA,CAAA;IACd;IACA,OAAOJ,EAAE,CAAC,IAAI,CAACK,oBAAoB,EAAE,CAAC;IAEtC;IACA;EACF;EAEQA,oBAAoBA,CAAA;IAC1B,OAAO;MACLC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,MAAM;MACpBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,CACb;QACEC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE;OAChB,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE;OAChB,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE;OAChB,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE;OAChB,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE;OAChB,CACF;MACDC,kBAAkB,EAAE,CAClB;QACEN,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;QACrCC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;OACZ,EACD;QACEZ,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;QACrCC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;OACZ,EACD;QACEZ,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;QACrCC,IAAI,EAAE,QAAQ;QACdC,MAAM,EAAE,GAAG;QACXC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;OACZ,EACD;QACEZ,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;QACrCC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE,GAAG;QACXC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;OACZ,EACD;QACEZ,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;QACrCC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;OACZ,CACF;MACDC,UAAU,EAAE,CACV;QAAEC,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,EAC/C;QAAED,MAAM,EAAE,KAAK;QAAEJ,MAAM,EAAE,KAAK;QAAEK,QAAQ,EAAE;MAAG,CAAE,CAChD;MACDC,gBAAgB,EAAE;QAChBC,YAAY,EAAE,GAAG;QACjBC,YAAY,EAAE,GAAG;QACjBC,gBAAgB,EAAE,GAAG;QACrBC,WAAW,EAAE,GAAG;QAChBC,UAAU,EAAE;OACb;MACDC,cAAc,EAAE,CACd;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE;KAEnE;EACH;;;uBAxIWnC,gBAAgB,EAAAoC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBvC,gBAAgB;MAAAwC,OAAA,EAAhBxC,gBAAgB,CAAAyC,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}