/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// This file exports all the `utils` as private exports so that other parts of `@angular/localize`
// can make use of them.
export { $localize as ɵ$localize } from './src/localize';
export { computeMsgId as ɵcomputeMsgId, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, MissingTranslationError as ɵMissingTranslationError, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate as ɵtranslate } from './src/utils';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJpdmF0ZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2xvY2FsaXplL3ByaXZhdGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsa0dBQWtHO0FBQ2xHLHdCQUF3QjtBQUN4QixPQUFPLEVBQUMsU0FBUyxJQUFJLFVBQVUsRUFBeUQsTUFBTSxnQkFBZ0IsQ0FBQztBQUMvRyxPQUFPLEVBQUMsWUFBWSxJQUFJLGFBQWEsRUFBRSxjQUFjLElBQUksZUFBZSxFQUFFLHlCQUF5QixJQUFJLDBCQUEwQixFQUFFLHFCQUFxQixJQUFJLHNCQUFzQixFQUFFLGtCQUFrQixJQUFJLG1CQUFtQixFQUFFLHVCQUF1QixJQUFJLHdCQUF3QixFQUF1SCxZQUFZLElBQUksYUFBYSxFQUFFLGFBQWEsSUFBSSxjQUFjLEVBQUUsZ0JBQWdCLElBQUksaUJBQWlCLEVBQXNFLFVBQVUsSUFBSSxXQUFXLEVBQUUsU0FBUyxJQUFJLFVBQVUsRUFBQyxNQUFNLGFBQWEsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBUaGlzIGZpbGUgZXhwb3J0cyBhbGwgdGhlIGB1dGlsc2AgYXMgcHJpdmF0ZSBleHBvcnRzIHNvIHRoYXQgb3RoZXIgcGFydHMgb2YgYEBhbmd1bGFyL2xvY2FsaXplYFxuLy8gY2FuIG1ha2UgdXNlIG9mIHRoZW0uXG5leHBvcnQgeyRsb2NhbGl6ZSBhcyDJtSRsb2NhbGl6ZSwgTG9jYWxpemVGbiBhcyDJtUxvY2FsaXplRm4sIFRyYW5zbGF0ZUZuIGFzIMm1VHJhbnNsYXRlRm59IGZyb20gJy4vc3JjL2xvY2FsaXplJztcbmV4cG9ydCB7Y29tcHV0ZU1zZ0lkIGFzIMm1Y29tcHV0ZU1zZ0lkLCBmaW5kRW5kT2ZCbG9jayBhcyDJtWZpbmRFbmRPZkJsb2NrLCBpc01pc3NpbmdUcmFuc2xhdGlvbkVycm9yIGFzIMm1aXNNaXNzaW5nVHJhbnNsYXRpb25FcnJvciwgbWFrZVBhcnNlZFRyYW5zbGF0aW9uIGFzIMm1bWFrZVBhcnNlZFRyYW5zbGF0aW9uLCBtYWtlVGVtcGxhdGVPYmplY3QgYXMgybVtYWtlVGVtcGxhdGVPYmplY3QsIE1pc3NpbmdUcmFuc2xhdGlvbkVycm9yIGFzIMm1TWlzc2luZ1RyYW5zbGF0aW9uRXJyb3IsIFBhcnNlZE1lc3NhZ2UgYXMgybVQYXJzZWRNZXNzYWdlLCBQYXJzZWRUcmFuc2xhdGlvbiBhcyDJtVBhcnNlZFRyYW5zbGF0aW9uLCBQYXJzZWRUcmFuc2xhdGlvbnMgYXMgybVQYXJzZWRUcmFuc2xhdGlvbnMsIHBhcnNlTWVzc2FnZSBhcyDJtXBhcnNlTWVzc2FnZSwgcGFyc2VNZXRhZGF0YSBhcyDJtXBhcnNlTWV0YWRhdGEsIHBhcnNlVHJhbnNsYXRpb24gYXMgybVwYXJzZVRyYW5zbGF0aW9uLCBTb3VyY2VMb2NhdGlvbiBhcyDJtVNvdXJjZUxvY2F0aW9uLCBTb3VyY2VNZXNzYWdlIGFzIMm1U291cmNlTWVzc2FnZSwgc3BsaXRCbG9jayBhcyDJtXNwbGl0QmxvY2ssIHRyYW5zbGF0ZSBhcyDJtXRyYW5zbGF0ZX0gZnJvbSAnLi9zcmMvdXRpbHMnO1xuIl19