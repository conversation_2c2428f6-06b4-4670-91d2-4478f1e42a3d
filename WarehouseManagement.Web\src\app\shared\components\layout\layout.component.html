<div class="layout-wrapper">
  <!-- Top Navigation Bar -->
  <div class="layout-topbar">
    <div class="flex align-items-center gap-3">
      <button
        pButton
        type="button"
        icon="pi pi-bars"
        class="p-button-text p-button-rounded p-button-plain"
        (click)="toggleSidebar()"
        [class.hidden-desktop]="true">
      </button>

      <div class="flex align-items-center gap-2">
        <i class="pi pi-box text-2xl text-primary"></i>
        <h2 class="m-0 text-xl font-semibold">{{ languageService.translate('app.title') }}</h2>
      </div>
    </div>

    <div class="flex align-items-center gap-3">
      <!-- Language Switcher -->
      <app-language-switcher></app-language-switcher>

      <!-- Notifications -->
      <div class="relative">
        <button
          pButton
          type="button"
          icon="pi pi-bell"
          class="p-button-text p-button-rounded p-button-plain"
          [pTooltip]="languageService.translate('app.notifications')"
          tooltipPosition="bottom">
        </button>
        <span class="notification-badge">3</span>
      </div>

      <!-- User Profile -->
      <div class="user-profile flex align-items-center gap-2 p-2 border-round cursor-pointer"
           style="border: 1px solid var(--surface-border);">
        <div class="w-2rem h-2rem border-circle bg-primary flex align-items-center justify-content-center">
          <i class="pi pi-user text-white"></i>
        </div>

        <div class="flex flex-column text-right" *ngIf="languageService.isArabic()">
          <span class="text-sm font-medium">أحمد محمد</span>
          <span class="text-xs text-500">مدير النظام</span>
        </div>

        <div class="flex flex-column" *ngIf="!languageService.isArabic()">
          <span class="text-sm font-medium">Ahmed Mohamed</span>
          <span class="text-xs text-500">System Admin</span>
        </div>

        <i class="pi pi-chevron-down text-500"></i>
      </div>
    </div>
  </div>

  <div class="layout-main">
    <!-- Desktop Sidebar -->
    <div class="layout-sidebar hidden-mobile">
      <p-panelMenu [model]="menuItems" [multiple]="false"></p-panelMenu>
    </div>

    <!-- Mobile Sidebar -->
    <p-sidebar 
      [(visible)]="sidebarVisible" 
      position="left" 
      [modal]="true"
      [dismissible]="true"
      styleClass="layout-sidebar-mobile">
      <ng-template pTemplate="header">
        <div class="flex align-items-center gap-2">
          <i class="pi pi-box text-2xl text-primary"></i>
          <span class="font-semibold">Menu</span>
        </div>
      </ng-template>
      
      <p-panelMenu [model]="menuItems" [multiple]="false"></p-panelMenu>
    </p-sidebar>

    <!-- Main Content Area -->
    <div class="layout-content">
      <ng-content></ng-content>
    </div>
  </div>
</div>
