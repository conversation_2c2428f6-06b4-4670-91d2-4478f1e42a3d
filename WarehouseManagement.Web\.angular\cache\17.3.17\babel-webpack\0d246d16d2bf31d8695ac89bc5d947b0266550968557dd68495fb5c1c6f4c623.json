{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { input, EventEmitter, effect, forwardRef, booleanAttribute, numberAttribute, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, signal, PLATFORM_ID, ChangeDetectionStrategy, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"sublist\"];\nconst _c1 = (a0, a1) => ({\n  \"p-submenu-list\": a0,\n  \"p-tieredmenu-root-list\": a1\n});\nconst _c2 = a0 => ({\n  \"p-menuitem-link\": true,\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  hasSubmenu: a1\n});\nfunction TieredMenuSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 7);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r2.getSeparatorItemClass(processedItem_r2));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template, 1, 3, \"AngleRightIcon\", 22)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.tieredMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.tieredMenu.submenuIconTemplate);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 14);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template, 1, 4, \"span\", 15)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template, 2, 2, \"span\", 16)(3, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template, 2, 2, \"span\", 17)(6, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r5 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(11, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(processedItem_r2, \"url\"), i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlLabel_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template, 1, 3, \"AngleRightIcon\", 22)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.tieredMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.tieredMenu.submenuIconTemplate);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template, 1, 5, \"span\", 15)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template, 2, 2, \"span\", 16)(3, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template, 2, 2, \"span\", 17)(6, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r6 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"))(\"queryParams\", ctx_r2.getItemProp(processedItem_r2, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r2.getItemProp(processedItem_r2, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c3))(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(21, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")))(\"fragment\", ctx_r2.getItemProp(processedItem_r2, \"fragment\"))(\"queryParamsHandling\", ctx_r2.getItemProp(processedItem_r2, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r2.getItemProp(processedItem_r2, \"preserveFragment\"))(\"skipLocationChange\", ctx_r2.getItemProp(processedItem_r2, \"skipLocationChange\"))(\"replaceUrl\", ctx_r2.getItemProp(processedItem_r2, \"replaceUrl\"))(\"state\", ctx_r2.getItemProp(processedItem_r2, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"tabindex\", -1)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_Template, 7, 13, \"a\", 12)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_Template, 7, 23, \"a\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_4_1_Template, 1, 0, null, 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, processedItem_r2.item, ctx_r2.getItemProp(processedItem_r2, \"items\")));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tieredMenuSub\", 28);\n    i0.ɵɵlistener(\"itemClick\", function TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit($event));\n    })(\"itemMouseEnter\", function TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", processedItem_r2.items)(\"itemTemplate\", ctx_r2.itemTemplate)(\"autoDisplay\", ctx_r2.autoDisplay)(\"menuId\", ctx_r2.menuId)(\"activeItemPath\", ctx_r2.activeItemPath())(\"focusedItemId\", ctx_r2.focusedItemId)(\"ariaLabelledBy\", ctx_r2.getItemId(processedItem_r2))(\"level\", ctx_r2.level + 1);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 8, 1)(2, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function TieredMenuSub_ng_template_2_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick($event, processedItem_r2));\n    })(\"mouseenter\", function TieredMenuSub_ng_template_2_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r2\n      }));\n    });\n    i0.ɵɵtemplate(3, TieredMenuSub_ng_template_2_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 10)(4, TieredMenuSub_ng_template_2_li_1_ng_container_4_Template, 2, 5, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template, 1, 8, \"p-tieredMenuSub\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    const processedItem_r2 = ctx_r7.$implicit;\n    const index_r9 = ctx_r7.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.getItemProp(processedItem_r2, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r2.getItemClass(processedItem_r2))(\"pTooltip\", ctx_r2.getItemProp(processedItem_r2, \"tooltip\"))(\"tooltipOptions\", ctx_r2.getItemProp(processedItem_r2, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r2.isItemActive(processedItem_r2))(\"data-p-focused\", ctx_r2.isItemFocused(processedItem_r2))(\"data-p-disabled\", ctx_r2.isItemDisabled(processedItem_r2))(\"aria-label\", ctx_r2.getItemLabel(processedItem_r2))(\"aria-disabled\", ctx_r2.isItemDisabled(processedItem_r2) || undefined)(\"aria-haspopup\", ctx_r2.isItemGroup(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r2.isItemGroup(processedItem_r2) ? ctx_r2.isItemActive(processedItem_r2) : undefined)(\"aria-setsize\", ctx_r2.getAriaSetSize())(\"aria-posinset\", ctx_r2.getAriaPosInset(index_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction TieredMenuSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_0_Template, 1, 4, \"li\", 5)(1, TieredMenuSub_ng_template_2_li_1_Template, 6, 21, \"li\", 6);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n  }\n}\nconst _c5 = [\"rootmenu\"];\nconst _c6 = [\"container\"];\nconst _c7 = a0 => ({\n  \"p-tieredmenu p-component\": true,\n  \"p-tieredmenu-overlay\": a0\n});\nconst _c8 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c9 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nfunction TieredMenu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 0);\n    i0.ɵɵlistener(\"click\", function TieredMenu_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function TieredMenu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function TieredMenu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(2, \"p-tieredMenuSub\", 4, 1);\n    i0.ɵɵlistener(\"itemClick\", function TieredMenu_div_0_Template_p_tieredMenuSub_itemClick_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onItemClick($event));\n    })(\"menuFocus\", function TieredMenu_div_0_Template_p_tieredMenuSub_menuFocus_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMenuFocus($event));\n    })(\"menuBlur\", function TieredMenu_div_0_Template_p_tieredMenuSub_menuBlur_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMenuBlur($event));\n    })(\"menuKeydown\", function TieredMenu_div_0_Template_p_tieredMenuSub_menuKeydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onKeyDown($event));\n    })(\"itemMouseEnter\", function TieredMenu_div_0_Template_p_tieredMenuSub_itemMouseEnter_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"id\", ctx_r1.id)(\"ngClass\", i0.ɵɵpureFunction1(22, _c7, ctx_r1.popup))(\"ngStyle\", ctx_r1.style)(\"@overlayAnimation\", i0.ɵɵpureFunction1(27, _c9, i0.ɵɵpureFunction2(24, _c8, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)))(\"@.disabled\", ctx_r1.popup !== true);\n    i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"tieredmenu\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"root\", true)(\"items\", ctx_r1.processedItems)(\"itemTemplate\", ctx_r1.itemTemplate)(\"menuId\", ctx_r1.id)(\"tabindex\", !ctx_r1.disabled ? ctx_r1.tabindex : -1)(\"ariaLabel\", ctx_r1.ariaLabel)(\"ariaLabelledBy\", ctx_r1.ariaLabelledBy)(\"baseZIndex\", ctx_r1.baseZIndex)(\"autoZIndex\", ctx_r1.autoZIndex)(\"autoDisplay\", ctx_r1.autoDisplay)(\"popup\", ctx_r1.popup)(\"focusedItemId\", ctx_r1.focused ? ctx_r1.focusedItemId : undefined)(\"activeItemPath\", ctx_r1.activeItemPath());\n  }\n}\nclass TieredMenuSub {\n  el;\n  renderer;\n  tieredMenu;\n  items;\n  itemTemplate;\n  root = false;\n  autoDisplay;\n  autoZIndex = true;\n  baseZIndex = 0;\n  popup;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  activeItemPath = input([]);\n  tabindex = 0;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  sublistViewChild;\n  constructor(el, renderer, tieredMenu) {\n    this.el = el;\n    this.renderer = renderer;\n    this.tieredMenu = tieredMenu;\n    effect(() => {\n      const path = this.activeItemPath();\n      if (ObjectUtils.isNotEmpty(path)) {\n        this.positionSubmenu();\n      }\n    });\n  }\n  positionSubmenu() {\n    if (isPlatformBrowser(this.tieredMenu.platformId)) {\n      const sublist = this.sublistViewChild && this.sublistViewChild.nativeElement;\n      if (sublist) {\n        const parentItem = sublist.parentElement.parentElement;\n        const containerOffset = DomHandler.getOffset(parentItem);\n        const viewport = DomHandler.getViewport();\n        const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getOuterWidth(sublist);\n        const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n        const sublistFlippedClass = 'p-submenu-list-flipped';\n        if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n          DomHandler.addClass(sublist, sublistFlippedClass);\n        } else if (DomHandler.hasClass(sublist, sublistFlippedClass)) {\n          DomHandler.removeClass(sublist, sublistFlippedClass);\n        }\n      }\n    }\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item?.id ?? `${this.menuId}_${processedItem.key}`;\n  }\n  getItemKey(processedItem) {\n    return this.getItemId(processedItem);\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem': true,\n      'p-highlight': this.isItemActive(processedItem),\n      'p-menuitem-active': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem-separator': true\n    };\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => {\n      const isItemVisible = this.isItemVisible(processedItem);\n      const isVisibleSeparator = isItemVisible && this.getItemProp(processedItem, 'separator');\n      return !isItemVisible || isVisibleSeparator;\n    }).length + 1;\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    if (this.activeItemPath()) {\n      return this.activeItemPath().some(path => path.key === processedItem.key);\n    }\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  onItemMouseEnter(param) {\n    if (this.autoDisplay) {\n      const {\n        event,\n        processedItem\n      } = param;\n      this.itemMouseEnter.emit({\n        originalEvent: event,\n        processedItem\n      });\n    }\n  }\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  static ɵfac = function TieredMenuSub_Factory(t) {\n    return new (t || TieredMenuSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(forwardRef(() => TieredMenu)));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TieredMenuSub,\n    selectors: [[\"p-tieredMenuSub\"]],\n    viewQuery: function TieredMenuSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sublistViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      root: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"root\", \"root\", booleanAttribute],\n      autoDisplay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoDisplay\", \"autoDisplay\", booleanAttribute],\n      autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      popup: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"popup\", \"popup\", booleanAttribute],\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"level\", \"level\", numberAttribute],\n      focusedItemId: \"focusedItemId\",\n      activeItemPath: [i0.ɵɵInputFlags.SignalBased, \"activeItemPath\"],\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute]\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 3,\n    vars: 12,\n    consts: [[\"sublist\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"role\", \"menu\", 3, \"keydown\", \"focus\", \"blur\", \"ngClass\", \"id\", \"tabindex\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"ngStyle\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", 3, \"ngStyle\", \"ngClass\", \"class\", \"pTooltip\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngStyle\", \"ngClass\"], [\"role\", \"menuitem\", 3, \"ngStyle\", \"ngClass\", \"pTooltip\", \"tooltipOptions\"], [1, \"p-menuitem-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [3, \"items\", \"itemTemplate\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"ariaLabelledBy\", \"level\", \"itemClick\", \"itemMouseEnter\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [3, \"data-pc-section\", \"aria-hidden\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"itemClick\", \"itemMouseEnter\", \"items\", \"itemTemplate\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"ariaLabelledBy\", \"level\"]],\n    template: function TieredMenuSub_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"ul\", 3, 0);\n        i0.ɵɵlistener(\"keydown\", function TieredMenuSub_Template_ul_keydown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuKeydown.emit($event));\n        })(\"focus\", function TieredMenuSub_Template_ul_focus_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuFocus.emit($event));\n        })(\"blur\", function TieredMenuSub_Template_ul_blur_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuBlur.emit($event));\n        });\n        i0.ɵɵtemplate(2, TieredMenuSub_ng_template_2_Template, 2, 2, \"ng-template\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, !ctx.root, ctx.root))(\"id\", ctx.menuId + \"_list\")(\"tabindex\", ctx.tabindex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-labelledBy\", ctx.ariaLabelledBy)(\"aria-activedescendant\", ctx.focusedItemId)(\"aria-orientation\", \"vertical\")(\"data-pc-section\", \"menu\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.items);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, AngleRightIcon, TieredMenuSub],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-tieredMenuSub',\n      template: `\n        <ul\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-tieredmenu-root-list': root }\"\n            [id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    [pTooltip]=\"getItemProp(processedItem, 'tooltip')\"\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, hasSubmenu: getItemProp(processedItem, 'items') }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-tieredMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath()\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [ariaLabelledBy]=\"getItemId(processedItem)\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: TieredMenu,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => TieredMenu)]\n    }]\n  }], {\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoDisplay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    popup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    sublistViewChild: [{\n      type: ViewChild,\n      args: ['sublist', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * TieredMenu displays submenus in nested overlays.\n * @group Components\n */\nclass TieredMenu {\n  document;\n  platformId;\n  el;\n  renderer;\n  cd;\n  config;\n  overlayService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Defines if menu would displayed as a popup.\n   * @group Props\n   */\n  popup;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element.\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Whether to show a root submenu on mouse over.\n   * @defaultValue true\n   * @group Props\n   */\n  autoDisplay = true;\n  /**\n   * Transition options of the show animation.\n   * @group Props\n   */\n  showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Transition options of the hide animation.\n   * @group Props\n   */\n  hideTransitionOptions = '.1s linear';\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @group Props\n   */\n  disabled = false;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = 0;\n  /**\n   * Callback to invoke when overlay menu is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when overlay menu is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  templates;\n  rootmenu;\n  containerViewChild;\n  submenuIconTemplate;\n  itemTemplate;\n  container;\n  outsideClickListener;\n  resizeListener;\n  scrollHandler;\n  target;\n  relatedTarget;\n  visible;\n  relativeAlign;\n  dirty = false;\n  focused = false;\n  activeItemPath = signal([]);\n  number = signal(0);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _model;\n  get visibleItems() {\n    const processedItem = this.activeItemPath().find(p => p.key === this.focusedItemInfo().parentKey);\n    return processedItem ? processedItem.items : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItemInfo = this.focusedItemInfo();\n    return focusedItemInfo.item?.id ? focusedItemInfo.item.id : focusedItemInfo.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItemInfo.parentKey) ? '_' + focusedItemInfo.parentKey : ''}_${focusedItemInfo.index}` : null;\n  }\n  constructor(document, platformId, el, renderer, cd, config, overlayService) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.overlayService = overlayService;\n    effect(() => {\n      const path = this.activeItemPath();\n      if (ObjectUtils.isNotEmpty(path)) {\n        this.bindOutsideClickListener();\n        this.bindResizeListener();\n      } else {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n      }\n    });\n  }\n  ngOnInit() {\n    this.id = this.id || UniqueComponentId();\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'submenuicon':\n          this.submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey\n      };\n      newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return this.activeItemPath().some(p => p.key === processedItem.key);\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item) && this.isItemVisible(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemVisible(item) {\n    return this.getItemProp(item, 'visible') !== false;\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  onOverlayClick(event) {\n    if (this.popup) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n  }\n  onItemClick(event) {\n    const {\n      originalEvent,\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        level,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItemPath.set(this.activeItemPath().filter(p => key !== p.key && key.startsWith(p.key)));\n      this.focusedItemInfo.set({\n        index,\n        level,\n        parentKey,\n        item\n      });\n      this.dirty = true;\n      DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    } else {\n      if (grouped) {\n        this.onItemChange(event);\n      } else {\n        const rootProcessedItem = root ? processedItem : this.activeItemPath().find(p => p.parentKey === '');\n        this.hide(originalEvent);\n        this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n        DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n      }\n    }\n  }\n  onItemMouseEnter(event) {\n    if (!DomHandler.isTouchDevice()) {\n      if (this.autoDisplay) {\n        this.dirty = true;\n      }\n      if (this.dirty) {\n        this.onItemChange(event);\n      }\n    } else {\n      this.onItemChange({\n        event,\n        processedItem: event.processedItem,\n        focus: this.autoDisplay\n      });\n    }\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  onArrowDownKey(event) {\n    const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n    this.changeFocusedItemIndex(event, itemIndex);\n    event.preventDefault();\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const grouped = this.isProccessedItemGroup(processedItem);\n    const item = processedItem?.item;\n    if (grouped) {\n      this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n      this.focusedItemInfo.set({\n        index: -1,\n        parentKey: processedItem.key,\n        item\n      });\n      this.searchValue = '';\n      this.onArrowDownKey(event);\n    }\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    if (event.altKey) {\n      if (this.focusedItemInfo().index !== -1) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        !grouped && this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n      }\n      this.popup && this.hide(event, true);\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = this.activeItemPath().find(p => p.key === processedItem.parentKey);\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    if (!root) {\n      this.focusedItemInfo.set({\n        index: -1,\n        parentKey: parentItem ? parentItem.parentKey : '',\n        item: processedItem.item\n      });\n      this.searchValue = '';\n      this.onArrowDownKey(event);\n    }\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n    this.activeItemPath.set(activeItemPath);\n    event.preventDefault();\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemIndex(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    this.hide(event, true);\n    this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n      if (!this.popup) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        !grouped && (this.focusedItemInfo().index = this.findFirstFocusedItemIndex());\n      }\n    }\n    event.preventDefault();\n  }\n  onItemChange(event) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (ObjectUtils.isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      level,\n      parentKey,\n      items,\n      item\n    } = processedItem;\n    const grouped = ObjectUtils.isNotEmpty(items);\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== parentKey && p.parentKey !== key);\n    grouped && activeItemPath.push(processedItem);\n    this.focusedItemInfo.set({\n      index,\n      level,\n      parentKey,\n      item\n    });\n    this.activeItemPath.set(activeItemPath);\n    grouped && (this.dirty = true);\n    isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    if (this.focusedItemInfo().index === -1 && !this.popup) {\n      // this.onArrowDownKey(event);\n    }\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n    this.dirty = false;\n  }\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (this.popup) {\n          this.container = event.element;\n          this.moveOnTop();\n          this.onShow.emit({});\n          this.appendOverlay();\n          this.alignOverlay();\n          this.bindOutsideClickListener();\n          this.bindResizeListener();\n          this.bindScrollListener();\n          DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n          this.scrollInView();\n        }\n        break;\n      case 'void':\n        this.onOverlayHide();\n        this.onHide.emit({});\n        break;\n    }\n  }\n  alignOverlay() {\n    if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);else DomHandler.absolutePosition(this.container, this.target);\n  }\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        ZIndexUtils.clear(event.element);\n        break;\n    }\n  }\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n  restoreOverlayAppend() {\n    if (this.container && this.appendTo) {\n      this.renderer.appendChild(this.el.nativeElement, this.container);\n    }\n  }\n  moveOnTop() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n  /**\n   * Hides the popup menu.\n   * @group Method\n   */\n  hide(event, isFocus) {\n    if (this.popup) {\n      this.onHide.emit({});\n      this.visible = false;\n    }\n    this.activeItemPath.set([]);\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    });\n    isFocus && DomHandler.focus(this.relatedTarget || this.target || this.rootmenu.sublistViewChild.nativeElement);\n    this.dirty = false;\n  }\n  /**\n   * Toggles the visibility of the popup menu.\n   * @param {Event} event - Browser event.\n   * @group Method\n   */\n  toggle(event) {\n    this.visible ? this.hide(event, true) : this.show(event);\n  }\n  /**\n   * Displays the popup menu.\n   * @param {Event} even - Browser event.\n   * @group Method\n   */\n  show(event, isFocus) {\n    if (this.popup) {\n      this.visible = true;\n      this.target = this.target || event.currentTarget;\n      this.relatedTarget = event.relatedTarget || null;\n      this.relativeAlign = event?.relativeAlign || null;\n    }\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    });\n    isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    this.cd.markForCheck();\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemIndex(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return ObjectUtils.findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  changeFocusedItemIndex(event, index) {\n    if (this.focusedItemInfo().index !== index) {\n      const focusedItemInfo = this.focusedItemInfo();\n      this.focusedItemInfo.set({\n        ...focusedItemInfo,\n        item: this.visibleItems[index].item,\n        index\n      });\n      this.scrollInView();\n    }\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, event => {\n        if (this.visible) {\n          this.hide(event, true);\n        }\n      });\n    }\n    this.scrollHandler.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n      this.scrollHandler = null;\n    }\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          if (!DomHandler.isTouchDevice()) {\n            this.hide(event, true);\n          }\n        });\n      }\n    }\n  }\n  bindOutsideClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.outsideClickListener) {\n        this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isOutsideContainer = this.containerViewChild && !this.containerViewChild.nativeElement.contains(event.target);\n          const isOutsideTarget = this.popup ? !(this.target && (this.target === event.target || this.target.contains(event.target))) : true;\n          if (isOutsideContainer && isOutsideTarget) {\n            this.hide();\n          }\n        });\n      }\n    }\n  }\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      document.removeEventListener('click', this.outsideClickListener);\n      this.outsideClickListener = null;\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n  }\n  onOverlayHide() {\n    this.unbindOutsideClickListener();\n    this.unbindResizeListener();\n    this.unbindScrollListener();\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n  }\n  ngOnDestroy() {\n    if (this.popup) {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.restoreOverlayAppend();\n      this.onOverlayHide();\n    }\n  }\n  static ɵfac = function TieredMenu_Factory(t) {\n    return new (t || TieredMenu)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(i5.OverlayService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TieredMenu,\n    selectors: [[\"p-tieredMenu\"]],\n    contentQueries: function TieredMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function TieredMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      popup: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"popup\", \"popup\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      appendTo: \"appendTo\",\n      autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      autoDisplay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoDisplay\", \"autoDisplay\", booleanAttribute],\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      id: \"id\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute]\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"container\", \"\"], [\"rootmenu\", \"\"], [3, \"id\", \"ngClass\", \"class\", \"ngStyle\", \"click\", 4, \"ngIf\"], [3, \"click\", \"id\", \"ngClass\", \"ngStyle\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"root\", \"items\", \"itemTemplate\", \"menuId\", \"tabindex\", \"ariaLabel\", \"ariaLabelledBy\", \"baseZIndex\", \"autoZIndex\", \"autoDisplay\", \"popup\", \"focusedItemId\", \"activeItemPath\"]],\n    template: function TieredMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, TieredMenu_div_0_Template, 4, 29, \"div\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.popup || ctx.visible);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle, TieredMenuSub],\n    styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-tieredMenu',\n      template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'tieredmenu'\"\n            [id]=\"id\"\n            [ngClass]=\"{ 'p-tieredmenu p-component': true, 'p-tieredmenu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"!popup || visible\"\n        >\n            <p-tieredMenuSub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [autoDisplay]=\"autoDisplay\"\n                [popup]=\"popup\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-tieredMenuSub>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i5.PrimeNGConfig\n  }, {\n    type: i5.OverlayService\n  }], {\n    model: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    autoDisplay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }]\n  });\n})();\nclass TieredMenuModule {\n  static ɵfac = function TieredMenuModule_Factory(t) {\n    return new (t || TieredMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TieredMenuModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule, RouterModule, TooltipModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule],\n      exports: [TieredMenu, RouterModule, TooltipModule, SharedModule],\n      declarations: [TieredMenu, TieredMenuSub]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TieredMenu, TieredMenuModule, TieredMenuSub };", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "input", "EventEmitter", "effect", "forwardRef", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "signal", "PLATFORM_ID", "ChangeDetectionStrategy", "ContentChildren", "NgModule", "i2", "RouterModule", "i5", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "AngleRightIcon", "i3", "RippleModule", "i4", "TooltipModule", "ObjectUtils", "UniqueComponentId", "ZIndexUtils", "_c0", "_c1", "a0", "a1", "_c2", "_c3", "exact", "_c4", "$implicit", "hasSubmenu", "TieredMenuSub_ng_template_2_li_0_Template", "rf", "ctx", "ɵɵelement", "processedItem_r2", "ɵɵnextContext", "ctx_r2", "ɵɵproperty", "getItemProp", "getSeparatorItemClass", "ɵɵattribute", "getItemId", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "getItemLabel", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template", "ɵɵsanitizeHtml", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template", "ɵɵtextInterpolate", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template", "ɵɵtemplate", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "tieredMenu", "submenuIconTemplate", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_Template", "ɵɵtemplateRefExtractor", "htmlLabel_r5", "ɵɵreference", "ɵɵpureFunction1", "ɵɵsanitizeUrl", "isItemGroup", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_Template", "htmlLabel_r6", "ɵɵpureFunction0", "TieredMenuSub_ng_template_2_li_1_ng_container_3_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_4_1_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_4_Template", "itemTemplate", "ɵɵpureFunction2", "item", "TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template", "_r7", "ɵɵgetCurrentView", "ɵɵlistener", "TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemClick_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "itemClick", "emit", "TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemMouseEnter_0_listener", "onItemMouseEnter", "items", "autoDisplay", "menuId", "activeItemPath", "focusedItemId", "level", "TieredMenuSub_ng_template_2_li_1_Template", "_r4", "TieredMenuSub_ng_template_2_li_1_Template_div_click_2_listener", "onItemClick", "TieredMenuSub_ng_template_2_li_1_Template_div_mouseenter_2_listener", "processedItem", "ctx_r7", "index_r9", "index", "ɵɵclassMap", "getItemClass", "isItemActive", "isItemFocused", "isItemDisabled", "undefined", "getAriaSetSize", "getAriaPosInset", "isItemVisible", "TieredMenuSub_ng_template_2_Template", "_c5", "_c6", "_c7", "_c8", "showTransitionParams", "hideTransitionParams", "_c9", "value", "params", "TieredMenu_div_0_Template", "_r1", "TieredMenu_div_0_Template_div_click_0_listener", "ctx_r1", "onOverlayClick", "TieredMenu_div_0_Template_div_animation_overlayAnimation_start_0_listener", "onOverlayAnimationStart", "TieredMenu_div_0_Template_div_animation_overlayAnimation_done_0_listener", "onOverlayAnimationEnd", "TieredMenu_div_0_Template_p_tieredMenuSub_itemClick_2_listener", "TieredMenu_div_0_Template_p_tieredMenuSub_menuFocus_2_listener", "onMenuFocus", "TieredMenu_div_0_Template_p_tieredMenuSub_menuBlur_2_listener", "onMenuBlur", "TieredMenu_div_0_Template_p_tieredMenuSub_menuKeydown_2_listener", "onKeyDown", "TieredMenu_div_0_Template_p_tieredMenuSub_itemMouseEnter_2_listener", "styleClass", "id", "popup", "showTransitionOptions", "hideTransitionOptions", "processedItems", "disabled", "tabindex", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "baseZIndex", "autoZIndex", "focused", "TieredMenuSub", "el", "renderer", "root", "itemMouseEnter", "menuFocus", "menuBlur", "menuKeydown", "sublist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "path", "isNotEmpty", "positionSubmenu", "platformId", "sublist", "nativeElement", "parentItem", "parentElement", "containerOffset", "getOffset", "viewport", "getViewport", "sublist<PERSON><PERSON><PERSON>", "offsetParent", "offsetWidth", "getOuterWidth", "itemOuterWidth", "children", "sublistFlippedClass", "parseInt", "left", "width", "calculateScrollbarWidth", "addClass", "hasClass", "removeClass", "name", "getItemValue", "key", "getItemKey", "filter", "length", "slice", "isVisibleSeparator", "some", "param", "event", "originalEvent", "isFocus", "ɵfac", "TieredMenuSub_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "TieredMenu", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TieredMenuSub_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "SignalBased", "outputs", "features", "ɵɵInputTransformsFeature", "decls", "vars", "consts", "template", "TieredMenuSub_Template", "TieredMenuSub_Template_ul_keydown_0_listener", "TieredMenuSub_Template_ul_focus_0_listener", "TieredMenuSub_Template_ul_blur_0_listener", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "RouterLink", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "host", "class", "decorators", "transform", "static", "document", "cd", "config", "overlayService", "model", "_model", "_processedItems", "createProcessedItems", "appendTo", "onShow", "onHide", "templates", "rootmenu", "containerViewChild", "container", "outsideClickListener", "resizeListener", "<PERSON><PERSON><PERSON><PERSON>", "target", "relatedTarget", "visible", "relativeAlign", "dirty", "number", "focusedItemInfo", "parent<PERSON><PERSON>", "searchValue", "searchTimeout", "visibleItems", "find", "p", "bindOutsideClickListener", "bindResizeListener", "unbindOutsideClickListener", "unbindResizeListener", "ngOnInit", "ngAfterContentInit", "for<PERSON>ach", "getType", "parent", "newItem", "push", "getProccessedItemLabel", "isProcessedItemGroup", "isSelected", "isValidSelectedItem", "isValidItem", "isItemSeparator", "isItemMatched", "toLocaleLowerCase", "startsWith", "isProccessedItemGroup", "add", "grouped", "isEmpty", "selected", "set", "focus", "onItemChange", "rootProcessedItem", "hide", "changeFocusedItemIndex", "isTouchDevice", "metaKey", "ctrl<PERSON>ey", "code", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "isPrintableCharacter", "searchItems", "itemIndex", "findNextItemIndex", "findFirstFocusedItemIndex", "preventDefault", "altKey", "findPrevItemIndex", "findLastFocusedItemIndex", "findFirstItemIndex", "findLastItemIndex", "element", "findSingle", "anchorElement", "click", "toState", "moveOnTop", "appendOverlay", "alignOverlay", "bindScrollListener", "scrollInView", "onOverlayHide", "relativePosition", "absolutePosition", "clear", "append<PERSON><PERSON><PERSON>", "body", "restoreOverlayAppend", "zIndex", "menu", "toggle", "show", "currentTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "char", "matched", "findIndex", "clearTimeout", "setTimeout", "selectedIndex", "findSelectedItemIndex", "findLastIndex", "matchedItemIndex", "scrollIntoView", "block", "inline", "unbindScrollListener", "listen", "defaultView", "isOutsideContainer", "contains", "isOutsideTarget", "removeEventListener", "destroyed", "ngOnDestroy", "destroy", "TieredMenu_Factory", "ChangeDetectorRef", "PrimeNGConfig", "OverlayService", "contentQueries", "TieredMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "TieredMenu_Query", "TieredMenu_Template", "styles", "data", "animation", "opacity", "changeDetection", "animations", "OnPush", "Document", "TieredMenuModule", "TieredMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-tieredmenu.mjs"], "sourcesContent": ["import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { input, EventEmitter, effect, forwardRef, booleanAttribute, numberAttribute, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, signal, PLATFORM_ID, ChangeDetectionStrategy, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\n\nclass TieredMenuSub {\n    el;\n    renderer;\n    tieredMenu;\n    items;\n    itemTemplate;\n    root = false;\n    autoDisplay;\n    autoZIndex = true;\n    baseZIndex = 0;\n    popup;\n    menuId;\n    ariaLabel;\n    ariaLabelledBy;\n    level = 0;\n    focusedItemId;\n    activeItemPath = input([]);\n    tabindex = 0;\n    itemClick = new EventEmitter();\n    itemMouseEnter = new EventEmitter();\n    menuFocus = new EventEmitter();\n    menuBlur = new EventEmitter();\n    menuKeydown = new EventEmitter();\n    sublistViewChild;\n    constructor(el, renderer, tieredMenu) {\n        this.el = el;\n        this.renderer = renderer;\n        this.tieredMenu = tieredMenu;\n        effect(() => {\n            const path = this.activeItemPath();\n            if (ObjectUtils.isNotEmpty(path)) {\n                this.positionSubmenu();\n            }\n        });\n    }\n    positionSubmenu() {\n        if (isPlatformBrowser(this.tieredMenu.platformId)) {\n            const sublist = this.sublistViewChild && this.sublistViewChild.nativeElement;\n            if (sublist) {\n                const parentItem = sublist.parentElement.parentElement;\n                const containerOffset = DomHandler.getOffset(parentItem);\n                const viewport = DomHandler.getViewport();\n                const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getOuterWidth(sublist);\n                const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n                const sublistFlippedClass = 'p-submenu-list-flipped';\n                if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n                    DomHandler.addClass(sublist, sublistFlippedClass);\n                }\n                else if (DomHandler.hasClass(sublist, sublistFlippedClass)) {\n                    DomHandler.removeClass(sublist, sublistFlippedClass);\n                }\n            }\n        }\n    }\n    getItemProp(processedItem, name, params = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n    getItemId(processedItem) {\n        return processedItem.item?.id ?? `${this.menuId}_${processedItem.key}`;\n    }\n    getItemKey(processedItem) {\n        return this.getItemId(processedItem);\n    }\n    getItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-highlight': this.isItemActive(processedItem),\n            'p-menuitem-active': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n    getItemLabel(processedItem) {\n        return this.getItemProp(processedItem, 'label');\n    }\n    getSeparatorItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n    getAriaPosInset(index) {\n        return (index -\n            this.items.slice(0, index).filter((processedItem) => {\n                const isItemVisible = this.isItemVisible(processedItem);\n                const isVisibleSeparator = isItemVisible && this.getItemProp(processedItem, 'separator');\n                return !isItemVisible || isVisibleSeparator;\n            }).length +\n            1);\n    }\n    isItemVisible(processedItem) {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n    isItemActive(processedItem) {\n        if (this.activeItemPath()) {\n            return this.activeItemPath().some((path) => path.key === processedItem.key);\n        }\n    }\n    isItemDisabled(processedItem) {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n    isItemFocused(processedItem) {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n    isItemGroup(processedItem) {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    onItemMouseEnter(param) {\n        if (this.autoDisplay) {\n            const { event, processedItem } = param;\n            this.itemMouseEnter.emit({ originalEvent: event, processedItem });\n        }\n    }\n    onItemClick(event, processedItem) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenuSub, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: forwardRef(() => TieredMenu) }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.1.0\", version: \"18.0.1\", type: TieredMenuSub, selector: \"p-tieredMenuSub\", inputs: { items: { classPropertyName: \"items\", publicName: \"items\", isSignal: false, isRequired: false, transformFunction: null }, itemTemplate: { classPropertyName: \"itemTemplate\", publicName: \"itemTemplate\", isSignal: false, isRequired: false, transformFunction: null }, root: { classPropertyName: \"root\", publicName: \"root\", isSignal: false, isRequired: false, transformFunction: booleanAttribute }, autoDisplay: { classPropertyName: \"autoDisplay\", publicName: \"autoDisplay\", isSignal: false, isRequired: false, transformFunction: booleanAttribute }, autoZIndex: { classPropertyName: \"autoZIndex\", publicName: \"autoZIndex\", isSignal: false, isRequired: false, transformFunction: booleanAttribute }, baseZIndex: { classPropertyName: \"baseZIndex\", publicName: \"baseZIndex\", isSignal: false, isRequired: false, transformFunction: numberAttribute }, popup: { classPropertyName: \"popup\", publicName: \"popup\", isSignal: false, isRequired: false, transformFunction: booleanAttribute }, menuId: { classPropertyName: \"menuId\", publicName: \"menuId\", isSignal: false, isRequired: false, transformFunction: null }, ariaLabel: { classPropertyName: \"ariaLabel\", publicName: \"ariaLabel\", isSignal: false, isRequired: false, transformFunction: null }, ariaLabelledBy: { classPropertyName: \"ariaLabelledBy\", publicName: \"ariaLabelledBy\", isSignal: false, isRequired: false, transformFunction: null }, level: { classPropertyName: \"level\", publicName: \"level\", isSignal: false, isRequired: false, transformFunction: numberAttribute }, focusedItemId: { classPropertyName: \"focusedItemId\", publicName: \"focusedItemId\", isSignal: false, isRequired: false, transformFunction: null }, activeItemPath: { classPropertyName: \"activeItemPath\", publicName: \"activeItemPath\", isSignal: true, isRequired: false, transformFunction: null }, tabindex: { classPropertyName: \"tabindex\", publicName: \"tabindex\", isSignal: false, isRequired: false, transformFunction: numberAttribute } }, outputs: { itemClick: \"itemClick\", itemMouseEnter: \"itemMouseEnter\", menuFocus: \"menuFocus\", menuBlur: \"menuBlur\", menuKeydown: \"menuKeydown\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"sublistViewChild\", first: true, predicate: [\"sublist\"], descendants: true, static: true }], ngImport: i0, template: `\n        <ul\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-tieredmenu-root-list': root }\"\n            [id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    [pTooltip]=\"getItemProp(processedItem, 'tooltip')\"\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, hasSubmenu: getItemProp(processedItem, 'items') }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-tieredMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath()\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [ariaLabelledBy]=\"getItemId(processedItem)\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLink), selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLinkActive), selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"directive\", type: i0.forwardRef(() => i4.Tooltip), selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"autoHide\", \"fitContent\", \"hideOnEscape\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: i0.forwardRef(() => AngleRightIcon), selector: \"AngleRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => TieredMenuSub), selector: \"p-tieredMenuSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoDisplay\", \"autoZIndex\", \"baseZIndex\", \"popup\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\", \"tabindex\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenuSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-tieredMenuSub',\n                    template: `\n        <ul\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-tieredmenu-root-list': root }\"\n            [id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    [pTooltip]=\"getItemProp(processedItem, 'tooltip')\"\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, hasSubmenu: getItemProp(processedItem, 'items') }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-tieredMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath()\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [ariaLabelledBy]=\"getItemId(processedItem)\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: TieredMenu, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => TieredMenu)]\n                }] }], propDecorators: { items: [{\n                type: Input\n            }], itemTemplate: [{\n                type: Input\n            }], root: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoDisplay: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoZIndex: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], baseZIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], popup: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], menuId: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], level: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], focusedItemId: [{\n                type: Input\n            }], tabindex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], itemClick: [{\n                type: Output\n            }], itemMouseEnter: [{\n                type: Output\n            }], menuFocus: [{\n                type: Output\n            }], menuBlur: [{\n                type: Output\n            }], menuKeydown: [{\n                type: Output\n            }], sublistViewChild: [{\n                type: ViewChild,\n                args: ['sublist', { static: true }]\n            }] } });\n/**\n * TieredMenu displays submenus in nested overlays.\n * @group Components\n */\nclass TieredMenu {\n    document;\n    platformId;\n    el;\n    renderer;\n    cd;\n    config;\n    overlayService;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    set model(value) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model() {\n        return this._model;\n    }\n    /**\n     * Defines if menu would displayed as a popup.\n     * @group Props\n     */\n    popup;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element.\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Whether to show a root submenu on mouse over.\n     * @defaultValue true\n     * @group Props\n     */\n    autoDisplay = true;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '.1s linear';\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    disabled = false;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * Callback to invoke when overlay menu is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when overlay menu is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    templates;\n    rootmenu;\n    containerViewChild;\n    submenuIconTemplate;\n    itemTemplate;\n    container;\n    outsideClickListener;\n    resizeListener;\n    scrollHandler;\n    target;\n    relatedTarget;\n    visible;\n    relativeAlign;\n    dirty = false;\n    focused = false;\n    activeItemPath = signal([]);\n    number = signal(0);\n    focusedItemInfo = signal({ index: -1, level: 0, parentKey: '', item: null });\n    searchValue = '';\n    searchTimeout;\n    _processedItems;\n    _model;\n    get visibleItems() {\n        const processedItem = this.activeItemPath().find((p) => p.key === this.focusedItemInfo().parentKey);\n        return processedItem ? processedItem.items : this.processedItems;\n    }\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n    get focusedItemId() {\n        const focusedItemInfo = this.focusedItemInfo();\n        return focusedItemInfo.item?.id ? focusedItemInfo.item.id : focusedItemInfo.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItemInfo.parentKey) ? '_' + focusedItemInfo.parentKey : ''}_${focusedItemInfo.index}` : null;\n    }\n    constructor(document, platformId, el, renderer, cd, config, overlayService) {\n        this.document = document;\n        this.platformId = platformId;\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.overlayService = overlayService;\n        effect(() => {\n            const path = this.activeItemPath();\n            if (ObjectUtils.isNotEmpty(path)) {\n                this.bindOutsideClickListener();\n                this.bindResizeListener();\n            }\n            else {\n                this.unbindOutsideClickListener();\n                this.unbindResizeListener();\n            }\n        });\n    }\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n        const processedItems = [];\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n        return processedItems;\n    }\n    getItemProp(item, name) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    getProccessedItemLabel(processedItem) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n    getItemLabel(item) {\n        return this.getItemProp(item, 'label');\n    }\n    isProcessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    isSelected(processedItem) {\n        return this.activeItemPath().some((p) => p.key === processedItem.key);\n    }\n    isValidSelectedItem(processedItem) {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n    isValidItem(processedItem) {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item) && this.isItemVisible(processedItem.item);\n    }\n    isItemDisabled(item) {\n        return this.getItemProp(item, 'disabled');\n    }\n    isItemVisible(item) {\n        return this.getItemProp(item, 'visible') !== false;\n    }\n    isItemSeparator(item) {\n        return this.getItemProp(item, 'separator');\n    }\n    isItemMatched(processedItem) {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n    isProccessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    onOverlayClick(event) {\n        if (this.popup) {\n            this.overlayService.add({\n                originalEvent: event,\n                target: this.el.nativeElement\n            });\n        }\n    }\n    onItemClick(event) {\n        const { originalEvent, processedItem } = event;\n        const grouped = this.isProcessedItemGroup(processedItem);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        const selected = this.isSelected(processedItem);\n        if (selected) {\n            const { index, key, level, parentKey, item } = processedItem;\n            this.activeItemPath.set(this.activeItemPath().filter((p) => key !== p.key && key.startsWith(p.key)));\n            this.focusedItemInfo.set({ index, level, parentKey, item });\n            this.dirty = true;\n            DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n        }\n        else {\n            if (grouped) {\n                this.onItemChange(event);\n            }\n            else {\n                const rootProcessedItem = root ? processedItem : this.activeItemPath().find((p) => p.parentKey === '');\n                this.hide(originalEvent);\n                this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n                DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n            }\n        }\n    }\n    onItemMouseEnter(event) {\n        if (!DomHandler.isTouchDevice()) {\n            if (this.autoDisplay) {\n                this.dirty = true;\n            }\n            if (this.dirty) {\n                this.onItemChange(event);\n            }\n        }\n        else {\n            this.onItemChange({ event, processedItem: event.processedItem, focus: this.autoDisplay });\n        }\n    }\n    onKeyDown(event) {\n        const metaKey = event.metaKey || event.ctrlKey;\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n            case 'End':\n                this.onEndKey(event);\n                break;\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n                break;\n        }\n    }\n    onArrowDownKey(event) {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n        event.preventDefault();\n    }\n    onArrowRightKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        const item = processedItem?.item;\n        if (grouped) {\n            this.onItemChange({ originalEvent: event, processedItem });\n            this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item });\n            this.searchValue = '';\n            this.onArrowDownKey(event);\n        }\n        event.preventDefault();\n    }\n    onArrowUpKey(event) {\n        if (event.altKey) {\n            if (this.focusedItemInfo().index !== -1) {\n                const processedItem = this.visibleItems[this.focusedItemInfo().index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n                !grouped && this.onItemChange({ originalEvent: event, processedItem });\n            }\n            this.popup && this.hide(event, true);\n            event.preventDefault();\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onArrowLeftKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = this.activeItemPath().find((p) => p.key === processedItem.parentKey);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        if (!root) {\n            this.focusedItemInfo.set({ index: -1, parentKey: parentItem ? parentItem.parentKey : '', item: processedItem.item });\n            this.searchValue = '';\n            this.onArrowDownKey(event);\n        }\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n        this.activeItemPath.set(activeItemPath);\n        event.preventDefault();\n    }\n    onHomeKey(event) {\n        this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n    onEndKey(event) {\n        this.changeFocusedItemIndex(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n    onSpaceKey(event) {\n        this.onEnterKey(event);\n    }\n    onEscapeKey(event) {\n        this.hide(event, true);\n        this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n        event.preventDefault();\n    }\n    onTabKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n            !grouped && this.onItemChange({ originalEvent: event, processedItem });\n        }\n        this.hide();\n    }\n    onEnterKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n            anchorElement ? anchorElement.click() : element && element.click();\n            if (!this.popup) {\n                const processedItem = this.visibleItems[this.focusedItemInfo().index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n                !grouped && (this.focusedItemInfo().index = this.findFirstFocusedItemIndex());\n            }\n        }\n        event.preventDefault();\n    }\n    onItemChange(event) {\n        const { processedItem, isFocus } = event;\n        if (ObjectUtils.isEmpty(processedItem))\n            return;\n        const { index, key, level, parentKey, items, item } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n        grouped && activeItemPath.push(processedItem);\n        this.focusedItemInfo.set({ index, level, parentKey, item });\n        this.activeItemPath.set(activeItemPath);\n        grouped && (this.dirty = true);\n        isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    }\n    onMenuFocus(event) {\n        this.focused = true;\n        if (this.focusedItemInfo().index === -1 && !this.popup) {\n            // this.onArrowDownKey(event);\n        }\n    }\n    onMenuBlur(event) {\n        this.focused = false;\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        this.searchValue = '';\n        this.dirty = false;\n    }\n    onOverlayAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                if (this.popup) {\n                    this.container = event.element;\n                    this.moveOnTop();\n                    this.onShow.emit({});\n                    this.appendOverlay();\n                    this.alignOverlay();\n                    this.bindOutsideClickListener();\n                    this.bindResizeListener();\n                    this.bindScrollListener();\n                    DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n                    this.scrollInView();\n                }\n                break;\n            case 'void':\n                this.onOverlayHide();\n                this.onHide.emit({});\n                break;\n        }\n    }\n    alignOverlay() {\n        if (this.relativeAlign)\n            DomHandler.relativePosition(this.container, this.target);\n        else\n            DomHandler.absolutePosition(this.container, this.target);\n    }\n    onOverlayAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(event.element);\n                break;\n        }\n    }\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                this.renderer.appendChild(this.document.body, this.container);\n            else\n                DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n    restoreOverlayAppend() {\n        if (this.container && this.appendTo) {\n            this.renderer.appendChild(this.el.nativeElement, this.container);\n        }\n    }\n    moveOnTop() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n    /**\n     * Hides the popup menu.\n     * @group Method\n     */\n    hide(event, isFocus) {\n        if (this.popup) {\n            this.onHide.emit({});\n            this.visible = false;\n        }\n        this.activeItemPath.set([]);\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '' });\n        isFocus && DomHandler.focus(this.relatedTarget || this.target || this.rootmenu.sublistViewChild.nativeElement);\n        this.dirty = false;\n    }\n    /**\n     * Toggles the visibility of the popup menu.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    toggle(event) {\n        this.visible ? this.hide(event, true) : this.show(event);\n    }\n    /**\n     * Displays the popup menu.\n     * @param {Event} even - Browser event.\n     * @group Method\n     */\n    show(event, isFocus) {\n        if (this.popup) {\n            this.visible = true;\n            this.target = this.target || event.currentTarget;\n            this.relatedTarget = event.relatedTarget || null;\n            this.relativeAlign = event?.relativeAlign || null;\n        }\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '' });\n        isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n        this.cd.markForCheck();\n    }\n    searchItems(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n        let itemIndex = -1;\n        let matched = false;\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        }\n        else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n        if (itemIndex !== -1) {\n            this.changeFocusedItemIndex(event, itemIndex);\n        }\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n        return matched;\n    }\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n    findPrevItemIndex(index) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n    findNextItemIndex(index) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n    changeFocusedItemIndex(event, index) {\n        if (this.focusedItemInfo().index !== index) {\n            const focusedItemInfo = this.focusedItemInfo();\n            this.focusedItemInfo.set({ ...focusedItemInfo, item: this.visibleItems[index].item, index });\n            this.scrollInView();\n        }\n    }\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, (event) => {\n                if (this.visible) {\n                    this.hide(event, true);\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n            this.scrollHandler = null;\n        }\n    }\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    if (!DomHandler.isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n                });\n            }\n        }\n    }\n    bindOutsideClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    const isOutsideContainer = this.containerViewChild && !this.containerViewChild.nativeElement.contains(event.target);\n                    const isOutsideTarget = this.popup ? !(this.target && (this.target === event.target || this.target.contains(event.target))) : true;\n                    if (isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    }\n                });\n            }\n        }\n    }\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            document.removeEventListener('click', this.outsideClickListener);\n            this.outsideClickListener = null;\n        }\n    }\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n    onOverlayHide() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n        this.unbindScrollListener();\n        if (!this.cd.destroyed) {\n            this.target = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.popup) {\n            if (this.scrollHandler) {\n                this.scrollHandler.destroy();\n                this.scrollHandler = null;\n            }\n            if (this.container && this.autoZIndex) {\n                ZIndexUtils.clear(this.container);\n            }\n            this.restoreOverlayAppend();\n            this.onOverlayHide();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenu, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i5.PrimeNGConfig }, { token: i5.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"18.0.1\", type: TieredMenu, selector: \"p-tieredMenu\", inputs: { model: \"model\", popup: [\"popup\", \"popup\", booleanAttribute], style: \"style\", styleClass: \"styleClass\", appendTo: \"appendTo\", autoZIndex: [\"autoZIndex\", \"autoZIndex\", booleanAttribute], baseZIndex: [\"baseZIndex\", \"baseZIndex\", numberAttribute], autoDisplay: [\"autoDisplay\", \"autoDisplay\", booleanAttribute], showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", id: \"id\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\", disabled: [\"disabled\", \"disabled\", booleanAttribute], tabindex: [\"tabindex\", \"tabindex\", numberAttribute] }, outputs: { onShow: \"onShow\", onHide: \"onHide\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"rootmenu\", first: true, predicate: [\"rootmenu\"], descendants: true }, { propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }], ngImport: i0, template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'tieredmenu'\"\n            [id]=\"id\"\n            [ngClass]=\"{ 'p-tieredmenu p-component': true, 'p-tieredmenu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"!popup || visible\"\n        >\n            <p-tieredMenuSub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [autoDisplay]=\"autoDisplay\"\n                [popup]=\"popup\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-tieredMenuSub>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: TieredMenuSub, selector: \"p-tieredMenuSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoDisplay\", \"autoZIndex\", \"baseZIndex\", \"popup\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\", \"tabindex\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-tieredMenu', template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'tieredmenu'\"\n            [id]=\"id\"\n            [ngClass]=\"{ 'p-tieredmenu p-component': true, 'p-tieredmenu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"!popup || visible\"\n        >\n            <p-tieredMenuSub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [autoDisplay]=\"autoDisplay\"\n                [popup]=\"popup\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-tieredMenuSub>\n        </div>\n    `, animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i5.PrimeNGConfig }, { type: i5.OverlayService }], propDecorators: { model: [{\n                type: Input\n            }], popup: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], baseZIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], autoDisplay: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabindex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], rootmenu: [{\n                type: ViewChild,\n                args: ['rootmenu']\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container']\n            }] } });\nclass TieredMenuModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenuModule, declarations: [TieredMenu, TieredMenuSub], imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule], exports: [TieredMenu, RouterModule, TooltipModule, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenuModule, imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule, RouterModule, TooltipModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: TieredMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule],\n                    exports: [TieredMenu, RouterModule, TooltipModule, SharedModule],\n                    declarations: [TieredMenu, TieredMenuSub]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TieredMenu, TieredMenuModule, TieredMenuSub };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AACzE,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACnP,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,EAAEC,6BAA6B,QAAQ,aAAa;AACvE,SAASC,cAAc,QAAQ,0BAA0B;AACzD,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,eAAe;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA,kBAAAD,EAAA;EAAA,0BAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAF,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAG,GAAA,GAAAA,CAAA;EAAAC,KAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAL,EAAA,EAAAC,EAAA;EAAAK,SAAA,EAAAN,EAAA;EAAAO,UAAA,EAAAN;AAAA;AAAA,SAAAO,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2HiB5C,EAAE,CAAA8C,SAAA,WAyB1E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAzBuE/C,EAAE,CAAAgD,aAAA,GAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAqB7B,CAAC,YAAAE,MAAA,CAAAG,qBAAA,CAAAL,gBAAA,CACA,CAAC;IAtByB/C,EAAE,CAAAqD,WAAA,OAAAJ,MAAA,CAAAK,SAAA,CAAAP,gBAAA;EAAA;AAAA;AAAA,SAAAQ,oEAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAA8C,SAAA,cAmEzD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAnEsD/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA8Dd,CAAC,YAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cACI,CAAC;IA/DM/C,EAAE,CAAAqD,WAAA;EAAA;AAAA;AAAA,SAAAG,oEAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAyD,cAAA,cAoE4D,CAAC;IApE/DzD,EAAE,CAAA0D,MAAA,EAsEhE,CAAC;IAtE6D1D,EAAE,CAAA2D,YAAA,CAsEzD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GAtEsD/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAqD,WAAA;IAAFrD,EAAE,CAAA4D,SAAA,CAsEhE,CAAC;IAtE6D5D,EAAE,CAAA6D,kBAAA,MAAAZ,MAAA,CAAAa,YAAA,CAAAf,gBAAA,MAsEhE,CAAC;EAAA;AAAA;AAAA,SAAAgB,2EAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtE6D5C,EAAE,CAAA8C,SAAA,cAwEoD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAxEvD/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,cAAAD,MAAA,CAAAa,YAAA,CAAAf,gBAAA,GAAF/C,EAAE,CAAAgE,cAwEW,CAAC;IAxEdhE,EAAE,CAAAqD,WAAA;EAAA;AAAA;AAAA,SAAAY,oEAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAyD,cAAA,cA0EqE,CAAC;IA1ExEzD,EAAE,CAAA0D,MAAA,EA0E8G,CAAC;IA1EjH1D,EAAE,CAAA2D,YAAA,CA0EqH,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GA1ExH/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,oBA0EoE,CAAC;IA1EvE/C,EAAE,CAAA4D,SAAA,CA0E8G,CAAC;IA1EjH5D,EAAE,CAAAkE,iBAAA,CAAAjB,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UA0E8G,CAAC;EAAA;AAAA;AAAA,SAAAoB,6FAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1EjH5C,EAAE,CAAA8C,SAAA,wBA6E+F,CAAC;EAAA;EAAA,IAAAF,EAAA;IA7ElG5C,EAAE,CAAAkD,UAAA,+BA6E2B,CAAC;IA7E9BlD,EAAE,CAAAqD,WAAA;EAAA;AAAA;AAAA,SAAAe,4FAAAxB,EAAA,EAAAC,GAAA;AAAA,SAAAwB,8EAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAsE,UAAA,IAAAF,2FAAA,yBA8EqE,CAAC;EAAA;EAAA,IAAAxB,EAAA;IA9ExE5C,EAAE,CAAAkD,UAAA,iCA8E0C,CAAC,oBAAyB,CAAC;EAAA;AAAA;AAAA,SAAAqB,4EAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9EvE5C,EAAE,CAAAwE,uBAAA,EA4Ef,CAAC;IA5EYxE,EAAE,CAAAsE,UAAA,IAAAH,4FAAA,4BA6E+F,CAAC,IAAAE,6EAAA,gBAC3B,CAAC;IA9ExErE,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAK,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAA4D,SAAA,CA6EN,CAAC;IA7EG5D,EAAE,CAAAkD,UAAA,UAAAD,MAAA,CAAAyB,UAAA,CAAAC,mBA6EN,CAAC;IA7EG3E,EAAE,CAAA4D,SAAA,CA8EE,CAAC;IA9EL5D,EAAE,CAAAkD,UAAA,qBAAAD,MAAA,CAAAyB,UAAA,CAAAC,mBA8EE,CAAC;EAAA;AAAA;AAAA,SAAAC,6DAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9EL5C,EAAE,CAAAyD,cAAA,WA0DnE,CAAC;IA1DgEzD,EAAE,CAAAsE,UAAA,IAAAf,mEAAA,kBAkE/D,CAAC,IAAAC,mEAAA,kBAE0H,CAAC,IAAAO,0EAAA,gCApE/D/D,EAAE,CAAA6E,sBAuExC,CAAC,IAAAZ,mEAAA,kBAG4G,CAAC,IAAAM,2EAAA,0BAErF,CAAC;IA5EYvE,EAAE,CAAA2D,YAAA,CAgFhE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAkC,YAAA,GAhF6D9E,EAAE,CAAA+E,WAAA;IAAA,MAAAhC,gBAAA,GAAF/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,WAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAsDjB,CAAC,YAtDc/C,EAAE,CAAAgF,eAAA,KAAA3C,GAAA,EAAAY,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAuD6B,CAAC;IAvDhC/C,EAAE,CAAAqD,WAAA,SAAAJ,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAAF/C,EAAE,CAAAiF,aAAA,uBAAAhC,MAAA,CAAAE,WAAA,CAAAJ,gBAAA;IAAF/C,EAAE,CAAA4D,SAAA,CA4DnB,CAAC;IA5DgB5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA4DnB,CAAC;IA5DgB/C,EAAE,CAAA4D,SAAA,CAoEb,CAAC;IApEU5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAoEb,CAAC,aAAA+B,YAAa,CAAC;IApEJ9E,EAAE,CAAA4D,SAAA,EA0ES,CAAC;IA1EZ5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UA0ES,CAAC;IA1EZ/C,EAAE,CAAA4D,SAAA,CA4EjB,CAAC;IA5Ec5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAiC,WAAA,CAAAnC,gBAAA,CA4EjB,CAAC;EAAA;AAAA;AAAA,SAAAoC,oEAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5Ec5C,EAAE,CAAA8C,SAAA,cA6GzD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GA7GsD/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SAuGd,CAAC,YAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cACI,CAAC;IAxGM/C,EAAE,CAAAqD,WAAA;EAAA;AAAA;AAAA,SAAA+B,oEAAAxC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAyD,cAAA,cA8G4D,CAAC;IA9G/DzD,EAAE,CAAA0D,MAAA,EAgHhE,CAAC;IAhH6D1D,EAAE,CAAA2D,YAAA,CAgHzD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GAhHsD/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAqD,WAAA;IAAFrD,EAAE,CAAA4D,SAAA,CAgHhE,CAAC;IAhH6D5D,EAAE,CAAA6D,kBAAA,MAAAZ,MAAA,CAAAa,YAAA,CAAAf,gBAAA,MAgHhE,CAAC;EAAA;AAAA;AAAA,SAAAsC,2EAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhH6D5C,EAAE,CAAA8C,SAAA,cAkHoD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAlHvD/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,cAAAD,MAAA,CAAAa,YAAA,CAAAf,gBAAA,GAAF/C,EAAE,CAAAgE,cAkHW,CAAC;IAlHdhE,EAAE,CAAAqD,WAAA;EAAA;AAAA;AAAA,SAAAiC,oEAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAyD,cAAA,cAoHqE,CAAC;IApHxEzD,EAAE,CAAA0D,MAAA,EAoH8G,CAAC;IApHjH1D,EAAE,CAAA2D,YAAA,CAoHqH,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GApHxH/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,oBAoHoE,CAAC;IApHvE/C,EAAE,CAAA4D,SAAA,CAoH8G,CAAC;IApHjH5D,EAAE,CAAAkE,iBAAA,CAAAjB,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAoH8G,CAAC;EAAA;AAAA;AAAA,SAAAwC,6FAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApHjH5C,EAAE,CAAA8C,SAAA,wBAuH+F,CAAC;EAAA;EAAA,IAAAF,EAAA;IAvHlG5C,EAAE,CAAAkD,UAAA,+BAuH2B,CAAC;IAvH9BlD,EAAE,CAAAqD,WAAA;EAAA;AAAA;AAAA,SAAAmC,4FAAA5C,EAAA,EAAAC,GAAA;AAAA,SAAA4C,8EAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAsE,UAAA,IAAAkB,2FAAA,yBAwHqE,CAAC;EAAA;EAAA,IAAA5C,EAAA;IAxHxE5C,EAAE,CAAAkD,UAAA,iCAwH0C,CAAC,oBAAyB,CAAC;EAAA;AAAA;AAAA,SAAAwC,4EAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxHvE5C,EAAE,CAAAwE,uBAAA,EAsHf,CAAC;IAtHYxE,EAAE,CAAAsE,UAAA,IAAAiB,4FAAA,4BAuH+F,CAAC,IAAAE,6EAAA,gBAC3B,CAAC;IAxHxEzF,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAK,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAA4D,SAAA,CAuHN,CAAC;IAvHG5D,EAAE,CAAAkD,UAAA,UAAAD,MAAA,CAAAyB,UAAA,CAAAC,mBAuHN,CAAC;IAvHG3E,EAAE,CAAA4D,SAAA,CAwHE,CAAC;IAxHL5D,EAAE,CAAAkD,UAAA,qBAAAD,MAAA,CAAAyB,UAAA,CAAAC,mBAwHE,CAAC;EAAA;AAAA;AAAA,SAAAgB,6DAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxHL5C,EAAE,CAAAyD,cAAA,WAmGnE,CAAC;IAnGgEzD,EAAE,CAAAsE,UAAA,IAAAa,mEAAA,kBA4G/D,CAAC,IAAAC,mEAAA,kBAE0H,CAAC,IAAAC,0EAAA,gCA9G/DrF,EAAE,CAAA6E,sBAiHxC,CAAC,IAAAS,mEAAA,kBAG4G,CAAC,IAAAI,2EAAA,0BAErF,CAAC;IAtHY1F,EAAE,CAAA2D,YAAA,CA0HhE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgD,YAAA,GA1H6D5F,EAAE,CAAA+E,WAAA;IAAA,MAAAhC,gBAAA,GAAF/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,eAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAmFT,CAAC,gBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,gBAIC,CAAC,6CACb,CAAC,4BAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,gCAxFgB/C,EAAE,CAAA6F,eAAA,KAAAvD,GAAA,CAyFqC,CAAC,WAAAW,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WACvD,CAAC,YA1Fc/C,EAAE,CAAAgF,eAAA,KAAA3C,GAAA,EAAAY,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cA2F6B,CAAC,aAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,aAC3C,CAAC,wBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,wBACqB,CAAC,qBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,qBACP,CAAC,uBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,uBACG,CAAC,eAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eACjB,CAAC,UAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UACX,CAAC;IAjGgB/C,EAAE,CAAAqD,WAAA,sBAAAJ,MAAA,CAAAE,WAAA,CAAAJ,gBAAA;IAAF/C,EAAE,CAAA4D,SAAA,CAqGnB,CAAC;IArGgB5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SAqGnB,CAAC;IArGgB/C,EAAE,CAAA4D,SAAA,CA8Gb,CAAC;IA9GU5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WA8Gb,CAAC,aAAA6C,YAAa,CAAC;IA9GJ5F,EAAE,CAAA4D,SAAA,EAoHS,CAAC;IApHZ5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAoHS,CAAC;IApHZ/C,EAAE,CAAA4D,SAAA,CAsHjB,CAAC;IAtHc5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAiC,WAAA,CAAAnC,gBAAA,CAsHjB,CAAC;EAAA;AAAA;AAAA,SAAA+C,yDAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtHc5C,EAAE,CAAAwE,uBAAA,EAgDpC,CAAC;IAhDiCxE,EAAE,CAAAsE,UAAA,IAAAM,4DAAA,gBA0DnE,CAAC,IAAAe,4DAAA,gBAyCD,CAAC;IAnGgE3F,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAG,gBAAA,GAAF/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAA4D,SAAA,CAkDhB,CAAC;IAlDa5D,EAAE,CAAAkD,UAAA,UAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAkDhB,CAAC;IAlDa/C,EAAE,CAAA4D,SAAA,CAkFjB,CAAC;IAlFc5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAkFjB,CAAC;EAAA;AAAA;AAAA,SAAAgD,yEAAAnD,EAAA,EAAAC,GAAA;AAAA,SAAAmD,2DAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlFc5C,EAAE,CAAAsE,UAAA,IAAAyB,wEAAA,qBA6HuE,CAAC;EAAA;AAAA;AAAA,SAAAE,yDAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7H1E5C,EAAE,CAAAwE,uBAAA,EA4HrC,CAAC;IA5HkCxE,EAAE,CAAAsE,UAAA,IAAA0B,0DAAA,gBA6HuE,CAAC;IA7H1EhG,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAG,gBAAA,GAAF/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAA4D,SAAA,CA6HtB,CAAC;IA7HmB5D,EAAE,CAAAkD,UAAA,qBAAAD,MAAA,CAAAiD,YA6HtB,CAAC,4BA7HmBlG,EAAE,CAAAmG,eAAA,IAAA3D,GAAA,EAAAO,gBAAA,CAAAqD,IAAA,EAAAnD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WA6HqE,CAAC;EAAA;AAAA;AAAA,SAAAsD,4DAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0D,GAAA,GA7HxEtG,EAAE,CAAAuG,gBAAA;IAAFvG,EAAE,CAAAyD,cAAA,yBA6I3E,CAAC;IA7IwEzD,EAAE,CAAAwG,UAAA,uBAAAC,iGAAAC,MAAA;MAAF1G,EAAE,CAAA2G,aAAA,CAAAL,GAAA;MAAA,MAAArD,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CA2I1D3D,MAAA,CAAA4D,SAAA,CAAAC,IAAA,CAAAJ,MAAqB,CAAC;IAAA,EAAC,4BAAAK,sGAAAL,MAAA;MA3IiC1G,EAAE,CAAA2G,aAAA,CAAAL,GAAA;MAAA,MAAArD,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CA4IrD3D,MAAA,CAAA+D,gBAAA,CAAAN,MAAuB,CAAC;IAAA,EAAC;IA5I0B1G,EAAE,CAAA2D,YAAA,CA6IzD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GA7IsD/C,EAAE,CAAAgD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,UAAAH,gBAAA,CAAAkE,KAmI3C,CAAC,iBAAAhE,MAAA,CAAAiD,YACD,CAAC,gBAAAjD,MAAA,CAAAiE,WACH,CAAC,WAAAjE,MAAA,CAAAkE,MACX,CAAC,mBAAAlE,MAAA,CAAAmE,cAAA,EACiB,CAAC,kBAAAnE,MAAA,CAAAoE,aACL,CAAC,mBAAApE,MAAA,CAAAK,SAAA,CAAAP,gBAAA,CACW,CAAC,UAAAE,MAAA,CAAAqE,KAAA,IACzB,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4E,GAAA,GA1IkDxH,EAAE,CAAAuG,gBAAA;IAAFvG,EAAE,CAAAyD,cAAA,cA8C/E,CAAC,YAC6K,CAAC;IA/ClGzD,EAAE,CAAAwG,UAAA,mBAAAiB,+DAAAf,MAAA;MAAF1G,EAAE,CAAA2G,aAAA,CAAAa,GAAA;MAAA,MAAAzE,gBAAA,GAAF/C,EAAE,CAAAgD,aAAA,GAAAP,SAAA;MAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CA+CC3D,MAAA,CAAAyE,WAAA,CAAAhB,MAAA,EAAA3D,gBAAiC,CAAC;IAAA,EAAC,wBAAA4E,oEAAAjB,MAAA;MA/CtC1G,EAAE,CAAA2G,aAAA,CAAAa,GAAA;MAAA,MAAAzE,gBAAA,GAAF/C,EAAE,CAAAgD,aAAA,GAAAP,SAAA;MAAA,MAAAQ,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CA+CmD3D,MAAA,CAAA+D,gBAAA,CAAiB;QAAAN,MAAA,EAAAA,MAAA;QAAAkB,aAAA,EAAA7E;MAAwB,CAAC,CAAC;IAAA,EAAC;IA/CjG/C,EAAE,CAAAsE,UAAA,IAAAwB,wDAAA,0BAgDpC,CAAC,IAAAG,wDAAA,0BA4EF,CAAC;IA5HkCjG,EAAE,CAAA2D,YAAA,CA+HtE,CAAC;IA/HmE3D,EAAE,CAAAsE,UAAA,IAAA+B,2DAAA,6BA6I3E,CAAC;IA7IwErG,EAAE,CAAA2D,YAAA,CA8I3E,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAiF,MAAA,GA9IwE7H,EAAE,CAAAgD,aAAA;IAAA,MAAAD,gBAAA,GAAA8E,MAAA,CAAApF,SAAA;IAAA,MAAAqF,QAAA,GAAAD,MAAA,CAAAE,KAAA;IAAA,MAAA9E,MAAA,GAAFjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAgI,UAAA,CAAA/E,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eA2C1B,CAAC;IA3CuB/C,EAAE,CAAAkD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAyC7B,CAAC,YAAAE,MAAA,CAAAgF,YAAA,CAAAlF,gBAAA,CACT,CAAC,aAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,YAEU,CAAC,mBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,mBACY,CAAC;IA7CU/C,EAAE,CAAAqD,WAAA,OAAAJ,MAAA,CAAAK,SAAA,CAAAP,gBAAA,sDAAAE,MAAA,CAAAiF,YAAA,CAAAnF,gBAAA,qBAAAE,MAAA,CAAAkF,aAAA,CAAApF,gBAAA,sBAAAE,MAAA,CAAAmF,cAAA,CAAArF,gBAAA,iBAAAE,MAAA,CAAAa,YAAA,CAAAf,gBAAA,oBAAAE,MAAA,CAAAmF,cAAA,CAAArF,gBAAA,KAAAsF,SAAA,mBAAApF,MAAA,CAAAiC,WAAA,CAAAnC,gBAAA,MAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,mBAAAsF,SAAA,mBAAApF,MAAA,CAAAiC,WAAA,CAAAnC,gBAAA,IAAAE,MAAA,CAAAiF,YAAA,CAAAnF,gBAAA,IAAAsF,SAAA,kBAAApF,MAAA,CAAAqF,cAAA,qBAAArF,MAAA,CAAAsF,eAAA,CAAAT,QAAA;IAAF9H,EAAE,CAAA4D,SAAA,EA+CrC,CAAC;IA/CkC5D,EAAE,CAAAqD,WAAA;IAAFrD,EAAE,CAAA4D,SAAA,CAgDtC,CAAC;IAhDmC5D,EAAE,CAAAkD,UAAA,UAAAD,MAAA,CAAAiD,YAgDtC,CAAC;IAhDmClG,EAAE,CAAA4D,SAAA,CA4HvC,CAAC;IA5HoC5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAiD,YA4HvC,CAAC;IA5HoClG,EAAE,CAAA4D,SAAA,CAkIP,CAAC;IAlII5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAuF,aAAA,CAAAzF,gBAAA,KAAAE,MAAA,CAAAiC,WAAA,CAAAnC,gBAAA,CAkIP,CAAC;EAAA;AAAA;AAAA,SAAA0F,qCAAA7F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlII5C,EAAE,CAAAsE,UAAA,IAAA3B,yCAAA,eAyB/E,CAAC,IAAA4E,yCAAA,gBAqBD,CAAC;EAAA;EAAA,IAAA3E,EAAA;IAAA,MAAAG,gBAAA,GAAAF,GAAA,CAAAJ,SAAA;IAAA,MAAAQ,MAAA,GA9C4EjD,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAuF,aAAA,CAAAzF,gBAAA,KAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAmBE,CAAC;IAnBL/C,EAAE,CAAA4D,SAAA,CA4BG,CAAC;IA5BN5D,EAAE,CAAAkD,UAAA,SAAAD,MAAA,CAAAuF,aAAA,CAAAzF,gBAAA,MAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cA4BG,CAAC;EAAA;AAAA;AAAA,MAAA2F,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAzG,EAAA;EAAA;EAAA,wBAAAA;AAAA;AAAA,MAAA0G,GAAA,GAAAA,CAAA1G,EAAA,EAAAC,EAAA;EAAA0G,oBAAA,EAAA3G,EAAA;EAAA4G,oBAAA,EAAA3G;AAAA;AAAA,MAAA4G,GAAA,GAAA7G,EAAA;EAAA8G,KAAA;EAAAC,MAAA,EAAA/G;AAAA;AAAA,SAAAgH,0BAAAvG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwG,GAAA,GA5BNpJ,EAAE,CAAAuG,gBAAA;IAAFvG,EAAE,CAAAyD,cAAA,eAugCvF,CAAC;IAvgCoFzD,EAAE,CAAAwG,UAAA,mBAAA6C,+CAAA3C,MAAA;MAAF1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CAigC1E0C,MAAA,CAAAC,cAAA,CAAA7C,MAAqB,CAAC;IAAA,EAAC,qCAAA8C,0EAAA9C,MAAA;MAjgCiD1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CAogCxD0C,MAAA,CAAAG,uBAAA,CAAA/C,MAA8B,CAAC;IAAA,EAAC,oCAAAgD,yEAAAhD,MAAA;MApgCsB1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CAqgCzD0C,MAAA,CAAAK,qBAAA,CAAAjD,MAA4B,CAAC;IAAA,EAAC;IArgCyB1G,EAAE,CAAAyD,cAAA,2BA4hCnF,CAAC;IA5hCgFzD,EAAE,CAAAwG,UAAA,uBAAAoD,+DAAAlD,MAAA;MAAF1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CAuhClE0C,MAAA,CAAA5B,WAAA,CAAAhB,MAAkB,CAAC;IAAA,EAAC,uBAAAmD,+DAAAnD,MAAA;MAvhC4C1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CAwhClE0C,MAAA,CAAAQ,WAAA,CAAApD,MAAkB,CAAC;IAAA,EAAC,sBAAAqD,8DAAArD,MAAA;MAxhC4C1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CAyhCnE0C,MAAA,CAAAU,UAAA,CAAAtD,MAAiB,CAAC;IAAA,EAAC,yBAAAuD,iEAAAvD,MAAA;MAzhC8C1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CA0hChE0C,MAAA,CAAAY,SAAA,CAAAxD,MAAgB,CAAC;IAAA,EAAC,4BAAAyD,oEAAAzD,MAAA;MA1hC4C1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;MAAA,MAAAE,MAAA,GAAFtJ,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAA4G,WAAA,CA2hC7D0C,MAAA,CAAAtC,gBAAA,CAAAN,MAAuB,CAAC;IAAA,EAAC;IA3hCkC1G,EAAE,CAAA2D,YAAA,CA4hCjE,CAAC,CAClB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA0G,MAAA,GA7hC+EtJ,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAgI,UAAA,CAAAsB,MAAA,CAAAc,UA+/BhE,CAAC;IA//B6DpK,EAAE,CAAAkD,UAAA,OAAAoG,MAAA,CAAAe,EA6/B3E,CAAC,YA7/BwErK,EAAE,CAAAgF,eAAA,KAAA4D,GAAA,EAAAU,MAAA,CAAAgB,KAAA,CA8/BL,CAAC,YAAAhB,MAAA,CAAA5J,KAE/D,CAAC,sBAhgCgEM,EAAE,CAAAgF,eAAA,KAAAgE,GAAA,EAAFhJ,EAAE,CAAAmG,eAAA,KAAA0C,GAAA,EAAAS,MAAA,CAAAiB,qBAAA,EAAAjB,MAAA,CAAAkB,qBAAA,EAkgC4D,CAAC,eAAAlB,MAAA,CAAAgB,KAAA,SACpH,CAAC;IAngCoDtK,EAAE,CAAAqD,WAAA;IAAFrD,EAAE,CAAA4D,SAAA,EA0gCnE,CAAC;IA1gCgE5D,EAAE,CAAAkD,UAAA,aA0gCnE,CAAC,UAAAoG,MAAA,CAAAmB,cACU,CAAC,iBAAAnB,MAAA,CAAApD,YACI,CAAC,WAAAoD,MAAA,CAAAe,EACjB,CAAC,cAAAf,MAAA,CAAAoB,QAAA,GAAApB,MAAA,CAAAqB,QAAA,KACwB,CAAC,cAAArB,MAAA,CAAAsB,SAChB,CAAC,mBAAAtB,MAAA,CAAAuB,cACS,CAAC,eAAAvB,MAAA,CAAAwB,UACT,CAAC,eAAAxB,MAAA,CAAAyB,UACD,CAAC,gBAAAzB,MAAA,CAAApC,WACC,CAAC,UAAAoC,MAAA,CAAAgB,KACb,CAAC,kBAAAhB,MAAA,CAAA0B,OAAA,GAAA1B,MAAA,CAAAjC,aAAA,GAAAgB,SACqC,CAAC,mBAAAiB,MAAA,CAAAlC,cAAA,EACnB,CAAC;EAAA;AAAA;AA/oCnD,MAAM6D,aAAa,CAAC;EAChBC,EAAE;EACFC,QAAQ;EACRzG,UAAU;EACVuC,KAAK;EACLf,YAAY;EACZkF,IAAI,GAAG,KAAK;EACZlE,WAAW;EACX6D,UAAU,GAAG,IAAI;EACjBD,UAAU,GAAG,CAAC;EACdR,KAAK;EACLnD,MAAM;EACNyD,SAAS;EACTC,cAAc;EACdvD,KAAK,GAAG,CAAC;EACTD,aAAa;EACbD,cAAc,GAAGnH,KAAK,CAAC,EAAE,CAAC;EAC1B0K,QAAQ,GAAG,CAAC;EACZ9D,SAAS,GAAG,IAAI3G,YAAY,CAAC,CAAC;EAC9BmL,cAAc,GAAG,IAAInL,YAAY,CAAC,CAAC;EACnCoL,SAAS,GAAG,IAAIpL,YAAY,CAAC,CAAC;EAC9BqL,QAAQ,GAAG,IAAIrL,YAAY,CAAC,CAAC;EAC7BsL,WAAW,GAAG,IAAItL,YAAY,CAAC,CAAC;EAChCuL,gBAAgB;EAChBC,WAAWA,CAACR,EAAE,EAAEC,QAAQ,EAAEzG,UAAU,EAAE;IAClC,IAAI,CAACwG,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACzG,UAAU,GAAGA,UAAU;IAC5BvE,MAAM,CAAC,MAAM;MACT,MAAMwL,IAAI,GAAG,IAAI,CAACvE,cAAc,CAAC,CAAC;MAClC,IAAItF,WAAW,CAAC8J,UAAU,CAACD,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACE,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACAA,eAAeA,CAAA,EAAG;IACd,IAAIhM,iBAAiB,CAAC,IAAI,CAAC6E,UAAU,CAACoH,UAAU,CAAC,EAAE;MAC/C,MAAMC,OAAO,GAAG,IAAI,CAACN,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACO,aAAa;MAC5E,IAAID,OAAO,EAAE;QACT,MAAME,UAAU,GAAGF,OAAO,CAACG,aAAa,CAACA,aAAa;QACtD,MAAMC,eAAe,GAAG5K,UAAU,CAAC6K,SAAS,CAACH,UAAU,CAAC;QACxD,MAAMI,QAAQ,GAAG9K,UAAU,CAAC+K,WAAW,CAAC,CAAC;QACzC,MAAMC,YAAY,GAAGR,OAAO,CAACS,YAAY,GAAGT,OAAO,CAACU,WAAW,GAAGlL,UAAU,CAACmL,aAAa,CAACX,OAAO,CAAC;QACnG,MAAMY,cAAc,GAAGpL,UAAU,CAACmL,aAAa,CAACT,UAAU,CAACW,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvE,MAAMC,mBAAmB,GAAG,wBAAwB;QACpD,IAAIC,QAAQ,CAACX,eAAe,CAACY,IAAI,EAAE,EAAE,CAAC,GAAGJ,cAAc,GAAGJ,YAAY,GAAGF,QAAQ,CAACW,KAAK,GAAGzL,UAAU,CAAC0L,uBAAuB,CAAC,CAAC,EAAE;UAC5H1L,UAAU,CAAC2L,QAAQ,CAACnB,OAAO,EAAEc,mBAAmB,CAAC;QACrD,CAAC,MACI,IAAItL,UAAU,CAAC4L,QAAQ,CAACpB,OAAO,EAAEc,mBAAmB,CAAC,EAAE;UACxDtL,UAAU,CAAC6L,WAAW,CAACrB,OAAO,EAAEc,mBAAmB,CAAC;QACxD;MACJ;IACJ;EACJ;EACA1J,WAAWA,CAACyE,aAAa,EAAEyF,IAAI,EAAEnE,MAAM,GAAG,IAAI,EAAE;IAC5C,OAAOtB,aAAa,IAAIA,aAAa,CAACxB,IAAI,GAAGtE,WAAW,CAACwL,YAAY,CAAC1F,aAAa,CAACxB,IAAI,CAACiH,IAAI,CAAC,EAAEnE,MAAM,CAAC,GAAGb,SAAS;EACvH;EACA/E,SAASA,CAACsE,aAAa,EAAE;IACrB,OAAOA,aAAa,CAACxB,IAAI,EAAEiE,EAAE,IAAI,GAAG,IAAI,CAAClD,MAAM,IAAIS,aAAa,CAAC2F,GAAG,EAAE;EAC1E;EACAC,UAAUA,CAAC5F,aAAa,EAAE;IACtB,OAAO,IAAI,CAACtE,SAAS,CAACsE,aAAa,CAAC;EACxC;EACAK,YAAYA,CAACL,aAAa,EAAE;IACxB,OAAO;MACH,GAAG,IAAI,CAACzE,WAAW,CAACyE,aAAa,EAAE,OAAO,CAAC;MAC3C,YAAY,EAAE,IAAI;MAClB,aAAa,EAAE,IAAI,CAACM,YAAY,CAACN,aAAa,CAAC;MAC/C,mBAAmB,EAAE,IAAI,CAACM,YAAY,CAACN,aAAa,CAAC;MACrD,SAAS,EAAE,IAAI,CAACO,aAAa,CAACP,aAAa,CAAC;MAC5C,YAAY,EAAE,IAAI,CAACQ,cAAc,CAACR,aAAa;IACnD,CAAC;EACL;EACA9D,YAAYA,CAAC8D,aAAa,EAAE;IACxB,OAAO,IAAI,CAACzE,WAAW,CAACyE,aAAa,EAAE,OAAO,CAAC;EACnD;EACAxE,qBAAqBA,CAACwE,aAAa,EAAE;IACjC,OAAO;MACH,GAAG,IAAI,CAACzE,WAAW,CAACyE,aAAa,EAAE,OAAO,CAAC;MAC3C,sBAAsB,EAAE;IAC5B,CAAC;EACL;EACAU,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrB,KAAK,CAACwG,MAAM,CAAE7F,aAAa,IAAK,IAAI,CAACY,aAAa,CAACZ,aAAa,CAAC,IAAI,CAAC,IAAI,CAACzE,WAAW,CAACyE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC8F,MAAM;EAC1I;EACAnF,eAAeA,CAACR,KAAK,EAAE;IACnB,OAAQA,KAAK,GACT,IAAI,CAACd,KAAK,CAAC0G,KAAK,CAAC,CAAC,EAAE5F,KAAK,CAAC,CAAC0F,MAAM,CAAE7F,aAAa,IAAK;MACjD,MAAMY,aAAa,GAAG,IAAI,CAACA,aAAa,CAACZ,aAAa,CAAC;MACvD,MAAMgG,kBAAkB,GAAGpF,aAAa,IAAI,IAAI,CAACrF,WAAW,CAACyE,aAAa,EAAE,WAAW,CAAC;MACxF,OAAO,CAACY,aAAa,IAAIoF,kBAAkB;IAC/C,CAAC,CAAC,CAACF,MAAM,GACT,CAAC;EACT;EACAlF,aAAaA,CAACZ,aAAa,EAAE;IACzB,OAAO,IAAI,CAACzE,WAAW,CAACyE,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK;EAC/D;EACAM,YAAYA,CAACN,aAAa,EAAE;IACxB,IAAI,IAAI,CAACR,cAAc,CAAC,CAAC,EAAE;MACvB,OAAO,IAAI,CAACA,cAAc,CAAC,CAAC,CAACyG,IAAI,CAAElC,IAAI,IAAKA,IAAI,CAAC4B,GAAG,KAAK3F,aAAa,CAAC2F,GAAG,CAAC;IAC/E;EACJ;EACAnF,cAAcA,CAACR,aAAa,EAAE;IAC1B,OAAO,IAAI,CAACzE,WAAW,CAACyE,aAAa,EAAE,UAAU,CAAC;EACtD;EACAO,aAAaA,CAACP,aAAa,EAAE;IACzB,OAAO,IAAI,CAACP,aAAa,KAAK,IAAI,CAAC/D,SAAS,CAACsE,aAAa,CAAC;EAC/D;EACA1C,WAAWA,CAAC0C,aAAa,EAAE;IACvB,OAAO9F,WAAW,CAAC8J,UAAU,CAAChE,aAAa,CAACX,KAAK,CAAC;EACtD;EACAD,gBAAgBA,CAAC8G,KAAK,EAAE;IACpB,IAAI,IAAI,CAAC5G,WAAW,EAAE;MAClB,MAAM;QAAE6G,KAAK;QAAEnG;MAAc,CAAC,GAAGkG,KAAK;MACtC,IAAI,CAACzC,cAAc,CAACvE,IAAI,CAAC;QAAEkH,aAAa,EAAED,KAAK;QAAEnG;MAAc,CAAC,CAAC;IACrE;EACJ;EACAF,WAAWA,CAACqG,KAAK,EAAEnG,aAAa,EAAE;IAC9B,IAAI,CAACzE,WAAW,CAACyE,aAAa,EAAE,SAAS,EAAE;MAAEoG,aAAa,EAAED,KAAK;MAAE3H,IAAI,EAAEwB,aAAa,CAACxB;IAAK,CAAC,CAAC;IAC9F,IAAI,CAACS,SAAS,CAACC,IAAI,CAAC;MAAEkH,aAAa,EAAED,KAAK;MAAEnG,aAAa;MAAEqG,OAAO,EAAE;IAAK,CAAC,CAAC;EAC/E;EACA,OAAOC,IAAI,YAAAC,sBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFnD,aAAa,EAAvBjL,EAAE,CAAAqO,iBAAA,CAAuCrO,EAAE,CAACsO,UAAU,GAAtDtO,EAAE,CAAAqO,iBAAA,CAAiErO,EAAE,CAACuO,SAAS,GAA/EvO,EAAE,CAAAqO,iBAAA,CAA0FjO,UAAU,CAAC,MAAMoO,UAAU,CAAC;EAAA;EACjN,OAAOC,IAAI,kBAD8EzO,EAAE,CAAA0O,iBAAA;IAAAC,IAAA,EACJ1D,aAAa;IAAA2D,SAAA;IAAAC,SAAA,WAAAC,oBAAAlM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADX5C,EAAE,CAAA+O,WAAA,CAAA9M,GAAA;MAAA;MAAA,IAAAW,EAAA;QAAA,IAAAoM,EAAA;QAAFhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAArM,GAAA,CAAA4I,gBAAA,GAAAuD,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAApI,KAAA;MAAAf,YAAA;MAAAkF,IAAA,GAAFpL,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,kBACualP,gBAAgB;MAAA6G,WAAA,GADzblH,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,gCAC8jBlP,gBAAgB;MAAA0K,UAAA,GADhlB/K,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,8BACktBlP,gBAAgB;MAAAyK,UAAA,GADpuB9K,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,8BACs2BjP,eAAe;MAAAgK,KAAA,GADv3BtK,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,oBAC0+BlP,gBAAgB;MAAA8G,MAAA;MAAAyD,SAAA;MAAAC,cAAA;MAAAvD,KAAA,GAD5/BtH,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,oBACogDjP,eAAe;MAAA+G,aAAA;MAAAD,cAAA,GADrhDpH,EAAE,CAAAsP,YAAA,CAAAE,WAAA;MAAA7E,QAAA,GAAF3K,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,0BACq7DjP,eAAe;IAAA;IAAAmP,OAAA;MAAA5I,SAAA;MAAAwE,cAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,WAAA;IAAA;IAAAkE,QAAA,GADt8D1P,EAAE,CAAA2P,wBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAApN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAwG,GAAA,GAAFpJ,EAAE,CAAAuG,gBAAA;QAAFvG,EAAE,CAAAyD,cAAA,cAgBvF,CAAC;QAhBoFzD,EAAE,CAAAwG,UAAA,qBAAAyJ,6CAAAvJ,MAAA;UAAF1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;UAAA,OAAFpJ,EAAE,CAAA4G,WAAA,CAaxE/D,GAAA,CAAA2I,WAAA,CAAA1E,IAAA,CAAAJ,MAAuB,CAAC;QAAA,EAAC,mBAAAwJ,2CAAAxJ,MAAA;UAb6C1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;UAAA,OAAFpJ,EAAE,CAAA4G,WAAA,CAc1E/D,GAAA,CAAAyI,SAAA,CAAAxE,IAAA,CAAAJ,MAAqB,CAAC;QAAA,EAAC,kBAAAyJ,0CAAAzJ,MAAA;UAdiD1G,EAAE,CAAA2G,aAAA,CAAAyC,GAAA;UAAA,OAAFpJ,EAAE,CAAA4G,WAAA,CAe3E/D,GAAA,CAAA0I,QAAA,CAAAzE,IAAA,CAAAJ,MAAoB,CAAC;QAAA,EAAC;QAfmD1G,EAAE,CAAAsE,UAAA,IAAAmE,oCAAA,wBAiBX,CAAC;QAjBQzI,EAAE,CAAA2D,YAAA,CAgJnF,CAAC;MAAA;MAAA,IAAAf,EAAA;QAhJgF5C,EAAE,CAAAkD,UAAA,YAAFlD,EAAE,CAAAmG,eAAA,IAAAjE,GAAA,GAAAW,GAAA,CAAAuI,IAAA,EAAAvI,GAAA,CAAAuI,IAAA,CAKb,CAAC,OAAAvI,GAAA,CAAAsE,MAAA,UACjD,CAAC,aAAAtE,GAAA,CAAA8H,QACH,CAAC;QAP4D3K,EAAE,CAAAqD,WAAA,eAAAR,GAAA,CAAA+H,SAAA,qBAAA/H,GAAA,CAAAgI,cAAA,2BAAAhI,GAAA,CAAAwE,aAAA;QAAFrH,EAAE,CAAA4D,SAAA,EAiB9B,CAAC;QAjB2B5D,EAAE,CAAAkD,UAAA,YAAAL,GAAA,CAAAoE,KAiB9B,CAAC;MAAA;IAAA;IAAAmJ,YAAA,EAAAA,CAAA,MAgImBxQ,EAAE,CAACyQ,OAAO,EAAyGzQ,EAAE,CAAC0Q,OAAO,EAAwI1Q,EAAE,CAAC2Q,IAAI,EAAkH3Q,EAAE,CAAC4Q,gBAAgB,EAAyK5Q,EAAE,CAAC6Q,OAAO,EAAgGvP,EAAE,CAACwP,UAAU,EAAyPxP,EAAE,CAACyP,gBAAgB,EAAmOjP,EAAE,CAACkP,MAAM,EAA2EhP,EAAE,CAACiP,OAAO,EAAkWpP,cAAc,EAAgFwJ,aAAa;IAAA6F,aAAA;EAAA;AAChyD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnJ6F/Q,EAAE,CAAAgR,iBAAA,CAmJJ/F,aAAa,EAAc,CAAC;IAC3G0D,IAAI,EAAEpO,SAAS;IACf0Q,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BnB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACee,aAAa,EAAEtQ,iBAAiB,CAAC2Q,IAAI;MACrCC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1C,IAAI,EAAE3O,EAAE,CAACsO;EAAW,CAAC,EAAE;IAAEK,IAAI,EAAE3O,EAAE,CAACuO;EAAU,CAAC,EAAE;IAAEI,IAAI,EAAEH,UAAU;IAAE8C,UAAU,EAAE,CAAC;MACjG3C,IAAI,EAAElO,MAAM;MACZwQ,IAAI,EAAE,CAAC7Q,UAAU,CAAC,MAAMoO,UAAU,CAAC;IACvC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEvH,KAAK,EAAE,CAAC;MACjC0H,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEwF,YAAY,EAAE,CAAC;MACfyI,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAE0K,IAAI,EAAE,CAAC;MACPuD,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6G,WAAW,EAAE,CAAC;MACdyH,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0K,UAAU,EAAE,CAAC;MACb4D,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyK,UAAU,EAAE,CAAC;MACb6D,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEjR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEgK,KAAK,EAAE,CAAC;MACRqE,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8G,MAAM,EAAE,CAAC;MACTwH,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEkK,SAAS,EAAE,CAAC;MACZ+D,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEmK,cAAc,EAAE,CAAC;MACjB8D,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAE4G,KAAK,EAAE,CAAC;MACRqH,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEjR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE+G,aAAa,EAAE,CAAC;MAChBsH,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEiK,QAAQ,EAAE,CAAC;MACXgE,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEjR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEuG,SAAS,EAAE,CAAC;MACZ8H,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAE0K,cAAc,EAAE,CAAC;MACjBsD,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAE2K,SAAS,EAAE,CAAC;MACZqD,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAE4K,QAAQ,EAAE,CAAC;MACXoD,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAE6K,WAAW,EAAE,CAAC;MACdmD,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAE8K,gBAAgB,EAAE,CAAC;MACnBkD,IAAI,EAAE/N,SAAS;MACfqQ,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEO,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMhD,UAAU,CAAC;EACbiD,QAAQ;EACR3F,UAAU;EACVZ,EAAE;EACFC,QAAQ;EACRuG,EAAE;EACFC,MAAM;EACNC,cAAc;EACd;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAC5I,KAAK,EAAE;IACb,IAAI,CAAC6I,MAAM,GAAG7I,KAAK;IACnB,IAAI,CAAC8I,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACF,MAAM,IAAI,EAAE,CAAC;EACvE;EACA,IAAID,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIxH,KAAK;EACL;AACJ;AACA;AACA;EACI5K,KAAK;EACL;AACJ;AACA;AACA;EACI0K,UAAU;EACV;AACJ;AACA;AACA;EACI6H,QAAQ;EACR;AACJ;AACA;AACA;EACIlH,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACID,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;AACA;EACI5D,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACIqD,qBAAqB,GAAG,iCAAiC;EACzD;AACJ;AACA;AACA;EACIC,qBAAqB,GAAG,YAAY;EACpC;AACJ;AACA;AACA;EACIH,EAAE;EACF;AACJ;AACA;AACA;EACIO,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;EACIH,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,CAAC;EACZ;AACJ;AACA;AACA;EACIuH,MAAM,GAAG,IAAIhS,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACIiS,MAAM,GAAG,IAAIjS,YAAY,CAAC,CAAC;EAC3BkS,SAAS;EACTC,QAAQ;EACRC,kBAAkB;EAClB3N,mBAAmB;EACnBuB,YAAY;EACZqM,SAAS;EACTC,oBAAoB;EACpBC,cAAc;EACdC,aAAa;EACbC,MAAM;EACNC,aAAa;EACbC,OAAO;EACPC,aAAa;EACbC,KAAK,GAAG,KAAK;EACb/H,OAAO,GAAG,KAAK;EACf5D,cAAc,GAAGvG,MAAM,CAAC,EAAE,CAAC;EAC3BmS,MAAM,GAAGnS,MAAM,CAAC,CAAC,CAAC;EAClBoS,eAAe,GAAGpS,MAAM,CAAC;IAAEkH,KAAK,EAAE,CAAC,CAAC;IAAET,KAAK,EAAE,CAAC;IAAE4L,SAAS,EAAE,EAAE;IAAE9M,IAAI,EAAE;EAAK,CAAC,CAAC;EAC5E+M,WAAW,GAAG,EAAE;EAChBC,aAAa;EACbrB,eAAe;EACfD,MAAM;EACN,IAAIuB,YAAYA,CAAA,EAAG;IACf,MAAMzL,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAACkM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAChG,GAAG,KAAK,IAAI,CAAC0F,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;IACnG,OAAOtL,aAAa,GAAGA,aAAa,CAACX,KAAK,GAAG,IAAI,CAACwD,cAAc;EACpE;EACA,IAAIA,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACsH,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACrE,MAAM,EAAE;MACvD,IAAI,CAACqE,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACH,KAAK,IAAI,EAAE,CAAC;IACtE;IACA,OAAO,IAAI,CAACE,eAAe;EAC/B;EACA,IAAI1K,aAAaA,CAAA,EAAG;IAChB,MAAM4L,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;IAC9C,OAAOA,eAAe,CAAC7M,IAAI,EAAEiE,EAAE,GAAG4I,eAAe,CAAC7M,IAAI,CAACiE,EAAE,GAAG4I,eAAe,CAAClL,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAACsC,EAAE,GAAGvI,WAAW,CAAC8J,UAAU,CAACqH,eAAe,CAACC,SAAS,CAAC,GAAG,GAAG,GAAGD,eAAe,CAACC,SAAS,GAAG,EAAE,IAAID,eAAe,CAAClL,KAAK,EAAE,GAAG,IAAI;EACtO;EACA2D,WAAWA,CAAC+F,QAAQ,EAAE3F,UAAU,EAAEZ,EAAE,EAAEC,QAAQ,EAAEuG,EAAE,EAAEC,MAAM,EAAEC,cAAc,EAAE;IACxE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC3F,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACZ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuG,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpCzR,MAAM,CAAC,MAAM;MACT,MAAMwL,IAAI,GAAG,IAAI,CAACvE,cAAc,CAAC,CAAC;MAClC,IAAItF,WAAW,CAAC8J,UAAU,CAACD,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC6H,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACvJ,EAAE,GAAG,IAAI,CAACA,EAAE,IAAItI,iBAAiB,CAAC,CAAC;EAC5C;EACA8R,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACzB,SAAS,EAAE0B,OAAO,CAAE1N,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAAC2N,OAAO,CAAC,CAAC;QAClB,KAAK,aAAa;UACd,IAAI,CAACpP,mBAAmB,GAAGyB,IAAI,CAAC2J,QAAQ;UACxC;QACJ,KAAK,MAAM;UACP,IAAI,CAAC7J,YAAY,GAAGE,IAAI,CAAC2J,QAAQ;UACjC;QACJ;UACI,IAAI,CAAC7J,YAAY,GAAGE,IAAI,CAAC2J,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAiC,oBAAoBA,CAAC/K,KAAK,EAAEK,KAAK,GAAG,CAAC,EAAE0M,MAAM,GAAG,CAAC,CAAC,EAAEd,SAAS,GAAG,EAAE,EAAE;IAChE,MAAMzI,cAAc,GAAG,EAAE;IACzBxD,KAAK,IACDA,KAAK,CAAC6M,OAAO,CAAC,CAAC1N,IAAI,EAAE2B,KAAK,KAAK;MAC3B,MAAMwF,GAAG,GAAG,CAAC2F,SAAS,KAAK,EAAE,GAAGA,SAAS,GAAG,GAAG,GAAG,EAAE,IAAInL,KAAK;MAC7D,MAAMkM,OAAO,GAAG;QACZ7N,IAAI;QACJ2B,KAAK;QACLT,KAAK;QACLiG,GAAG;QACHyG,MAAM;QACNd;MACJ,CAAC;MACDe,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAACjC,oBAAoB,CAAC5L,IAAI,CAACa,KAAK,EAAEK,KAAK,GAAG,CAAC,EAAE2M,OAAO,EAAE1G,GAAG,CAAC;MACjF9C,cAAc,CAACyJ,IAAI,CAACD,OAAO,CAAC;IAChC,CAAC,CAAC;IACN,OAAOxJ,cAAc;EACzB;EACAtH,WAAWA,CAACiD,IAAI,EAAEiH,IAAI,EAAE;IACpB,OAAOjH,IAAI,GAAGtE,WAAW,CAACwL,YAAY,CAAClH,IAAI,CAACiH,IAAI,CAAC,CAAC,GAAGhF,SAAS;EAClE;EACA8L,sBAAsBA,CAACvM,aAAa,EAAE;IAClC,OAAOA,aAAa,GAAG,IAAI,CAAC9D,YAAY,CAAC8D,aAAa,CAACxB,IAAI,CAAC,GAAGiC,SAAS;EAC5E;EACAvE,YAAYA,CAACsC,IAAI,EAAE;IACf,OAAO,IAAI,CAACjD,WAAW,CAACiD,IAAI,EAAE,OAAO,CAAC;EAC1C;EACAgO,oBAAoBA,CAACxM,aAAa,EAAE;IAChC,OAAOA,aAAa,IAAI9F,WAAW,CAAC8J,UAAU,CAAChE,aAAa,CAACX,KAAK,CAAC;EACvE;EACAoN,UAAUA,CAACzM,aAAa,EAAE;IACtB,OAAO,IAAI,CAACR,cAAc,CAAC,CAAC,CAACyG,IAAI,CAAE0F,CAAC,IAAKA,CAAC,CAAChG,GAAG,KAAK3F,aAAa,CAAC2F,GAAG,CAAC;EACzE;EACA+G,mBAAmBA,CAAC1M,aAAa,EAAE;IAC/B,OAAO,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,IAAI,IAAI,CAACyM,UAAU,CAACzM,aAAa,CAAC;EAC5E;EACA2M,WAAWA,CAAC3M,aAAa,EAAE;IACvB,OAAO,CAAC,CAACA,aAAa,IAAI,CAAC,IAAI,CAACQ,cAAc,CAACR,aAAa,CAACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAACoO,eAAe,CAAC5M,aAAa,CAACxB,IAAI,CAAC,IAAI,IAAI,CAACoC,aAAa,CAACZ,aAAa,CAACxB,IAAI,CAAC;EAC7J;EACAgC,cAAcA,CAAChC,IAAI,EAAE;IACjB,OAAO,IAAI,CAACjD,WAAW,CAACiD,IAAI,EAAE,UAAU,CAAC;EAC7C;EACAoC,aAAaA,CAACpC,IAAI,EAAE;IAChB,OAAO,IAAI,CAACjD,WAAW,CAACiD,IAAI,EAAE,SAAS,CAAC,KAAK,KAAK;EACtD;EACAoO,eAAeA,CAACpO,IAAI,EAAE;IAClB,OAAO,IAAI,CAACjD,WAAW,CAACiD,IAAI,EAAE,WAAW,CAAC;EAC9C;EACAqO,aAAaA,CAAC7M,aAAa,EAAE;IACzB,OAAO,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,IAAI,IAAI,CAACuM,sBAAsB,CAACvM,aAAa,CAAC,CAAC8M,iBAAiB,CAAC,CAAC,CAACC,UAAU,CAAC,IAAI,CAACxB,WAAW,CAACuB,iBAAiB,CAAC,CAAC,CAAC;EAC7J;EACAE,qBAAqBA,CAAChN,aAAa,EAAE;IACjC,OAAOA,aAAa,IAAI9F,WAAW,CAAC8J,UAAU,CAAChE,aAAa,CAACX,KAAK,CAAC;EACvE;EACAsC,cAAcA,CAACwE,KAAK,EAAE;IAClB,IAAI,IAAI,CAACzD,KAAK,EAAE;MACZ,IAAI,CAACsH,cAAc,CAACiD,GAAG,CAAC;QACpB7G,aAAa,EAAED,KAAK;QACpB4E,MAAM,EAAE,IAAI,CAACzH,EAAE,CAACc;MACpB,CAAC,CAAC;IACN;EACJ;EACAtE,WAAWA,CAACqG,KAAK,EAAE;IACf,MAAM;MAAEC,aAAa;MAAEpG;IAAc,CAAC,GAAGmG,KAAK;IAC9C,MAAM+G,OAAO,GAAG,IAAI,CAACV,oBAAoB,CAACxM,aAAa,CAAC;IACxD,MAAMwD,IAAI,GAAGtJ,WAAW,CAACiT,OAAO,CAACnN,aAAa,CAACoM,MAAM,CAAC;IACtD,MAAMgB,QAAQ,GAAG,IAAI,CAACX,UAAU,CAACzM,aAAa,CAAC;IAC/C,IAAIoN,QAAQ,EAAE;MACV,MAAM;QAAEjN,KAAK;QAAEwF,GAAG;QAAEjG,KAAK;QAAE4L,SAAS;QAAE9M;MAAK,CAAC,GAAGwB,aAAa;MAC5D,IAAI,CAACR,cAAc,CAAC6N,GAAG,CAAC,IAAI,CAAC7N,cAAc,CAAC,CAAC,CAACqG,MAAM,CAAE8F,CAAC,IAAKhG,GAAG,KAAKgG,CAAC,CAAChG,GAAG,IAAIA,GAAG,CAACoH,UAAU,CAACpB,CAAC,CAAChG,GAAG,CAAC,CAAC,CAAC;MACpG,IAAI,CAAC0F,eAAe,CAACgC,GAAG,CAAC;QAAElN,KAAK;QAAET,KAAK;QAAE4L,SAAS;QAAE9M;MAAK,CAAC,CAAC;MAC3D,IAAI,CAAC2M,KAAK,GAAG,IAAI;MACjBxR,UAAU,CAAC2T,KAAK,CAAC,IAAI,CAAC7C,QAAQ,CAAC5G,gBAAgB,CAACO,aAAa,CAAC;IAClE,CAAC,MACI;MACD,IAAI8I,OAAO,EAAE;QACT,IAAI,CAACK,YAAY,CAACpH,KAAK,CAAC;MAC5B,CAAC,MACI;QACD,MAAMqH,iBAAiB,GAAGhK,IAAI,GAAGxD,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAACkM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,EAAE,CAAC;QACtG,IAAI,CAACmC,IAAI,CAACrH,aAAa,CAAC;QACxB,IAAI,CAACsH,sBAAsB,CAACtH,aAAa,EAAEoH,iBAAiB,GAAGA,iBAAiB,CAACrN,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5FxG,UAAU,CAAC2T,KAAK,CAAC,IAAI,CAAC7C,QAAQ,CAAC5G,gBAAgB,CAACO,aAAa,CAAC;MAClE;IACJ;EACJ;EACAhF,gBAAgBA,CAAC+G,KAAK,EAAE;IACpB,IAAI,CAACxM,UAAU,CAACgU,aAAa,CAAC,CAAC,EAAE;MAC7B,IAAI,IAAI,CAACrO,WAAW,EAAE;QAClB,IAAI,CAAC6L,KAAK,GAAG,IAAI;MACrB;MACA,IAAI,IAAI,CAACA,KAAK,EAAE;QACZ,IAAI,CAACoC,YAAY,CAACpH,KAAK,CAAC;MAC5B;IACJ,CAAC,MACI;MACD,IAAI,CAACoH,YAAY,CAAC;QAAEpH,KAAK;QAAEnG,aAAa,EAAEmG,KAAK,CAACnG,aAAa;QAAEsN,KAAK,EAAE,IAAI,CAAChO;MAAY,CAAC,CAAC;IAC7F;EACJ;EACAgD,SAASA,CAAC6D,KAAK,EAAE;IACb,MAAMyH,OAAO,GAAGzH,KAAK,CAACyH,OAAO,IAAIzH,KAAK,CAAC0H,OAAO;IAC9C,QAAQ1H,KAAK,CAAC2H,IAAI;MACd,KAAK,WAAW;QACZ,IAAI,CAACC,cAAc,CAAC5H,KAAK,CAAC;QAC1B;MACJ,KAAK,SAAS;QACV,IAAI,CAAC6H,YAAY,CAAC7H,KAAK,CAAC;QACxB;MACJ,KAAK,WAAW;QACZ,IAAI,CAAC8H,cAAc,CAAC9H,KAAK,CAAC;QAC1B;MACJ,KAAK,YAAY;QACb,IAAI,CAAC+H,eAAe,CAAC/H,KAAK,CAAC;QAC3B;MACJ,KAAK,MAAM;QACP,IAAI,CAACgI,SAAS,CAAChI,KAAK,CAAC;QACrB;MACJ,KAAK,KAAK;QACN,IAAI,CAACiI,QAAQ,CAACjI,KAAK,CAAC;QACpB;MACJ,KAAK,OAAO;QACR,IAAI,CAACkI,UAAU,CAAClI,KAAK,CAAC;QACtB;MACJ,KAAK,OAAO;QACR,IAAI,CAACmI,UAAU,CAACnI,KAAK,CAAC;QACtB;MACJ,KAAK,QAAQ;QACT,IAAI,CAACoI,WAAW,CAACpI,KAAK,CAAC;QACvB;MACJ,KAAK,KAAK;QACN,IAAI,CAACqI,QAAQ,CAACrI,KAAK,CAAC;QACpB;MACJ,KAAK,UAAU;MACf,KAAK,QAAQ;MACb,KAAK,WAAW;MAChB,KAAK,WAAW;MAChB,KAAK,YAAY;QACb;QACA;MACJ;QACI,IAAI,CAACyH,OAAO,IAAI1T,WAAW,CAACuU,oBAAoB,CAACtI,KAAK,CAACR,GAAG,CAAC,EAAE;UACzD,IAAI,CAAC+I,WAAW,CAACvI,KAAK,EAAEA,KAAK,CAACR,GAAG,CAAC;QACtC;QACA;IACR;EACJ;EACAoI,cAAcA,CAAC5H,KAAK,EAAE;IAClB,MAAMwI,SAAS,GAAG,IAAI,CAACtD,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACyO,iBAAiB,CAAC,IAAI,CAACvD,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC,GAAG,IAAI,CAAC0O,yBAAyB,CAAC,CAAC;IAC/I,IAAI,CAACnB,sBAAsB,CAACvH,KAAK,EAAEwI,SAAS,CAAC;IAC7CxI,KAAK,CAAC2I,cAAc,CAAC,CAAC;EAC1B;EACAZ,eAAeA,CAAC/H,KAAK,EAAE;IACnB,MAAMnG,aAAa,GAAG,IAAI,CAACyL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC;IACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;IACzD,MAAMxB,IAAI,GAAGwB,aAAa,EAAExB,IAAI;IAChC,IAAI0O,OAAO,EAAE;MACT,IAAI,CAACK,YAAY,CAAC;QAAEnH,aAAa,EAAED,KAAK;QAAEnG;MAAc,CAAC,CAAC;MAC1D,IAAI,CAACqL,eAAe,CAACgC,GAAG,CAAC;QAAElN,KAAK,EAAE,CAAC,CAAC;QAAEmL,SAAS,EAAEtL,aAAa,CAAC2F,GAAG;QAAEnH;MAAK,CAAC,CAAC;MAC3E,IAAI,CAAC+M,WAAW,GAAG,EAAE;MACrB,IAAI,CAACwC,cAAc,CAAC5H,KAAK,CAAC;IAC9B;IACAA,KAAK,CAAC2I,cAAc,CAAC,CAAC;EAC1B;EACAd,YAAYA,CAAC7H,KAAK,EAAE;IAChB,IAAIA,KAAK,CAAC4I,MAAM,EAAE;MACd,IAAI,IAAI,CAAC1D,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,EAAE;QACrC,MAAMH,aAAa,GAAG,IAAI,CAACyL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC;QACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;QACzD,CAACkN,OAAO,IAAI,IAAI,CAACK,YAAY,CAAC;UAAEnH,aAAa,EAAED,KAAK;UAAEnG;QAAc,CAAC,CAAC;MAC1E;MACA,IAAI,CAAC0C,KAAK,IAAI,IAAI,CAAC+K,IAAI,CAACtH,KAAK,EAAE,IAAI,CAAC;MACpCA,KAAK,CAAC2I,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,MAAMH,SAAS,GAAG,IAAI,CAACtD,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC6O,iBAAiB,CAAC,IAAI,CAAC3D,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC,GAAG,IAAI,CAAC8O,wBAAwB,CAAC,CAAC;MAC9I,IAAI,CAACvB,sBAAsB,CAACvH,KAAK,EAAEwI,SAAS,CAAC;MAC7CxI,KAAK,CAAC2I,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAb,cAAcA,CAAC9H,KAAK,EAAE;IAClB,MAAMnG,aAAa,GAAG,IAAI,CAACyL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC;IACrE,MAAMkE,UAAU,GAAG,IAAI,CAAC7E,cAAc,CAAC,CAAC,CAACkM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAChG,GAAG,KAAK3F,aAAa,CAACsL,SAAS,CAAC;IACvF,MAAM9H,IAAI,GAAGtJ,WAAW,CAACiT,OAAO,CAACnN,aAAa,CAACoM,MAAM,CAAC;IACtD,IAAI,CAAC5I,IAAI,EAAE;MACP,IAAI,CAAC6H,eAAe,CAACgC,GAAG,CAAC;QAAElN,KAAK,EAAE,CAAC,CAAC;QAAEmL,SAAS,EAAEjH,UAAU,GAAGA,UAAU,CAACiH,SAAS,GAAG,EAAE;QAAE9M,IAAI,EAAEwB,aAAa,CAACxB;MAAK,CAAC,CAAC;MACpH,IAAI,CAAC+M,WAAW,GAAG,EAAE;MACrB,IAAI,CAACwC,cAAc,CAAC5H,KAAK,CAAC;IAC9B;IACA,MAAM3G,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAACqG,MAAM,CAAE8F,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;IAC5G,IAAI,CAAC9L,cAAc,CAAC6N,GAAG,CAAC7N,cAAc,CAAC;IACvC2G,KAAK,CAAC2I,cAAc,CAAC,CAAC;EAC1B;EACAX,SAASA,CAAChI,KAAK,EAAE;IACb,IAAI,CAACuH,sBAAsB,CAACvH,KAAK,EAAE,IAAI,CAAC+I,kBAAkB,CAAC,CAAC,CAAC;IAC7D/I,KAAK,CAAC2I,cAAc,CAAC,CAAC;EAC1B;EACAV,QAAQA,CAACjI,KAAK,EAAE;IACZ,IAAI,CAACuH,sBAAsB,CAACvH,KAAK,EAAE,IAAI,CAACgJ,iBAAiB,CAAC,CAAC,CAAC;IAC5DhJ,KAAK,CAAC2I,cAAc,CAAC,CAAC;EAC1B;EACAT,UAAUA,CAAClI,KAAK,EAAE;IACd,IAAI,CAACmI,UAAU,CAACnI,KAAK,CAAC;EAC1B;EACAoI,WAAWA,CAACpI,KAAK,EAAE;IACf,IAAI,CAACsH,IAAI,CAACtH,KAAK,EAAE,IAAI,CAAC;IACtB,IAAI,CAACkF,eAAe,CAAC,CAAC,CAAClL,KAAK,GAAG,IAAI,CAAC0O,yBAAyB,CAAC,CAAC;IAC/D1I,KAAK,CAAC2I,cAAc,CAAC,CAAC;EAC1B;EACAN,QAAQA,CAACrI,KAAK,EAAE;IACZ,IAAI,IAAI,CAACkF,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMH,aAAa,GAAG,IAAI,CAACyL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC;MACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;MACzD,CAACkN,OAAO,IAAI,IAAI,CAACK,YAAY,CAAC;QAAEnH,aAAa,EAAED,KAAK;QAAEnG;MAAc,CAAC,CAAC;IAC1E;IACA,IAAI,CAACyN,IAAI,CAAC,CAAC;EACf;EACAa,UAAUA,CAACnI,KAAK,EAAE;IACd,IAAI,IAAI,CAACkF,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMiP,OAAO,GAAGzV,UAAU,CAAC0V,UAAU,CAAC,IAAI,CAAC5E,QAAQ,CAACnH,EAAE,CAACc,aAAa,EAAE,UAAU,GAAG,IAAI,CAAC3E,aAAa,EAAE,IAAI,CAAC;MAC5G,MAAM6P,aAAa,GAAGF,OAAO,IAAIzV,UAAU,CAAC0V,UAAU,CAACD,OAAO,EAAE,6BAA6B,CAAC;MAC9FE,aAAa,GAAGA,aAAa,CAACC,KAAK,CAAC,CAAC,GAAGH,OAAO,IAAIA,OAAO,CAACG,KAAK,CAAC,CAAC;MAClE,IAAI,CAAC,IAAI,CAAC7M,KAAK,EAAE;QACb,MAAM1C,aAAa,GAAG,IAAI,CAACyL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC;QACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;QACzD,CAACkN,OAAO,KAAK,IAAI,CAAC7B,eAAe,CAAC,CAAC,CAAClL,KAAK,GAAG,IAAI,CAAC0O,yBAAyB,CAAC,CAAC,CAAC;MACjF;IACJ;IACA1I,KAAK,CAAC2I,cAAc,CAAC,CAAC;EAC1B;EACAvB,YAAYA,CAACpH,KAAK,EAAE;IAChB,MAAM;MAAEnG,aAAa;MAAEqG;IAAQ,CAAC,GAAGF,KAAK;IACxC,IAAIjM,WAAW,CAACiT,OAAO,CAACnN,aAAa,CAAC,EAClC;IACJ,MAAM;MAAEG,KAAK;MAAEwF,GAAG;MAAEjG,KAAK;MAAE4L,SAAS;MAAEjM,KAAK;MAAEb;IAAK,CAAC,GAAGwB,aAAa;IACnE,MAAMkN,OAAO,GAAGhT,WAAW,CAAC8J,UAAU,CAAC3E,KAAK,CAAC;IAC7C,MAAMG,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAACqG,MAAM,CAAE8F,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAKA,SAAS,IAAIK,CAAC,CAACL,SAAS,KAAK3F,GAAG,CAAC;IAC5GuH,OAAO,IAAI1N,cAAc,CAAC8M,IAAI,CAACtM,aAAa,CAAC;IAC7C,IAAI,CAACqL,eAAe,CAACgC,GAAG,CAAC;MAAElN,KAAK;MAAET,KAAK;MAAE4L,SAAS;MAAE9M;IAAK,CAAC,CAAC;IAC3D,IAAI,CAACgB,cAAc,CAAC6N,GAAG,CAAC7N,cAAc,CAAC;IACvC0N,OAAO,KAAK,IAAI,CAAC/B,KAAK,GAAG,IAAI,CAAC;IAC9B9E,OAAO,IAAI1M,UAAU,CAAC2T,KAAK,CAAC,IAAI,CAAC7C,QAAQ,CAAC5G,gBAAgB,CAACO,aAAa,CAAC;EAC7E;EACAlC,WAAWA,CAACiE,KAAK,EAAE;IACf,IAAI,CAAC/C,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAACiI,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACuC,KAAK,EAAE;MACpD;IAAA;EAER;EACAN,UAAUA,CAAC+D,KAAK,EAAE;IACd,IAAI,CAAC/C,OAAO,GAAG,KAAK;IACpB,IAAI,CAACiI,eAAe,CAACgC,GAAG,CAAC;MAAElN,KAAK,EAAE,CAAC,CAAC;MAAET,KAAK,EAAE,CAAC;MAAE4L,SAAS,EAAE,EAAE;MAAE9M,IAAI,EAAE;IAAK,CAAC,CAAC;IAC5E,IAAI,CAAC+M,WAAW,GAAG,EAAE;IACrB,IAAI,CAACJ,KAAK,GAAG,KAAK;EACtB;EACAtJ,uBAAuBA,CAACsE,KAAK,EAAE;IAC3B,QAAQA,KAAK,CAACqJ,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,IAAI,CAAC9M,KAAK,EAAE;UACZ,IAAI,CAACiI,SAAS,GAAGxE,KAAK,CAACiJ,OAAO;UAC9B,IAAI,CAACK,SAAS,CAAC,CAAC;UAChB,IAAI,CAACnF,MAAM,CAACpL,IAAI,CAAC,CAAC,CAAC,CAAC;UACpB,IAAI,CAACwQ,aAAa,CAAC,CAAC;UACpB,IAAI,CAACC,YAAY,CAAC,CAAC;UACnB,IAAI,CAAC/D,wBAAwB,CAAC,CAAC;UAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;UACzB,IAAI,CAAC+D,kBAAkB,CAAC,CAAC;UACzBjW,UAAU,CAAC2T,KAAK,CAAC,IAAI,CAAC7C,QAAQ,CAAC5G,gBAAgB,CAACO,aAAa,CAAC;UAC9D,IAAI,CAACyL,YAAY,CAAC,CAAC;QACvB;QACA;MACJ,KAAK,MAAM;QACP,IAAI,CAACC,aAAa,CAAC,CAAC;QACpB,IAAI,CAACvF,MAAM,CAACrL,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB;IACR;EACJ;EACAyQ,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACzE,aAAa,EAClBvR,UAAU,CAACoW,gBAAgB,CAAC,IAAI,CAACpF,SAAS,EAAE,IAAI,CAACI,MAAM,CAAC,CAAC,KAEzDpR,UAAU,CAACqW,gBAAgB,CAAC,IAAI,CAACrF,SAAS,EAAE,IAAI,CAACI,MAAM,CAAC;EAChE;EACAhJ,qBAAqBA,CAACoE,KAAK,EAAE;IACzB,QAAQA,KAAK,CAACqJ,OAAO;MACjB,KAAK,MAAM;QACPpV,WAAW,CAAC6V,KAAK,CAAC9J,KAAK,CAACiJ,OAAO,CAAC;QAChC;IACR;EACJ;EACAM,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACrF,QAAQ,EAAE;MACf,IAAI,IAAI,CAACA,QAAQ,KAAK,MAAM,EACxB,IAAI,CAAC9G,QAAQ,CAAC2M,WAAW,CAAC,IAAI,CAACrG,QAAQ,CAACsG,IAAI,EAAE,IAAI,CAACxF,SAAS,CAAC,CAAC,KAE9DhR,UAAU,CAACuW,WAAW,CAAC,IAAI,CAACvF,SAAS,EAAE,IAAI,CAACN,QAAQ,CAAC;IAC7D;EACJ;EACA+F,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACzF,SAAS,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjC,IAAI,CAAC9G,QAAQ,CAAC2M,WAAW,CAAC,IAAI,CAAC5M,EAAE,CAACc,aAAa,EAAE,IAAI,CAACuG,SAAS,CAAC;IACpE;EACJ;EACA8E,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACtM,UAAU,EAAE;MACjB/I,WAAW,CAACiT,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC1C,SAAS,EAAE,IAAI,CAACzH,UAAU,GAAG,IAAI,CAAC6G,MAAM,CAACsG,MAAM,CAACC,IAAI,CAAC;IACtF;EACJ;EACA;AACJ;AACA;AACA;EACI7C,IAAIA,CAACtH,KAAK,EAAEE,OAAO,EAAE;IACjB,IAAI,IAAI,CAAC3D,KAAK,EAAE;MACZ,IAAI,CAAC6H,MAAM,CAACrL,IAAI,CAAC,CAAC,CAAC,CAAC;MACpB,IAAI,CAAC+L,OAAO,GAAG,KAAK;IACxB;IACA,IAAI,CAACzL,cAAc,CAAC6N,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAI,CAAChC,eAAe,CAACgC,GAAG,CAAC;MAAElN,KAAK,EAAE,CAAC,CAAC;MAAET,KAAK,EAAE,CAAC;MAAE4L,SAAS,EAAE;IAAG,CAAC,CAAC;IAChEjF,OAAO,IAAI1M,UAAU,CAAC2T,KAAK,CAAC,IAAI,CAACtC,aAAa,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,CAACN,QAAQ,CAAC5G,gBAAgB,CAACO,aAAa,CAAC;IAC9G,IAAI,CAAC+G,KAAK,GAAG,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;EACIoF,MAAMA,CAACpK,KAAK,EAAE;IACV,IAAI,CAAC8E,OAAO,GAAG,IAAI,CAACwC,IAAI,CAACtH,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAACqK,IAAI,CAACrK,KAAK,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;EACIqK,IAAIA,CAACrK,KAAK,EAAEE,OAAO,EAAE;IACjB,IAAI,IAAI,CAAC3D,KAAK,EAAE;MACZ,IAAI,CAACuI,OAAO,GAAG,IAAI;MACnB,IAAI,CAACF,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI5E,KAAK,CAACsK,aAAa;MAChD,IAAI,CAACzF,aAAa,GAAG7E,KAAK,CAAC6E,aAAa,IAAI,IAAI;MAChD,IAAI,CAACE,aAAa,GAAG/E,KAAK,EAAE+E,aAAa,IAAI,IAAI;IACrD;IACA,IAAI,CAACG,eAAe,CAACgC,GAAG,CAAC;MAAElN,KAAK,EAAE,CAAC,CAAC;MAAET,KAAK,EAAE,CAAC;MAAE4L,SAAS,EAAE;IAAG,CAAC,CAAC;IAChEjF,OAAO,IAAI1M,UAAU,CAAC2T,KAAK,CAAC,IAAI,CAAC7C,QAAQ,CAAC5G,gBAAgB,CAACO,aAAa,CAAC;IACzE,IAAI,CAAC0F,EAAE,CAAC4G,YAAY,CAAC,CAAC;EAC1B;EACAhC,WAAWA,CAACvI,KAAK,EAAEwK,IAAI,EAAE;IACrB,IAAI,CAACpF,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW,IAAI,EAAE,IAAIoF,IAAI;IAClD,IAAIhC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIiC,OAAO,GAAG,KAAK;IACnB,IAAI,IAAI,CAACvF,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,EAAE;MACrCwO,SAAS,GAAG,IAAI,CAAClD,YAAY,CAAC1F,KAAK,CAAC,IAAI,CAACsF,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC,CAAC0Q,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC6M,aAAa,CAAC7M,aAAa,CAAC,CAAC;MACjI2O,SAAS,GAAGA,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAClD,YAAY,CAAC1F,KAAK,CAAC,CAAC,EAAE,IAAI,CAACsF,eAAe,CAAC,CAAC,CAAClL,KAAK,CAAC,CAAC0Q,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC6M,aAAa,CAAC7M,aAAa,CAAC,CAAC,GAAG2O,SAAS,GAAG,IAAI,CAACtD,eAAe,CAAC,CAAC,CAAClL,KAAK;IACtM,CAAC,MACI;MACDwO,SAAS,GAAG,IAAI,CAAClD,YAAY,CAACoF,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC6M,aAAa,CAAC7M,aAAa,CAAC,CAAC;IACjG;IACA,IAAI2O,SAAS,KAAK,CAAC,CAAC,EAAE;MAClBiC,OAAO,GAAG,IAAI;IAClB;IACA,IAAIjC,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAACtD,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAK,CAAC,CAAC,EAAE;MACzDwO,SAAS,GAAG,IAAI,CAACE,yBAAyB,CAAC,CAAC;IAChD;IACA,IAAIF,SAAS,KAAK,CAAC,CAAC,EAAE;MAClB,IAAI,CAACjB,sBAAsB,CAACvH,KAAK,EAAEwI,SAAS,CAAC;IACjD;IACA,IAAI,IAAI,CAACnD,aAAa,EAAE;MACpBsF,YAAY,CAAC,IAAI,CAACtF,aAAa,CAAC;IACpC;IACA,IAAI,CAACA,aAAa,GAAGuF,UAAU,CAAC,MAAM;MAClC,IAAI,CAACxF,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,IAAI;IAC7B,CAAC,EAAE,GAAG,CAAC;IACP,OAAOoF,OAAO;EAClB;EACA3B,wBAAwBA,CAAA,EAAG;IACvB,MAAM+B,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC7B,iBAAiB,CAAC,CAAC,GAAG6B,aAAa;EACvE;EACA7B,iBAAiBA,CAAA,EAAG;IAChB,OAAOjV,WAAW,CAACgX,aAAa,CAAC,IAAI,CAACzF,YAAY,EAAGzL,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC;EAC3G;EACAgP,iBAAiBA,CAAC7O,KAAK,EAAE;IACrB,MAAMgR,gBAAgB,GAAGhR,KAAK,GAAG,CAAC,GAAGjG,WAAW,CAACgX,aAAa,CAAC,IAAI,CAACzF,YAAY,CAAC1F,KAAK,CAAC,CAAC,EAAE5F,KAAK,CAAC,EAAGH,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1J,OAAOmR,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGhR,KAAK;EAC3D;EACAyO,iBAAiBA,CAACzO,KAAK,EAAE;IACrB,MAAMgR,gBAAgB,GAAGhR,KAAK,GAAG,IAAI,CAACsL,YAAY,CAAC3F,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC2F,YAAY,CAAC1F,KAAK,CAAC5F,KAAK,GAAG,CAAC,CAAC,CAAC0Q,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IACrK,OAAOmR,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGhR,KAAK,GAAG,CAAC,GAAGA,KAAK;EACvE;EACA0O,yBAAyBA,CAAA,EAAG;IACxB,MAAMmC,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC9B,kBAAkB,CAAC,CAAC,GAAG8B,aAAa;EACxE;EACA9B,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACzD,YAAY,CAACoF,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC;EAC1F;EACAiR,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACxF,YAAY,CAACoF,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC0M,mBAAmB,CAAC1M,aAAa,CAAC,CAAC;EAClG;EACA0N,sBAAsBA,CAACvH,KAAK,EAAEhG,KAAK,EAAE;IACjC,IAAI,IAAI,CAACkL,eAAe,CAAC,CAAC,CAAClL,KAAK,KAAKA,KAAK,EAAE;MACxC,MAAMkL,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACA,eAAe,CAACgC,GAAG,CAAC;QAAE,GAAGhC,eAAe;QAAE7M,IAAI,EAAE,IAAI,CAACiN,YAAY,CAACtL,KAAK,CAAC,CAAC3B,IAAI;QAAE2B;MAAM,CAAC,CAAC;MAC5F,IAAI,CAAC0P,YAAY,CAAC,CAAC;IACvB;EACJ;EACAA,YAAYA,CAAC1P,KAAK,GAAG,CAAC,CAAC,EAAE;IACrB,MAAMsC,EAAE,GAAGtC,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAACsC,EAAE,IAAItC,KAAK,EAAE,GAAG,IAAI,CAACV,aAAa;IACpE,MAAM2P,OAAO,GAAGzV,UAAU,CAAC0V,UAAU,CAAC,IAAI,CAAC5E,QAAQ,CAACnH,EAAE,CAACc,aAAa,EAAE,UAAU3B,EAAE,IAAI,CAAC;IACvF,IAAI2M,OAAO,EAAE;MACTA,OAAO,CAACgC,cAAc,IAAIhC,OAAO,CAACgC,cAAc,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IAC7F;EACJ;EACA1B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC9E,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG,IAAIlR,6BAA6B,CAAC,IAAI,CAACmR,MAAM,EAAG5E,KAAK,IAAK;QAC3E,IAAI,IAAI,CAAC8E,OAAO,EAAE;UACd,IAAI,CAACwC,IAAI,CAACtH,KAAK,EAAE,IAAI,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC2E,aAAa,CAAC8E,kBAAkB,CAAC,CAAC;EAC3C;EACA2B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACzG,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACyG,oBAAoB,CAAC,CAAC;MACzC,IAAI,CAACzG,aAAa,GAAG,IAAI;IAC7B;EACJ;EACAe,kBAAkBA,CAAA,EAAG;IACjB,IAAI5T,iBAAiB,CAAC,IAAI,CAACiM,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAAC2G,cAAc,EAAE;QACtB,IAAI,CAACA,cAAc,GAAG,IAAI,CAACtH,QAAQ,CAACiO,MAAM,CAAC,IAAI,CAAC3H,QAAQ,CAAC4H,WAAW,EAAE,QAAQ,EAAGtL,KAAK,IAAK;UACvF,IAAI,CAACxM,UAAU,CAACgU,aAAa,CAAC,CAAC,EAAE;YAC7B,IAAI,CAACF,IAAI,CAACtH,KAAK,EAAE,IAAI,CAAC;UAC1B;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACAyF,wBAAwBA,CAAA,EAAG;IACvB,IAAI3T,iBAAiB,CAAC,IAAI,CAACiM,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAAC0G,oBAAoB,EAAE;QAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAACrH,QAAQ,CAACiO,MAAM,CAAC,IAAI,CAAC3H,QAAQ,EAAE,OAAO,EAAG1D,KAAK,IAAK;UAChF,MAAMuL,kBAAkB,GAAG,IAAI,CAAChH,kBAAkB,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAACtG,aAAa,CAACuN,QAAQ,CAACxL,KAAK,CAAC4E,MAAM,CAAC;UACnH,MAAM6G,eAAe,GAAG,IAAI,CAAClP,KAAK,GAAG,EAAE,IAAI,CAACqI,MAAM,KAAK,IAAI,CAACA,MAAM,KAAK5E,KAAK,CAAC4E,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC4G,QAAQ,CAACxL,KAAK,CAAC4E,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;UAClI,IAAI2G,kBAAkB,IAAIE,eAAe,EAAE;YACvC,IAAI,CAACnE,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACA3B,0BAA0BA,CAAA,EAAG;IACzB,IAAI,IAAI,CAAClB,oBAAoB,EAAE;MAC3Bf,QAAQ,CAACgI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACjH,oBAAoB,CAAC;MAChE,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACAmB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAClB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;EACAiF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAChE,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACwF,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC,IAAI,CAACzH,EAAE,CAACgI,SAAS,EAAE;MACpB,IAAI,CAAC/G,MAAM,GAAG,IAAI;IACtB;EACJ;EACAgH,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACrP,KAAK,EAAE;MACZ,IAAI,IAAI,CAACoI,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAACkH,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAClH,aAAa,GAAG,IAAI;MAC7B;MACA,IAAI,IAAI,CAACH,SAAS,IAAI,IAAI,CAACxH,UAAU,EAAE;QACnC/I,WAAW,CAAC6V,KAAK,CAAC,IAAI,CAACtF,SAAS,CAAC;MACrC;MACA,IAAI,CAACyF,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACN,aAAa,CAAC,CAAC;IACxB;EACJ;EACA,OAAOxJ,IAAI,YAAA2L,mBAAAzL,CAAA;IAAA,YAAAA,CAAA,IAAwFI,UAAU,EAv/BpBxO,EAAE,CAAAqO,iBAAA,CAu/BoCvO,QAAQ,GAv/B9CE,EAAE,CAAAqO,iBAAA,CAu/ByDvN,WAAW,GAv/BtEd,EAAE,CAAAqO,iBAAA,CAu/BiFrO,EAAE,CAACsO,UAAU,GAv/BhGtO,EAAE,CAAAqO,iBAAA,CAu/B2GrO,EAAE,CAACuO,SAAS,GAv/BzHvO,EAAE,CAAAqO,iBAAA,CAu/BoIrO,EAAE,CAAC8Z,iBAAiB,GAv/B1J9Z,EAAE,CAAAqO,iBAAA,CAu/BqKjN,EAAE,CAAC2Y,aAAa,GAv/BvL/Z,EAAE,CAAAqO,iBAAA,CAu/BkMjN,EAAE,CAAC4Y,cAAc;EAAA;EAC9S,OAAOvL,IAAI,kBAx/B8EzO,EAAE,CAAA0O,iBAAA;IAAAC,IAAA,EAw/BJH,UAAU;IAAAI,SAAA;IAAAqL,cAAA,WAAAC,0BAAAtX,EAAA,EAAAC,GAAA,EAAAsX,QAAA;MAAA,IAAAvX,EAAA;QAx/BR5C,EAAE,CAAAoa,cAAA,CAAAD,QAAA,EAw/B0vB9Y,aAAa;MAAA;MAAA,IAAAuB,EAAA;QAAA,IAAAoM,EAAA;QAx/BzwBhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAArM,GAAA,CAAAuP,SAAA,GAAApD,EAAA;MAAA;IAAA;IAAAH,SAAA,WAAAwL,iBAAAzX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5C,EAAE,CAAA+O,WAAA,CAAArG,GAAA;QAAF1I,EAAE,CAAA+O,WAAA,CAAApG,GAAA;MAAA;MAAA,IAAA/F,EAAA;QAAA,IAAAoM,EAAA;QAAFhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAArM,GAAA,CAAAwP,QAAA,GAAArD,EAAA,CAAAG,KAAA;QAAFnP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAArM,GAAA,CAAAyP,kBAAA,GAAAtD,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAwC,KAAA;MAAAvH,KAAA,GAAFtK,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,oBAw/BsFlP,gBAAgB;MAAAX,KAAA;MAAA0K,UAAA;MAAA6H,QAAA;MAAAlH,UAAA,GAx/BxG/K,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,8BAw/BkNlP,gBAAgB;MAAAyK,UAAA,GAx/BpO9K,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,8BAw/B8QjP,eAAe;MAAA4G,WAAA,GAx/B/RlH,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,gCAw/B4UlP,gBAAgB;MAAAkK,qBAAA;MAAAC,qBAAA;MAAAH,EAAA;MAAAO,SAAA;MAAAC,cAAA;MAAAH,QAAA,GAx/B9V1K,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,0BAw/BsiBlP,gBAAgB;MAAAsK,QAAA,GAx/BxjB3K,EAAE,CAAAsP,YAAA,CAAAC,0BAAA,0BAw/B4lBjP,eAAe;IAAA;IAAAmP,OAAA;MAAAyC,MAAA;MAAAC,MAAA;IAAA;IAAAzC,QAAA,GAx/B7mB1P,EAAE,CAAA2P,wBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAuK,oBAAA1X,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5C,EAAE,CAAAsE,UAAA,IAAA6E,yBAAA,iBAugCvF,CAAC;MAAA;MAAA,IAAAvG,EAAA;QAvgCoF5C,EAAE,CAAAkD,UAAA,UAAAL,GAAA,CAAAyH,KAAA,IAAAzH,GAAA,CAAAgQ,OAsgC5D,CAAC;MAAA;IAAA;IAAAzC,YAAA,GAwBqyBxQ,EAAE,CAACyQ,OAAO,EAAoFzQ,EAAE,CAAC2Q,IAAI,EAA6F3Q,EAAE,CAAC6Q,OAAO,EAA2ExF,aAAa;IAAAsP,MAAA;IAAAzJ,aAAA;IAAA0J,IAAA;MAAAC,SAAA,EAAgU,CAACjb,OAAO,CAAC,kBAAkB,EAAE,CAACC,UAAU,CAAC,QAAQ,EAAE,CAACC,KAAK,CAAC;QAAEgb,OAAO,EAAE,CAAC;QAAEnJ,SAAS,EAAE;MAAc,CAAC,CAAC,EAAE5R,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAEF,UAAU,CAAC,QAAQ,EAAE,CAACE,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;QAAEgb,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC;IAAAC,eAAA;EAAA;AAC9oD;AACA;EAAA,QAAA5J,SAAA,oBAAAA,SAAA,KAhiC6F/Q,EAAE,CAAAgR,iBAAA,CAgiCJxC,UAAU,EAAc,CAAC;IACxGG,IAAI,EAAEpO,SAAS;IACf0Q,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEnB,QAAQ,EAAE;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE6K,UAAU,EAAE,CAACpb,OAAO,CAAC,kBAAkB,EAAE,CAACC,UAAU,CAAC,QAAQ,EAAE,CAACC,KAAK,CAAC;QAAEgb,OAAO,EAAE,CAAC;QAAEnJ,SAAS,EAAE;MAAc,CAAC,CAAC,EAAE5R,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAEF,UAAU,CAAC,QAAQ,EAAE,CAACE,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;QAAEgb,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAEC,eAAe,EAAE5Z,uBAAuB,CAAC8Z,MAAM;MAAE/J,aAAa,EAAEtQ,iBAAiB,CAAC2Q,IAAI;MAAEC,IAAI,EAAE;QAC5TC,KAAK,EAAE;MACX,CAAC;MAAEkJ,MAAM,EAAE,CAAC,4vBAA4vB;IAAE,CAAC;EACvxB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5L,IAAI,EAAEmM,QAAQ;IAAExJ,UAAU,EAAE,CAAC;MAC9C3C,IAAI,EAAElO,MAAM;MACZwQ,IAAI,EAAE,CAACnR,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE6O,IAAI,EAAEtG,SAAS;IAAEiJ,UAAU,EAAE,CAAC;MAClC3C,IAAI,EAAElO,MAAM;MACZwQ,IAAI,EAAE,CAACnQ,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE6N,IAAI,EAAE3O,EAAE,CAACsO;EAAW,CAAC,EAAE;IAAEK,IAAI,EAAE3O,EAAE,CAACuO;EAAU,CAAC,EAAE;IAAEI,IAAI,EAAE3O,EAAE,CAAC8Z;EAAkB,CAAC,EAAE;IAAEnL,IAAI,EAAEvN,EAAE,CAAC2Y;EAAc,CAAC,EAAE;IAAEpL,IAAI,EAAEvN,EAAE,CAAC4Y;EAAe,CAAC,CAAC,EAAkB;IAAEnI,KAAK,EAAE,CAAC;MAC3KlD,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAE4J,KAAK,EAAE,CAAC;MACRqE,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEX,KAAK,EAAE,CAAC;MACRiP,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAE0J,UAAU,EAAE,CAAC;MACbuE,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEuR,QAAQ,EAAE,CAAC;MACXtD,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEqK,UAAU,EAAE,CAAC;MACb4D,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyK,UAAU,EAAE,CAAC;MACb6D,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEjR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE4G,WAAW,EAAE,CAAC;MACdyH,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkK,qBAAqB,EAAE,CAAC;MACxBoE,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAE8J,qBAAqB,EAAE,CAAC;MACxBmE,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAE2J,EAAE,EAAE,CAAC;MACLsE,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEkK,SAAS,EAAE,CAAC;MACZ+D,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEmK,cAAc,EAAE,CAAC;MACjB8D,IAAI,EAAEjO;IACV,CAAC,CAAC;IAAEgK,QAAQ,EAAE,CAAC;MACXiE,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsK,QAAQ,EAAE,CAAC;MACXgE,IAAI,EAAEjO,KAAK;MACXuQ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEjR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE4R,MAAM,EAAE,CAAC;MACTvD,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAEwR,MAAM,EAAE,CAAC;MACTxD,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAEyR,SAAS,EAAE,CAAC;MACZzD,IAAI,EAAE3N,eAAe;MACrBiQ,IAAI,EAAE,CAAC5P,aAAa;IACxB,CAAC,CAAC;IAAEgR,QAAQ,EAAE,CAAC;MACX1D,IAAI,EAAE/N,SAAS;MACfqQ,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEqB,kBAAkB,EAAE,CAAC;MACrB3D,IAAI,EAAE/N,SAAS;MACfqQ,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8J,gBAAgB,CAAC;EACnB,OAAO7M,IAAI,YAAA8M,yBAAA5M,CAAA;IAAA,YAAAA,CAAA,IAAwF2M,gBAAgB;EAAA;EACnH,OAAOE,IAAI,kBAroC8Ejb,EAAE,CAAAkb,gBAAA;IAAAvM,IAAA,EAqoCSoM;EAAgB;EACpH,OAAOI,IAAI,kBAtoC8Enb,EAAE,CAAAob,gBAAA;IAAAC,OAAA,GAsoCqCtb,YAAY,EAAEoB,YAAY,EAAEQ,YAAY,EAAEE,aAAa,EAAEJ,cAAc,EAAEH,YAAY,EAAEH,YAAY,EAAEU,aAAa,EAAEP,YAAY;EAAA;AACpQ;AACA;EAAA,QAAAyP,SAAA,oBAAAA,SAAA,KAxoC6F/Q,EAAE,CAAAgR,iBAAA,CAwoCJ+J,gBAAgB,EAAc,CAAC;IAC9GpM,IAAI,EAAE1N,QAAQ;IACdgQ,IAAI,EAAE,CAAC;MACCoK,OAAO,EAAE,CAACtb,YAAY,EAAEoB,YAAY,EAAEQ,YAAY,EAAEE,aAAa,EAAEJ,cAAc,EAAEH,YAAY,CAAC;MAChGga,OAAO,EAAE,CAAC9M,UAAU,EAAErN,YAAY,EAAEU,aAAa,EAAEP,YAAY,CAAC;MAChEia,YAAY,EAAE,CAAC/M,UAAU,EAAEvD,aAAa;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASuD,UAAU,EAAEuM,gBAAgB,EAAE9P,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}