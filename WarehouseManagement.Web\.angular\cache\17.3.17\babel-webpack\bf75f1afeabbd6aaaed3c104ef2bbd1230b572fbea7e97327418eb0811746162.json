{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class LanguageService {\n  constructor() {\n    this.STORAGE_KEY = 'selected-language';\n    this.languages = [{\n      code: 'en',\n      name: 'English',\n      direction: 'ltr',\n      flag: '🇺🇸'\n    }, {\n      code: 'ar',\n      name: 'العربية',\n      direction: 'rtl',\n      flag: '🇸🇦'\n    }];\n    this.currentLanguageSubject = new BehaviorSubject(this.getDefaultLanguage());\n    this.currentLanguage$ = this.currentLanguageSubject.asObservable();\n    this.initializeLanguage();\n  }\n  getDefaultLanguage() {\n    const savedLanguage = localStorage.getItem(this.STORAGE_KEY);\n    if (savedLanguage) {\n      const found = this.languages.find(lang => lang.code === savedLanguage);\n      if (found) return found;\n    }\n    // Default to Arabic\n    return this.languages.find(lang => lang.code === 'ar') || this.languages[0];\n  }\n  initializeLanguage() {\n    const currentLang = this.currentLanguageSubject.value;\n    this.applyLanguageSettings(currentLang);\n  }\n  getCurrentLanguage() {\n    return this.currentLanguageSubject.value;\n  }\n  getAvailableLanguages() {\n    return [...this.languages];\n  }\n  setLanguage(languageCode) {\n    const language = this.languages.find(lang => lang.code === languageCode);\n    if (language && language.code !== this.currentLanguageSubject.value.code) {\n      this.currentLanguageSubject.next(language);\n      localStorage.setItem(this.STORAGE_KEY, languageCode);\n      this.applyLanguageSettings(language);\n    }\n  }\n  applyLanguageSettings(language) {\n    // Set document direction\n    document.documentElement.dir = language.direction;\n    document.documentElement.lang = language.code;\n    // Add/remove RTL class to body for styling\n    if (language.direction === 'rtl') {\n      document.body.classList.add('rtl');\n      document.body.classList.remove('ltr');\n    } else {\n      document.body.classList.add('ltr');\n      document.body.classList.remove('rtl');\n    }\n    // Update PrimeNG configuration for RTL\n    this.updatePrimeNGDirection(language.direction);\n  }\n  updatePrimeNGDirection(direction) {\n    // This will be used to configure PrimeNG components for RTL\n    const primeNGConfig = window.PrimeNG?.config;\n    if (primeNGConfig) {\n      primeNGConfig.ripple = true;\n      primeNGConfig.inputStyle = 'outlined';\n      primeNGConfig.rtl = direction === 'rtl';\n    }\n  }\n  isRTL() {\n    return this.currentLanguageSubject.value.direction === 'rtl';\n  }\n  isArabic() {\n    return this.currentLanguageSubject.value.code === 'ar';\n  }\n  // Translation helper methods\n  translate(key, params) {\n    // This is a simple implementation. In a real app, you'd use Angular i18n or ngx-translate\n    const translations = this.getTranslations();\n    const currentLang = this.getCurrentLanguage().code;\n    let translation = translations[currentLang]?.[key] || translations['en']?.[key] || key;\n    // Simple parameter replacement\n    if (params) {\n      Object.keys(params).forEach(param => {\n        translation = translation.replace(`{{${param}}}`, params[param]);\n      });\n    }\n    return translation;\n  }\n  getTranslations() {\n    return {\n      en: {\n        // Navigation\n        'nav.dashboard': 'Dashboard',\n        'nav.items': 'Items',\n        'nav.categories': 'Categories',\n        'nav.warehouses': 'Warehouses',\n        'nav.inventory': 'Inventory',\n        'nav.customers': 'Customers',\n        'nav.suppliers': 'Suppliers',\n        'nav.invoices': 'Invoices',\n        'nav.payments': 'Payments',\n        'nav.reports': 'Reports',\n        'nav.settings': 'Settings',\n        'nav.logout': 'Logout',\n        // Dashboard\n        'dashboard.title': 'Dashboard',\n        'dashboard.totalSales': 'Total Sales',\n        'dashboard.totalPurchases': 'Total Purchases',\n        'dashboard.totalItems': 'Total Items',\n        'dashboard.lowStockItems': 'Low Stock Items',\n        'dashboard.recentTransactions': 'Recent Transactions',\n        'dashboard.salesTrend': 'Sales Trend',\n        'dashboard.inventoryOverview': 'Inventory Overview',\n        'dashboard.monthlyRevenue': 'Monthly Revenue',\n        // Common\n        'common.search': 'Search',\n        'common.add': 'Add',\n        'common.edit': 'Edit',\n        'common.delete': 'Delete',\n        'common.save': 'Save',\n        'common.cancel': 'Cancel',\n        'common.confirm': 'Confirm',\n        'common.yes': 'Yes',\n        'common.no': 'No',\n        'common.loading': 'Loading...',\n        'common.noData': 'No data available',\n        'common.actions': 'Actions',\n        'common.status': 'Status',\n        'common.date': 'Date',\n        'common.amount': 'Amount',\n        'common.total': 'Total',\n        'common.refresh': 'Refresh',\n        // Authentication\n        'auth.login.title': 'Sign In',\n        'auth.login.subtitle': 'Welcome to Warehouse Management System',\n        'auth.login.username': 'Username',\n        'auth.login.usernamePlaceholder': 'Enter your username',\n        'auth.login.password': 'Password',\n        'auth.login.passwordPlaceholder': 'Enter your password',\n        'auth.login.rememberMe': 'Remember me',\n        'auth.login.signIn': 'Sign In',\n        'auth.login.forgotPassword': 'Forgot password?',\n        'auth.login.success': 'Login successful',\n        'auth.login.welcomeBack': 'Welcome back {{name}}',\n        'auth.login.securityNotice': 'Your session is protected with SSL encryption and advanced security features',\n        'auth.login.attemptsRemaining': 'Attempts remaining: {{attempts}}',\n        'auth.login.error.title': 'Login Error',\n        'auth.login.error.generic': 'An error occurred during login. Please try again.',\n        'auth.login.error.invalidCredentials': 'Invalid username or password',\n        'auth.login.error.accountLocked': 'Account temporarily locked due to multiple failed login attempts',\n        'auth.login.error.tooManyAttempts': 'Too many attempts. Please try again later',\n        'auth.login.lockout.title': 'Account Locked',\n        'auth.login.lockout.message': 'Your account has been locked for {{minutes}} minutes due to multiple failed login attempts',\n        'auth.login.lockout.active': 'Account locked. Time remaining: {{time}}',\n        'auth.login.lockout.expired': 'Lockout period expired',\n        'auth.login.lockout.canTryAgain': 'You can now try again',\n        'auth.login.rateLimit.title': 'Rate Limit Exceeded',\n        'auth.login.rateLimit.message': 'Too many attempts. Please wait before trying again',\n        'auth.login.validation.invalid': 'Invalid value',\n        'auth.login.validation.username.required': 'Username is required',\n        'auth.login.validation.username.minLength': 'Username must be at least {{min}} characters',\n        'auth.login.validation.username.maxLength': 'Username must be at most {{max}} characters',\n        'auth.login.validation.password.required': 'Password is required',\n        'auth.login.validation.password.minLength': 'Password must be at least {{min}} characters',\n        'auth.login.footer.copyright': '© 2024 Warehouse Management System. All rights reserved.',\n        'auth.login.footer.privacy': 'Privacy Policy',\n        'auth.login.footer.terms': 'Terms of Service',\n        'auth.login.footer.support': 'Support',\n        // Unauthorized\n        'auth.unauthorized.title': 'Access Denied',\n        'auth.unauthorized.description': 'You do not have permission to access this resource.',\n        'auth.unauthorized.details': 'Please contact your administrator if you believe this is an error.',\n        'auth.unauthorized.goToDashboard': 'Go to Dashboard',\n        'auth.unauthorized.goBack': 'Go Back',\n        'auth.unauthorized.contactSupport': 'Contact Support',\n        'auth.unauthorized.logout': 'Logout',\n        'auth.unauthorized.autoRedirect': 'You will be redirected to the dashboard in 10 seconds.'\n      },\n      ar: {\n        // App\n        'app.title': 'نظام إدارة المستودعات',\n        'app.welcome': 'مرحباً بك',\n        'app.profile': 'الملف الشخصي',\n        'app.notifications': 'الإشعارات',\n        // Navigation\n        'nav.dashboard': 'لوحة التحكم',\n        'nav.inventoryManagement': 'إدارة المخزون',\n        'nav.items': 'الأصناف',\n        'nav.categories': 'الفئات',\n        'nav.warehouses': 'المستودعات',\n        'nav.stockMovements': 'حركات المخزون',\n        'nav.stockAdjustments': 'تسويات المخزون',\n        'nav.transfers': 'التحويلات',\n        'nav.salesPurchases': 'المبيعات والمشتريات',\n        'nav.salesInvoices': 'فواتير المبيعات',\n        'nav.purchaseInvoices': 'فواتير المشتريات',\n        'nav.salesReturns': 'مرتجعات المبيعات',\n        'nav.purchaseReturns': 'مرتجعات المشتريات',\n        'nav.customersSuppliers': 'العملاء والموردين',\n        'nav.customers': 'العملاء',\n        'nav.suppliers': 'الموردين',\n        'nav.financialManagement': 'الإدارة المالية',\n        'nav.payments': 'المدفوعات',\n        'nav.accountStatements': 'كشوف الحسابات',\n        'nav.cashRegister': 'الخزينة',\n        'nav.reports': 'التقارير',\n        'nav.inventoryReports': 'تقارير المخزون',\n        'nav.financialReports': 'التقارير المالية',\n        'nav.salesReports': 'تقارير المبيعات',\n        'nav.purchaseReports': 'تقارير المشتريات',\n        'nav.settings': 'الإعدادات',\n        'nav.logout': 'تسجيل الخروج',\n        // Dashboard\n        'dashboard.title': 'لوحة التحكم',\n        'dashboard.totalSales': 'إجمالي المبيعات',\n        'dashboard.totalPurchases': 'إجمالي المشتريات',\n        'dashboard.totalItems': 'إجمالي الأصناف',\n        'dashboard.lowStockItems': 'أصناف منخفضة المخزون',\n        'dashboard.recentTransactions': 'المعاملات الأخيرة',\n        'dashboard.salesTrend': 'اتجاه المبيعات',\n        'dashboard.inventoryOverview': 'نظرة عامة على المخزون',\n        'dashboard.monthlyRevenue': 'الإيرادات الشهرية',\n        // Common\n        'common.search': 'بحث',\n        'common.add': 'إضافة',\n        'common.edit': 'تعديل',\n        'common.delete': 'حذف',\n        'common.save': 'حفظ',\n        'common.cancel': 'إلغاء',\n        'common.confirm': 'تأكيد',\n        'common.yes': 'نعم',\n        'common.no': 'لا',\n        'common.loading': 'جاري التحميل...',\n        'common.noData': 'لا توجد بيانات متاحة',\n        'common.actions': 'الإجراءات',\n        'common.status': 'الحالة',\n        'common.date': 'التاريخ',\n        'common.amount': 'المبلغ',\n        'common.total': 'الإجمالي',\n        'common.refresh': 'تحديث',\n        // Authentication\n        'auth.login.title': 'تسجيل الدخول',\n        'auth.login.subtitle': 'مرحباً بك في نظام إدارة المخازن',\n        'auth.login.username': 'اسم المستخدم',\n        'auth.login.usernamePlaceholder': 'أدخل اسم المستخدم',\n        'auth.login.password': 'كلمة المرور',\n        'auth.login.passwordPlaceholder': 'أدخل كلمة المرور',\n        'auth.login.rememberMe': 'تذكرني',\n        'auth.login.signIn': 'تسجيل الدخول',\n        'auth.login.forgotPassword': 'نسيت كلمة المرور؟',\n        'auth.login.success': 'تم تسجيل الدخول بنجاح',\n        'auth.login.welcomeBack': 'مرحباً بعودتك {{name}}',\n        'auth.login.securityNotice': 'يتم حماية جلستك بتشفير SSL وتقنيات الأمان المتقدمة',\n        'auth.login.attemptsRemaining': 'محاولات متبقية: {{attempts}}',\n        'auth.login.error.title': 'خطأ في تسجيل الدخول',\n        'auth.login.error.generic': 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',\n        'auth.login.error.invalidCredentials': 'اسم المستخدم أو كلمة المرور غير صحيحة',\n        'auth.login.error.accountLocked': 'تم قفل الحساب مؤقتاً بسبب محاولات دخول متعددة فاشلة',\n        'auth.login.error.tooManyAttempts': 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً',\n        'auth.login.lockout.title': 'تم قفل الحساب',\n        'auth.login.lockout.message': 'تم قفل حسابك لمدة {{minutes}} دقيقة بسبب محاولات دخول فاشلة متعددة',\n        'auth.login.lockout.active': 'الحساب مقفل. الوقت المتبقي: {{time}}',\n        'auth.login.lockout.expired': 'انتهت مدة القفل',\n        'auth.login.lockout.canTryAgain': 'يمكنك الآن المحاولة مرة أخرى',\n        'auth.login.rateLimit.title': 'تم تجاوز الحد المسموح',\n        'auth.login.rateLimit.message': 'تم تجاوز عدد المحاولات المسموح. يرجى الانتظار قبل المحاولة مرة أخرى',\n        'auth.login.validation.invalid': 'القيمة المدخلة غير صحيحة',\n        'auth.login.validation.username.required': 'اسم المستخدم مطلوب',\n        'auth.login.validation.username.minLength': 'اسم المستخدم يجب أن يكون {{min}} أحرف على الأقل',\n        'auth.login.validation.username.maxLength': 'اسم المستخدم يجب أن يكون {{max}} حرف كحد أقصى',\n        'auth.login.validation.password.required': 'كلمة المرور مطلوبة',\n        'auth.login.validation.password.minLength': 'كلمة المرور يجب أن تكون {{min}} أحرف على الأقل',\n        'auth.login.footer.copyright': '© 2024 نظام إدارة المخازن. جميع الحقوق محفوظة.',\n        'auth.login.footer.privacy': 'سياسة الخصوصية',\n        'auth.login.footer.terms': 'شروط الاستخدام',\n        'auth.login.footer.support': 'الدعم الفني',\n        // Unauthorized\n        'auth.unauthorized.title': 'تم رفض الوصول',\n        'auth.unauthorized.description': 'ليس لديك صلاحية للوصول إلى هذا المورد.',\n        'auth.unauthorized.details': 'يرجى الاتصال بالمسؤول إذا كنت تعتقد أن هذا خطأ.',\n        'auth.unauthorized.goToDashboard': 'الذهاب إلى لوحة التحكم',\n        'auth.unauthorized.goBack': 'العودة',\n        'auth.unauthorized.contactSupport': 'الاتصال بالدعم',\n        'auth.unauthorized.logout': 'تسجيل الخروج',\n        'auth.unauthorized.autoRedirect': 'سيتم توجيهك إلى لوحة التحكم خلال 10 ثوانٍ.'\n      }\n    };\n  }\n  static {\n    this.ɵfac = function LanguageService_Factory(t) {\n      return new (t || LanguageService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LanguageService,\n      factory: LanguageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "LanguageService", "constructor", "STORAGE_KEY", "languages", "code", "name", "direction", "flag", "currentLanguageSubject", "getDefaultLanguage", "currentLanguage$", "asObservable", "initializeLanguage", "savedLanguage", "localStorage", "getItem", "found", "find", "lang", "currentLang", "value", "applyLanguageSettings", "getCurrentLanguage", "getAvailableLanguages", "setLanguage", "languageCode", "language", "next", "setItem", "document", "documentElement", "dir", "body", "classList", "add", "remove", "updatePrimeNGDirection", "primeNGConfig", "window", "PrimeNG", "config", "ripple", "inputStyle", "rtl", "isRTL", "isArabic", "translate", "key", "params", "translations", "getTranslations", "translation", "Object", "keys", "for<PERSON>ach", "param", "replace", "en", "ar", "factory", "ɵfac", "providedIn"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\services\\language.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\n\nexport interface Language {\n  code: string;\n  name: string;\n  direction: 'ltr' | 'rtl';\n  flag: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class LanguageService {\n  private readonly STORAGE_KEY = 'selected-language';\n  \n  private readonly languages: Language[] = [\n    {\n      code: 'en',\n      name: 'English',\n      direction: 'ltr',\n      flag: '🇺🇸'\n    },\n    {\n      code: 'ar',\n      name: 'العربية',\n      direction: 'rtl',\n      flag: '🇸🇦'\n    }\n  ];\n\n  private currentLanguageSubject = new BehaviorSubject<Language>(this.getDefaultLanguage());\n  public currentLanguage$ = this.currentLanguageSubject.asObservable();\n\n  constructor() {\n    this.initializeLanguage();\n  }\n\n  private getDefaultLanguage(): Language {\n    const savedLanguage = localStorage.getItem(this.STORAGE_KEY);\n    if (savedLanguage) {\n      const found = this.languages.find(lang => lang.code === savedLanguage);\n      if (found) return found;\n    }\n    \n    // Default to Arabic\n    return this.languages.find(lang => lang.code === 'ar') || this.languages[0];\n  }\n\n  private initializeLanguage(): void {\n    const currentLang = this.currentLanguageSubject.value;\n    this.applyLanguageSettings(currentLang);\n  }\n\n  getCurrentLanguage(): Language {\n    return this.currentLanguageSubject.value;\n  }\n\n  getAvailableLanguages(): Language[] {\n    return [...this.languages];\n  }\n\n  setLanguage(languageCode: string): void {\n    const language = this.languages.find(lang => lang.code === languageCode);\n    if (language && language.code !== this.currentLanguageSubject.value.code) {\n      this.currentLanguageSubject.next(language);\n      localStorage.setItem(this.STORAGE_KEY, languageCode);\n      this.applyLanguageSettings(language);\n    }\n  }\n\n  private applyLanguageSettings(language: Language): void {\n    // Set document direction\n    document.documentElement.dir = language.direction;\n    document.documentElement.lang = language.code;\n    \n    // Add/remove RTL class to body for styling\n    if (language.direction === 'rtl') {\n      document.body.classList.add('rtl');\n      document.body.classList.remove('ltr');\n    } else {\n      document.body.classList.add('ltr');\n      document.body.classList.remove('rtl');\n    }\n\n    // Update PrimeNG configuration for RTL\n    this.updatePrimeNGDirection(language.direction);\n  }\n\n  private updatePrimeNGDirection(direction: 'ltr' | 'rtl'): void {\n    // This will be used to configure PrimeNG components for RTL\n    const primeNGConfig = (window as any).PrimeNG?.config;\n    if (primeNGConfig) {\n      primeNGConfig.ripple = true;\n      primeNGConfig.inputStyle = 'outlined';\n      primeNGConfig.rtl = direction === 'rtl';\n    }\n  }\n\n  isRTL(): boolean {\n    return this.currentLanguageSubject.value.direction === 'rtl';\n  }\n\n  isArabic(): boolean {\n    return this.currentLanguageSubject.value.code === 'ar';\n  }\n\n  // Translation helper methods\n  translate(key: string, params?: any): string {\n    // This is a simple implementation. In a real app, you'd use Angular i18n or ngx-translate\n    const translations = this.getTranslations();\n    const currentLang = this.getCurrentLanguage().code;\n    \n    let translation = translations[currentLang]?.[key] || translations['en']?.[key] || key;\n    \n    // Simple parameter replacement\n    if (params) {\n      Object.keys(params).forEach(param => {\n        translation = translation.replace(`{{${param}}}`, params[param]);\n      });\n    }\n    \n    return translation;\n  }\n\n  private getTranslations(): any {\n    return {\n      en: {\n        // Navigation\n        'nav.dashboard': 'Dashboard',\n        'nav.items': 'Items',\n        'nav.categories': 'Categories',\n        'nav.warehouses': 'Warehouses',\n        'nav.inventory': 'Inventory',\n        'nav.customers': 'Customers',\n        'nav.suppliers': 'Suppliers',\n        'nav.invoices': 'Invoices',\n        'nav.payments': 'Payments',\n        'nav.reports': 'Reports',\n        'nav.settings': 'Settings',\n        'nav.logout': 'Logout',\n        \n        // Dashboard\n        'dashboard.title': 'Dashboard',\n        'dashboard.totalSales': 'Total Sales',\n        'dashboard.totalPurchases': 'Total Purchases',\n        'dashboard.totalItems': 'Total Items',\n        'dashboard.lowStockItems': 'Low Stock Items',\n        'dashboard.recentTransactions': 'Recent Transactions',\n        'dashboard.salesTrend': 'Sales Trend',\n        'dashboard.inventoryOverview': 'Inventory Overview',\n        'dashboard.monthlyRevenue': 'Monthly Revenue',\n        \n        // Common\n        'common.search': 'Search',\n        'common.add': 'Add',\n        'common.edit': 'Edit',\n        'common.delete': 'Delete',\n        'common.save': 'Save',\n        'common.cancel': 'Cancel',\n        'common.confirm': 'Confirm',\n        'common.yes': 'Yes',\n        'common.no': 'No',\n        'common.loading': 'Loading...',\n        'common.noData': 'No data available',\n        'common.actions': 'Actions',\n        'common.status': 'Status',\n        'common.date': 'Date',\n        'common.amount': 'Amount',\n        'common.total': 'Total',\n        'common.refresh': 'Refresh',\n\n        // Authentication\n        'auth.login.title': 'Sign In',\n        'auth.login.subtitle': 'Welcome to Warehouse Management System',\n        'auth.login.username': 'Username',\n        'auth.login.usernamePlaceholder': 'Enter your username',\n        'auth.login.password': 'Password',\n        'auth.login.passwordPlaceholder': 'Enter your password',\n        'auth.login.rememberMe': 'Remember me',\n        'auth.login.signIn': 'Sign In',\n        'auth.login.forgotPassword': 'Forgot password?',\n        'auth.login.success': 'Login successful',\n        'auth.login.welcomeBack': 'Welcome back {{name}}',\n        'auth.login.securityNotice': 'Your session is protected with SSL encryption and advanced security features',\n        'auth.login.attemptsRemaining': 'Attempts remaining: {{attempts}}',\n        'auth.login.error.title': 'Login Error',\n        'auth.login.error.generic': 'An error occurred during login. Please try again.',\n        'auth.login.error.invalidCredentials': 'Invalid username or password',\n        'auth.login.error.accountLocked': 'Account temporarily locked due to multiple failed login attempts',\n        'auth.login.error.tooManyAttempts': 'Too many attempts. Please try again later',\n        'auth.login.lockout.title': 'Account Locked',\n        'auth.login.lockout.message': 'Your account has been locked for {{minutes}} minutes due to multiple failed login attempts',\n        'auth.login.lockout.active': 'Account locked. Time remaining: {{time}}',\n        'auth.login.lockout.expired': 'Lockout period expired',\n        'auth.login.lockout.canTryAgain': 'You can now try again',\n        'auth.login.rateLimit.title': 'Rate Limit Exceeded',\n        'auth.login.rateLimit.message': 'Too many attempts. Please wait before trying again',\n        'auth.login.validation.invalid': 'Invalid value',\n        'auth.login.validation.username.required': 'Username is required',\n        'auth.login.validation.username.minLength': 'Username must be at least {{min}} characters',\n        'auth.login.validation.username.maxLength': 'Username must be at most {{max}} characters',\n        'auth.login.validation.password.required': 'Password is required',\n        'auth.login.validation.password.minLength': 'Password must be at least {{min}} characters',\n        'auth.login.footer.copyright': '© 2024 Warehouse Management System. All rights reserved.',\n        'auth.login.footer.privacy': 'Privacy Policy',\n        'auth.login.footer.terms': 'Terms of Service',\n        'auth.login.footer.support': 'Support',\n\n        // Unauthorized\n        'auth.unauthorized.title': 'Access Denied',\n        'auth.unauthorized.description': 'You do not have permission to access this resource.',\n        'auth.unauthorized.details': 'Please contact your administrator if you believe this is an error.',\n        'auth.unauthorized.goToDashboard': 'Go to Dashboard',\n        'auth.unauthorized.goBack': 'Go Back',\n        'auth.unauthorized.contactSupport': 'Contact Support',\n        'auth.unauthorized.logout': 'Logout',\n        'auth.unauthorized.autoRedirect': 'You will be redirected to the dashboard in 10 seconds.'\n      },\n      ar: {\n        // App\n        'app.title': 'نظام إدارة المستودعات',\n        'app.welcome': 'مرحباً بك',\n        'app.profile': 'الملف الشخصي',\n        'app.notifications': 'الإشعارات',\n\n        // Navigation\n        'nav.dashboard': 'لوحة التحكم',\n        'nav.inventoryManagement': 'إدارة المخزون',\n        'nav.items': 'الأصناف',\n        'nav.categories': 'الفئات',\n        'nav.warehouses': 'المستودعات',\n        'nav.stockMovements': 'حركات المخزون',\n        'nav.stockAdjustments': 'تسويات المخزون',\n        'nav.transfers': 'التحويلات',\n        'nav.salesPurchases': 'المبيعات والمشتريات',\n        'nav.salesInvoices': 'فواتير المبيعات',\n        'nav.purchaseInvoices': 'فواتير المشتريات',\n        'nav.salesReturns': 'مرتجعات المبيعات',\n        'nav.purchaseReturns': 'مرتجعات المشتريات',\n        'nav.customersSuppliers': 'العملاء والموردين',\n        'nav.customers': 'العملاء',\n        'nav.suppliers': 'الموردين',\n        'nav.financialManagement': 'الإدارة المالية',\n        'nav.payments': 'المدفوعات',\n        'nav.accountStatements': 'كشوف الحسابات',\n        'nav.cashRegister': 'الخزينة',\n        'nav.reports': 'التقارير',\n        'nav.inventoryReports': 'تقارير المخزون',\n        'nav.financialReports': 'التقارير المالية',\n        'nav.salesReports': 'تقارير المبيعات',\n        'nav.purchaseReports': 'تقارير المشتريات',\n        'nav.settings': 'الإعدادات',\n        'nav.logout': 'تسجيل الخروج',\n        \n        // Dashboard\n        'dashboard.title': 'لوحة التحكم',\n        'dashboard.totalSales': 'إجمالي المبيعات',\n        'dashboard.totalPurchases': 'إجمالي المشتريات',\n        'dashboard.totalItems': 'إجمالي الأصناف',\n        'dashboard.lowStockItems': 'أصناف منخفضة المخزون',\n        'dashboard.recentTransactions': 'المعاملات الأخيرة',\n        'dashboard.salesTrend': 'اتجاه المبيعات',\n        'dashboard.inventoryOverview': 'نظرة عامة على المخزون',\n        'dashboard.monthlyRevenue': 'الإيرادات الشهرية',\n        \n        // Common\n        'common.search': 'بحث',\n        'common.add': 'إضافة',\n        'common.edit': 'تعديل',\n        'common.delete': 'حذف',\n        'common.save': 'حفظ',\n        'common.cancel': 'إلغاء',\n        'common.confirm': 'تأكيد',\n        'common.yes': 'نعم',\n        'common.no': 'لا',\n        'common.loading': 'جاري التحميل...',\n        'common.noData': 'لا توجد بيانات متاحة',\n        'common.actions': 'الإجراءات',\n        'common.status': 'الحالة',\n        'common.date': 'التاريخ',\n        'common.amount': 'المبلغ',\n        'common.total': 'الإجمالي',\n        'common.refresh': 'تحديث',\n\n        // Authentication\n        'auth.login.title': 'تسجيل الدخول',\n        'auth.login.subtitle': 'مرحباً بك في نظام إدارة المخازن',\n        'auth.login.username': 'اسم المستخدم',\n        'auth.login.usernamePlaceholder': 'أدخل اسم المستخدم',\n        'auth.login.password': 'كلمة المرور',\n        'auth.login.passwordPlaceholder': 'أدخل كلمة المرور',\n        'auth.login.rememberMe': 'تذكرني',\n        'auth.login.signIn': 'تسجيل الدخول',\n        'auth.login.forgotPassword': 'نسيت كلمة المرور؟',\n        'auth.login.success': 'تم تسجيل الدخول بنجاح',\n        'auth.login.welcomeBack': 'مرحباً بعودتك {{name}}',\n        'auth.login.securityNotice': 'يتم حماية جلستك بتشفير SSL وتقنيات الأمان المتقدمة',\n        'auth.login.attemptsRemaining': 'محاولات متبقية: {{attempts}}',\n        'auth.login.error.title': 'خطأ في تسجيل الدخول',\n        'auth.login.error.generic': 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',\n        'auth.login.error.invalidCredentials': 'اسم المستخدم أو كلمة المرور غير صحيحة',\n        'auth.login.error.accountLocked': 'تم قفل الحساب مؤقتاً بسبب محاولات دخول متعددة فاشلة',\n        'auth.login.error.tooManyAttempts': 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً',\n        'auth.login.lockout.title': 'تم قفل الحساب',\n        'auth.login.lockout.message': 'تم قفل حسابك لمدة {{minutes}} دقيقة بسبب محاولات دخول فاشلة متعددة',\n        'auth.login.lockout.active': 'الحساب مقفل. الوقت المتبقي: {{time}}',\n        'auth.login.lockout.expired': 'انتهت مدة القفل',\n        'auth.login.lockout.canTryAgain': 'يمكنك الآن المحاولة مرة أخرى',\n        'auth.login.rateLimit.title': 'تم تجاوز الحد المسموح',\n        'auth.login.rateLimit.message': 'تم تجاوز عدد المحاولات المسموح. يرجى الانتظار قبل المحاولة مرة أخرى',\n        'auth.login.validation.invalid': 'القيمة المدخلة غير صحيحة',\n        'auth.login.validation.username.required': 'اسم المستخدم مطلوب',\n        'auth.login.validation.username.minLength': 'اسم المستخدم يجب أن يكون {{min}} أحرف على الأقل',\n        'auth.login.validation.username.maxLength': 'اسم المستخدم يجب أن يكون {{max}} حرف كحد أقصى',\n        'auth.login.validation.password.required': 'كلمة المرور مطلوبة',\n        'auth.login.validation.password.minLength': 'كلمة المرور يجب أن تكون {{min}} أحرف على الأقل',\n        'auth.login.footer.copyright': '© 2024 نظام إدارة المخازن. جميع الحقوق محفوظة.',\n        'auth.login.footer.privacy': 'سياسة الخصوصية',\n        'auth.login.footer.terms': 'شروط الاستخدام',\n        'auth.login.footer.support': 'الدعم الفني',\n\n        // Unauthorized\n        'auth.unauthorized.title': 'تم رفض الوصول',\n        'auth.unauthorized.description': 'ليس لديك صلاحية للوصول إلى هذا المورد.',\n        'auth.unauthorized.details': 'يرجى الاتصال بالمسؤول إذا كنت تعتقد أن هذا خطأ.',\n        'auth.unauthorized.goToDashboard': 'الذهاب إلى لوحة التحكم',\n        'auth.unauthorized.goBack': 'العودة',\n        'auth.unauthorized.contactSupport': 'الاتصال بالدعم',\n        'auth.unauthorized.logout': 'تسجيل الخروج',\n        'auth.unauthorized.autoRedirect': 'سيتم توجيهك إلى لوحة التحكم خلال 10 ثوانٍ.'\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;;AAYlD,OAAM,MAAOC,eAAe;EAqB1BC,YAAA;IApBiB,KAAAC,WAAW,GAAG,mBAAmB;IAEjC,KAAAC,SAAS,GAAe,CACvC;MACEC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE;KACP,EACD;MACEH,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE;KACP,CACF;IAEO,KAAAC,sBAAsB,GAAG,IAAIT,eAAe,CAAW,IAAI,CAACU,kBAAkB,EAAE,CAAC;IAClF,KAAAC,gBAAgB,GAAG,IAAI,CAACF,sBAAsB,CAACG,YAAY,EAAE;IAGlE,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQH,kBAAkBA,CAAA;IACxB,MAAMI,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACb,WAAW,CAAC;IAC5D,IAAIW,aAAa,EAAE;MACjB,MAAMG,KAAK,GAAG,IAAI,CAACb,SAAS,CAACc,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAKS,aAAa,CAAC;MACtE,IAAIG,KAAK,EAAE,OAAOA,KAAK;;IAGzB;IACA,OAAO,IAAI,CAACb,SAAS,CAACc,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC;EAC7E;EAEQS,kBAAkBA,CAAA;IACxB,MAAMO,WAAW,GAAG,IAAI,CAACX,sBAAsB,CAACY,KAAK;IACrD,IAAI,CAACC,qBAAqB,CAACF,WAAW,CAAC;EACzC;EAEAG,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACd,sBAAsB,CAACY,KAAK;EAC1C;EAEAG,qBAAqBA,CAAA;IACnB,OAAO,CAAC,GAAG,IAAI,CAACpB,SAAS,CAAC;EAC5B;EAEAqB,WAAWA,CAACC,YAAoB;IAC9B,MAAMC,QAAQ,GAAG,IAAI,CAACvB,SAAS,CAACc,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAKqB,YAAY,CAAC;IACxE,IAAIC,QAAQ,IAAIA,QAAQ,CAACtB,IAAI,KAAK,IAAI,CAACI,sBAAsB,CAACY,KAAK,CAAChB,IAAI,EAAE;MACxE,IAAI,CAACI,sBAAsB,CAACmB,IAAI,CAACD,QAAQ,CAAC;MAC1CZ,YAAY,CAACc,OAAO,CAAC,IAAI,CAAC1B,WAAW,EAAEuB,YAAY,CAAC;MACpD,IAAI,CAACJ,qBAAqB,CAACK,QAAQ,CAAC;;EAExC;EAEQL,qBAAqBA,CAACK,QAAkB;IAC9C;IACAG,QAAQ,CAACC,eAAe,CAACC,GAAG,GAAGL,QAAQ,CAACpB,SAAS;IACjDuB,QAAQ,CAACC,eAAe,CAACZ,IAAI,GAAGQ,QAAQ,CAACtB,IAAI;IAE7C;IACA,IAAIsB,QAAQ,CAACpB,SAAS,KAAK,KAAK,EAAE;MAChCuB,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,KAAK,CAAC;MAClCL,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,KAAK,CAAC;KACtC,MAAM;MACLN,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,KAAK,CAAC;MAClCL,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,KAAK,CAAC;;IAGvC;IACA,IAAI,CAACC,sBAAsB,CAACV,QAAQ,CAACpB,SAAS,CAAC;EACjD;EAEQ8B,sBAAsBA,CAAC9B,SAAwB;IACrD;IACA,MAAM+B,aAAa,GAAIC,MAAc,CAACC,OAAO,EAAEC,MAAM;IACrD,IAAIH,aAAa,EAAE;MACjBA,aAAa,CAACI,MAAM,GAAG,IAAI;MAC3BJ,aAAa,CAACK,UAAU,GAAG,UAAU;MACrCL,aAAa,CAACM,GAAG,GAAGrC,SAAS,KAAK,KAAK;;EAE3C;EAEAsC,KAAKA,CAAA;IACH,OAAO,IAAI,CAACpC,sBAAsB,CAACY,KAAK,CAACd,SAAS,KAAK,KAAK;EAC9D;EAEAuC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACrC,sBAAsB,CAACY,KAAK,CAAChB,IAAI,KAAK,IAAI;EACxD;EAEA;EACA0C,SAASA,CAACC,GAAW,EAAEC,MAAY;IACjC;IACA,MAAMC,YAAY,GAAG,IAAI,CAACC,eAAe,EAAE;IAC3C,MAAM/B,WAAW,GAAG,IAAI,CAACG,kBAAkB,EAAE,CAAClB,IAAI;IAElD,IAAI+C,WAAW,GAAGF,YAAY,CAAC9B,WAAW,CAAC,GAAG4B,GAAG,CAAC,IAAIE,YAAY,CAAC,IAAI,CAAC,GAAGF,GAAG,CAAC,IAAIA,GAAG;IAEtF;IACA,IAAIC,MAAM,EAAE;MACVI,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,OAAO,CAACC,KAAK,IAAG;QAClCJ,WAAW,GAAGA,WAAW,CAACK,OAAO,CAAC,KAAKD,KAAK,IAAI,EAAEP,MAAM,CAACO,KAAK,CAAC,CAAC;MAClE,CAAC,CAAC;;IAGJ,OAAOJ,WAAW;EACpB;EAEQD,eAAeA,CAAA;IACrB,OAAO;MACLO,EAAE,EAAE;QACF;QACA,eAAe,EAAE,WAAW;QAC5B,WAAW,EAAE,OAAO;QACpB,gBAAgB,EAAE,YAAY;QAC9B,gBAAgB,EAAE,YAAY;QAC9B,eAAe,EAAE,WAAW;QAC5B,eAAe,EAAE,WAAW;QAC5B,eAAe,EAAE,WAAW;QAC5B,cAAc,EAAE,UAAU;QAC1B,cAAc,EAAE,UAAU;QAC1B,aAAa,EAAE,SAAS;QACxB,cAAc,EAAE,UAAU;QAC1B,YAAY,EAAE,QAAQ;QAEtB;QACA,iBAAiB,EAAE,WAAW;QAC9B,sBAAsB,EAAE,aAAa;QACrC,0BAA0B,EAAE,iBAAiB;QAC7C,sBAAsB,EAAE,aAAa;QACrC,yBAAyB,EAAE,iBAAiB;QAC5C,8BAA8B,EAAE,qBAAqB;QACrD,sBAAsB,EAAE,aAAa;QACrC,6BAA6B,EAAE,oBAAoB;QACnD,0BAA0B,EAAE,iBAAiB;QAE7C;QACA,eAAe,EAAE,QAAQ;QACzB,YAAY,EAAE,KAAK;QACnB,aAAa,EAAE,MAAM;QACrB,eAAe,EAAE,QAAQ;QACzB,aAAa,EAAE,MAAM;QACrB,eAAe,EAAE,QAAQ;QACzB,gBAAgB,EAAE,SAAS;QAC3B,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,IAAI;QACjB,gBAAgB,EAAE,YAAY;QAC9B,eAAe,EAAE,mBAAmB;QACpC,gBAAgB,EAAE,SAAS;QAC3B,eAAe,EAAE,QAAQ;QACzB,aAAa,EAAE,MAAM;QACrB,eAAe,EAAE,QAAQ;QACzB,cAAc,EAAE,OAAO;QACvB,gBAAgB,EAAE,SAAS;QAE3B;QACA,kBAAkB,EAAE,SAAS;QAC7B,qBAAqB,EAAE,wCAAwC;QAC/D,qBAAqB,EAAE,UAAU;QACjC,gCAAgC,EAAE,qBAAqB;QACvD,qBAAqB,EAAE,UAAU;QACjC,gCAAgC,EAAE,qBAAqB;QACvD,uBAAuB,EAAE,aAAa;QACtC,mBAAmB,EAAE,SAAS;QAC9B,2BAA2B,EAAE,kBAAkB;QAC/C,oBAAoB,EAAE,kBAAkB;QACxC,wBAAwB,EAAE,uBAAuB;QACjD,2BAA2B,EAAE,8EAA8E;QAC3G,8BAA8B,EAAE,kCAAkC;QAClE,wBAAwB,EAAE,aAAa;QACvC,0BAA0B,EAAE,mDAAmD;QAC/E,qCAAqC,EAAE,8BAA8B;QACrE,gCAAgC,EAAE,kEAAkE;QACpG,kCAAkC,EAAE,2CAA2C;QAC/E,0BAA0B,EAAE,gBAAgB;QAC5C,4BAA4B,EAAE,4FAA4F;QAC1H,2BAA2B,EAAE,0CAA0C;QACvE,4BAA4B,EAAE,wBAAwB;QACtD,gCAAgC,EAAE,uBAAuB;QACzD,4BAA4B,EAAE,qBAAqB;QACnD,8BAA8B,EAAE,oDAAoD;QACpF,+BAA+B,EAAE,eAAe;QAChD,yCAAyC,EAAE,sBAAsB;QACjE,0CAA0C,EAAE,8CAA8C;QAC1F,0CAA0C,EAAE,6CAA6C;QACzF,yCAAyC,EAAE,sBAAsB;QACjE,0CAA0C,EAAE,8CAA8C;QAC1F,6BAA6B,EAAE,0DAA0D;QACzF,2BAA2B,EAAE,gBAAgB;QAC7C,yBAAyB,EAAE,kBAAkB;QAC7C,2BAA2B,EAAE,SAAS;QAEtC;QACA,yBAAyB,EAAE,eAAe;QAC1C,+BAA+B,EAAE,qDAAqD;QACtF,2BAA2B,EAAE,oEAAoE;QACjG,iCAAiC,EAAE,iBAAiB;QACpD,0BAA0B,EAAE,SAAS;QACrC,kCAAkC,EAAE,iBAAiB;QACrD,0BAA0B,EAAE,QAAQ;QACpC,gCAAgC,EAAE;OACnC;MACDC,EAAE,EAAE;QACF;QACA,WAAW,EAAE,uBAAuB;QACpC,aAAa,EAAE,WAAW;QAC1B,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,WAAW;QAEhC;QACA,eAAe,EAAE,aAAa;QAC9B,yBAAyB,EAAE,eAAe;QAC1C,WAAW,EAAE,SAAS;QACtB,gBAAgB,EAAE,QAAQ;QAC1B,gBAAgB,EAAE,YAAY;QAC9B,oBAAoB,EAAE,eAAe;QACrC,sBAAsB,EAAE,gBAAgB;QACxC,eAAe,EAAE,WAAW;QAC5B,oBAAoB,EAAE,qBAAqB;QAC3C,mBAAmB,EAAE,iBAAiB;QACtC,sBAAsB,EAAE,kBAAkB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,qBAAqB,EAAE,mBAAmB;QAC1C,wBAAwB,EAAE,mBAAmB;QAC7C,eAAe,EAAE,SAAS;QAC1B,eAAe,EAAE,UAAU;QAC3B,yBAAyB,EAAE,iBAAiB;QAC5C,cAAc,EAAE,WAAW;QAC3B,uBAAuB,EAAE,eAAe;QACxC,kBAAkB,EAAE,SAAS;QAC7B,aAAa,EAAE,UAAU;QACzB,sBAAsB,EAAE,gBAAgB;QACxC,sBAAsB,EAAE,kBAAkB;QAC1C,kBAAkB,EAAE,iBAAiB;QACrC,qBAAqB,EAAE,kBAAkB;QACzC,cAAc,EAAE,WAAW;QAC3B,YAAY,EAAE,cAAc;QAE5B;QACA,iBAAiB,EAAE,aAAa;QAChC,sBAAsB,EAAE,iBAAiB;QACzC,0BAA0B,EAAE,kBAAkB;QAC9C,sBAAsB,EAAE,gBAAgB;QACxC,yBAAyB,EAAE,sBAAsB;QACjD,8BAA8B,EAAE,mBAAmB;QACnD,sBAAsB,EAAE,gBAAgB;QACxC,6BAA6B,EAAE,uBAAuB;QACtD,0BAA0B,EAAE,mBAAmB;QAE/C;QACA,eAAe,EAAE,KAAK;QACtB,YAAY,EAAE,OAAO;QACrB,aAAa,EAAE,OAAO;QACtB,eAAe,EAAE,KAAK;QACtB,aAAa,EAAE,KAAK;QACpB,eAAe,EAAE,OAAO;QACxB,gBAAgB,EAAE,OAAO;QACzB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,IAAI;QACjB,gBAAgB,EAAE,iBAAiB;QACnC,eAAe,EAAE,sBAAsB;QACvC,gBAAgB,EAAE,WAAW;QAC7B,eAAe,EAAE,QAAQ;QACzB,aAAa,EAAE,SAAS;QACxB,eAAe,EAAE,QAAQ;QACzB,cAAc,EAAE,UAAU;QAC1B,gBAAgB,EAAE,OAAO;QAEzB;QACA,kBAAkB,EAAE,cAAc;QAClC,qBAAqB,EAAE,iCAAiC;QACxD,qBAAqB,EAAE,cAAc;QACrC,gCAAgC,EAAE,mBAAmB;QACrD,qBAAqB,EAAE,aAAa;QACpC,gCAAgC,EAAE,kBAAkB;QACpD,uBAAuB,EAAE,QAAQ;QACjC,mBAAmB,EAAE,cAAc;QACnC,2BAA2B,EAAE,mBAAmB;QAChD,oBAAoB,EAAE,uBAAuB;QAC7C,wBAAwB,EAAE,wBAAwB;QAClD,2BAA2B,EAAE,oDAAoD;QACjF,8BAA8B,EAAE,8BAA8B;QAC9D,wBAAwB,EAAE,qBAAqB;QAC/C,0BAA0B,EAAE,qDAAqD;QACjF,qCAAqC,EAAE,uCAAuC;QAC9E,gCAAgC,EAAE,qDAAqD;QACvF,kCAAkC,EAAE,sDAAsD;QAC1F,0BAA0B,EAAE,eAAe;QAC3C,4BAA4B,EAAE,oEAAoE;QAClG,2BAA2B,EAAE,sCAAsC;QACnE,4BAA4B,EAAE,iBAAiB;QAC/C,gCAAgC,EAAE,8BAA8B;QAChE,4BAA4B,EAAE,uBAAuB;QACrD,8BAA8B,EAAE,qEAAqE;QACrG,+BAA+B,EAAE,0BAA0B;QAC3D,yCAAyC,EAAE,oBAAoB;QAC/D,0CAA0C,EAAE,iDAAiD;QAC7F,0CAA0C,EAAE,+CAA+C;QAC3F,yCAAyC,EAAE,oBAAoB;QAC/D,0CAA0C,EAAE,gDAAgD;QAC5F,6BAA6B,EAAE,gDAAgD;QAC/E,2BAA2B,EAAE,gBAAgB;QAC7C,yBAAyB,EAAE,gBAAgB;QAC3C,2BAA2B,EAAE,aAAa;QAE1C;QACA,yBAAyB,EAAE,eAAe;QAC1C,+BAA+B,EAAE,wCAAwC;QACzE,2BAA2B,EAAE,iDAAiD;QAC9E,iCAAiC,EAAE,wBAAwB;QAC3D,0BAA0B,EAAE,QAAQ;QACpC,kCAAkC,EAAE,gBAAgB;QACpD,0BAA0B,EAAE,cAAc;QAC1C,gCAAgC,EAAE;;KAErC;EACH;;;uBAhUW1D,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAA2D,OAAA,EAAf3D,eAAe,CAAA4D,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}