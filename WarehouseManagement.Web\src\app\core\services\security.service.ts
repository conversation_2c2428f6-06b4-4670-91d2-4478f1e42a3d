import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, timer } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  SecurityConfig,
  LoginAttempt,
  SecurityEvent,
  SecurityEventType,
  SecurityEventSeverity,
  CsrfToken,
  RateLimitInfo,
  ApiResponse
} from '../models/auth.models';
import { environment } from '../../../environments/environment';

interface RateLimitEntry {
  count: number;
  resetTime: Date;
  blocked: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SecurityService {
  private readonly API_URL = environment.apiUrl;
  private readonly STORAGE_PREFIX = 'security_';
  
  // Rate limiting storage
  private rateLimits = new Map<string, RateLimitEntry>();
  
  // Security configuration
  private securityConfigSubject = new BehaviorSubject<SecurityConfig>({
    maxLoginAttempts: environment.security.maxLoginAttempts,
    lockoutDuration: environment.security.lockoutDuration,
    tokenExpirationTime: environment.security.sessionTimeout,
    refreshTokenExpirationTime: 7, // days
    passwordMinLength: environment.security.passwordPolicy.minLength,
    passwordRequireSpecialChar: environment.security.passwordPolicy.requireSpecialChars,
    passwordRequireNumber: environment.security.passwordPolicy.requireNumbers,
    passwordRequireUppercase: environment.security.passwordPolicy.requireUppercase
  });

  public securityConfig$ = this.securityConfigSubject.asObservable();

  constructor(private http: HttpClient) {
    this.initializeSecurity();
  }

  /**
   * Initialize security service
   */
  private initializeSecurity(): void {
    this.loadSecurityConfig();
    this.startRateLimitCleanup();
    this.setupCSP();
  }

  /**
   * Load security configuration from backend
   */
  private loadSecurityConfig(): void {
    this.http.get<ApiResponse<SecurityConfig>>(`${this.API_URL}/security/config`)
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.securityConfigSubject.next(response.data);
          }
        },
        error: (error) => {
          console.warn('Failed to load security config, using defaults:', error);
        }
      });
  }

  /**
   * Check if action is rate limited
   */
  isRateLimited(action: string, identifier: string = 'global'): boolean {
    const key = `${action}_${identifier}`;
    const entry = this.rateLimits.get(key);
    
    if (!entry) {
      return false;
    }

    // Check if rate limit has expired
    if (new Date() > entry.resetTime) {
      this.rateLimits.delete(key);
      return false;
    }

    return entry.blocked;
  }

  /**
   * Record rate limit attempt
   */
  recordRateLimitAttempt(
    action: string, 
    identifier: string = 'global', 
    maxAttempts: number = 10,
    windowMinutes: number = 15
  ): RateLimitInfo {
    const key = `${action}_${identifier}`;
    const now = new Date();
    const resetTime = new Date(now.getTime() + windowMinutes * 60 * 1000);
    
    let entry = this.rateLimits.get(key);
    
    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 1,
        resetTime,
        blocked: false
      };
    } else {
      // Increment existing entry
      entry.count++;
    }

    // Check if limit exceeded
    if (entry.count > maxAttempts) {
      entry.blocked = true;
      this.logSecurityEvent(
        SecurityEventType.SUSPICIOUS_ACTIVITY,
        `Rate limit exceeded for ${action} by ${identifier}`,
        SecurityEventSeverity.HIGH
      );
    }

    this.rateLimits.set(key, entry);

    return {
      limit: maxAttempts,
      remaining: Math.max(0, maxAttempts - entry.count),
      resetTime: entry.resetTime
    };
  }

  /**
   * Get rate limit info
   */
  getRateLimitInfo(action: string, identifier: string = 'global'): RateLimitInfo | null {
    const key = `${action}_${identifier}`;
    const entry = this.rateLimits.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (new Date() > entry.resetTime) {
      this.rateLimits.delete(key);
      return null;
    }

    return {
      limit: 10, // Default limit, should be configurable
      remaining: Math.max(0, 10 - entry.count),
      resetTime: entry.resetTime
    };
  }

  /**
   * Record login attempt
   */
  recordLoginAttempt(username: string, success: boolean, ipAddress?: string): Observable<void> {
    const attempt: Partial<LoginAttempt> = {
      username,
      success,
      timestamp: new Date(),
      ipAddress: ipAddress || this.getClientIP(),
      userAgent: navigator.userAgent
    };

    return this.http.post<ApiResponse<void>>(`${this.API_URL}/security/login-attempts`, attempt)
      .pipe(
        map(response => {
          if (!response.success) {
            throw new Error(response.message || 'Failed to record login attempt');
          }
        }),
        tap(() => {
          // Also record locally for rate limiting
          if (!success) {
            this.recordRateLimitAttempt('login', username, 5, 15);
          }
        })
      );
  }

  /**
   * Get CSRF token
   */
  getCsrfToken(): Observable<CsrfToken> {
    return this.http.get<ApiResponse<CsrfToken>>(`${this.API_URL}/security/csrf-token`)
      .pipe(
        map(response => {
          if (!response.success || !response.data) {
            throw new Error('Failed to get CSRF token');
          }
          return response.data;
        }),
        tap(token => {
          // Store token for use in requests
          sessionStorage.setItem('csrf-token', token.token);
        })
      );
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password: string): { valid: boolean; errors: string[] } {
    const config = this.securityConfigSubject.value;
    const errors: string[] = [];

    if (password.length < config.passwordMinLength) {
      errors.push(`Password must be at least ${config.passwordMinLength} characters long`);
    }

    if (config.passwordRequireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (config.passwordRequireNumber && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (config.passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common weak passwords
    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein'];
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common and easily guessable');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Sanitize user input to prevent XSS
   */
  sanitizeInput(input: string): string {
    if (!input) return '';

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Log security event
   */
  logSecurityEvent(
    eventType: SecurityEventType,
    description: string,
    severity: SecurityEventSeverity = SecurityEventSeverity.LOW
  ): void {
    const event: Partial<SecurityEvent> = {
      eventType,
      description,
      severity,
      timestamp: new Date(),
      ipAddress: this.getClientIP(),
      userAgent: navigator.userAgent
    };

    // Send to backend
    this.http.post(`${this.API_URL}/security/events`, event).subscribe({
      error: (error) => console.error('Failed to log security event:', error)
    });

    // Also log locally for debugging
    console.log(`Security Event [${severity.toUpperCase()}]:`, description);
  }

  /**
   * Setup Content Security Policy
   */
  private setupCSP(): void {
    // This would typically be done on the server side
    // Here we can add some client-side security measures
    
    // Disable eval and similar dangerous functions
    if (typeof window !== 'undefined') {
      // Override eval to prevent XSS
      (window as any).eval = () => {
        throw new Error('eval() is disabled for security reasons');
      };

      // Monitor for suspicious activity
      this.monitorSuspiciousActivity();
    }
  }

  /**
   * Monitor for suspicious activity
   */
  private monitorSuspiciousActivity(): void {
    // Monitor for rapid form submissions
    let lastSubmissionTime = 0;
    document.addEventListener('submit', () => {
      const now = Date.now();
      if (now - lastSubmissionTime < 1000) { // Less than 1 second
        this.logSecurityEvent(
          SecurityEventType.SUSPICIOUS_ACTIVITY,
          'Rapid form submissions detected',
          SecurityEventSeverity.MEDIUM
        );
      }
      lastSubmissionTime = now;
    });

    // Monitor for console access (potential developer tools usage)
    let devtools = false;
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
        if (!devtools) {
          devtools = true;
          this.logSecurityEvent(
            SecurityEventType.SUSPICIOUS_ACTIVITY,
            'Developer tools opened',
            SecurityEventSeverity.LOW
          );
        }
      } else {
        devtools = false;
      }
    }, 500);
  }

  /**
   * Start periodic cleanup of expired rate limit entries
   */
  private startRateLimitCleanup(): void {
    timer(0, 60000).subscribe(() => { // Every minute
      const now = new Date();
      for (const [key, entry] of this.rateLimits.entries()) {
        if (now > entry.resetTime) {
          this.rateLimits.delete(key);
        }
      }
    });
  }

  /**
   * Get client IP address (best effort)
   */
  private getClientIP(): string {
    // This is a simplified implementation
    // In a real application, you'd get this from the server
    return 'client-ip';
  }

  /**
   * Get current security configuration
   */
  getSecurityConfig(): SecurityConfig {
    return this.securityConfigSubject.value;
  }

  /**
   * Check if feature is enabled based on security config
   */
  isFeatureEnabled(feature: string): boolean {
    // This would check against security configuration
    // For now, return true for all features
    return true;
  }
}
