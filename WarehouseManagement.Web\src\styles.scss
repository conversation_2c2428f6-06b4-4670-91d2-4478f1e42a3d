/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background-color: #f8f9fa;
}

/* Import RTL Styles */
@import './assets/styles/rtl.scss';

/* Custom PrimeNG Theme Overrides */
:root {
  --primary-color: #3b82f6;
  --primary-color-text: #ffffff;
  --surface-0: #ffffff;
  --surface-50: #f8f9fa;
  --surface-100: #e9ecef;
  --surface-200: #dee2e6;
  --surface-300: #ced4da;
  --surface-400: #adb5bd;
  --surface-500: #6c757d;
  --surface-600: #495057;
  --surface-700: #343a40;
  --surface-800: #212529;
  --surface-900: #121212;
  --text-color: #212529;
  --text-color-secondary: #6c757d;
  --border-radius: 6px;
}

/* Layout Utilities */
.layout-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-main {
  flex: 1;
  display: flex;
}

.layout-sidebar {
  width: 280px;
  background: var(--surface-0);
  border-right: 1px solid var(--surface-200);
  transition: all 0.3s ease;
}

.layout-content {
  flex: 1;
  padding: 1.5rem;
  overflow-x: auto;
}

.layout-topbar {
  background: var(--surface-0);
  border-bottom: 1px solid var(--surface-200);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Card Styles */
.card {
  background: var(--surface-0);
  border: 1px solid var(--surface-200);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-200);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* Button Styles */
.btn-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Table Styles */
.data-table {
  .p-datatable-header {
    background: var(--surface-50);
    border: 1px solid var(--surface-200);
    padding: 1rem;
  }

  .p-datatable-thead > tr > th {
    background: var(--surface-50);
    color: var(--text-color);
    font-weight: 600;
    border: 1px solid var(--surface-200);
    padding: 0.75rem;
  }

  .p-datatable-tbody > tr > td {
    border: 1px solid var(--surface-200);
    padding: 0.75rem;
  }

  .p-datatable-tbody > tr:nth-child(even) {
    background: var(--surface-50);
  }
}

/* Form Styles */
.form-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 500;
  color: var(--text-color);
}

.form-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-200);
}

/* Responsive Design */
@media (max-width: 768px) {
  .layout-sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .layout-sidebar.active {
    left: 0;
  }

  .layout-content {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .btn-group {
    width: 100%;
    justify-content: stretch;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .layout-content {
    padding: 0.75rem;
  }

  .card {
    padding: 1rem;
  }

  .layout-topbar {
    padding: 0.75rem 1rem;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.align-items-center { align-items: center; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
.gap-4 { gap: 1.5rem; }
