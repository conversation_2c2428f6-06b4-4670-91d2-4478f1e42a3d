/* RTL (Right-to-Left) Styles for Arabic Language Support */

/* Global RTL Styles */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* PrimeNG RTL Overrides */
.rtl {
  /* Dropdown */
  .p-dropdown {
    .p-dropdown-trigger {
      right: auto;
      left: 0.75rem;
    }
    
    .p-dropdown-label {
      padding-right: 0.75rem;
      padding-left: 2.25rem;
      text-align: right;
    }
  }

  /* Input Text */
  .p-inputtext {
    text-align: right;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
  }

  /* Button */
  .p-button {
    .p-button-icon-left {
      margin-right: 0;
      margin-left: 0.5rem;
    }
    
    .p-button-icon-right {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }

  /* Table */
  .p-datatable {
    .p-datatable-thead > tr > th {
      text-align: right;
    }
    
    .p-datatable-tbody > tr > td {
      text-align: right;
    }
    
    .p-sortable-column {
      .p-sortable-column-icon {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }
    
    .p-column-filter {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }

  /* Menu */
  .p-menubar {
    .p-menubar-root-list {
      flex-direction: row-reverse;
    }
    
    .p-menuitem-link {
      .p-menuitem-icon {
        margin-right: 0;
        margin-left: 0.5rem;
      }
    }
  }

  .p-panelmenu {
    .p-panelmenu-header-link {
      .p-panelmenu-icon {
        margin-right: 0;
        margin-left: 0.5rem;
      }
    }
  }

  /* Sidebar */
  .p-sidebar {
    &.p-sidebar-left {
      right: 0;
      left: auto;
    }
    
    &.p-sidebar-right {
      left: 0;
      right: auto;
    }
  }

  /* Toast */
  .p-toast {
    &.p-toast-top-right {
      top: 20px;
      right: auto;
      left: 20px;
    }
    
    &.p-toast-top-left {
      top: 20px;
      left: auto;
      right: 20px;
    }
    
    &.p-toast-bottom-right {
      bottom: 20px;
      right: auto;
      left: 20px;
    }
    
    &.p-toast-bottom-left {
      bottom: 20px;
      left: auto;
      right: 20px;
    }
  }

  /* Dialog */
  .p-dialog {
    .p-dialog-header {
      .p-dialog-header-icons {
        margin-left: 0;
        margin-right: auto;
      }
    }
  }

  /* Toolbar */
  .p-toolbar {
    .p-toolbar-group-left {
      margin-left: auto;
      margin-right: 0;
    }
    
    .p-toolbar-group-right {
      margin-right: auto;
      margin-left: 0;
    }
  }

  /* Card */
  .p-card {
    .p-card-header {
      text-align: right;
    }
    
    .p-card-title {
      text-align: right;
    }
    
    .p-card-subtitle {
      text-align: right;
    }
    
    .p-card-content {
      text-align: right;
    }
  }

  /* Form Layout */
  .p-field {
    .p-field-label {
      text-align: right;
      margin-bottom: 0.5rem;
    }
  }

  .p-formgrid {
    .p-field {
      text-align: right;
    }
  }

  /* Flex utilities for RTL */
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  
  .justify-content-end {
    justify-content: flex-start;
  }
  
  .justify-content-start {
    justify-content: flex-end;
  }
  
  .text-left {
    text-align: right;
  }
  
  .text-right {
    text-align: left;
  }

  /* Custom Layout Adjustments */
  .layout-sidebar {
    right: 0;
    left: auto;
  }

  .layout-main-container {
    margin-right: 250px;
    margin-left: 0;
  }

  .layout-topbar {
    .layout-topbar-logo {
      margin-right: 0;
      margin-left: 2rem;
    }
    
    .layout-topbar-menu {
      margin-left: 0;
      margin-right: auto;
    }
  }

  /* Navigation adjustments */
  .breadcrumb {
    .breadcrumb-item {
      &::before {
        content: "\\";
        margin: 0 0.5rem;
      }
    }
  }

  /* Progress Bar */
  .p-progressbar {
    .p-progressbar-value {
      right: 0;
      left: auto;
    }
  }

  /* Tag */
  .p-tag {
    .p-tag-icon {
      margin-right: 0;
      margin-left: 0.25rem;
    }
  }

  /* Chart adjustments */
  .chart-container {
    .chart-legend {
      text-align: right;
    }
  }
}

/* Font adjustments for Arabic */
.rtl {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
  
  /* Arabic-specific font stack */
  &.arabic-font {
    font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Lucida Sans Unicode', sans-serif;
  }
  
  /* Improve Arabic text rendering */
  .arabic-text {
    font-feature-settings: "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Responsive RTL adjustments */
@media screen and (max-width: 768px) {
  .rtl {
    .layout-sidebar {
      right: -250px;
      left: auto;
    }
    
    .layout-sidebar-active .layout-sidebar {
      right: 0;
    }
    
    .layout-main-container {
      margin-right: 0;
      margin-left: 0;
    }
  }
}

/* Animation adjustments for RTL */
.rtl {
  .slide-in-left {
    animation: slideInRight 0.3s ease-in-out;
  }
  
  .slide-in-right {
    animation: slideInLeft 0.3s ease-in-out;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
