{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CardModule } from 'primeng/card';\nimport { ChartModule } from 'primeng/chart';\nimport { TableModule } from 'primeng/table';\nimport { TagModule } from 'primeng/tag';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./dashboard.service\";\nimport * as i2 from \"@core/services/language.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/card\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/chart\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/tag\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/progressbar\";\nimport * as i11 from \"primeng/progressspinner\";\nfunction DashboardComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Sales Trend\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Inventory Distribution\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Monthly Revenue & Profit\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Low Stock Items\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Item\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Current Stock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Min Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵelement(11, \"p-progressBar\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.currentStock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.minimumLevel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", item_r1.currentStock / item_r1.minimumLevel * 100)(\"showValue\", false)(\"ngClass\", \"progress-\" + ctx_r1.getStockLevelSeverity(item_r1.currentStock / item_r1.minimumLevel * 100));\n  }\n}\nfunction DashboardComponent_div_6_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Recent Transactions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵelement(5, \"p-tag\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelement(10, \"p-tag\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const transaction_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 6, transaction_r3.date, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", transaction_r3.type)(\"severity\", ctx_r1.getTransactionTypeSeverity(transaction_r3.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 9, transaction_r3.amount));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", transaction_r3.status)(\"severity\", transaction_r3.status === \"Completed\" ? \"success\" : \"warning\");\n  }\n}\nfunction DashboardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"p-card\", 10)(4, \"div\", 11)(5, \"div\")(6, \"div\", 12);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 13);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 14);\n    i0.ɵɵelement(12, \"i\", 15);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 9)(14, \"p-card\", 10)(15, \"div\", 11)(16, \"div\")(17, \"div\", 12);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 16);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 17);\n    i0.ɵɵelement(22, \"i\", 18);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 9)(24, \"p-card\", 10)(25, \"div\", 11)(26, \"div\")(27, \"div\", 12);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 19);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 20);\n    i0.ɵɵelement(33, \"i\", 21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 9)(35, \"p-card\", 10)(36, \"div\", 11)(37, \"div\")(38, \"div\", 12);\n    i0.ɵɵtext(39, \"Pending Orders\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 22);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 23);\n    i0.ɵɵelement(43, \"i\", 24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(44, \"div\", 8)(45, \"div\", 25)(46, \"p-card\");\n    i0.ɵɵtemplate(47, DashboardComponent_div_6_ng_template_47_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelement(48, \"p-chart\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"div\", 28)(50, \"p-card\");\n    i0.ɵɵtemplate(51, DashboardComponent_div_6_ng_template_51_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelement(52, \"p-chart\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 8)(54, \"div\", 30)(55, \"p-card\");\n    i0.ɵɵtemplate(56, DashboardComponent_div_6_ng_template_56_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelement(57, \"p-chart\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 8)(59, \"div\", 32)(60, \"p-card\");\n    i0.ɵɵtemplate(61, DashboardComponent_div_6_ng_template_61_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelementStart(62, \"p-table\", 33);\n    i0.ɵɵtemplate(63, DashboardComponent_div_6_ng_template_63_Template, 9, 0, \"ng-template\", 26)(64, DashboardComponent_div_6_ng_template_64_Template, 12, 7, \"ng-template\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 32)(66, \"p-card\");\n    i0.ɵɵtemplate(67, DashboardComponent_div_6_ng_template_67_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelementStart(68, \"p-table\", 33);\n    i0.ɵɵtemplate(69, DashboardComponent_div_6_ng_template_69_Template, 9, 0, \"ng-template\", 26)(70, DashboardComponent_div_6_ng_template_70_Template, 11, 11, \"ng-template\", 34);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"dashboard.totalItems\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 19, ctx_r1.dashboardData.totalItems));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"dashboard.lowStockItems\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dashboardData.lowStockItems.length);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"dashboard.monthlyRevenue\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 21, ctx_r1.dashboardData.totalRevenue));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.dashboardData.pendingOrders);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"data\", ctx_r1.salesChartData)(\"options\", ctx_r1.salesChartOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r1.inventoryChartData)(\"options\", ctx_r1.inventoryChartOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r1.revenueChartData)(\"options\", ctx_r1.revenueChartOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r1.dashboardData.lowStockItems)(\"paginator\", true)(\"rows\", 5);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r1.dashboardData.recentTransactions)(\"paginator\", true)(\"rows\", 5);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor(dashboardService, languageService) {\n      this.dashboardService = dashboardService;\n      this.languageService = languageService;\n      this.dashboardData = null;\n      this.loading = true;\n    }\n    ngOnInit() {\n      this.loadDashboardData();\n      this.initializeChartOptions();\n    }\n    loadDashboardData() {\n      this.loading = true;\n      this.dashboardService.getDashboardData().subscribe({\n        next: data => {\n          this.dashboardData = data;\n          this.setupCharts();\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading dashboard data:', error);\n          this.loading = false;\n        }\n      });\n    }\n    initializeChartOptions() {\n      const documentStyle = getComputedStyle(document.documentElement);\n      const textColor = documentStyle.getPropertyValue('--text-color');\n      const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n      const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n      this.salesChartOptions = {\n        maintainAspectRatio: false,\n        aspectRatio: 0.8,\n        plugins: {\n          legend: {\n            labels: {\n              color: textColor\n            }\n          }\n        },\n        scales: {\n          x: {\n            ticks: {\n              color: textColorSecondary,\n              font: {\n                weight: 500\n              }\n            },\n            grid: {\n              color: surfaceBorder,\n              drawBorder: false\n            }\n          },\n          y: {\n            ticks: {\n              color: textColorSecondary\n            },\n            grid: {\n              color: surfaceBorder,\n              drawBorder: false\n            }\n          }\n        }\n      };\n      this.inventoryChartOptions = {\n        plugins: {\n          legend: {\n            labels: {\n              usePointStyle: true,\n              color: textColor\n            }\n          }\n        }\n      };\n      this.revenueChartOptions = {\n        maintainAspectRatio: false,\n        aspectRatio: 0.6,\n        plugins: {\n          legend: {\n            labels: {\n              color: textColor\n            }\n          }\n        },\n        scales: {\n          x: {\n            ticks: {\n              color: textColorSecondary\n            },\n            grid: {\n              color: surfaceBorder\n            }\n          },\n          y: {\n            ticks: {\n              color: textColorSecondary\n            },\n            grid: {\n              color: surfaceBorder\n            }\n          }\n        }\n      };\n    }\n    setupCharts() {\n      if (!this.dashboardData) return;\n      // Sales Chart\n      this.salesChartData = {\n        labels: this.dashboardData.salesTrend.map(s => s.period),\n        datasets: [{\n          label: 'Sales',\n          data: this.dashboardData.salesTrend.map(s => s.amount),\n          fill: false,\n          backgroundColor: '#3b82f6',\n          borderColor: '#3b82f6',\n          tension: 0.4\n        }]\n      };\n      // Inventory Chart\n      this.inventoryChartData = {\n        labels: ['Raw Materials', 'Semi-Finished', 'Finished Products', 'Consumables'],\n        datasets: [{\n          data: [this.dashboardData.inventorySummary.rawMaterials, this.dashboardData.inventorySummary.semiFinished, this.dashboardData.inventorySummary.finishedProducts, this.dashboardData.inventorySummary.consumables],\n          backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],\n          hoverBackgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626']\n        }]\n      };\n      // Revenue Chart\n      this.revenueChartData = {\n        labels: this.dashboardData.monthlyRevenue.map(r => r.month),\n        datasets: [{\n          type: 'line',\n          label: 'Revenue',\n          borderColor: '#3b82f6',\n          borderWidth: 2,\n          fill: false,\n          tension: 0.4,\n          data: this.dashboardData.monthlyRevenue.map(r => r.revenue)\n        }, {\n          type: 'bar',\n          label: 'Profit',\n          backgroundColor: '#10b981',\n          data: this.dashboardData.monthlyRevenue.map(r => r.profit)\n        }]\n      };\n    }\n    getStockLevelSeverity(percentage) {\n      if (percentage <= 20) return 'danger';\n      if (percentage <= 50) return 'warning';\n      return 'success';\n    }\n    getTransactionTypeSeverity(type) {\n      switch (type.toLowerCase()) {\n        case 'sale':\n          return 'success';\n        case 'purchase':\n          return 'info';\n        case 'return':\n          return 'warning';\n        case 'adjustment':\n          return 'secondary';\n        default:\n          return 'info';\n      }\n    }\n    refreshDashboard() {\n      this.loadDashboardData();\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.DashboardService), i0.ɵɵdirectiveInject(i2.LanguageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 7,\n        vars: 5,\n        consts: [[1, \"dashboard-container\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"m-0\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-refresh\", 1, \"p-button-outlined\", 3, \"click\", \"label\", \"loading\"], [\"class\", \"flex justify-content-center align-items-center\", \"style\", \"height: 400px;\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"400px\"], [1, \"dashboard-content\"], [1, \"grid\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [\"styleClass\", \"metric-card\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-500\", \"font-medium\", \"mb-2\"], [1, \"text-2xl\", \"font-bold\", \"text-900\"], [1, \"metric-icon\", \"bg-blue-100\", \"text-blue-600\"], [1, \"pi\", \"pi-box\", \"text-2xl\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-600\"], [1, \"metric-icon\", \"bg-orange-100\", \"text-orange-600\"], [1, \"pi\", \"pi-exclamation-triangle\", \"text-2xl\"], [1, \"text-2xl\", \"font-bold\", \"text-green-600\"], [1, \"metric-icon\", \"bg-green-100\", \"text-green-600\"], [1, \"pi\", \"pi-dollar\", \"text-2xl\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-600\"], [1, \"metric-icon\", \"bg-purple-100\", \"text-purple-600\"], [1, \"pi\", \"pi-clock\", \"text-2xl\"], [1, \"col-12\", \"lg:col-8\"], [\"pTemplate\", \"header\"], [\"type\", \"line\", \"height\", \"300px\", 3, \"data\", \"options\"], [1, \"col-12\", \"lg:col-4\"], [\"type\", \"doughnut\", \"height\", \"300px\", 3, \"data\", \"options\"], [1, \"col-12\"], [\"type\", \"bar\", \"height\", \"400px\", 3, \"data\", \"options\"], [1, \"col-12\", \"lg:col-6\"], [\"styleClass\", \"p-datatable-sm\", 3, \"value\", \"paginator\", \"rows\"], [\"pTemplate\", \"body\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"font-medium\"], [1, \"text-sm\", \"text-500\"], [\"styleClass\", \"w-full\", 3, \"value\", \"showValue\", \"ngClass\"], [3, \"value\", \"severity\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_4_listener() {\n              return ctx.refreshDashboard();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(5, DashboardComponent_div_5_Template, 2, 0, \"div\", 4)(6, DashboardComponent_div_6_Template, 71, 23, \"div\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.languageService.translate(\"dashboard.title\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"label\", ctx.languageService.translate(\"common.refresh\"))(\"loading\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardData);\n          }\n        },\n        dependencies: [CommonModule, i3.NgClass, i3.NgIf, i3.DecimalPipe, i3.CurrencyPipe, i3.DatePipe, CardModule, i4.Card, i5.PrimeTemplate, ChartModule, i6.UIChart, TableModule, i7.Table, TagModule, i8.Tag, ButtonModule, i9.ButtonDirective, ProgressBarModule, i10.ProgressBar, ProgressSpinnerModule, i11.ProgressSpinner],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{padding:0}.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]{margin:0}.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:.75rem}.metric-card[_ngcontent-%COMP%]{border:1px solid var(--surface-200);border-radius:var(--border-radius);transition:all .2s}.metric-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #0000001a;transform:translateY(-2px)}.metric-card[_ngcontent-%COMP%]     .p-card-body{padding:1.5rem}.metric-icon[_ngcontent-%COMP%]{width:3rem;height:3rem;border-radius:50%;display:flex;align-items:center;justify-content:center}.card-header[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid var(--surface-200);background:var(--surface-50);margin:-1.5rem -1.5rem 1.5rem;border-radius:var(--border-radius) var(--border-radius) 0 0}.card-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:var(--text-color);margin:0}  .p-chart .p-chart-canvas{max-height:400px}  .p-datatable-sm .p-datatable-thead>tr>th{padding:.5rem;font-size:.875rem;font-weight:600}  .p-datatable-sm .p-datatable-tbody>tr>td{padding:.5rem;font-size:.875rem}  .p-progressbar{height:.5rem}  .p-progressbar .p-progressbar-value{border-radius:.25rem}@media (max-width: 768px){.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:.5rem}.metric-card[_ngcontent-%COMP%]     .p-card-body{padding:1rem}.card-header[_ngcontent-%COMP%]{padding:.75rem 1rem;margin:-1rem -1rem 1rem}.card-title[_ngcontent-%COMP%]{font-size:1rem}.metric-icon[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem}.metric-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem!important}}@media (max-width: 480px){.dashboard-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem!important}.flex.justify-content-between[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important;gap:1rem}.metric-card[_ngcontent-%COMP%]   .flex.justify-content-between[_ngcontent-%COMP%]{flex-direction:row!important;align-items:center!important;gap:0}}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}