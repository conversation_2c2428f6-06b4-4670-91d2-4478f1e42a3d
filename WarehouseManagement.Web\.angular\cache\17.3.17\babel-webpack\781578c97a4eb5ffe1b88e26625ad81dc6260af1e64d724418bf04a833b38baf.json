{"ast": null, "code": "import { Chart, registerables } from '../dist/chart.js';\nChart.register(...registerables);\nexport * from '../dist/chart.js';\nexport default Chart;", "map": {"version": 3, "names": ["Chart", "registerables", "register"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/chart.js/auto/auto.js"], "sourcesContent": ["import {Chart, registerables} from '../dist/chart.js';\n\nChart.register(...registerables);\n\nexport * from '../dist/chart.js';\nexport default Chart;\n"], "mappings": "AAAA,SAAQA,KAAK,EAAEC,aAAa,QAAO,kBAAkB;AAErDD,KAAK,CAACE,QAAQ,CAAC,GAAGD,aAAa,CAAC;AAEhC,cAAc,kBAAkB;AAChC,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}