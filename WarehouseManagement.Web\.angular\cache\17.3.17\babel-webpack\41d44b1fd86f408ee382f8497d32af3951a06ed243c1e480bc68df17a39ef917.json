{"ast": null, "code": "export const observable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();", "map": {"version": 3, "names": ["observable", "Symbol"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/rxjs/dist/esm/internal/symbol/observable.js"], "sourcesContent": ["export const observable = (() => (typeof Symbol === 'function' && Symbol.observable) || '@@observable')();\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG,CAAC,MAAO,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACD,UAAU,IAAK,cAAc,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}