import { 
  Directive, 
  Input, 
  TemplateRef, 
  ViewContainerRef, 
  OnInit, 
  OnDestroy 
} from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { AuthorizationService, PermissionCheck } from '../services/authorization.service';
import { AuthService } from '../services/auth.service';

@Directive({
  selector: '[appHasPermission]'
})
export class HasPermissionDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private hasView = false;

  @Input() set appHasPermission(permission: string | PermissionCheck | PermissionCheck[]) {
    this.checkPermission(permission);
  }

  @Input() appHasPermissionResource?: string;
  @Input() appHasPermissionAction?: string;
  @Input() appHasPermissionRequireAll?: boolean = true;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authorizationService: AuthorizationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Listen for authentication state changes
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateView();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private checkPermission(permission: string | PermissionCheck | PermissionCheck[]): void {
    this.updateView();
  }

  private updateView(): void {
    const hasPermission = this.evaluatePermission();

    if (hasPermission && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasPermission && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }

  private evaluatePermission(): boolean {
    // If user is not authenticated, deny access
    if (!this.authService.isAuthenticated()) {
      return false;
    }

    // Handle different input types
    const permissionInput = this.getPermissionInput();

    if (typeof permissionInput === 'string') {
      // Simple string permission check (resource:action format)
      return this.checkStringPermission(permissionInput);
    }

    if (Array.isArray(permissionInput)) {
      // Multiple permissions
      return this.checkMultiplePermissions(permissionInput);
    }

    if (typeof permissionInput === 'object') {
      // Single permission object
      return this.checkSinglePermission(permissionInput);
    }

    return false;
  }

  private getPermissionInput(): string | PermissionCheck | PermissionCheck[] {
    // Priority: direct input > resource/action combination
    if (this.appHasPermissionResource && this.appHasPermissionAction) {
      return {
        resource: this.appHasPermissionResource,
        action: this.appHasPermissionAction
      };
    }

    // This will be set by the @Input setter
    return '';
  }

  private checkStringPermission(permission: string): boolean {
    // Handle different string formats
    if (permission.includes(':')) {
      // Format: "resource:action"
      const [resource, action] = permission.split(':');
      return this.authorizationService.hasPermission(resource, action);
    }

    if (permission.includes('.')) {
      // Format: "resource.action"
      const [resource, action] = permission.split('.');
      return this.authorizationService.hasPermission(resource, action);
    }

    // Assume it's a role name
    return this.authorizationService.hasRole(permission);
  }

  private checkSinglePermission(permission: PermissionCheck): boolean {
    return this.authorizationService.hasPermission(permission.resource, permission.action);
  }

  private checkMultiplePermissions(permissions: PermissionCheck[]): boolean {
    if (this.appHasPermissionRequireAll) {
      // User must have ALL permissions (AND logic)
      return this.authorizationService.hasAllPermissions(permissions);
    } else {
      // User must have ANY permission (OR logic)
      return this.authorizationService.hasAnyPermission(permissions);
    }
  }
}

@Directive({
  selector: '[appHasRole]'
})
export class HasRoleDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private hasView = false;

  @Input() set appHasRole(roles: string | string[]) {
    this.checkRole(roles);
  }

  @Input() appHasRoleRequireAll?: boolean = false;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authorizationService: AuthorizationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Listen for authentication state changes
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateView();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private checkRole(roles: string | string[]): void {
    this.updateView();
  }

  private updateView(): void {
    const hasRole = this.evaluateRole();

    if (hasRole && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasRole && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }

  private evaluateRole(): boolean {
    // If user is not authenticated, deny access
    if (!this.authService.isAuthenticated()) {
      return false;
    }

    const roleInput = this.getRoleInput();

    if (typeof roleInput === 'string') {
      return this.authorizationService.hasRole(roleInput);
    }

    if (Array.isArray(roleInput)) {
      if (this.appHasRoleRequireAll) {
        // User must have ALL roles
        return roleInput.every(role => this.authorizationService.hasRole(role));
      } else {
        // User must have ANY role
        return roleInput.some(role => this.authorizationService.hasRole(role));
      }
    }

    return false;
  }

  private getRoleInput(): string | string[] {
    // This will be set by the @Input setter
    return '';
  }
}

@Directive({
  selector: '[appIsAuthenticated]'
})
export class IsAuthenticatedDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private hasView = false;

  @Input() set appIsAuthenticated(show: boolean) {
    this.showWhenAuthenticated = show;
    this.updateView();
  }

  private showWhenAuthenticated = true;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Listen for authentication state changes
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateView();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateView(): void {
    const isAuthenticated = this.authService.isAuthenticated();
    const shouldShow = this.showWhenAuthenticated ? isAuthenticated : !isAuthenticated;

    if (shouldShow && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!shouldShow && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }
}
