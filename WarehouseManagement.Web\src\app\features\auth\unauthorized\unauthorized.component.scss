.unauthorized-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;

  &.rtl {
    direction: rtl;
  }
}

.unauthorized-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 20px;
  padding: 3rem 2rem;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ef4444, #dc2626);
  }
}

.error-icon {
  margin-bottom: 2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.error-message {
  margin-bottom: 2rem;

  .error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #dc2626;
    margin: 0 0 1rem 0;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .error-description {
    font-size: 1.1rem;
    color: var(--text-color-secondary);
    margin: 0;
    line-height: 1.6;
  }
}

.error-details {
  margin-bottom: 2.5rem;

  ::ng-deep .p-message {
    margin: 0;
    border-radius: 12px;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;

  button {
    height: 3rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-2px);
    }

    &.p-button-primary {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border: none;

      &:hover {
        box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
      }
    }

    &.p-button-secondary {
      background: linear-gradient(135deg, #6b7280, #4b5563);
      border: none;

      &:hover {
        box-shadow: 0 10px 25px rgba(107, 114, 128, 0.3);
      }
    }

    &.p-button-help {
      background: linear-gradient(135deg, #10b981, #059669);
      border: none;

      &:hover {
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
      }
    }

    &.p-button-danger {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      border: none;

      &:hover {
        box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
      }
    }

    // Shine effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }
}

.auto-redirect-notice {
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-200);

  .redirect-text {
    color: var(--text-color-secondary);
    font-size: 0.9rem;
    margin: 0;
    font-style: italic;
  }
}

// Responsive Design
@media (min-width: 768px) {
  .action-buttons {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;

    button {
      flex: 0 0 auto;
      min-width: 180px;
    }
  }
}

@media (max-width: 480px) {
  .unauthorized-container {
    padding: 1rem;
  }

  .unauthorized-content {
    padding: 2rem 1rem;
  }

  .error-message {
    .error-title {
      font-size: 2rem;
    }

    .error-description {
      font-size: 1rem;
    }
  }

  .action-buttons {
    button {
      height: 2.5rem;
      font-size: 0.9rem;
    }
  }
}

// Dark theme support
:host-context(.dark-theme) {
  .unauthorized-content {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .error-message {
    .error-description {
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .unauthorized-content {
    background: white;
    border: 2px solid black;
  }

  .action-buttons button {
    border: 2px solid currentColor;
  }
}
