import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Language {
  code: string;
  name: string;
  direction: 'ltr' | 'rtl';
  flag: string;
}

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private readonly STORAGE_KEY = 'selected-language';
  
  private readonly languages: Language[] = [
    {
      code: 'en',
      name: 'English',
      direction: 'ltr',
      flag: '🇺🇸'
    },
    {
      code: 'ar',
      name: 'العربية',
      direction: 'rtl',
      flag: '🇸🇦'
    }
  ];

  private currentLanguageSubject = new BehaviorSubject<Language>(this.getDefaultLanguage());
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  constructor() {
    this.initializeLanguage();
  }

  private getDefaultLanguage(): Language {
    const savedLanguage = localStorage.getItem(this.STORAGE_KEY);
    if (savedLanguage) {
      const found = this.languages.find(lang => lang.code === savedLanguage);
      if (found) return found;
    }
    
    // Default to Arabic
    return this.languages.find(lang => lang.code === 'ar') || this.languages[0];
  }

  private initializeLanguage(): void {
    const currentLang = this.currentLanguageSubject.value;
    this.applyLanguageSettings(currentLang);
  }

  getCurrentLanguage(): Language {
    return this.currentLanguageSubject.value;
  }

  getAvailableLanguages(): Language[] {
    return [...this.languages];
  }

  setLanguage(languageCode: string): void {
    const language = this.languages.find(lang => lang.code === languageCode);
    if (language && language.code !== this.currentLanguageSubject.value.code) {
      this.currentLanguageSubject.next(language);
      localStorage.setItem(this.STORAGE_KEY, languageCode);
      this.applyLanguageSettings(language);
    }
  }

  toggleLanguage(): void {
    const currentCode = this.currentLanguageSubject.value.code;
    const newCode = currentCode === 'en' ? 'ar' : 'en';
    this.setLanguage(newCode);
  }

  private applyLanguageSettings(language: Language): void {
    // Set document direction
    document.documentElement.dir = language.direction;
    document.documentElement.lang = language.code;
    
    // Add/remove RTL class to body for styling
    if (language.direction === 'rtl') {
      document.body.classList.add('rtl');
      document.body.classList.remove('ltr');
    } else {
      document.body.classList.add('ltr');
      document.body.classList.remove('rtl');
    }

    // Update PrimeNG configuration for RTL
    this.updatePrimeNGDirection(language.direction);
  }

  private updatePrimeNGDirection(direction: 'ltr' | 'rtl'): void {
    // This will be used to configure PrimeNG components for RTL
    const primeNGConfig = (window as any).PrimeNG?.config;
    if (primeNGConfig) {
      primeNGConfig.ripple = true;
      primeNGConfig.inputStyle = 'outlined';
      primeNGConfig.rtl = direction === 'rtl';
    }
  }

  isRTL(): boolean {
    return this.currentLanguageSubject.value.direction === 'rtl';
  }

  isArabic(): boolean {
    return this.currentLanguageSubject.value.code === 'ar';
  }

  // Translation helper methods
  translate(key: string, params?: any): string {
    // This is a simple implementation. In a real app, you'd use Angular i18n or ngx-translate
    const translations = this.getTranslations();
    const currentLang = this.getCurrentLanguage().code;
    
    let translation = translations[currentLang]?.[key] || translations['en']?.[key] || key;
    
    // Simple parameter replacement
    if (params) {
      Object.keys(params).forEach(param => {
        translation = translation.replace(`{{${param}}}`, params[param]);
      });
    }
    
    return translation;
  }

  private getTranslations(): any {
    return {
      en: {
        // Navigation
        'nav.dashboard': 'Dashboard',
        'nav.items': 'Items',
        'nav.categories': 'Categories',
        'nav.warehouses': 'Warehouses',
        'nav.inventory': 'Inventory',
        'nav.customers': 'Customers',
        'nav.suppliers': 'Suppliers',
        'nav.invoices': 'Invoices',
        'nav.payments': 'Payments',
        'nav.reports': 'Reports',
        'nav.settings': 'Settings',
        'nav.logout': 'Logout',
        
        // Dashboard
        'dashboard.title': 'Dashboard',
        'dashboard.totalSales': 'Total Sales',
        'dashboard.totalPurchases': 'Total Purchases',
        'dashboard.totalItems': 'Total Items',
        'dashboard.lowStockItems': 'Low Stock Items',
        'dashboard.recentTransactions': 'Recent Transactions',
        'dashboard.salesTrend': 'Sales Trend',
        'dashboard.inventoryOverview': 'Inventory Overview',
        'dashboard.monthlyRevenue': 'Monthly Revenue',
        
        // Common
        'common.search': 'Search',
        'common.add': 'Add',
        'common.edit': 'Edit',
        'common.delete': 'Delete',
        'common.save': 'Save',
        'common.cancel': 'Cancel',
        'common.confirm': 'Confirm',
        'common.yes': 'Yes',
        'common.no': 'No',
        'common.loading': 'Loading...',
        'common.noData': 'No data available',
        'common.actions': 'Actions',
        'common.status': 'Status',
        'common.date': 'Date',
        'common.amount': 'Amount',
        'common.total': 'Total',
        'common.refresh': 'Refresh',

        // Authentication
        'auth.login.title': 'Sign In',
        'auth.login.subtitle': 'Welcome to Warehouse Management System',
        'auth.login.username': 'Username',
        'auth.login.usernamePlaceholder': 'Enter your username',
        'auth.login.password': 'Password',
        'auth.login.passwordPlaceholder': 'Enter your password',
        'auth.login.rememberMe': 'Remember me',
        'auth.login.signIn': 'Sign In',
        'auth.login.forgotPassword': 'Forgot password?',
        'auth.login.success': 'Login successful',
        'auth.login.welcomeBack': 'Welcome back {{name}}',
        'auth.login.securityNotice': 'Your session is protected with SSL encryption and advanced security features',
        'auth.login.attemptsRemaining': 'Attempts remaining: {{attempts}}',
        'auth.login.error.title': 'Login Error',
        'auth.login.error.generic': 'An error occurred during login. Please try again.',
        'auth.login.error.invalidCredentials': 'Invalid username or password',
        'auth.login.error.accountLocked': 'Account temporarily locked due to multiple failed login attempts',
        'auth.login.error.tooManyAttempts': 'Too many attempts. Please try again later',
        'auth.login.lockout.title': 'Account Locked',
        'auth.login.lockout.message': 'Your account has been locked for {{minutes}} minutes due to multiple failed login attempts',
        'auth.login.lockout.active': 'Account locked. Time remaining: {{time}}',
        'auth.login.lockout.expired': 'Lockout period expired',
        'auth.login.lockout.canTryAgain': 'You can now try again',
        'auth.login.rateLimit.title': 'Rate Limit Exceeded',
        'auth.login.rateLimit.message': 'Too many attempts. Please wait before trying again',
        'auth.login.validation.invalid': 'Invalid value',
        'auth.login.validation.username.required': 'Username is required',
        'auth.login.validation.username.minLength': 'Username must be at least {{min}} characters',
        'auth.login.validation.username.maxLength': 'Username must be at most {{max}} characters',
        'auth.login.validation.password.required': 'Password is required',
        'auth.login.validation.password.minLength': 'Password must be at least {{min}} characters',
        'auth.login.footer.copyright': '© 2024 Warehouse Management System. All rights reserved.',
        'auth.login.footer.privacy': 'Privacy Policy',
        'auth.login.footer.terms': 'Terms of Service',
        'auth.login.footer.support': 'Support',

        // Unauthorized
        'auth.unauthorized.title': 'Access Denied',
        'auth.unauthorized.description': 'You do not have permission to access this resource.',
        'auth.unauthorized.details': 'Please contact your administrator if you believe this is an error.',
        'auth.unauthorized.goToDashboard': 'Go to Dashboard',
        'auth.unauthorized.goBack': 'Go Back',
        'auth.unauthorized.contactSupport': 'Contact Support',
        'auth.unauthorized.logout': 'Logout',
        'auth.unauthorized.autoRedirect': 'You will be redirected to the dashboard in 10 seconds.',

        // Security Monitoring
        'security.monitoring.title': 'Security Monitoring',
        'security.monitoring.autoRefresh': 'Auto Refresh',
        'security.monitoring.refresh': 'Refresh',
        'security.monitoring.export': 'Export Data',
        'security.monitoring.totalEvents': 'Total Events',
        'security.monitoring.criticalEvents': 'Critical Events',
        'security.monitoring.successfulLogins': 'Successful Logins',
        'security.monitoring.failedLogins': 'Failed Logins',
        'security.monitoring.uniqueIPs': 'Unique IPs',
        'security.monitoring.lockedAccounts': 'Locked Accounts',
        'security.monitoring.filters': 'Filters',
        'security.monitoring.eventType': 'Event Type',
        'security.monitoring.severity': 'Severity',
        'security.monitoring.dateRange': 'Date Range',
        'security.monitoring.apply': 'Apply',
        'security.monitoring.clear': 'Clear',
        'security.monitoring.eventsBySeverity': 'Events by Severity',
        'security.monitoring.loginAttempts': 'Login Attempts',
        'security.monitoring.securityEvents': 'Security Events',
        'security.monitoring.timestamp': 'Timestamp',
        'security.monitoring.description': 'Description',
        'security.monitoring.ipAddress': 'IP Address',
        'security.monitoring.actions': 'Actions',
        'security.monitoring.username': 'Username',
        'security.monitoring.status': 'Status',
        'security.monitoring.failureReason': 'Failure Reason',
        'security.monitoring.viewDetails': 'View Details',
        'security.monitoring.noEvents': 'No security events found',
        'security.monitoring.noAttempts': 'No login attempts found',
        'security.monitoring.success': 'Success',
        'security.monitoring.failure': 'Failure',
        'security.monitoring.accessDenied': 'Access Denied',
        'security.monitoring.accessDeniedMessage': 'You do not have permission to view security monitoring data.'
      },
      ar: {
        // App
        'app.title': 'نظام إدارة المستودعات',
        'app.welcome': 'مرحباً بك',
        'app.profile': 'الملف الشخصي',
        'app.notifications': 'الإشعارات',

        // Navigation
        'nav.dashboard': 'لوحة التحكم',
        'nav.inventoryManagement': 'إدارة المخزون',
        'nav.items': 'الأصناف',
        'nav.categories': 'الفئات',
        'nav.warehouses': 'المستودعات',
        'nav.stockMovements': 'حركات المخزون',
        'nav.stockAdjustments': 'تسويات المخزون',
        'nav.transfers': 'التحويلات',
        'nav.salesPurchases': 'المبيعات والمشتريات',
        'nav.salesInvoices': 'فواتير المبيعات',
        'nav.purchaseInvoices': 'فواتير المشتريات',
        'nav.salesReturns': 'مرتجعات المبيعات',
        'nav.purchaseReturns': 'مرتجعات المشتريات',
        'nav.customersSuppliers': 'العملاء والموردين',
        'nav.customers': 'العملاء',
        'nav.suppliers': 'الموردين',
        'nav.financialManagement': 'الإدارة المالية',
        'nav.payments': 'المدفوعات',
        'nav.accountStatements': 'كشوف الحسابات',
        'nav.cashRegister': 'الخزينة',
        'nav.reports': 'التقارير',
        'nav.inventoryReports': 'تقارير المخزون',
        'nav.financialReports': 'التقارير المالية',
        'nav.salesReports': 'تقارير المبيعات',
        'nav.purchaseReports': 'تقارير المشتريات',
        'nav.settings': 'الإعدادات',
        'nav.logout': 'تسجيل الخروج',
        
        // Dashboard
        'dashboard.title': 'لوحة التحكم',
        'dashboard.totalSales': 'إجمالي المبيعات',
        'dashboard.totalPurchases': 'إجمالي المشتريات',
        'dashboard.totalItems': 'إجمالي الأصناف',
        'dashboard.lowStockItems': 'أصناف منخفضة المخزون',
        'dashboard.recentTransactions': 'المعاملات الأخيرة',
        'dashboard.salesTrend': 'اتجاه المبيعات',
        'dashboard.inventoryOverview': 'نظرة عامة على المخزون',
        'dashboard.monthlyRevenue': 'الإيرادات الشهرية',
        
        // Common
        'common.search': 'بحث',
        'common.add': 'إضافة',
        'common.edit': 'تعديل',
        'common.delete': 'حذف',
        'common.save': 'حفظ',
        'common.cancel': 'إلغاء',
        'common.confirm': 'تأكيد',
        'common.yes': 'نعم',
        'common.no': 'لا',
        'common.loading': 'جاري التحميل...',
        'common.noData': 'لا توجد بيانات متاحة',
        'common.actions': 'الإجراءات',
        'common.status': 'الحالة',
        'common.date': 'التاريخ',
        'common.amount': 'المبلغ',
        'common.total': 'الإجمالي',
        'common.refresh': 'تحديث',

        // Authentication
        'auth.login.title': 'تسجيل الدخول',
        'auth.login.subtitle': 'مرحباً بك في نظام إدارة المخازن',
        'auth.login.username': 'اسم المستخدم',
        'auth.login.usernamePlaceholder': 'أدخل اسم المستخدم',
        'auth.login.password': 'كلمة المرور',
        'auth.login.passwordPlaceholder': 'أدخل كلمة المرور',
        'auth.login.rememberMe': 'تذكرني',
        'auth.login.signIn': 'تسجيل الدخول',
        'auth.login.forgotPassword': 'نسيت كلمة المرور؟',
        'auth.login.success': 'تم تسجيل الدخول بنجاح',
        'auth.login.welcomeBack': 'مرحباً بعودتك {{name}}',
        'auth.login.securityNotice': 'يتم حماية جلستك بتشفير SSL وتقنيات الأمان المتقدمة',
        'auth.login.attemptsRemaining': 'محاولات متبقية: {{attempts}}',
        'auth.login.error.title': 'خطأ في تسجيل الدخول',
        'auth.login.error.generic': 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',
        'auth.login.error.invalidCredentials': 'اسم المستخدم أو كلمة المرور غير صحيحة',
        'auth.login.error.accountLocked': 'تم قفل الحساب مؤقتاً بسبب محاولات دخول متعددة فاشلة',
        'auth.login.error.tooManyAttempts': 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً',
        'auth.login.lockout.title': 'تم قفل الحساب',
        'auth.login.lockout.message': 'تم قفل حسابك لمدة {{minutes}} دقيقة بسبب محاولات دخول فاشلة متعددة',
        'auth.login.lockout.active': 'الحساب مقفل. الوقت المتبقي: {{time}}',
        'auth.login.lockout.expired': 'انتهت مدة القفل',
        'auth.login.lockout.canTryAgain': 'يمكنك الآن المحاولة مرة أخرى',
        'auth.login.rateLimit.title': 'تم تجاوز الحد المسموح',
        'auth.login.rateLimit.message': 'تم تجاوز عدد المحاولات المسموح. يرجى الانتظار قبل المحاولة مرة أخرى',
        'auth.login.validation.invalid': 'القيمة المدخلة غير صحيحة',
        'auth.login.validation.username.required': 'اسم المستخدم مطلوب',
        'auth.login.validation.username.minLength': 'اسم المستخدم يجب أن يكون {{min}} أحرف على الأقل',
        'auth.login.validation.username.maxLength': 'اسم المستخدم يجب أن يكون {{max}} حرف كحد أقصى',
        'auth.login.validation.password.required': 'كلمة المرور مطلوبة',
        'auth.login.validation.password.minLength': 'كلمة المرور يجب أن تكون {{min}} أحرف على الأقل',
        'auth.login.footer.copyright': '© 2024 نظام إدارة المخازن. جميع الحقوق محفوظة.',
        'auth.login.footer.privacy': 'سياسة الخصوصية',
        'auth.login.footer.terms': 'شروط الاستخدام',
        'auth.login.footer.support': 'الدعم الفني',

        // Unauthorized
        'auth.unauthorized.title': 'تم رفض الوصول',
        'auth.unauthorized.description': 'ليس لديك صلاحية للوصول إلى هذا المورد.',
        'auth.unauthorized.details': 'يرجى الاتصال بالمسؤول إذا كنت تعتقد أن هذا خطأ.',
        'auth.unauthorized.goToDashboard': 'الذهاب إلى لوحة التحكم',
        'auth.unauthorized.goBack': 'العودة',
        'auth.unauthorized.contactSupport': 'الاتصال بالدعم',
        'auth.unauthorized.logout': 'تسجيل الخروج',
        'auth.unauthorized.autoRedirect': 'سيتم توجيهك إلى لوحة التحكم خلال 10 ثوانٍ.',

        // Security Monitoring
        'security.monitoring.title': 'مراقبة الأمان',
        'security.monitoring.autoRefresh': 'التحديث التلقائي',
        'security.monitoring.refresh': 'تحديث',
        'security.monitoring.export': 'تصدير البيانات',
        'security.monitoring.totalEvents': 'إجمالي الأحداث',
        'security.monitoring.criticalEvents': 'الأحداث الحرجة',
        'security.monitoring.successfulLogins': 'تسجيلات الدخول الناجحة',
        'security.monitoring.failedLogins': 'تسجيلات الدخول الفاشلة',
        'security.monitoring.uniqueIPs': 'عناوين IP الفريدة',
        'security.monitoring.lockedAccounts': 'الحسابات المقفلة',
        'security.monitoring.filters': 'المرشحات',
        'security.monitoring.eventType': 'نوع الحدث',
        'security.monitoring.severity': 'الخطورة',
        'security.monitoring.dateRange': 'نطاق التاريخ',
        'security.monitoring.apply': 'تطبيق',
        'security.monitoring.clear': 'مسح',
        'security.monitoring.eventsBySeverity': 'الأحداث حسب الخطورة',
        'security.monitoring.loginAttempts': 'محاولات تسجيل الدخول',
        'security.monitoring.securityEvents': 'أحداث الأمان',
        'security.monitoring.timestamp': 'الوقت',
        'security.monitoring.description': 'الوصف',
        'security.monitoring.ipAddress': 'عنوان IP',
        'security.monitoring.actions': 'الإجراءات',
        'security.monitoring.username': 'اسم المستخدم',
        'security.monitoring.status': 'الحالة',
        'security.monitoring.failureReason': 'سبب الفشل',
        'security.monitoring.viewDetails': 'عرض التفاصيل',
        'security.monitoring.noEvents': 'لم يتم العثور على أحداث أمان',
        'security.monitoring.noAttempts': 'لم يتم العثور على محاولات تسجيل دخول',
        'security.monitoring.success': 'نجح',
        'security.monitoring.failure': 'فشل',
        'security.monitoring.accessDenied': 'تم رفض الوصول',
        'security.monitoring.accessDeniedMessage': 'ليس لديك صلاحية لعرض بيانات مراقبة الأمان.'
      }
    };
  }
}
