{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"*\"];\nclass BaseIcon {\n  label;\n  spin = false;\n  styleClass;\n  role;\n  ariaLabel;\n  ariaHidden;\n  ngOnInit() {\n    this.getAttributes();\n  }\n  getAttributes() {\n    const isLabelEmpty = ObjectUtils.isEmpty(this.label);\n    this.role = !isLabelEmpty ? 'img' : undefined;\n    this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n    this.ariaHidden = isLabelEmpty;\n  }\n  getClassNames() {\n    return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n  }\n  static ɵfac = function BaseIcon_Factory(t) {\n    return new (t || BaseIcon)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: BaseIcon,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [1, \"p-element\", \"p-icon-wrapper\"],\n    inputs: {\n      label: \"label\",\n      spin: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"spin\", \"spin\", booleanAttribute],\n      styleClass: \"styleClass\"\n    },\n    standalone: true,\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function BaseIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseIcon, [{\n    type: Component,\n    args: [{\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element p-icon-wrapper'\n      }\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    spin: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon };", "map": {"version": 3, "names": ["i0", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ObjectUtils", "_c0", "BaseIcon", "label", "spin", "styleClass", "role", "aria<PERSON><PERSON><PERSON>", "ariaHidden", "ngOnInit", "getAttributes", "isLabelEmpty", "isEmpty", "undefined", "getClassNames", "ɵfac", "BaseIcon_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "BaseIcon_Template", "rf", "ctx", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "OnPush", "None", "host", "class", "transform"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-baseicon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { ObjectUtils } from 'primeng/utils';\n\nclass BaseIcon {\n    label;\n    spin = false;\n    styleClass;\n    role;\n    ariaLabel;\n    ariaHidden;\n    ngOnInit() {\n        this.getAttributes();\n    }\n    getAttributes() {\n        const isLabelEmpty = ObjectUtils.isEmpty(this.label);\n        this.role = !isLabelEmpty ? 'img' : undefined;\n        this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n        this.ariaHidden = isLabelEmpty;\n    }\n    getClassNames() {\n        return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: BaseIcon, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"18.0.1\", type: BaseIcon, isStandalone: true, selector: \"ng-component\", inputs: { label: \"label\", spin: [\"spin\", \"spin\", booleanAttribute], styleClass: \"styleClass\" }, host: { classAttribute: \"p-element p-icon-wrapper\" }, ngImport: i0, template: ` <ng-content></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: BaseIcon, decorators: [{\n            type: Component,\n            args: [{\n                    template: ` <ng-content></ng-content> `,\n                    standalone: true,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        class: 'p-element p-icon-wrapper'\n                    }\n                }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], spin: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], styleClass: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,QAAQ,eAAe;AAC9G,SAASC,WAAW,QAAQ,eAAe;AAAC,MAAAC,GAAA;AAE5C,MAAMC,QAAQ,CAAC;EACXC,KAAK;EACLC,IAAI,GAAG,KAAK;EACZC,UAAU;EACVC,IAAI;EACJC,SAAS;EACTC,UAAU;EACVC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,MAAMC,YAAY,GAAGX,WAAW,CAACY,OAAO,CAAC,IAAI,CAACT,KAAK,CAAC;IACpD,IAAI,CAACG,IAAI,GAAG,CAACK,YAAY,GAAG,KAAK,GAAGE,SAAS;IAC7C,IAAI,CAACN,SAAS,GAAG,CAACI,YAAY,GAAG,IAAI,CAACR,KAAK,GAAGU,SAAS;IACvD,IAAI,CAACL,UAAU,GAAGG,YAAY;EAClC;EACAG,aAAaA,CAAA,EAAG;IACZ,OAAO,UAAU,IAAI,CAACT,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,CAACD,IAAI,GAAG,aAAa,GAAG,EAAE,EAAE;EACpG;EACA,OAAOW,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFf,QAAQ;EAAA;EAC3G,OAAOgB,IAAI,kBAD8ExB,EAAE,CAAAyB,iBAAA;IAAAC,IAAA,EACJlB,QAAQ;IAAAmB,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAApB,KAAA;MAAAC,IAAA,GADNV,EAAE,CAAA8B,YAAA,CAAAC,0BAAA,kBACqG9B,gBAAgB;MAAAU,UAAA;IAAA;IAAAqB,UAAA;IAAAC,QAAA,GADvHjC,EAAE,CAAAkC,wBAAA,EAAFlC,EAAE,CAAAmC,mBAAA;IAAAC,kBAAA,EAAA7B,GAAA;IAAA8B,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFzC,EAAE,CAAA2C,eAAA;QAAF3C,EAAE,CAAA4C,YAAA,EAC4P,CAAC;MAAA;IAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC5V;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F/C,EAAE,CAAAgD,iBAAA,CAGJxC,QAAQ,EAAc,CAAC;IACtGkB,IAAI,EAAExB,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCV,QAAQ,EAAE,6BAA6B;MACvCP,UAAU,EAAE,IAAI;MAChBc,eAAe,EAAE3C,uBAAuB,CAAC+C,MAAM;MAC/CL,aAAa,EAAEzC,iBAAiB,CAAC+C,IAAI;MACrCC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE5C,KAAK,EAAE,CAAC;MACtBiB,IAAI,EAAErB;IACV,CAAC,CAAC;IAAEK,IAAI,EAAE,CAAC;MACPgB,IAAI,EAAErB,KAAK;MACX4C,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAErD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEU,UAAU,EAAE,CAAC;MACbe,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}