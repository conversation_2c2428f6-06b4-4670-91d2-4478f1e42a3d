{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, booleanAttribute, numberAttribute, Component, ViewEncapsulation, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, Inject, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { BarsIcon } from 'primeng/icons/bars';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { Subject, interval } from 'rxjs';\nimport { debounce, filter } from 'rxjs/operators';\nconst _c0 = [\"menubar\"];\nconst _c1 = (a0, a1) => ({\n  \"p-submenu-list\": a0,\n  \"p-menubar-root-list\": a1\n});\nconst _c2 = a0 => ({\n  \"p-menuitem-link\": true,\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  root: a1\n});\nfunction MenubarSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r2.getSeparatorItemClass(processedItem_r2));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r2.getItemLabelId(processedItem_r2));\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml)(\"id\", ctx_r2.getItemLabelId(processedItem_r2));\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 1, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.submenuIconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template, 1, 4, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template, 2, 3, \"span\", 17)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template, 2, 2, \"span\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r5 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(11, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(processedItem_r2, \"url\"), i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlLabel_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemLabel(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 1, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const menubar_r6 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !menubar_r6.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", menubar_r6.submenuIconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 27);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template, 1, 4, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template, 2, 1, \"span\", 28)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template, 2, 2, \"span\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r7 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"))(\"queryParams\", ctx_r2.getItemProp(processedItem_r2, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r2.getItemProp(processedItem_r2, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c3))(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(21, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")))(\"fragment\", ctx_r2.getItemProp(processedItem_r2, \"fragment\"))(\"queryParamsHandling\", ctx_r2.getItemProp(processedItem_r2, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r2.getItemProp(processedItem_r2, \"preserveFragment\"))(\"skipLocationChange\", ctx_r2.getItemProp(processedItem_r2, \"skipLocationChange\"))(\"replaceUrl\", ctx_r2.getItemProp(processedItem_r2, \"replaceUrl\"))(\"state\", ctx_r2.getItemProp(processedItem_r2, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"tabindex\", -1)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlRouteLabel_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template, 7, 13, \"a\", 13)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template, 7, 23, \"a\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_4_1_Template, 1, 0, null, 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, processedItem_r2.item, ctx_r2.root));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-menubarSub\", 32);\n    i0.ɵɵlistener(\"itemClick\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit($event));\n    })(\"itemMouseEnter\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"itemTemplate\", ctx_r2.itemTemplate)(\"items\", processedItem_r2.items)(\"mobileActive\", ctx_r2.mobileActive)(\"autoDisplay\", ctx_r2.autoDisplay)(\"menuId\", ctx_r2.menuId)(\"activeItemPath\", ctx_r2.activeItemPath)(\"focusedItemId\", ctx_r2.focusedItemId)(\"level\", ctx_r2.level + 1)(\"ariaLabelledBy\", ctx_r2.getItemLabelId(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 9, 1)(2, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function MenubarSub_ng_template_2_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick($event, processedItem_r2));\n    })(\"mouseenter\", function MenubarSub_ng_template_2_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r2\n      }));\n    });\n    i0.ɵɵtemplate(3, MenubarSub_ng_template_2_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 11)(4, MenubarSub_ng_template_2_li_1_ng_container_4_Template, 2, 5, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template, 1, 9, \"p-menubarSub\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const processedItem_r2 = ctx_r8.$implicit;\n    const index_r10 = ctx_r8.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.getItemProp(processedItem_r2, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r2.getItemClass(processedItem_r2))(\"tooltipOptions\", ctx_r2.getItemProp(processedItem_r2, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r2.isItemActive(processedItem_r2))(\"data-p-focused\", ctx_r2.isItemFocused(processedItem_r2))(\"data-p-disabled\", ctx_r2.isItemDisabled(processedItem_r2))(\"aria-label\", ctx_r2.getItemLabel(processedItem_r2))(\"aria-disabled\", ctx_r2.isItemDisabled(processedItem_r2) || undefined)(\"aria-haspopup\", ctx_r2.isItemGroup(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r2.isItemGroup(processedItem_r2) ? ctx_r2.isItemActive(processedItem_r2) : undefined)(\"aria-level\", ctx_r2.level + 1)(\"aria-setsize\", ctx_r2.getAriaSetSize())(\"aria-posinset\", ctx_r2.getAriaPosInset(index_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_0_Template, 1, 4, \"li\", 6)(1, MenubarSub_ng_template_2_li_1_Template, 6, 21, \"li\", 7);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n  }\n}\nconst _c5 = [\"menubutton\"];\nconst _c6 = [\"rootmenu\"];\nconst _c7 = [\"*\"];\nconst _c8 = a0 => ({\n  \"p-menubar p-component\": true,\n  \"p-menubar-mobile-active\": a0\n});\nfunction Menubar_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, Menubar_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate);\n  }\n}\nfunction Menubar_a_2_BarsIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"BarsIcon\");\n  }\n}\nfunction Menubar_a_2_3_ng_template_0_Template(rf, ctx) {}\nfunction Menubar_a_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menubar_a_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Menubar_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10, 2);\n    i0.ɵɵlistener(\"click\", function Menubar_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonClick($event));\n    })(\"keydown\", function Menubar_a_2_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonKeydown($event));\n    });\n    i0.ɵɵtemplate(2, Menubar_a_2_BarsIcon_2_Template, 1, 0, \"BarsIcon\", 11)(3, Menubar_a_2_3_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-haspopup\", ctx_r1.model.length && ctx_r1.model.length > 0 ? true : false)(\"aria-expanded\", ctx_r1.mobileActive)(\"aria-controls\", ctx_r1.id)(\"aria-label\", ctx_r1.config.translation.aria.navigation)(\"data-pc-section\", \"button\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.menuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.menuIconTemplate);\n  }\n}\nfunction Menubar_div_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Menubar_div_5_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate);\n  }\n}\nfunction Menubar_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nclass MenubarService {\n  autoHide;\n  autoHideDelay;\n  mouseLeaves = new Subject();\n  mouseLeft$ = this.mouseLeaves.pipe(debounce(() => interval(this.autoHideDelay)), filter(mouseLeft => this.autoHide && mouseLeft));\n  static ɵfac = function MenubarService_Factory(t) {\n    return new (t || MenubarService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MenubarService,\n    factory: MenubarService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass MenubarSub {\n  el;\n  renderer;\n  cd;\n  menubarService;\n  items;\n  itemTemplate;\n  root = false;\n  autoZIndex = true;\n  baseZIndex = 0;\n  mobileActive;\n  autoDisplay;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  activeItemPath;\n  submenuIconTemplate;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  menubarViewChild;\n  mouseLeaveSubscriber;\n  constructor(el, renderer, cd, menubarService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.menubarService = menubarService;\n  }\n  ngOnInit() {\n    this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => {\n      this.cd.markForCheck();\n    });\n  }\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n  }\n  getItemKey(processedItem) {\n    return this.getItemId(processedItem);\n  }\n  getItemLabelId(processedItem) {\n    return `${this.menuId}_${processedItem.key}_label`;\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem': true,\n      'p-highlight': this.isItemActive(processedItem),\n      'p-menuitem-active': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem-separator': true\n    };\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    if (this.activeItemPath) {\n      return this.activeItemPath.some(path => path.key === processedItem.key);\n    }\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  onItemMouseLeave() {\n    this.menubarService.mouseLeaves.next(true);\n  }\n  onItemMouseEnter(param) {\n    if (this.autoDisplay) {\n      this.menubarService.mouseLeaves.next(false);\n      const {\n        event,\n        processedItem\n      } = param;\n      this.itemMouseEnter.emit({\n        originalEvent: event,\n        processedItem\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.mouseLeaveSubscriber?.unsubscribe();\n  }\n  static ɵfac = function MenubarSub_Factory(t) {\n    return new (t || MenubarSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MenubarService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MenubarSub,\n    selectors: [[\"p-menubarSub\"]],\n    viewQuery: function MenubarSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubarViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      root: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"root\", \"root\", booleanAttribute],\n      autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      mobileActive: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"mobileActive\", \"mobileActive\", booleanAttribute],\n      autoDisplay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoDisplay\", \"autoDisplay\", booleanAttribute],\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"level\", \"level\", numberAttribute],\n      focusedItemId: \"focusedItemId\",\n      activeItemPath: \"activeItemPath\",\n      submenuIconTemplate: \"submenuIconTemplate\"\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 3,\n    vars: 11,\n    consts: [[\"menubar\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"role\", \"menubar\", 3, \"focus\", \"blur\", \"keydown\", \"ngClass\", \"tabindex\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"ngStyle\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngStyle\", \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [1, \"p-menuitem-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [3, \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"ariaLabelledBy\", \"itemClick\", \"itemMouseEnter\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 3, \"id\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\", 3, \"id\"], [1, \"p-menuitem-text\", 3, \"innerHTML\", \"id\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [3, \"data-pc-section\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"itemClick\", \"itemMouseEnter\", \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"ariaLabelledBy\"]],\n    template: function MenubarSub_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"ul\", 4, 0);\n        i0.ɵɵlistener(\"focus\", function MenubarSub_Template_ul_focus_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuFocus.emit($event));\n        })(\"blur\", function MenubarSub_Template_ul_blur_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuBlur.emit($event));\n        })(\"keydown\", function MenubarSub_Template_ul_keydown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuKeydown.emit($event));\n        });\n        i0.ɵɵtemplate(2, MenubarSub_ng_template_2_Template, 2, 2, \"ng-template\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c1, !ctx.root, ctx.root))(\"tabindex\", 0);\n        i0.ɵɵattribute(\"data-pc-section\", \"menu\")(\"aria-label\", ctx.ariaLabel)(\"aria-labelledBy\", ctx.ariaLabelledBy)(\"id\", ctx.root ? ctx.menuId : null)(\"aria-activedescendant\", ctx.focusedItemId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.items);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, AngleDownIcon, AngleRightIcon, MenubarSub],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubarSub',\n      template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menubar\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"root ? menuId : null\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"root\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, root: root }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        [ariaLabelledBy]=\"getItemLabelId(processedItem)\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: MenubarService\n  }], {\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    mobileActive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoDisplay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    activeItemPath: [{\n      type: Input\n    }],\n    submenuIconTemplate: [{\n      type: Input\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    menubarViewChild: [{\n      type: ViewChild,\n      args: ['menubar', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Menubar is a horizontal menu component.\n * @group Components\n */\nclass Menubar {\n  document;\n  platformId;\n  el;\n  renderer;\n  cd;\n  config;\n  menubarService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Whether to show a root submenu on mouse over.\n   * @defaultValue true\n   * @group Props\n   */\n  autoDisplay = true;\n  /**\n   * Whether to hide a root submenu when mouse leaves.\n   * @group Props\n   */\n  autoHide;\n  /**\n   * Delay to hide the root submenu in milliseconds when mouse leaves.\n   * @group Props\n   */\n  autoHideDelay = 100;\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Callback to execute when button is focused.\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to execute when button loses focus.\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  templates;\n  menubutton;\n  rootmenu;\n  startTemplate;\n  endTemplate;\n  menuIconTemplate;\n  submenuIconTemplate;\n  itemTemplate;\n  mobileActive;\n  outsideClickListener;\n  resizeListener;\n  mouseLeaveSubscriber;\n  dirty = false;\n  focused = false;\n  activeItemPath = signal([]);\n  number = signal(0);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _model;\n  get visibleItems() {\n    const processedItem = this.activeItemPath().find(p => p.key === this.focusedItemInfo().parentKey);\n    return processedItem ? processedItem.items : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItem = this.focusedItemInfo();\n    return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n  }\n  constructor(document, platformId, el, renderer, cd, config, menubarService) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.menubarService = menubarService;\n    effect(() => {\n      const path = this.activeItemPath();\n      if (ObjectUtils.isNotEmpty(path)) {\n        this.bindOutsideClickListener();\n        this.bindResizeListener();\n      } else {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n      }\n    });\n  }\n  ngOnInit() {\n    this.menubarService.autoHide = this.autoHide;\n    this.menubarService.autoHideDelay = this.autoHideDelay;\n    this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => this.unbindOutsideClickListener());\n    this.id = this.id || UniqueComponentId();\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this.startTemplate = item.template;\n          break;\n        case 'end':\n          this.endTemplate = item.template;\n          break;\n        case 'menuicon':\n          this.menuIconTemplate = item.template;\n          break;\n        case 'submenuicon':\n          this.submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey\n      };\n      newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  }\n  menuButtonClick(event) {\n    this.toggle(event);\n  }\n  menuButtonKeydown(event) {\n    (event.code === 'Enter' || event.code === 'Space') && this.menuButtonClick(event);\n  }\n  onItemClick(event) {\n    const {\n      originalEvent,\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        level,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItemPath.set(this.activeItemPath().filter(p => key !== p.key && key.startsWith(p.key)));\n      this.focusedItemInfo.set({\n        index,\n        level,\n        parentKey,\n        item\n      });\n      this.dirty = !root;\n      DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    } else {\n      if (grouped) {\n        this.onItemChange(event);\n      } else {\n        const rootProcessedItem = root ? processedItem : this.activeItemPath().find(p => p.parentKey === '');\n        this.hide(originalEvent);\n        this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n        this.mobileActive = false;\n        DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n      }\n    }\n  }\n  onItemMouseEnter(event) {\n    if (!DomHandler.isTouchDevice()) {\n      if (!this.mobileActive) {\n        this.onItemChange(event);\n      }\n    }\n  }\n  changeFocusedItemIndex(event, index) {\n    const processedItem = this.findVisibleItem(index);\n    if (this.focusedItemInfo().index !== index) {\n      const focusedItemInfo = this.focusedItemInfo();\n      this.focusedItemInfo.set({\n        ...focusedItemInfo,\n        item: processedItem.item,\n        index\n      });\n      this.scrollInView();\n    }\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  onItemChange(event) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (ObjectUtils.isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      level,\n      parentKey,\n      items,\n      item\n    } = processedItem;\n    const grouped = ObjectUtils.isNotEmpty(items);\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== parentKey && p.parentKey !== key);\n    grouped && activeItemPath.push(processedItem);\n    this.focusedItemInfo.set({\n      index,\n      level,\n      parentKey,\n      item\n    });\n    this.activeItemPath.set(activeItemPath);\n    grouped && (this.dirty = true);\n    isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n  }\n  toggle(event) {\n    if (this.mobileActive) {\n      this.mobileActive = false;\n      ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n      this.hide();\n    } else {\n      this.mobileActive = true;\n      ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n      setTimeout(() => {\n        this.show();\n      }, 0);\n    }\n    this.cd.markForCheck();\n    this.bindOutsideClickListener();\n    event.preventDefault();\n  }\n  hide(event, isFocus) {\n    if (this.mobileActive) {\n      setTimeout(() => {\n        DomHandler.focus(this.menubutton.nativeElement);\n      }, 0);\n    }\n    this.activeItemPath.set([]);\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    isFocus && DomHandler.focus(this.rootmenu?.menubarViewChild.nativeElement);\n    this.dirty = false;\n  }\n  show() {\n    const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n    this.focusedItemInfo.set({\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: '',\n      item: processedItem?.item\n    });\n    DomHandler.focus(this.rootmenu?.menubarViewChild.nativeElement);\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n    const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : {\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: '',\n      item: processedItem?.item\n    };\n    this.focusedItemInfo.set(focusedItemInfo);\n    this.onFocus.emit(event);\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n    this.dirty = false;\n    this.onBlur.emit(event);\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  findVisibleItem(index) {\n    return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return this.activeItemPath().some(p => p.key === processedItem.key);\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemIndex(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  onArrowDownKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n    if (root) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        this.onArrowRightKey(event);\n      }\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n    if (parentItem) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        this.onArrowDownKey(event);\n      }\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowUpKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    if (root) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        const itemIndex = this.findLastItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n      }\n    } else {\n      const parentItem = this.activeItemPath().find(p => p.key === processedItem.parentKey);\n      if (this.focusedItemInfo().index === 0) {\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: parentItem ? parentItem.parentKey : '',\n          item: processedItem.item\n        });\n        this.searchValue = '';\n        this.onArrowLeftKey(event);\n        const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n        this.activeItemPath.set(activeItemPath);\n      } else {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n      }\n    }\n    event.preventDefault();\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n    if (parentItem) {\n      this.onItemChange({\n        originalEvent: event,\n        processedItem: parentItem\n      });\n      const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n      this.activeItemPath.set(activeItemPath);\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemIndex(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    this.hide(event, true);\n    this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n    }\n    event.preventDefault();\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return ObjectUtils.findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          if (!DomHandler.isTouchDevice()) {\n            this.hide(event, true);\n          }\n          this.mobileActive = false;\n        });\n      }\n    }\n  }\n  bindOutsideClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.outsideClickListener) {\n        this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isOutsideContainer = this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target);\n          const isOutsideMenuButton = this.mobileActive && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target);\n          if (isOutsideContainer) {\n            isOutsideMenuButton ? this.mobileActive = false : this.hide();\n          }\n        });\n      }\n    }\n  }\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      this.outsideClickListener();\n      this.outsideClickListener = null;\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.mouseLeaveSubscriber?.unsubscribe();\n    this.unbindOutsideClickListener();\n    this.unbindResizeListener();\n  }\n  static ɵfac = function Menubar_Factory(t) {\n    return new (t || Menubar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(MenubarService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Menubar,\n    selectors: [[\"p-menubar\"]],\n    contentQueries: function Menubar_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Menubar_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubutton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      autoDisplay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoDisplay\", \"autoDisplay\", booleanAttribute],\n      autoHide: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoHide\", \"autoHide\", booleanAttribute],\n      autoHideDelay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoHideDelay\", \"autoHideDelay\", numberAttribute],\n      id: \"id\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\"\n    },\n    outputs: {\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\"\n    },\n    features: [i0.ɵɵProvidersFeature([MenubarService]), i0.ɵɵInputTransformsFeature],\n    ngContentSelectors: _c7,\n    decls: 8,\n    vars: 25,\n    consts: [[\"rootmenu\", \"\"], [\"legacy\", \"\"], [\"menubutton\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-menubar-start\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", \"class\", \"p-menubar-button\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"items\", \"itemTemplate\", \"menuId\", \"root\", \"baseZIndex\", \"autoZIndex\", \"mobileActive\", \"autoDisplay\", \"ariaLabel\", \"ariaLabelledBy\", \"focusedItemId\", \"submenuIconTemplate\", \"activeItemPath\"], [\"class\", \"p-menubar-end\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-menubar-start\"], [4, \"ngTemplateOutlet\"], [\"tabindex\", \"0\", \"role\", \"button\", 1, \"p-menubar-button\", 3, \"click\", \"keydown\"], [4, \"ngIf\"], [1, \"p-menubar-end\"]],\n    template: function Menubar_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵtemplate(1, Menubar_div_1_Template, 2, 1, \"div\", 4)(2, Menubar_a_2_Template, 4, 7, \"a\", 5);\n        i0.ɵɵelementStart(3, \"p-menubarSub\", 6, 0);\n        i0.ɵɵlistener(\"itemClick\", function Menubar_Template_p_menubarSub_itemClick_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemClick($event));\n        })(\"menuFocus\", function Menubar_Template_p_menubarSub_menuFocus_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuFocus($event));\n        })(\"menuBlur\", function Menubar_Template_p_menubarSub_menuBlur_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuBlur($event));\n        })(\"menuKeydown\", function Menubar_Template_p_menubarSub_menuKeydown_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"itemMouseEnter\", function Menubar_Template_p_menubarSub_itemMouseEnter_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemMouseEnter($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, Menubar_div_5_Template, 2, 1, \"div\", 7)(6, Menubar_ng_template_6_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const legacy_r4 = i0.ɵɵreference(7);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c8, ctx.mobileActive))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"menubar\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.startTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.model && ctx.model.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"items\", ctx.processedItems)(\"itemTemplate\", ctx.itemTemplate)(\"menuId\", ctx.id)(\"root\", true)(\"baseZIndex\", ctx.baseZIndex)(\"autoZIndex\", ctx.autoZIndex)(\"mobileActive\", ctx.mobileActive)(\"autoDisplay\", ctx.autoDisplay)(\"ariaLabel\", ctx.ariaLabel)(\"ariaLabelledBy\", ctx.ariaLabelledBy)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined)(\"submenuIconTemplate\", ctx.submenuIconTemplate)(\"activeItemPath\", ctx.activeItemPath());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.endTemplate)(\"ngIfElse\", legacy_r4);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, BarsIcon, MenubarSub],\n    styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Menubar, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubar',\n      template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [submenuIconTemplate]=\"submenuIconTemplate\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      providers: [MenubarService],\n      styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i5.PrimeNGConfig\n  }, {\n    type: MenubarService\n  }], {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    autoDisplay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoHide: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoHideDelay: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    menubutton: [{\n      type: ViewChild,\n      args: ['menubutton']\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }]\n  });\n})();\nclass MenubarModule {\n  static ɵfac = function MenubarModule_Factory(t) {\n    return new (t || MenubarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MenubarModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon, RouterModule, TooltipModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon],\n      exports: [Menubar, RouterModule, TooltipModule, SharedModule],\n      declarations: [Menubar, MenubarSub]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menubar, MenubarModule, MenubarService, MenubarSub };", "map": {"version": 3, "names": ["i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "Injectable", "EventEmitter", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "Input", "Output", "ViewChild", "signal", "effect", "PLATFORM_ID", "ChangeDetectionStrategy", "Inject", "ContentChildren", "NgModule", "i2", "RouterModule", "i5", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "AngleDownIcon", "AngleRightIcon", "BarsIcon", "i3", "RippleModule", "i4", "TooltipModule", "ObjectUtils", "UniqueComponentId", "ZIndexUtils", "Subject", "interval", "debounce", "filter", "_c0", "_c1", "a0", "a1", "_c2", "_c3", "exact", "_c4", "$implicit", "root", "MenubarSub_ng_template_2_li_0_Template", "rf", "ctx", "ɵɵelement", "processedItem_r2", "ɵɵnextContext", "ctx_r2", "ɵɵproperty", "getItemProp", "getSeparatorItemClass", "ɵɵattribute", "getItemId", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "getItemLabelId", "ɵɵadvance", "ɵɵtextInterpolate1", "getItemLabel", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template", "ɵɵsanitizeHtml", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template", "ɵɵtextInterpolate", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template", "submenuIconTemplate", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template", "ɵɵtemplateRefExtractor", "htmlLabel_r5", "ɵɵreference", "ɵɵpureFunction1", "ɵɵsanitizeUrl", "isItemGroup", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template", "menubar_r6", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template", "htmlRouteLabel_r7", "ɵɵpureFunction0", "MenubarSub_ng_template_2_li_1_ng_container_3_Template", "MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template", "MenubarSub_ng_template_2_li_1_ng_container_4_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_4_Template", "itemTemplate", "ɵɵpureFunction2", "item", "MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template", "_r8", "ɵɵgetCurrentView", "ɵɵlistener", "MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemClick_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "itemClick", "emit", "MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemMouseEnter_0_listener", "onItemMouseEnter", "items", "mobileActive", "autoDisplay", "menuId", "activeItemPath", "focusedItemId", "level", "MenubarSub_ng_template_2_li_1_Template", "_r4", "MenubarSub_ng_template_2_li_1_Template_div_click_2_listener", "onItemClick", "MenubarSub_ng_template_2_li_1_Template_div_mouseenter_2_listener", "processedItem", "ctx_r8", "index_r10", "index", "ɵɵclassMap", "getItemClass", "isItemActive", "isItemFocused", "isItemDisabled", "undefined", "getAriaSetSize", "getAriaPosInset", "isItemVisible", "MenubarSub_ng_template_2_Template", "_c5", "_c6", "_c7", "_c8", "Menubar_div_1_ng_container_1_Template", "ɵɵelementContainer", "Menubar_div_1_Template", "ctx_r1", "startTemplate", "Menubar_a_2_BarsIcon_2_Template", "Menubar_a_2_3_ng_template_0_Template", "Menubar_a_2_3_Template", "Menubar_a_2_Template", "_r3", "Menubar_a_2_Template_a_click_0_listener", "menuButtonClick", "Menubar_a_2_Template_a_keydown_0_listener", "menuButtonKeydown", "model", "length", "id", "config", "translation", "aria", "navigation", "menuIconTemplate", "Menubar_div_5_ng_container_1_Template", "Menubar_div_5_Template", "endTemplate", "<PERSON><PERSON>r_ng_template_6_Template", "ɵɵprojection", "MenubarService", "autoHide", "autoHideDelay", "mouseLeaves", "mouseLeft$", "pipe", "mouseLeft", "ɵfac", "MenubarService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el", "renderer", "cd", "menubarService", "autoZIndex", "baseZIndex", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "itemMouseEnter", "menuFocus", "menuBlur", "menuKeydown", "menubarViewChild", "mouseLeaveSubscriber", "constructor", "ngOnInit", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "originalEvent", "isFocus", "name", "params", "getItemValue", "key", "getItemKey", "some", "path", "isNotEmpty", "slice", "onItemMouseLeave", "next", "param", "ngOnDestroy", "unsubscribe", "MenubarSub_Factory", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "MenubarSub_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "features", "ɵɵInputTransformsFeature", "decls", "vars", "consts", "template", "MenubarSub_Template", "_r1", "MenubarSub_Template_ul_focus_0_listener", "MenubarSub_Template_ul_blur_0_listener", "MenubarSub_Template_ul_keydown_0_listener", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "RouterLink", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "encapsulation", "args", "selector", "None", "host", "class", "transform", "static", "Men<PERSON><PERSON>", "document", "platformId", "value", "_model", "_processedItems", "createProcessedItems", "style", "styleClass", "onFocus", "onBlur", "templates", "menubutton", "rootmenu", "outsideClickListener", "resizeListener", "dirty", "focused", "number", "focusedItemInfo", "parent<PERSON><PERSON>", "searchValue", "searchTimeout", "visibleItems", "find", "p", "processedItems", "focusedItem", "bindOutsideClickListener", "bindResizeListener", "unbindOutsideClickListener", "unbindResizeListener", "ngAfterContentInit", "for<PERSON>ach", "getType", "parent", "newItem", "push", "toggle", "code", "grouped", "isProcessedItemGroup", "isEmpty", "selected", "isSelected", "set", "startsWith", "focus", "nativeElement", "onItemChange", "rootProcessedItem", "hide", "changeFocusedItemIndex", "isTouchDevice", "findVisibleItem", "scrollInView", "element", "findSingle", "scrollIntoView", "block", "inline", "clear", "zIndex", "menu", "setTimeout", "show", "preventDefault", "findFirstFocusedItemIndex", "onMenuFocus", "onMenuBlur", "onKeyDown", "metaKey", "ctrl<PERSON>ey", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "isPrintableCharacter", "searchItems", "selectedIndex", "findSelectedItemIndex", "findFirstItemIndex", "findIndex", "isValidItem", "isValidSelectedItem", "isItemSeparator", "isItemMatched", "getProccessedItemLabel", "toLocaleLowerCase", "isProccessedItemGroup", "char", "itemIndex", "matched", "clearTimeout", "findNextItemIndex", "parentItem", "findLastItemIndex", "findPrevItemIndex", "findLastFocusedItemIndex", "anchorElement", "click", "findLastIndex", "matchedItemIndex", "listen", "defaultView", "isOutsideContainer", "target", "contains", "isOutsideMenuButton", "Menubar_Factory", "PrimeNGConfig", "contentQueries", "Menubar_ContentQueries", "dirIndex", "ɵɵcontentQuery", "Menubar_Query", "ɵɵProvidersFeature", "ngContentSelectors", "<PERSON><PERSON>r_Template", "ɵɵprojectionDef", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_itemClick_3_listener", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_menuFocus_3_listener", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_menuBlur_3_listener", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_menuKeydown_3_listener", "<PERSON><PERSON>r_Template_p_menubarSub_itemMouseEnter_3_listener", "legacy_r4", "styles", "changeDetection", "OnPush", "providers", "Document", "decorators", "MenubarModule", "MenubarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-menubar.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, booleanAttribute, numberAttribute, Component, ViewEncapsulation, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, Inject, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { BarsIcon } from 'primeng/icons/bars';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { Subject, interval } from 'rxjs';\nimport { debounce, filter } from 'rxjs/operators';\n\nclass MenubarService {\n    autoHide;\n    autoHideDelay;\n    mouseLeaves = new Subject();\n    mouseLeft$ = this.mouseLeaves.pipe(debounce(() => interval(this.autoHideDelay)), filter((mouseLeft) => this.autoHide && mouseLeft));\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarService, decorators: [{\n            type: Injectable\n        }] });\nclass MenubarSub {\n    el;\n    renderer;\n    cd;\n    menubarService;\n    items;\n    itemTemplate;\n    root = false;\n    autoZIndex = true;\n    baseZIndex = 0;\n    mobileActive;\n    autoDisplay;\n    menuId;\n    ariaLabel;\n    ariaLabelledBy;\n    level = 0;\n    focusedItemId;\n    activeItemPath;\n    submenuIconTemplate;\n    itemClick = new EventEmitter();\n    itemMouseEnter = new EventEmitter();\n    menuFocus = new EventEmitter();\n    menuBlur = new EventEmitter();\n    menuKeydown = new EventEmitter();\n    menubarViewChild;\n    mouseLeaveSubscriber;\n    constructor(el, renderer, cd, menubarService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.menubarService = menubarService;\n    }\n    ngOnInit() {\n        this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => {\n            this.cd.markForCheck();\n        });\n    }\n    onItemClick(event, processedItem) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n    }\n    getItemProp(processedItem, name, params = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n    getItemId(processedItem) {\n        return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n    }\n    getItemKey(processedItem) {\n        return this.getItemId(processedItem);\n    }\n    getItemLabelId(processedItem) {\n        return `${this.menuId}_${processedItem.key}_label`;\n    }\n    getItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-highlight': this.isItemActive(processedItem),\n            'p-menuitem-active': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n    getItemLabel(processedItem) {\n        return this.getItemProp(processedItem, 'label');\n    }\n    getSeparatorItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n    isItemVisible(processedItem) {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n    isItemActive(processedItem) {\n        if (this.activeItemPath) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        }\n    }\n    isItemDisabled(processedItem) {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n    isItemFocused(processedItem) {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n    isItemGroup(processedItem) {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n    getAriaPosInset(index) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n    onItemMouseLeave() {\n        this.menubarService.mouseLeaves.next(true);\n    }\n    onItemMouseEnter(param) {\n        if (this.autoDisplay) {\n            this.menubarService.mouseLeaves.next(false);\n            const { event, processedItem } = param;\n            this.itemMouseEnter.emit({ originalEvent: event, processedItem });\n        }\n    }\n    ngOnDestroy() {\n        this.mouseLeaveSubscriber?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarSub, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: MenubarService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"18.0.1\", type: MenubarSub, selector: \"p-menubarSub\", inputs: { items: \"items\", itemTemplate: \"itemTemplate\", root: [\"root\", \"root\", booleanAttribute], autoZIndex: [\"autoZIndex\", \"autoZIndex\", booleanAttribute], baseZIndex: [\"baseZIndex\", \"baseZIndex\", numberAttribute], mobileActive: [\"mobileActive\", \"mobileActive\", booleanAttribute], autoDisplay: [\"autoDisplay\", \"autoDisplay\", booleanAttribute], menuId: \"menuId\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\", level: [\"level\", \"level\", numberAttribute], focusedItemId: \"focusedItemId\", activeItemPath: \"activeItemPath\", submenuIconTemplate: \"submenuIconTemplate\" }, outputs: { itemClick: \"itemClick\", itemMouseEnter: \"itemMouseEnter\", menuFocus: \"menuFocus\", menuBlur: \"menuBlur\", menuKeydown: \"menuKeydown\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"menubarViewChild\", first: true, predicate: [\"menubar\"], descendants: true, static: true }], ngImport: i0, template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menubar\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"root ? menuId : null\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"root\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, root: root }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        [ariaLabelledBy]=\"getItemLabelId(processedItem)\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLink), selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLinkActive), selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"directive\", type: i0.forwardRef(() => i4.Tooltip), selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"autoHide\", \"fitContent\", \"hideOnEscape\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: i0.forwardRef(() => AngleDownIcon), selector: \"AngleDownIcon\" }, { kind: \"component\", type: i0.forwardRef(() => AngleRightIcon), selector: \"AngleRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => MenubarSub), selector: \"p-menubarSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\", \"submenuIconTemplate\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-menubarSub',\n                    template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menubar\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"root ? menuId : null\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"root\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, root: root }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        [ariaLabelledBy]=\"getItemLabelId(processedItem)\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: MenubarService }], propDecorators: { items: [{\n                type: Input\n            }], itemTemplate: [{\n                type: Input\n            }], root: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoZIndex: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], baseZIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], mobileActive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoDisplay: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], menuId: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], level: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], focusedItemId: [{\n                type: Input\n            }], activeItemPath: [{\n                type: Input\n            }], submenuIconTemplate: [{\n                type: Input\n            }], itemClick: [{\n                type: Output\n            }], itemMouseEnter: [{\n                type: Output\n            }], menuFocus: [{\n                type: Output\n            }], menuBlur: [{\n                type: Output\n            }], menuKeydown: [{\n                type: Output\n            }], menubarViewChild: [{\n                type: ViewChild,\n                args: ['menubar', { static: true }]\n            }] } });\n/**\n * Menubar is a horizontal menu component.\n * @group Components\n */\nclass Menubar {\n    document;\n    platformId;\n    el;\n    renderer;\n    cd;\n    config;\n    menubarService;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    set model(value) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model() {\n        return this._model;\n    }\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Whether to show a root submenu on mouse over.\n     * @defaultValue true\n     * @group Props\n     */\n    autoDisplay = true;\n    /**\n     * Whether to hide a root submenu when mouse leaves.\n     * @group Props\n     */\n    autoHide;\n    /**\n     * Delay to hide the root submenu in milliseconds when mouse leaves.\n     * @group Props\n     */\n    autoHideDelay = 100;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Callback to execute when button is focused.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to execute when button loses focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    templates;\n    menubutton;\n    rootmenu;\n    startTemplate;\n    endTemplate;\n    menuIconTemplate;\n    submenuIconTemplate;\n    itemTemplate;\n    mobileActive;\n    outsideClickListener;\n    resizeListener;\n    mouseLeaveSubscriber;\n    dirty = false;\n    focused = false;\n    activeItemPath = signal([]);\n    number = signal(0);\n    focusedItemInfo = signal({ index: -1, level: 0, parentKey: '', item: null });\n    searchValue = '';\n    searchTimeout;\n    _processedItems;\n    _model;\n    get visibleItems() {\n        const processedItem = this.activeItemPath().find((p) => p.key === this.focusedItemInfo().parentKey);\n        return processedItem ? processedItem.items : this.processedItems;\n    }\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n    get focusedItemId() {\n        const focusedItem = this.focusedItemInfo();\n        return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n    }\n    constructor(document, platformId, el, renderer, cd, config, menubarService) {\n        this.document = document;\n        this.platformId = platformId;\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.menubarService = menubarService;\n        effect(() => {\n            const path = this.activeItemPath();\n            if (ObjectUtils.isNotEmpty(path)) {\n                this.bindOutsideClickListener();\n                this.bindResizeListener();\n            }\n            else {\n                this.unbindOutsideClickListener();\n                this.unbindResizeListener();\n            }\n        });\n    }\n    ngOnInit() {\n        this.menubarService.autoHide = this.autoHide;\n        this.menubarService.autoHideDelay = this.autoHideDelay;\n        this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => this.unbindOutsideClickListener());\n        this.id = this.id || UniqueComponentId();\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n                case 'menuicon':\n                    this.menuIconTemplate = item.template;\n                    break;\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n        const processedItems = [];\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n        return processedItems;\n    }\n    getItemProp(item, name) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    menuButtonClick(event) {\n        this.toggle(event);\n    }\n    menuButtonKeydown(event) {\n        (event.code === 'Enter' || event.code === 'Space') && this.menuButtonClick(event);\n    }\n    onItemClick(event) {\n        const { originalEvent, processedItem } = event;\n        const grouped = this.isProcessedItemGroup(processedItem);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        const selected = this.isSelected(processedItem);\n        if (selected) {\n            const { index, key, level, parentKey, item } = processedItem;\n            this.activeItemPath.set(this.activeItemPath().filter((p) => key !== p.key && key.startsWith(p.key)));\n            this.focusedItemInfo.set({ index, level, parentKey, item });\n            this.dirty = !root;\n            DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n        }\n        else {\n            if (grouped) {\n                this.onItemChange(event);\n            }\n            else {\n                const rootProcessedItem = root ? processedItem : this.activeItemPath().find((p) => p.parentKey === '');\n                this.hide(originalEvent);\n                this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n                this.mobileActive = false;\n                DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n            }\n        }\n    }\n    onItemMouseEnter(event) {\n        if (!DomHandler.isTouchDevice()) {\n            if (!this.mobileActive) {\n                this.onItemChange(event);\n            }\n        }\n    }\n    changeFocusedItemIndex(event, index) {\n        const processedItem = this.findVisibleItem(index);\n        if (this.focusedItemInfo().index !== index) {\n            const focusedItemInfo = this.focusedItemInfo();\n            this.focusedItemInfo.set({ ...focusedItemInfo, item: processedItem.item, index });\n            this.scrollInView();\n        }\n    }\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n    onItemChange(event) {\n        const { processedItem, isFocus } = event;\n        if (ObjectUtils.isEmpty(processedItem))\n            return;\n        const { index, key, level, parentKey, items, item } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n        grouped && activeItemPath.push(processedItem);\n        this.focusedItemInfo.set({ index, level, parentKey, item });\n        this.activeItemPath.set(activeItemPath);\n        grouped && (this.dirty = true);\n        isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    }\n    toggle(event) {\n        if (this.mobileActive) {\n            this.mobileActive = false;\n            ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n            this.hide();\n        }\n        else {\n            this.mobileActive = true;\n            ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n            setTimeout(() => {\n                this.show();\n            }, 0);\n        }\n        this.cd.markForCheck();\n        this.bindOutsideClickListener();\n        event.preventDefault();\n    }\n    hide(event, isFocus) {\n        if (this.mobileActive) {\n            setTimeout(() => {\n                DomHandler.focus(this.menubutton.nativeElement);\n            }, 0);\n        }\n        this.activeItemPath.set([]);\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        isFocus && DomHandler.focus(this.rootmenu?.menubarViewChild.nativeElement);\n        this.dirty = false;\n    }\n    show() {\n        const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n        this.focusedItemInfo.set({ index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '', item: processedItem?.item });\n        DomHandler.focus(this.rootmenu?.menubarViewChild.nativeElement);\n    }\n    onMenuFocus(event) {\n        this.focused = true;\n        const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n        const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : { index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '', item: processedItem?.item };\n        this.focusedItemInfo.set(focusedItemInfo);\n        this.onFocus.emit(event);\n    }\n    onMenuBlur(event) {\n        this.focused = false;\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        this.searchValue = '';\n        this.dirty = false;\n        this.onBlur.emit(event);\n    }\n    onKeyDown(event) {\n        const metaKey = event.metaKey || event.ctrlKey;\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n            case 'End':\n                this.onEndKey(event);\n                break;\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n                break;\n        }\n    }\n    findVisibleItem(index) {\n        return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n    }\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n    isProcessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    isSelected(processedItem) {\n        return this.activeItemPath().some((p) => p.key === processedItem.key);\n    }\n    isValidSelectedItem(processedItem) {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n    isValidItem(processedItem) {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n    isItemDisabled(item) {\n        return this.getItemProp(item, 'disabled');\n    }\n    isItemSeparator(item) {\n        return this.getItemProp(item, 'separator');\n    }\n    isItemMatched(processedItem) {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n    isProccessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    searchItems(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n        let itemIndex = -1;\n        let matched = false;\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        }\n        else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n        if (itemIndex !== -1) {\n            this.changeFocusedItemIndex(event, itemIndex);\n        }\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n        return matched;\n    }\n    getProccessedItemLabel(processedItem) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n    getItemLabel(item) {\n        return this.getItemProp(item, 'label');\n    }\n    onArrowDownKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n        if (root) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                this.onArrowRightKey(event);\n            }\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onArrowRightKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = processedItem ? this.activeItemPath().find((p) => p.key === processedItem.parentKey) : null;\n        if (parentItem) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                this.onArrowDownKey(event);\n            }\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onArrowUpKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        if (root) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                const itemIndex = this.findLastItemIndex();\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n        }\n        else {\n            const parentItem = this.activeItemPath().find((p) => p.key === processedItem.parentKey);\n            if (this.focusedItemInfo().index === 0) {\n                this.focusedItemInfo.set({ index: -1, parentKey: parentItem ? parentItem.parentKey : '', item: processedItem.item });\n                this.searchValue = '';\n                this.onArrowLeftKey(event);\n                const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n                this.activeItemPath.set(activeItemPath);\n            }\n            else {\n                const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n        }\n        event.preventDefault();\n    }\n    onArrowLeftKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = processedItem ? this.activeItemPath().find((p) => p.key === processedItem.parentKey) : null;\n        if (parentItem) {\n            this.onItemChange({ originalEvent: event, processedItem: parentItem });\n            const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n            this.activeItemPath.set(activeItemPath);\n            event.preventDefault();\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onHomeKey(event) {\n        this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n    onEndKey(event) {\n        this.changeFocusedItemIndex(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n    onSpaceKey(event) {\n        this.onEnterKey(event);\n    }\n    onEscapeKey(event) {\n        this.hide(event, true);\n        this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n        event.preventDefault();\n    }\n    onTabKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n            !grouped && this.onItemChange({ originalEvent: event, processedItem });\n        }\n        this.hide();\n    }\n    onEnterKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n            anchorElement ? anchorElement.click() : element && element.click();\n        }\n        event.preventDefault();\n    }\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n    findPrevItemIndex(index) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n    findNextItemIndex(index) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    if (!DomHandler.isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n                    this.mobileActive = false;\n                });\n            }\n        }\n    }\n    bindOutsideClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    const isOutsideContainer = this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target);\n                    const isOutsideMenuButton = this.mobileActive && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target);\n                    if (isOutsideContainer) {\n                        isOutsideMenuButton ? (this.mobileActive = false) : this.hide();\n                    }\n                });\n            }\n        }\n    }\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            this.outsideClickListener();\n            this.outsideClickListener = null;\n        }\n    }\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.mouseLeaveSubscriber?.unsubscribe();\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: Menubar, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i5.PrimeNGConfig }, { token: MenubarService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"18.0.1\", type: Menubar, selector: \"p-menubar\", inputs: { model: \"model\", style: \"style\", styleClass: \"styleClass\", autoZIndex: [\"autoZIndex\", \"autoZIndex\", booleanAttribute], baseZIndex: [\"baseZIndex\", \"baseZIndex\", numberAttribute], autoDisplay: [\"autoDisplay\", \"autoDisplay\", booleanAttribute], autoHide: [\"autoHide\", \"autoHide\", booleanAttribute], autoHideDelay: [\"autoHideDelay\", \"autoHideDelay\", numberAttribute], id: \"id\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\" }, outputs: { onFocus: \"onFocus\", onBlur: \"onBlur\" }, host: { classAttribute: \"p-element\" }, providers: [MenubarService], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"menubutton\", first: true, predicate: [\"menubutton\"], descendants: true }, { propertyName: \"rootmenu\", first: true, predicate: [\"rootmenu\"], descendants: true }], ngImport: i0, template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [submenuIconTemplate]=\"submenuIconTemplate\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => BarsIcon), selector: \"BarsIcon\" }, { kind: \"component\", type: i0.forwardRef(() => MenubarSub), selector: \"p-menubarSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\", \"submenuIconTemplate\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: Menubar, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-menubar', template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [submenuIconTemplate]=\"submenuIconTemplate\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, providers: [MenubarService], styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i5.PrimeNGConfig }, { type: MenubarService }], propDecorators: { model: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], baseZIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], autoDisplay: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoHide: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoHideDelay: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], id: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], menubutton: [{\n                type: ViewChild,\n                args: ['menubutton']\n            }], rootmenu: [{\n                type: ViewChild,\n                args: ['rootmenu']\n            }] } });\nclass MenubarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarModule, declarations: [Menubar, MenubarSub], imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon], exports: [Menubar, RouterModule, TooltipModule, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarModule, imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon, RouterModule, TooltipModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: MenubarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon],\n                    exports: [Menubar, RouterModule, TooltipModule, SharedModule],\n                    declarations: [Menubar, MenubarSub]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menubar, MenubarModule, MenubarService, MenubarSub };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC5O,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,eAAe;AAC3E,SAASC,OAAO,EAAEC,QAAQ,QAAQ,MAAM;AACxC,SAASC,QAAQ,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA,kBAAAD,EAAA;EAAA,uBAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAF,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAG,GAAA,GAAAA,CAAA;EAAAC,KAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAL,EAAA,EAAAC,EAAA;EAAAK,SAAA,EAAAN,EAAA;EAAAO,IAAA,EAAAN;AAAA;AAAA,SAAAO,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAO2ChD,EAAE,CAAAkD,SAAA,WA0I1E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GA1IuEnD,EAAE,CAAAoD,aAAA,GAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAsI7B,CAAC,YAAAE,MAAA,CAAAG,qBAAA,CAAAL,gBAAA,CACA,CAAC;IAvIyBnD,EAAE,CAAAyD,WAAA,OAAAJ,MAAA,CAAAK,SAAA,CAAAP,gBAAA;EAAA;AAAA;AAAA,SAAAQ,iEAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAAkD,SAAA,cAqLzD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GArLsDnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SAgLd,CAAC,YAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cACI,CAAC;IAjLMnD,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAG,iEAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAA6D,cAAA,cAsLiG,CAAC;IAtLpG7D,EAAE,CAAA8D,MAAA,EAwLhE,CAAC;IAxL6D9D,EAAE,CAAA+D,YAAA,CAwLzD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GAxLsDnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,OAAAD,MAAA,CAAAW,cAAA,CAAAb,gBAAA,CAsLgG,CAAC;IAtLnGnD,EAAE,CAAAyD,WAAA;IAAFzD,EAAE,CAAAiE,SAAA,CAwLhE,CAAC;IAxL6DjE,EAAE,CAAAkE,kBAAA,MAAAb,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,MAwLhE,CAAC;EAAA;AAAA;AAAA,SAAAiB,wEAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxL6DhD,EAAE,CAAAkD,SAAA,cA0LyF,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GA1L5FnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,cAAAD,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,GAAFnD,EAAE,CAAAqE,cA0LW,CAAC,OAAAhB,MAAA,CAAAW,cAAA,CAAAb,gBAAA,CAAqE,CAAC;IA1LpFnD,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAa,iEAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAA6D,cAAA,cA4LqE,CAAC;IA5LxE7D,EAAE,CAAA8D,MAAA,EA4L8G,CAAC;IA5LjH9D,EAAE,CAAA+D,YAAA,CA4LqH,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GA5LxHnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,oBA4LoE,CAAC;IA5LvEnD,EAAE,CAAAiE,SAAA,CA4L8G,CAAC;IA5LjHjE,EAAE,CAAAuE,iBAAA,CAAAlB,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UA4L8G,CAAC;EAAA;AAAA;AAAA,SAAAqB,wGAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5LjHhD,EAAE,CAAAkD,SAAA,uBAgM6C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAhMhDhD,EAAE,CAAAsD,UAAA,+BAgMV,CAAC;IAhMOtD,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAgB,yGAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAAkD,SAAA,wBAiM+C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAjMlDhD,EAAE,CAAAsD,UAAA,+BAiMT,CAAC;IAjMMtD,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAiB,wFAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAA2E,uBAAA,EA+LjB,CAAC;IA/Lc3E,EAAE,CAAA4E,UAAA,IAAAJ,uGAAA,2BAgM6C,CAAC,IAAAC,wGAAA,4BACC,CAAC;IAjMlDzE,EAAE,CAAA6E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAK,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CAgME,CAAC;IAhMLjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAP,IAgME,CAAC;IAhML9C,EAAE,CAAAiE,SAAA,CAiMI,CAAC;IAjMPjE,EAAE,CAAAsD,UAAA,UAAAD,MAAA,CAAAP,IAiMI,CAAC;EAAA;AAAA;AAAA,SAAAgC,yFAAA9B,EAAA,EAAAC,GAAA;AAAA,SAAA8B,2EAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjMPhD,EAAE,CAAA4E,UAAA,IAAAE,wFAAA,yBAmMgC,CAAC;EAAA;EAAA,IAAA9B,EAAA;IAnMnChD,EAAE,CAAAsD,UAAA,iCAmM+B,CAAC;EAAA;AAAA;AAAA,SAAA0B,yEAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnMlChD,EAAE,CAAA2E,uBAAA,EA8Lf,CAAC;IA9LY3E,EAAE,CAAA4E,UAAA,IAAAF,uFAAA,0BA+LjB,CAAC,IAAAK,0EAAA,gBAIgD,CAAC;IAnMnC/E,EAAE,CAAA6E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAK,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CA+LnB,CAAC;IA/LgBjE,EAAE,CAAAsD,UAAA,UAAAD,MAAA,CAAA4B,mBA+LnB,CAAC;IA/LgBjF,EAAE,CAAAiE,SAAA,CAmMT,CAAC;IAnMMjE,EAAE,CAAAsD,UAAA,qBAAAD,MAAA,CAAA4B,mBAmMT,CAAC;EAAA;AAAA;AAAA,SAAAC,0DAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnMMhD,EAAE,CAAA6D,cAAA,WA4KnE,CAAC;IA5KgE7D,EAAE,CAAA4E,UAAA,IAAAjB,gEAAA,kBAoL/D,CAAC,IAAAC,gEAAA,kBAE+J,CAAC,IAAAQ,uEAAA,gCAtLpGpE,EAAE,CAAAmF,sBAyLxC,CAAC,IAAAb,gEAAA,kBAG4G,CAAC,IAAAU,wEAAA,0BAErF,CAAC;IA9LYhF,EAAE,CAAA+D,YAAA,CAqMhE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAoC,YAAA,GArM6DpF,EAAE,CAAAqF,WAAA;IAAA,MAAAlC,gBAAA,GAAFnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,WAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAwKjB,CAAC,YAxKcnD,EAAE,CAAAsF,eAAA,KAAA7C,GAAA,EAAAY,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAyK6B,CAAC;IAzKhCnD,EAAE,CAAAyD,WAAA,SAAAJ,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAAFnD,EAAE,CAAAuF,aAAA,uBAAAlC,MAAA,CAAAE,WAAA,CAAAJ,gBAAA;IAAFnD,EAAE,CAAAiE,SAAA,CA8KnB,CAAC;IA9KgBjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA8KnB,CAAC;IA9KgBnD,EAAE,CAAAiE,SAAA,CAsLb,CAAC;IAtLUjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAsLb,CAAC,aAAAiC,YAAa,CAAC;IAtLJpF,EAAE,CAAAiE,SAAA,EA4LS,CAAC;IA5LZjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UA4LS,CAAC;IA5LZnD,EAAE,CAAAiE,SAAA,CA8LjB,CAAC;IA9LcjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAmC,WAAA,CAAArC,gBAAA,CA8LjB,CAAC;EAAA;AAAA;AAAA,SAAAsC,iEAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9LchD,EAAE,CAAAkD,SAAA,cAgOxD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAhOqDnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA4Nd,CAAC,YAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cACI,CAAC;IA7NMnD,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAiC,iEAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAA6D,cAAA,cAiOgC,CAAC;IAjOnC7D,EAAE,CAAA8D,MAAA,EAiOiE,CAAC;IAjOpE9D,EAAE,CAAA+D,YAAA,CAiOwE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GAjO3EnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CAiOiE,CAAC;IAjOpEjE,EAAE,CAAAuE,iBAAA,CAAAlB,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,CAiOiE,CAAC;EAAA;AAAA;AAAA,SAAAwC,wEAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjOpEhD,EAAE,CAAAkD,SAAA,cAkO6E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAlOhFnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,cAAAD,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,GAAFnD,EAAE,CAAAqE,cAkOoC,CAAC;IAlOvCrE,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAmC,iEAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAA6D,cAAA,cAmOqE,CAAC;IAnOxE7D,EAAE,CAAA8D,MAAA,EAmO8G,CAAC;IAnOjH9D,EAAE,CAAA+D,YAAA,CAmOqH,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GAnOxHnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,oBAmOoE,CAAC;IAnOvEnD,EAAE,CAAAiE,SAAA,CAmO8G,CAAC;IAnOjHjE,EAAE,CAAAuE,iBAAA,CAAAlB,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAmO8G,CAAC;EAAA;AAAA;AAAA,SAAA0C,wGAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnOjHhD,EAAE,CAAAkD,SAAA,uBAsO6C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAtOhDhD,EAAE,CAAAsD,UAAA,+BAsOV,CAAC;IAtOOtD,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAqC,yGAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAAkD,SAAA,wBAuO+C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAvOlDhD,EAAE,CAAAsD,UAAA,+BAuOT,CAAC;IAvOMtD,EAAE,CAAAyD,WAAA;EAAA;AAAA;AAAA,SAAAsC,wFAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhD,EAAE,CAAA2E,uBAAA,EAqOT,CAAC;IArOM3E,EAAE,CAAA4E,UAAA,IAAAiB,uGAAA,2BAsO6C,CAAC,IAAAC,wGAAA,4BACC,CAAC;IAvOlD9F,EAAE,CAAA6E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAK,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CAsOyC,CAAC;IAtO5CjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAP,IAsOyC,CAAC;IAtO5C9C,EAAE,CAAAiE,SAAA,CAuO2C,CAAC;IAvO9CjE,EAAE,CAAAsD,UAAA,UAAAD,MAAA,CAAAP,IAuO2C,CAAC;EAAA;AAAA;AAAA,SAAAkD,yFAAAhD,EAAA,EAAAC,GAAA;AAAA,SAAAgD,2EAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvO9ChD,EAAE,CAAA4E,UAAA,IAAAoB,wFAAA,yBAyOwC,CAAC;EAAA;EAAA,IAAAhD,EAAA;IAzO3ChD,EAAE,CAAAsD,UAAA,iCAyOuC,CAAC;EAAA;AAAA;AAAA,SAAA4C,yEAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzO1ChD,EAAE,CAAA2E,uBAAA,EAoOf,CAAC;IApOY3E,EAAE,CAAA4E,UAAA,IAAAmB,uFAAA,0BAqOT,CAAC,IAAAE,0EAAA,gBAIgD,CAAC;IAzO3CjG,EAAE,CAAA6E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAFhD,EAAE,CAAAoD,aAAA;IAAA,MAAA+C,UAAA,GAAFnG,EAAE,CAAAqF,WAAA;IAAFrF,EAAE,CAAAiE,SAAA,CAqOX,CAAC;IArOQjE,EAAE,CAAAsD,UAAA,UAAA6C,UAAA,CAAAlB,mBAqOX,CAAC;IArOQjF,EAAE,CAAAiE,SAAA,CAyOD,CAAC;IAzOFjE,EAAE,CAAAsD,UAAA,qBAAA6C,UAAA,CAAAlB,mBAyOD,CAAC;EAAA;AAAA;AAAA,SAAAmB,0DAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzOFhD,EAAE,CAAA6D,cAAA,WAwNnE,CAAC;IAxNgE7D,EAAE,CAAA4E,UAAA,IAAAa,gEAAA,kBAgO/D,CAAC,IAAAC,gEAAA,kBAC8F,CAAC,IAAAC,uEAAA,gCAjOnC3F,EAAE,CAAAmF,sBAkOnC,CAAC,IAAAS,gEAAA,kBACuG,CAAC,IAAAM,wEAAA,0BACrF,CAAC;IApOYlG,EAAE,CAAA+D,YAAA,CA2OhE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAqD,iBAAA,GA3O6DrG,EAAE,CAAAqF,WAAA;IAAA,MAAAlC,gBAAA,GAAFnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,eAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAwMT,CAAC,gBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,gBAIC,CAAC,6CACb,CAAC,4BAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,gCA7MgBnD,EAAE,CAAAsG,eAAA,KAAA5D,GAAA,CA8MqC,CAAC,WAAAW,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WACvD,CAAC,YA/McnD,EAAE,CAAAsF,eAAA,KAAA7C,GAAA,EAAAY,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAgN6B,CAAC,aAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,aAC3C,CAAC,wBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,wBACqB,CAAC,qBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,qBACP,CAAC,uBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,uBACG,CAAC,eAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eACjB,CAAC,UAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UACX,CAAC;IAtNgBnD,EAAE,CAAAyD,WAAA,sBAAAJ,MAAA,CAAAE,WAAA,CAAAJ,gBAAA;IAAFnD,EAAE,CAAAiE,SAAA,CA2NnB,CAAC;IA3NgBjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA2NnB,CAAC;IA3NgBnD,EAAE,CAAAiE,SAAA,CAiOW,CAAC;IAjOdjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAiOW,CAAC,aAAAkD,iBAAkB,CAAC;IAjOjCrG,EAAE,CAAAiE,SAAA,EAmOS,CAAC;IAnOZjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAmOS,CAAC;IAnOZnD,EAAE,CAAAiE,SAAA,CAoOjB,CAAC;IApOcjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAmC,WAAA,CAAArC,gBAAA,CAoOjB,CAAC;EAAA;AAAA;AAAA,SAAAoD,sDAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApOchD,EAAE,CAAA2E,uBAAA,EAkKpC,CAAC;IAlKiC3E,EAAE,CAAA4E,UAAA,IAAAM,yDAAA,gBA4KnE,CAAC,IAAAkB,yDAAA,gBA4CD,CAAC;IAxNgEpG,EAAE,CAAA6E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAG,gBAAA,GAAFnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CAoKhB,CAAC;IApKajE,EAAE,CAAAsD,UAAA,UAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAoKhB,CAAC;IApKanD,EAAE,CAAAiE,SAAA,CAuMjB,CAAC;IAvMcjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAuMjB,CAAC;EAAA;AAAA;AAAA,SAAAqD,sEAAAxD,EAAA,EAAAC,GAAA;AAAA,SAAAwD,wDAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvMchD,EAAE,CAAA4E,UAAA,IAAA4B,qEAAA,qBA8OkC,CAAC;EAAA;AAAA;AAAA,SAAAE,sDAAA1D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9OrChD,EAAE,CAAA2E,uBAAA,EA6OrC,CAAC;IA7OkC3E,EAAE,CAAA4E,UAAA,IAAA6B,uDAAA,gBA8OkC,CAAC;IA9OrCzG,EAAE,CAAA6E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAG,gBAAA,GAAFnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CA8OtB,CAAC;IA9OmBjE,EAAE,CAAAsD,UAAA,qBAAAD,MAAA,CAAAsD,YA8OtB,CAAC,4BA9OmB3G,EAAE,CAAA4G,eAAA,IAAAhE,GAAA,EAAAO,gBAAA,CAAA0D,IAAA,EAAAxD,MAAA,CAAAP,IAAA,CA8OgC,CAAC;EAAA;AAAA;AAAA,SAAAgE,sDAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+D,GAAA,GA9OnC/G,EAAE,CAAAgH,gBAAA;IAAFhH,EAAE,CAAA6D,cAAA,sBA8P3E,CAAC;IA9PwE7D,EAAE,CAAAiH,UAAA,uBAAAC,wFAAAC,MAAA;MAAFnH,EAAE,CAAAoH,aAAA,CAAAL,GAAA;MAAA,MAAA1D,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAqH,WAAA,CA4P1DhE,MAAA,CAAAiE,SAAA,CAAAC,IAAA,CAAAJ,MAAqB,CAAC;IAAA,EAAC,4BAAAK,6FAAAL,MAAA;MA5PiCnH,EAAE,CAAAoH,aAAA,CAAAL,GAAA;MAAA,MAAA1D,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAqH,WAAA,CA6PrDhE,MAAA,CAAAoE,gBAAA,CAAAN,MAAuB,CAAC;IAAA,EAAC;IA7P0BnH,EAAE,CAAA+D,YAAA,CA+P7D,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,gBAAA,GA/P0DnD,EAAE,CAAAoD,aAAA,IAAAP,SAAA;IAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,iBAAAD,MAAA,CAAAsD,YAmP3C,CAAC,UAAAxD,gBAAA,CAAAuE,KACD,CAAC,iBAAArE,MAAA,CAAAsE,YACD,CAAC,gBAAAtE,MAAA,CAAAuE,WACH,CAAC,WAAAvE,MAAA,CAAAwE,MACX,CAAC,mBAAAxE,MAAA,CAAAyE,cACe,CAAC,kBAAAzE,MAAA,CAAA0E,aACH,CAAC,UAAA1E,MAAA,CAAA2E,KAAA,IACb,CAAC,mBAAA3E,MAAA,CAAAW,cAAA,CAAAb,gBAAA,CAC4B,CAAC;EAAA;AAAA;AAAA,SAAA8E,uCAAAjF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkF,GAAA,GA3PqBlI,EAAE,CAAAgH,gBAAA;IAAFhH,EAAE,CAAA6D,cAAA,cAgK/E,CAAC,aAC6K,CAAC;IAjKlG7D,EAAE,CAAAiH,UAAA,mBAAAkB,4DAAAhB,MAAA;MAAFnH,EAAE,CAAAoH,aAAA,CAAAc,GAAA;MAAA,MAAA/E,gBAAA,GAAFnD,EAAE,CAAAoD,aAAA,GAAAP,SAAA;MAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAqH,WAAA,CAiKChE,MAAA,CAAA+E,WAAA,CAAAjB,MAAA,EAAAhE,gBAAiC,CAAC;IAAA,EAAC,wBAAAkF,iEAAAlB,MAAA;MAjKtCnH,EAAE,CAAAoH,aAAA,CAAAc,GAAA;MAAA,MAAA/E,gBAAA,GAAFnD,EAAE,CAAAoD,aAAA,GAAAP,SAAA;MAAA,MAAAQ,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAqH,WAAA,CAiKmDhE,MAAA,CAAAoE,gBAAA,CAAiB;QAAAN,MAAA,EAAAA,MAAA;QAAAmB,aAAA,EAAAnF;MAAwB,CAAC,CAAC;IAAA,EAAC;IAjKjGnD,EAAE,CAAA4E,UAAA,IAAA2B,qDAAA,0BAkKpC,CAAC,IAAAG,qDAAA,0BA2EF,CAAC;IA7OkC1G,EAAE,CAAA+D,YAAA,CAgPtE,CAAC;IAhPmE/D,EAAE,CAAA4E,UAAA,IAAAkC,qDAAA,0BA8P3E,CAAC;IA9PwE9G,EAAE,CAAA+D,YAAA,CAgQ3E,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAuF,MAAA,GAhQwEvI,EAAE,CAAAoD,aAAA;IAAA,MAAAD,gBAAA,GAAAoF,MAAA,CAAA1F,SAAA;IAAA,MAAA2F,SAAA,GAAAD,MAAA,CAAAE,KAAA;IAAA,MAAApF,MAAA,GAAFrD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA0I,UAAA,CAAArF,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eA6J1B,CAAC;IA7JuBnD,EAAE,CAAAsD,UAAA,YAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UA2J7B,CAAC,YAAAE,MAAA,CAAAsF,YAAA,CAAAxF,gBAAA,CACT,CAAC,mBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,mBAGuB,CAAC;IA/JUnD,EAAE,CAAAyD,WAAA,OAAAJ,MAAA,CAAAK,SAAA,CAAAP,gBAAA,sDAAAE,MAAA,CAAAuF,YAAA,CAAAzF,gBAAA,qBAAAE,MAAA,CAAAwF,aAAA,CAAA1F,gBAAA,sBAAAE,MAAA,CAAAyF,cAAA,CAAA3F,gBAAA,iBAAAE,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,oBAAAE,MAAA,CAAAyF,cAAA,CAAA3F,gBAAA,KAAA4F,SAAA,mBAAA1F,MAAA,CAAAmC,WAAA,CAAArC,gBAAA,MAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,mBAAA4F,SAAA,mBAAA1F,MAAA,CAAAmC,WAAA,CAAArC,gBAAA,IAAAE,MAAA,CAAAuF,YAAA,CAAAzF,gBAAA,IAAA4F,SAAA,gBAAA1F,MAAA,CAAA2E,KAAA,sBAAA3E,MAAA,CAAA2F,cAAA,qBAAA3F,MAAA,CAAA4F,eAAA,CAAAT,SAAA;IAAFxI,EAAE,CAAAiE,SAAA,EAiKV,CAAC;IAjKOjE,EAAE,CAAAyD,WAAA;IAAFzD,EAAE,CAAAiE,SAAA,CAkKtC,CAAC;IAlKmCjE,EAAE,CAAAsD,UAAA,UAAAD,MAAA,CAAAsD,YAkKtC,CAAC;IAlKmC3G,EAAE,CAAAiE,SAAA,CA6OvC,CAAC;IA7OoCjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAAsD,YA6OvC,CAAC;IA7OoC3G,EAAE,CAAAiE,SAAA,CAkPP,CAAC;IAlPIjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAA6F,aAAA,CAAA/F,gBAAA,KAAAE,MAAA,CAAAmC,WAAA,CAAArC,gBAAA,CAkPP,CAAC;EAAA;AAAA;AAAA,SAAAgG,kCAAAnG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlPIhD,EAAE,CAAA4E,UAAA,IAAA7B,sCAAA,eA0I/E,CAAC,IAAAkF,sCAAA,gBAsBD,CAAC;EAAA;EAAA,IAAAjF,EAAA;IAAA,MAAAG,gBAAA,GAAAF,GAAA,CAAAJ,SAAA;IAAA,MAAAQ,MAAA,GAhK4ErD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAA6F,aAAA,CAAA/F,gBAAA,KAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAoIE,CAAC;IApILnD,EAAE,CAAAiE,SAAA,CA6IG,CAAC;IA7INjE,EAAE,CAAAsD,UAAA,SAAAD,MAAA,CAAA6F,aAAA,CAAA/F,gBAAA,MAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cA6IG,CAAC;EAAA;AAAA;AAAA,MAAAiG,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAhH,EAAA;EAAA;EAAA,2BAAAA;AAAA;AAAA,SAAAiH,sCAAAxG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7INhD,EAAE,CAAAyJ,kBAAA,EAiiCjB,CAAC;EAAA;AAAA;AAAA,SAAAC,uBAAA1G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjiCchD,EAAE,CAAA6D,cAAA,YAgiCjC,CAAC;IAhiC8B7D,EAAE,CAAA4E,UAAA,IAAA4E,qCAAA,yBAiiChC,CAAC;IAjiC6BxJ,EAAE,CAAA+D,YAAA,CAkiC9E,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA2G,MAAA,GAliC2E3J,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CAiiClC,CAAC;IAjiC+BjE,EAAE,CAAAsD,UAAA,qBAAAqG,MAAA,CAAAC,aAiiClC,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAA7G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjiC+BhD,EAAE,CAAAkD,SAAA,cAijC1C,CAAC;EAAA;AAAA;AAAA,SAAA4G,qCAAA9G,EAAA,EAAAC,GAAA;AAAA,SAAA8G,uBAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjjCuChD,EAAE,CAAA4E,UAAA,IAAAkF,oCAAA,qBAkjC9B,CAAC;EAAA;AAAA;AAAA,SAAAE,qBAAAhH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiH,GAAA,GAljC2BjK,EAAE,CAAAgH,gBAAA;IAAFhH,EAAE,CAAA6D,cAAA,cAgjCnF,CAAC;IAhjCgF7D,EAAE,CAAAiH,UAAA,mBAAAiD,wCAAA/C,MAAA;MAAFnH,EAAE,CAAAoH,aAAA,CAAA6C,GAAA;MAAA,MAAAN,MAAA,GAAF3J,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAqH,WAAA,CA8iCtEsC,MAAA,CAAAQ,eAAA,CAAAhD,MAAsB,CAAC;IAAA,EAAC,qBAAAiD,0CAAAjD,MAAA;MA9iC4CnH,EAAE,CAAAoH,aAAA,CAAA6C,GAAA;MAAA,MAAAN,MAAA,GAAF3J,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAqH,WAAA,CA+iCpEsC,MAAA,CAAAU,iBAAA,CAAAlD,MAAwB,CAAC;IAAA,EAAC;IA/iCwCnH,EAAE,CAAA4E,UAAA,IAAAiF,+BAAA,sBAijC1C,CAAC,IAAAE,sBAAA,eACW,CAAC;IAljC2B/J,EAAE,CAAA+D,YAAA,CAmjChF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA2G,MAAA,GAnjC6E3J,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAyD,WAAA,kBAAAkG,MAAA,CAAAW,KAAA,CAAAC,MAAA,IAAAZ,MAAA,CAAAW,KAAA,CAAAC,MAAA,sCAAAZ,MAAA,CAAAhC,YAAA,mBAAAgC,MAAA,CAAAa,EAAA,gBAAAb,MAAA,CAAAc,MAAA,CAAAC,WAAA,CAAAC,IAAA,CAAAC,UAAA;IAAF5K,EAAE,CAAAiE,SAAA,EAijC9C,CAAC;IAjjC2CjE,EAAE,CAAAsD,UAAA,UAAAqG,MAAA,CAAAkB,gBAijC9C,CAAC;IAjjC2C7K,EAAE,CAAAiE,SAAA,CAkjChC,CAAC;IAljC6BjE,EAAE,CAAAsD,UAAA,qBAAAqG,MAAA,CAAAkB,gBAkjChC,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAA9H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAljC6BhD,EAAE,CAAAyJ,kBAAA,EA0kCnB,CAAC;EAAA;AAAA;AAAA,SAAAsB,uBAAA/H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1kCgBhD,EAAE,CAAA6D,cAAA,aAykCxB,CAAC;IAzkCqB7D,EAAE,CAAA4E,UAAA,IAAAkG,qCAAA,yBA0kClC,CAAC;IA1kC+B9K,EAAE,CAAA+D,YAAA,CA2kC9E,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA2G,MAAA,GA3kC2E3J,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAiE,SAAA,CA0kCpC,CAAC;IA1kCiCjE,EAAE,CAAAsD,UAAA,qBAAAqG,MAAA,CAAAqB,WA0kCpC,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAAjI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1kCiChD,EAAE,CAAA6D,cAAA,aA6kCrD,CAAC;IA7kCkD7D,EAAE,CAAAkL,YAAA,EA8kCnD,CAAC;IA9kCgDlL,EAAE,CAAA+D,YAAA,CA+kC1E,CAAC;EAAA;AAAA;AAplCtB,MAAMoH,cAAc,CAAC;EACjBC,QAAQ;EACRC,aAAa;EACbC,WAAW,GAAG,IAAIrJ,OAAO,CAAC,CAAC;EAC3BsJ,UAAU,GAAG,IAAI,CAACD,WAAW,CAACE,IAAI,CAACrJ,QAAQ,CAAC,MAAMD,QAAQ,CAAC,IAAI,CAACmJ,aAAa,CAAC,CAAC,EAAEjJ,MAAM,CAAEqJ,SAAS,IAAK,IAAI,CAACL,QAAQ,IAAIK,SAAS,CAAC,CAAC;EACnI,OAAOC,IAAI,YAAAC,uBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFT,cAAc;EAAA;EACjH,OAAOU,KAAK,kBAD6E7L,EAAE,CAAA8L,kBAAA;IAAAC,KAAA,EACYZ,cAAc;IAAAa,OAAA,EAAdb,cAAc,CAAAO;EAAA;AACzH;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAH6FjM,EAAE,CAAAkM,iBAAA,CAGJf,cAAc,EAAc,CAAC;IAC5GgB,IAAI,EAAElM;EACV,CAAC,CAAC;AAAA;AACV,MAAMmM,UAAU,CAAC;EACbC,EAAE;EACFC,QAAQ;EACRC,EAAE;EACFC,cAAc;EACd9E,KAAK;EACLf,YAAY;EACZ7D,IAAI,GAAG,KAAK;EACZ2J,UAAU,GAAG,IAAI;EACjBC,UAAU,GAAG,CAAC;EACd/E,YAAY;EACZC,WAAW;EACXC,MAAM;EACN8E,SAAS;EACTC,cAAc;EACd5E,KAAK,GAAG,CAAC;EACTD,aAAa;EACbD,cAAc;EACd7C,mBAAmB;EACnBqC,SAAS,GAAG,IAAIpH,YAAY,CAAC,CAAC;EAC9B2M,cAAc,GAAG,IAAI3M,YAAY,CAAC,CAAC;EACnC4M,SAAS,GAAG,IAAI5M,YAAY,CAAC,CAAC;EAC9B6M,QAAQ,GAAG,IAAI7M,YAAY,CAAC,CAAC;EAC7B8M,WAAW,GAAG,IAAI9M,YAAY,CAAC,CAAC;EAChC+M,gBAAgB;EAChBC,oBAAoB;EACpBC,WAAWA,CAACd,EAAE,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,cAAc,EAAE;IAC1C,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACAY,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAACV,cAAc,CAACjB,UAAU,CAAC8B,SAAS,CAAC,MAAM;MACvE,IAAI,CAACd,EAAE,CAACe,YAAY,CAAC,CAAC;IAC1B,CAAC,CAAC;EACN;EACAlF,WAAWA,CAACmF,KAAK,EAAEjF,aAAa,EAAE;IAC9B,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,SAAS,EAAE;MAAEkF,aAAa,EAAED,KAAK;MAAE1G,IAAI,EAAEyB,aAAa,CAACzB;IAAK,CAAC,CAAC;IAC9F,IAAI,CAACS,SAAS,CAACC,IAAI,CAAC;MAAEiG,aAAa,EAAED,KAAK;MAAEjF,aAAa;MAAEmF,OAAO,EAAE;IAAK,CAAC,CAAC;EAC/E;EACAlK,WAAWA,CAAC+E,aAAa,EAAEoF,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAE;IAC5C,OAAOrF,aAAa,IAAIA,aAAa,CAACzB,IAAI,GAAG/E,WAAW,CAAC8L,YAAY,CAACtF,aAAa,CAACzB,IAAI,CAAC6G,IAAI,CAAC,EAAEC,MAAM,CAAC,GAAG5E,SAAS;EACvH;EACArF,SAASA,CAAC4E,aAAa,EAAE;IACrB,OAAOA,aAAa,CAACzB,IAAI,IAAIyB,aAAa,CAACzB,IAAI,EAAE2D,EAAE,GAAGlC,aAAa,CAACzB,IAAI,CAAC2D,EAAE,GAAG,GAAG,IAAI,CAAC3C,MAAM,IAAIS,aAAa,CAACuF,GAAG,EAAE;EACvH;EACAC,UAAUA,CAACxF,aAAa,EAAE;IACtB,OAAO,IAAI,CAAC5E,SAAS,CAAC4E,aAAa,CAAC;EACxC;EACAtE,cAAcA,CAACsE,aAAa,EAAE;IAC1B,OAAO,GAAG,IAAI,CAACT,MAAM,IAAIS,aAAa,CAACuF,GAAG,QAAQ;EACtD;EACAlF,YAAYA,CAACL,aAAa,EAAE;IACxB,OAAO;MACH,GAAG,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,OAAO,CAAC;MAC3C,YAAY,EAAE,IAAI;MAClB,aAAa,EAAE,IAAI,CAACM,YAAY,CAACN,aAAa,CAAC;MAC/C,mBAAmB,EAAE,IAAI,CAACM,YAAY,CAACN,aAAa,CAAC;MACrD,SAAS,EAAE,IAAI,CAACO,aAAa,CAACP,aAAa,CAAC;MAC5C,YAAY,EAAE,IAAI,CAACQ,cAAc,CAACR,aAAa;IACnD,CAAC;EACL;EACAnE,YAAYA,CAACmE,aAAa,EAAE;IACxB,OAAO,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,OAAO,CAAC;EACnD;EACA9E,qBAAqBA,CAAC8E,aAAa,EAAE;IACjC,OAAO;MACH,GAAG,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,OAAO,CAAC;MAC3C,sBAAsB,EAAE;IAC5B,CAAC;EACL;EACAY,aAAaA,CAACZ,aAAa,EAAE;IACzB,OAAO,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK;EAC/D;EACAM,YAAYA,CAACN,aAAa,EAAE;IACxB,IAAI,IAAI,CAACR,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc,CAACiG,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACH,GAAG,KAAKvF,aAAa,CAACuF,GAAG,CAAC;IAC7E;EACJ;EACA/E,cAAcA,CAACR,aAAa,EAAE;IAC1B,OAAO,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,UAAU,CAAC;EACtD;EACAO,aAAaA,CAACP,aAAa,EAAE;IACzB,OAAO,IAAI,CAACP,aAAa,KAAK,IAAI,CAACrE,SAAS,CAAC4E,aAAa,CAAC;EAC/D;EACA9C,WAAWA,CAAC8C,aAAa,EAAE;IACvB,OAAOxG,WAAW,CAACmM,UAAU,CAAC3F,aAAa,CAACZ,KAAK,CAAC;EACtD;EACAsB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACtB,KAAK,CAACtF,MAAM,CAAEkG,aAAa,IAAK,IAAI,CAACY,aAAa,CAACZ,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,WAAW,CAAC,CAAC,CAACiC,MAAM;EAC1I;EACAtB,eAAeA,CAACR,KAAK,EAAE;IACnB,OAAOA,KAAK,GAAG,IAAI,CAACf,KAAK,CAACwG,KAAK,CAAC,CAAC,EAAEzF,KAAK,CAAC,CAACrG,MAAM,CAAEkG,aAAa,IAAK,IAAI,CAACY,aAAa,CAACZ,aAAa,CAAC,IAAI,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,WAAW,CAAC,CAAC,CAACiC,MAAM,GAAG,CAAC;EACrK;EACA4D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC3B,cAAc,CAAClB,WAAW,CAAC8C,IAAI,CAAC,IAAI,CAAC;EAC9C;EACA3G,gBAAgBA,CAAC4G,KAAK,EAAE;IACpB,IAAI,IAAI,CAACzG,WAAW,EAAE;MAClB,IAAI,CAAC4E,cAAc,CAAClB,WAAW,CAAC8C,IAAI,CAAC,KAAK,CAAC;MAC3C,MAAM;QAAEb,KAAK;QAAEjF;MAAc,CAAC,GAAG+F,KAAK;MACtC,IAAI,CAACxB,cAAc,CAACtF,IAAI,CAAC;QAAEiG,aAAa,EAAED,KAAK;QAAEjF;MAAc,CAAC,CAAC;IACrE;EACJ;EACAgG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,oBAAoB,EAAEqB,WAAW,CAAC,CAAC;EAC5C;EACA,OAAO7C,IAAI,YAAA8C,mBAAA5C,CAAA;IAAA,YAAAA,CAAA,IAAwFQ,UAAU,EAlHpBpM,EAAE,CAAAyO,iBAAA,CAkHoCzO,EAAE,CAAC0O,UAAU,GAlHnD1O,EAAE,CAAAyO,iBAAA,CAkH8DzO,EAAE,CAAC2O,SAAS,GAlH5E3O,EAAE,CAAAyO,iBAAA,CAkHuFzO,EAAE,CAAC4O,iBAAiB,GAlH7G5O,EAAE,CAAAyO,iBAAA,CAkHwHtD,cAAc;EAAA;EACjO,OAAO0D,IAAI,kBAnH8E7O,EAAE,CAAA8O,iBAAA;IAAA3C,IAAA,EAmHJC,UAAU;IAAA2C,SAAA;IAAAC,SAAA,WAAAC,iBAAAjM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAnHRhD,EAAE,CAAAkP,WAAA,CAAA7M,GAAA;MAAA;MAAA,IAAAW,EAAA;QAAA,IAAAmM,EAAA;QAAFnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAApM,GAAA,CAAAgK,gBAAA,GAAAkC,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAA9H,KAAA;MAAAf,YAAA;MAAA7D,IAAA,GAAF9C,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,kBAmHiHvP,gBAAgB;MAAAsM,UAAA,GAnHnIzM,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,8BAmH6KvP,gBAAgB;MAAAuM,UAAA,GAnH/L1M,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,8BAmHyOtP,eAAe;MAAAuH,YAAA,GAnH1P3H,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,kCAmH0SvP,gBAAgB;MAAAyH,WAAA,GAnH5T5H,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,gCAmHyWvP,gBAAgB;MAAA0H,MAAA;MAAA8E,SAAA;MAAAC,cAAA;MAAA5E,KAAA,GAnH3XhI,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,oBAmHketP,eAAe;MAAA2H,aAAA;MAAAD,cAAA;MAAA7C,mBAAA;IAAA;IAAA0K,OAAA;MAAArI,SAAA;MAAAuF,cAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,WAAA;IAAA;IAAA4C,QAAA,GAnHnf5P,EAAE,CAAA6P,wBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oBAAAlN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAmN,GAAA,GAAFnQ,EAAE,CAAAgH,gBAAA;QAAFhH,EAAE,CAAA6D,cAAA,cAiIvF,CAAC;QAjIoF7D,EAAE,CAAAiH,UAAA,mBAAAmJ,wCAAAjJ,MAAA;UAAFnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CAyH1EpE,GAAA,CAAA6J,SAAA,CAAAvF,IAAA,CAAAJ,MAAqB,CAAC;QAAA,EAAC,kBAAAkJ,uCAAAlJ,MAAA;UAzHiDnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CA0H3EpE,GAAA,CAAA8J,QAAA,CAAAxF,IAAA,CAAAJ,MAAoB,CAAC;QAAA,EAAC,qBAAAmJ,0CAAAnJ,MAAA;UA1HmDnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CA8HxEpE,GAAA,CAAA+J,WAAA,CAAAzF,IAAA,CAAAJ,MAAuB,CAAC;QAAA,EAAC;QA9H6CnH,EAAE,CAAA4E,UAAA,IAAAuE,iCAAA,wBAkIX,CAAC;QAlIQnJ,EAAE,CAAA+D,YAAA,CAkQnF,CAAC;MAAA;MAAA,IAAAf,EAAA;QAlQgFhD,EAAE,CAAAsD,UAAA,YAAFtD,EAAE,CAAA4G,eAAA,IAAAtE,GAAA,GAAAW,GAAA,CAAAH,IAAA,EAAAG,GAAA,CAAAH,IAAA,CAsHhB,CAAC,cAKvD,CAAC;QA3HmE9C,EAAE,CAAAyD,WAAA,0CAAAR,GAAA,CAAA0J,SAAA,qBAAA1J,GAAA,CAAA2J,cAAA,QAAA3J,GAAA,CAAAH,IAAA,GAAAG,GAAA,CAAA4E,MAAA,kCAAA5E,GAAA,CAAA8E,aAAA;QAAF/H,EAAE,CAAAiE,SAAA,EAkI9B,CAAC;QAlI2BjE,EAAE,CAAAsD,UAAA,YAAAL,GAAA,CAAAyE,KAkI9B,CAAC;MAAA;IAAA;IAAA6I,YAAA,EAAAA,CAAA,MAiImB3Q,EAAE,CAAC4Q,OAAO,EAAyG5Q,EAAE,CAAC6Q,OAAO,EAAwI7Q,EAAE,CAAC8Q,IAAI,EAAkH9Q,EAAE,CAAC+Q,gBAAgB,EAAyK/Q,EAAE,CAACgR,OAAO,EAAgG3P,EAAE,CAAC4P,UAAU,EAAyP5P,EAAE,CAAC6P,gBAAgB,EAAmOpP,EAAE,CAACqP,MAAM,EAA2EnP,EAAE,CAACoP,OAAO,EAAkWzP,aAAa,EAA+EC,cAAc,EAAgF4K,UAAU;IAAA6E,aAAA;EAAA;AACz3D;AACA;EAAA,QAAAhF,SAAA,oBAAAA,SAAA,KArQ6FjM,EAAE,CAAAkM,iBAAA,CAqQJE,UAAU,EAAc,CAAC;IACxGD,IAAI,EAAE9L,SAAS;IACf6Q,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBlB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACegB,aAAa,EAAE3Q,iBAAiB,CAAC8Q,IAAI;MACrCC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnF,IAAI,EAAEnM,EAAE,CAAC0O;EAAW,CAAC,EAAE;IAAEvC,IAAI,EAAEnM,EAAE,CAAC2O;EAAU,CAAC,EAAE;IAAExC,IAAI,EAAEnM,EAAE,CAAC4O;EAAkB,CAAC,EAAE;IAAEzC,IAAI,EAAEhB;EAAe,CAAC,CAAC,EAAkB;IAAEzD,KAAK,EAAE,CAAC;MACzJyE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEoG,YAAY,EAAE,CAAC;MACfwF,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEuC,IAAI,EAAE,CAAC;MACPqJ,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEpR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsM,UAAU,EAAE,CAAC;MACbN,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEpR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuM,UAAU,EAAE,CAAC;MACbP,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEnR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEuH,YAAY,EAAE,CAAC;MACfwE,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEpR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyH,WAAW,EAAE,CAAC;MACduE,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEpR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0H,MAAM,EAAE,CAAC;MACTsE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEoM,SAAS,EAAE,CAAC;MACZR,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEqM,cAAc,EAAE,CAAC;MACjBT,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEyH,KAAK,EAAE,CAAC;MACRmE,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEnR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE2H,aAAa,EAAE,CAAC;MAChBoE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEuH,cAAc,EAAE,CAAC;MACjBqE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE0E,mBAAmB,EAAE,CAAC;MACtBkH,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE+G,SAAS,EAAE,CAAC;MACZ6E,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEqM,cAAc,EAAE,CAAC;MACjBV,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEsM,SAAS,EAAE,CAAC;MACZX,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEuM,QAAQ,EAAE,CAAC;MACXZ,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEwM,WAAW,EAAE,CAAC;MACdb,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEyM,gBAAgB,EAAE,CAAC;MACnBd,IAAI,EAAE1L,SAAS;MACfyQ,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,QAAQ;EACRC,UAAU;EACVtF,EAAE;EACFC,QAAQ;EACRC,EAAE;EACF9B,MAAM;EACN+B,cAAc;EACd;AACJ;AACA;AACA;EACI,IAAIlC,KAAKA,CAACsH,KAAK,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACF,MAAM,IAAI,EAAE,CAAC;EACvE;EACA,IAAIvH,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACuH,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIG,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIxF,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;AACA;EACI9E,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACIwD,QAAQ;EACR;AACJ;AACA;AACA;EACIC,aAAa,GAAG,GAAG;EACnB;AACJ;AACA;AACA;EACIb,EAAE;EACF;AACJ;AACA;AACA;EACImC,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIsF,OAAO,GAAG,IAAIhS,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACIiS,MAAM,GAAG,IAAIjS,YAAY,CAAC,CAAC;EAC3BkS,SAAS;EACTC,UAAU;EACVC,QAAQ;EACR1I,aAAa;EACboB,WAAW;EACXH,gBAAgB;EAChB5F,mBAAmB;EACnB0B,YAAY;EACZgB,YAAY;EACZ4K,oBAAoB;EACpBC,cAAc;EACdtF,oBAAoB;EACpBuF,KAAK,GAAG,KAAK;EACbC,OAAO,GAAG,KAAK;EACf5K,cAAc,GAAGpH,MAAM,CAAC,EAAE,CAAC;EAC3BiS,MAAM,GAAGjS,MAAM,CAAC,CAAC,CAAC;EAClBkS,eAAe,GAAGlS,MAAM,CAAC;IAAE+H,KAAK,EAAE,CAAC,CAAC;IAAET,KAAK,EAAE,CAAC;IAAE6K,SAAS,EAAE,EAAE;IAAEhM,IAAI,EAAE;EAAK,CAAC,CAAC;EAC5EiM,WAAW,GAAG,EAAE;EAChBC,aAAa;EACbjB,eAAe;EACfD,MAAM;EACN,IAAImB,YAAYA,CAAA,EAAG;IACf,MAAM1K,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAACmL,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrF,GAAG,KAAK,IAAI,CAAC+E,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;IACnG,OAAOvK,aAAa,GAAGA,aAAa,CAACZ,KAAK,GAAG,IAAI,CAACyL,cAAc;EACpE;EACA,IAAIA,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACrB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACvH,MAAM,EAAE;MACvD,IAAI,CAACuH,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACzH,KAAK,IAAI,EAAE,CAAC;IACtE;IACA,OAAO,IAAI,CAACwH,eAAe;EAC/B;EACA,IAAI/J,aAAaA,CAAA,EAAG;IAChB,MAAMqL,WAAW,GAAG,IAAI,CAACR,eAAe,CAAC,CAAC;IAC1C,OAAOQ,WAAW,CAACvM,IAAI,IAAIuM,WAAW,CAACvM,IAAI,EAAE2D,EAAE,GAAG4I,WAAW,CAACvM,IAAI,CAAC2D,EAAE,GAAG4I,WAAW,CAAC3K,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC+B,EAAE,GAAG1I,WAAW,CAACmM,UAAU,CAACmF,WAAW,CAACP,SAAS,CAAC,GAAG,GAAG,GAAGO,WAAW,CAACP,SAAS,GAAG,EAAE,IAAIO,WAAW,CAAC3K,KAAK,EAAE,GAAG,IAAI;EAClO;EACA0E,WAAWA,CAACuE,QAAQ,EAAEC,UAAU,EAAEtF,EAAE,EAAEC,QAAQ,EAAEC,EAAE,EAAE9B,MAAM,EAAE+B,cAAc,EAAE;IACxE,IAAI,CAACkF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACtF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAAC9B,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+B,cAAc,GAAGA,cAAc;IACpC7L,MAAM,CAAC,MAAM;MACT,MAAMqN,IAAI,GAAG,IAAI,CAAClG,cAAc,CAAC,CAAC;MAClC,IAAIhG,WAAW,CAACmM,UAAU,CAACD,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACqF,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC;EACN;EACApG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACZ,cAAc,CAACpB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5C,IAAI,CAACoB,cAAc,CAACnB,aAAa,GAAG,IAAI,CAACA,aAAa;IACtD,IAAI,CAAC6B,oBAAoB,GAAG,IAAI,CAACV,cAAc,CAACjB,UAAU,CAAC8B,SAAS,CAAC,MAAM,IAAI,CAACkG,0BAA0B,CAAC,CAAC,CAAC;IAC7G,IAAI,CAAC/I,EAAE,GAAG,IAAI,CAACA,EAAE,IAAIzI,iBAAiB,CAAC,CAAC;EAC5C;EACA0R,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACrB,SAAS,EAAEsB,OAAO,CAAE7M,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAAC8M,OAAO,CAAC,CAAC;QAClB,KAAK,OAAO;UACR,IAAI,CAAC/J,aAAa,GAAG/C,IAAI,CAACoJ,QAAQ;UAClC;QACJ,KAAK,KAAK;UACN,IAAI,CAACjF,WAAW,GAAGnE,IAAI,CAACoJ,QAAQ;UAChC;QACJ,KAAK,UAAU;UACX,IAAI,CAACpF,gBAAgB,GAAGhE,IAAI,CAACoJ,QAAQ;UACrC;QACJ,KAAK,aAAa;UACd,IAAI,CAAChL,mBAAmB,GAAG4B,IAAI,CAACoJ,QAAQ;UACxC;QACJ,KAAK,MAAM;UACP,IAAI,CAACtJ,YAAY,GAAGE,IAAI,CAACoJ,QAAQ;UACjC;QACJ;UACI,IAAI,CAACtJ,YAAY,GAAGE,IAAI,CAACoJ,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACA8B,oBAAoBA,CAACrK,KAAK,EAAEM,KAAK,GAAG,CAAC,EAAE4L,MAAM,GAAG,CAAC,CAAC,EAAEf,SAAS,GAAG,EAAE,EAAE;IAChE,MAAMM,cAAc,GAAG,EAAE;IACzBzL,KAAK,IACDA,KAAK,CAACgM,OAAO,CAAC,CAAC7M,IAAI,EAAE4B,KAAK,KAAK;MAC3B,MAAMoF,GAAG,GAAG,CAACgF,SAAS,KAAK,EAAE,GAAGA,SAAS,GAAG,GAAG,GAAG,EAAE,IAAIpK,KAAK;MAC7D,MAAMoL,OAAO,GAAG;QACZhN,IAAI;QACJ4B,KAAK;QACLT,KAAK;QACL6F,GAAG;QACH+F,MAAM;QACNf;MACJ,CAAC;MACDgB,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC9B,oBAAoB,CAAClL,IAAI,CAACa,KAAK,EAAEM,KAAK,GAAG,CAAC,EAAE6L,OAAO,EAAEhG,GAAG,CAAC;MACjFsF,cAAc,CAACW,IAAI,CAACD,OAAO,CAAC;IAChC,CAAC,CAAC;IACN,OAAOV,cAAc;EACzB;EACA5P,WAAWA,CAACsD,IAAI,EAAE6G,IAAI,EAAE;IACpB,OAAO7G,IAAI,GAAG/E,WAAW,CAAC8L,YAAY,CAAC/G,IAAI,CAAC6G,IAAI,CAAC,CAAC,GAAG3E,SAAS;EAClE;EACAoB,eAAeA,CAACoD,KAAK,EAAE;IACnB,IAAI,CAACwG,MAAM,CAACxG,KAAK,CAAC;EACtB;EACAlD,iBAAiBA,CAACkD,KAAK,EAAE;IACrB,CAACA,KAAK,CAACyG,IAAI,KAAK,OAAO,IAAIzG,KAAK,CAACyG,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC7J,eAAe,CAACoD,KAAK,CAAC;EACrF;EACAnF,WAAWA,CAACmF,KAAK,EAAE;IACf,MAAM;MAAEC,aAAa;MAAElF;IAAc,CAAC,GAAGiF,KAAK;IAC9C,MAAM0G,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAAC5L,aAAa,CAAC;IACxD,MAAMxF,IAAI,GAAGhB,WAAW,CAACqS,OAAO,CAAC7L,aAAa,CAACsL,MAAM,CAAC;IACtD,MAAMQ,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC/L,aAAa,CAAC;IAC/C,IAAI8L,QAAQ,EAAE;MACV,MAAM;QAAE3L,KAAK;QAAEoF,GAAG;QAAE7F,KAAK;QAAE6K,SAAS;QAAEhM;MAAK,CAAC,GAAGyB,aAAa;MAC5D,IAAI,CAACR,cAAc,CAACwM,GAAG,CAAC,IAAI,CAACxM,cAAc,CAAC,CAAC,CAAC1F,MAAM,CAAE8Q,CAAC,IAAKrF,GAAG,KAAKqF,CAAC,CAACrF,GAAG,IAAIA,GAAG,CAAC0G,UAAU,CAACrB,CAAC,CAACrF,GAAG,CAAC,CAAC,CAAC;MACpG,IAAI,CAAC+E,eAAe,CAAC0B,GAAG,CAAC;QAAE7L,KAAK;QAAET,KAAK;QAAE6K,SAAS;QAAEhM;MAAK,CAAC,CAAC;MAC3D,IAAI,CAAC4L,KAAK,GAAG,CAAC3P,IAAI;MAClBxB,UAAU,CAACkT,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACrF,gBAAgB,CAACwH,aAAa,CAAC;IAClE,CAAC,MACI;MACD,IAAIR,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAACnH,KAAK,CAAC;MAC5B,CAAC,MACI;QACD,MAAMoH,iBAAiB,GAAG7R,IAAI,GAAGwF,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAACmL,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,EAAE,CAAC;QACtG,IAAI,CAAC+B,IAAI,CAACpH,aAAa,CAAC;QACxB,IAAI,CAACqH,sBAAsB,CAACrH,aAAa,EAAEmH,iBAAiB,GAAGA,iBAAiB,CAAClM,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5F,IAAI,CAACd,YAAY,GAAG,KAAK;QACzBrG,UAAU,CAACkT,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACrF,gBAAgB,CAACwH,aAAa,CAAC;MAClE;IACJ;EACJ;EACAhN,gBAAgBA,CAAC8F,KAAK,EAAE;IACpB,IAAI,CAACjM,UAAU,CAACwT,aAAa,CAAC,CAAC,EAAE;MAC7B,IAAI,CAAC,IAAI,CAACnN,YAAY,EAAE;QACpB,IAAI,CAAC+M,YAAY,CAACnH,KAAK,CAAC;MAC5B;IACJ;EACJ;EACAsH,sBAAsBA,CAACtH,KAAK,EAAE9E,KAAK,EAAE;IACjC,MAAMH,aAAa,GAAG,IAAI,CAACyM,eAAe,CAACtM,KAAK,CAAC;IACjD,IAAI,IAAI,CAACmK,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAKA,KAAK,EAAE;MACxC,MAAMmK,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACA,eAAe,CAAC0B,GAAG,CAAC;QAAE,GAAG1B,eAAe;QAAE/L,IAAI,EAAEyB,aAAa,CAACzB,IAAI;QAAE4B;MAAM,CAAC,CAAC;MACjF,IAAI,CAACuM,YAAY,CAAC,CAAC;IACvB;EACJ;EACAA,YAAYA,CAACvM,KAAK,GAAG,CAAC,CAAC,EAAE;IACrB,MAAM+B,EAAE,GAAG/B,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC+B,EAAE,IAAI/B,KAAK,EAAE,GAAG,IAAI,CAACV,aAAa;IACpE,MAAMkN,OAAO,GAAG3T,UAAU,CAAC4T,UAAU,CAAC,IAAI,CAAC5C,QAAQ,CAACjG,EAAE,CAACoI,aAAa,EAAE,UAAUjK,EAAE,IAAI,CAAC;IACvF,IAAIyK,OAAO,EAAE;MACTA,OAAO,CAACE,cAAc,IAAIF,OAAO,CAACE,cAAc,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IAC7F;EACJ;EACAX,YAAYA,CAACnH,KAAK,EAAE;IAChB,MAAM;MAAEjF,aAAa;MAAEmF;IAAQ,CAAC,GAAGF,KAAK;IACxC,IAAIzL,WAAW,CAACqS,OAAO,CAAC7L,aAAa,CAAC,EAClC;IACJ,MAAM;MAAEG,KAAK;MAAEoF,GAAG;MAAE7F,KAAK;MAAE6K,SAAS;MAAEnL,KAAK;MAAEb;IAAK,CAAC,GAAGyB,aAAa;IACnE,MAAM2L,OAAO,GAAGnS,WAAW,CAACmM,UAAU,CAACvG,KAAK,CAAC;IAC7C,MAAMI,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC1F,MAAM,CAAE8Q,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAKA,SAAS,IAAIK,CAAC,CAACL,SAAS,KAAKhF,GAAG,CAAC;IAC5GoG,OAAO,IAAInM,cAAc,CAACgM,IAAI,CAACxL,aAAa,CAAC;IAC7C,IAAI,CAACsK,eAAe,CAAC0B,GAAG,CAAC;MAAE7L,KAAK;MAAET,KAAK;MAAE6K,SAAS;MAAEhM;IAAK,CAAC,CAAC;IAC3D,IAAI,CAACiB,cAAc,CAACwM,GAAG,CAACxM,cAAc,CAAC;IACvCmM,OAAO,KAAK,IAAI,CAACxB,KAAK,GAAG,IAAI,CAAC;IAC9BhF,OAAO,IAAInM,UAAU,CAACkT,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACrF,gBAAgB,CAACwH,aAAa,CAAC;EAC7E;EACAV,MAAMA,CAACxG,KAAK,EAAE;IACV,IAAI,IAAI,CAAC5F,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,GAAG,KAAK;MACzB3F,WAAW,CAACsT,KAAK,CAAC,IAAI,CAAChD,QAAQ,CAACjG,EAAE,CAACoI,aAAa,CAAC;MACjD,IAAI,CAACG,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACjN,YAAY,GAAG,IAAI;MACxB3F,WAAW,CAACsS,GAAG,CAAC,MAAM,EAAE,IAAI,CAAChC,QAAQ,CAACjG,EAAE,CAACoI,aAAa,EAAE,IAAI,CAAChK,MAAM,CAAC8K,MAAM,CAACC,IAAI,CAAC;MAChFC,UAAU,CAAC,MAAM;QACb,IAAI,CAACC,IAAI,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC;IACT;IACA,IAAI,CAACnJ,EAAE,CAACe,YAAY,CAAC,CAAC;IACtB,IAAI,CAAC+F,wBAAwB,CAAC,CAAC;IAC/B9F,KAAK,CAACoI,cAAc,CAAC,CAAC;EAC1B;EACAf,IAAIA,CAACrH,KAAK,EAAEE,OAAO,EAAE;IACjB,IAAI,IAAI,CAAC9F,YAAY,EAAE;MACnB8N,UAAU,CAAC,MAAM;QACbnU,UAAU,CAACkT,KAAK,CAAC,IAAI,CAACnC,UAAU,CAACoC,aAAa,CAAC;MACnD,CAAC,EAAE,CAAC,CAAC;IACT;IACA,IAAI,CAAC3M,cAAc,CAACwM,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAI,CAAC1B,eAAe,CAAC0B,GAAG,CAAC;MAAE7L,KAAK,EAAE,CAAC,CAAC;MAAET,KAAK,EAAE,CAAC;MAAE6K,SAAS,EAAE,EAAE;MAAEhM,IAAI,EAAE;IAAK,CAAC,CAAC;IAC5E4G,OAAO,IAAInM,UAAU,CAACkT,KAAK,CAAC,IAAI,CAAClC,QAAQ,EAAErF,gBAAgB,CAACwH,aAAa,CAAC;IAC1E,IAAI,CAAChC,KAAK,GAAG,KAAK;EACtB;EACAiD,IAAIA,CAAA,EAAG;IACH,MAAMpN,aAAa,GAAG,IAAI,CAACyM,eAAe,CAAC,IAAI,CAACa,yBAAyB,CAAC,CAAC,CAAC;IAC5E,IAAI,CAAChD,eAAe,CAAC0B,GAAG,CAAC;MAAE7L,KAAK,EAAE,IAAI,CAACmN,yBAAyB,CAAC,CAAC;MAAE5N,KAAK,EAAE,CAAC;MAAE6K,SAAS,EAAE,EAAE;MAAEhM,IAAI,EAAEyB,aAAa,EAAEzB;IAAK,CAAC,CAAC;IACzHvF,UAAU,CAACkT,KAAK,CAAC,IAAI,CAAClC,QAAQ,EAAErF,gBAAgB,CAACwH,aAAa,CAAC;EACnE;EACAoB,WAAWA,CAACtI,KAAK,EAAE;IACf,IAAI,CAACmF,OAAO,GAAG,IAAI;IACnB,MAAMpK,aAAa,GAAG,IAAI,CAACyM,eAAe,CAAC,IAAI,CAACa,yBAAyB,CAAC,CAAC,CAAC;IAC5E,MAAMhD,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACmK,eAAe,CAAC,CAAC,GAAG;MAAEnK,KAAK,EAAE,IAAI,CAACmN,yBAAyB,CAAC,CAAC;MAAE5N,KAAK,EAAE,CAAC;MAAE6K,SAAS,EAAE,EAAE;MAAEhM,IAAI,EAAEyB,aAAa,EAAEzB;IAAK,CAAC;IACtL,IAAI,CAAC+L,eAAe,CAAC0B,GAAG,CAAC1B,eAAe,CAAC;IACzC,IAAI,CAACV,OAAO,CAAC3K,IAAI,CAACgG,KAAK,CAAC;EAC5B;EACAuI,UAAUA,CAACvI,KAAK,EAAE;IACd,IAAI,CAACmF,OAAO,GAAG,KAAK;IACpB,IAAI,CAACE,eAAe,CAAC0B,GAAG,CAAC;MAAE7L,KAAK,EAAE,CAAC,CAAC;MAAET,KAAK,EAAE,CAAC;MAAE6K,SAAS,EAAE,EAAE;MAAEhM,IAAI,EAAE;IAAK,CAAC,CAAC;IAC5E,IAAI,CAACiM,WAAW,GAAG,EAAE;IACrB,IAAI,CAACL,KAAK,GAAG,KAAK;IAClB,IAAI,CAACN,MAAM,CAAC5K,IAAI,CAACgG,KAAK,CAAC;EAC3B;EACAwI,SAASA,CAACxI,KAAK,EAAE;IACb,MAAMyI,OAAO,GAAGzI,KAAK,CAACyI,OAAO,IAAIzI,KAAK,CAAC0I,OAAO;IAC9C,QAAQ1I,KAAK,CAACyG,IAAI;MACd,KAAK,WAAW;QACZ,IAAI,CAACkC,cAAc,CAAC3I,KAAK,CAAC;QAC1B;MACJ,KAAK,SAAS;QACV,IAAI,CAAC4I,YAAY,CAAC5I,KAAK,CAAC;QACxB;MACJ,KAAK,WAAW;QACZ,IAAI,CAAC6I,cAAc,CAAC7I,KAAK,CAAC;QAC1B;MACJ,KAAK,YAAY;QACb,IAAI,CAAC8I,eAAe,CAAC9I,KAAK,CAAC;QAC3B;MACJ,KAAK,MAAM;QACP,IAAI,CAAC+I,SAAS,CAAC/I,KAAK,CAAC;QACrB;MACJ,KAAK,KAAK;QACN,IAAI,CAACgJ,QAAQ,CAAChJ,KAAK,CAAC;QACpB;MACJ,KAAK,OAAO;QACR,IAAI,CAACiJ,UAAU,CAACjJ,KAAK,CAAC;QACtB;MACJ,KAAK,OAAO;QACR,IAAI,CAACkJ,UAAU,CAAClJ,KAAK,CAAC;QACtB;MACJ,KAAK,QAAQ;QACT,IAAI,CAACmJ,WAAW,CAACnJ,KAAK,CAAC;QACvB;MACJ,KAAK,KAAK;QACN,IAAI,CAACoJ,QAAQ,CAACpJ,KAAK,CAAC;QACpB;MACJ,KAAK,UAAU;MACf,KAAK,QAAQ;MACb,KAAK,WAAW;MAChB,KAAK,WAAW;MAChB,KAAK,YAAY;QACb;QACA;MACJ;QACI,IAAI,CAACyI,OAAO,IAAIlU,WAAW,CAAC8U,oBAAoB,CAACrJ,KAAK,CAACM,GAAG,CAAC,EAAE;UACzD,IAAI,CAACgJ,WAAW,CAACtJ,KAAK,EAAEA,KAAK,CAACM,GAAG,CAAC;QACtC;QACA;IACR;EACJ;EACAkH,eAAeA,CAACtM,KAAK,EAAE;IACnB,OAAO3G,WAAW,CAACmM,UAAU,CAAC,IAAI,CAAC+E,YAAY,CAAC,GAAG,IAAI,CAACA,YAAY,CAACvK,KAAK,CAAC,GAAG,IAAI;EACtF;EACAmN,yBAAyBA,CAAA,EAAG;IACxB,MAAMkB,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC,CAAC,GAAGF,aAAa;EACxE;EACAE,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAChE,YAAY,CAACiE,SAAS,CAAE3O,aAAa,IAAK,IAAI,CAAC4O,WAAW,CAAC5O,aAAa,CAAC,CAAC;EAC1F;EACAyO,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC/D,YAAY,CAACiE,SAAS,CAAE3O,aAAa,IAAK,IAAI,CAAC6O,mBAAmB,CAAC7O,aAAa,CAAC,CAAC;EAClG;EACA4L,oBAAoBA,CAAC5L,aAAa,EAAE;IAChC,OAAOA,aAAa,IAAIxG,WAAW,CAACmM,UAAU,CAAC3F,aAAa,CAACZ,KAAK,CAAC;EACvE;EACA2M,UAAUA,CAAC/L,aAAa,EAAE;IACtB,OAAO,IAAI,CAACR,cAAc,CAAC,CAAC,CAACiG,IAAI,CAAEmF,CAAC,IAAKA,CAAC,CAACrF,GAAG,KAAKvF,aAAa,CAACuF,GAAG,CAAC;EACzE;EACAsJ,mBAAmBA,CAAC7O,aAAa,EAAE;IAC/B,OAAO,IAAI,CAAC4O,WAAW,CAAC5O,aAAa,CAAC,IAAI,IAAI,CAAC+L,UAAU,CAAC/L,aAAa,CAAC;EAC5E;EACA4O,WAAWA,CAAC5O,aAAa,EAAE;IACvB,OAAO,CAAC,CAACA,aAAa,IAAI,CAAC,IAAI,CAACQ,cAAc,CAACR,aAAa,CAACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAACuQ,eAAe,CAAC9O,aAAa,CAACzB,IAAI,CAAC;EACnH;EACAiC,cAAcA,CAACjC,IAAI,EAAE;IACjB,OAAO,IAAI,CAACtD,WAAW,CAACsD,IAAI,EAAE,UAAU,CAAC;EAC7C;EACAuQ,eAAeA,CAACvQ,IAAI,EAAE;IAClB,OAAO,IAAI,CAACtD,WAAW,CAACsD,IAAI,EAAE,WAAW,CAAC;EAC9C;EACAwQ,aAAaA,CAAC/O,aAAa,EAAE;IACzB,OAAO,IAAI,CAAC4O,WAAW,CAAC5O,aAAa,CAAC,IAAI,IAAI,CAACgP,sBAAsB,CAAChP,aAAa,CAAC,CAACiP,iBAAiB,CAAC,CAAC,CAAChD,UAAU,CAAC,IAAI,CAACzB,WAAW,CAACyE,iBAAiB,CAAC,CAAC,CAAC;EAC7J;EACAC,qBAAqBA,CAAClP,aAAa,EAAE;IACjC,OAAOA,aAAa,IAAIxG,WAAW,CAACmM,UAAU,CAAC3F,aAAa,CAACZ,KAAK,CAAC;EACvE;EACAmP,WAAWA,CAACtJ,KAAK,EAAEkK,IAAI,EAAE;IACrB,IAAI,CAAC3E,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW,IAAI,EAAE,IAAI2E,IAAI;IAClD,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAI,IAAI,CAAC/E,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,EAAE;MACrCiP,SAAS,GAAG,IAAI,CAAC1E,YAAY,CAAC9E,KAAK,CAAC,IAAI,CAAC0E,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC,CAACwO,SAAS,CAAE3O,aAAa,IAAK,IAAI,CAAC+O,aAAa,CAAC/O,aAAa,CAAC,CAAC;MACjIoP,SAAS,GAAGA,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC1E,YAAY,CAAC9E,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC0E,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC,CAACwO,SAAS,CAAE3O,aAAa,IAAK,IAAI,CAAC+O,aAAa,CAAC/O,aAAa,CAAC,CAAC,GAAGoP,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAACnK,KAAK;IACtM,CAAC,MACI;MACDiP,SAAS,GAAG,IAAI,CAAC1E,YAAY,CAACiE,SAAS,CAAE3O,aAAa,IAAK,IAAI,CAAC+O,aAAa,CAAC/O,aAAa,CAAC,CAAC;IACjG;IACA,IAAIoP,SAAS,KAAK,CAAC,CAAC,EAAE;MAClBC,OAAO,GAAG,IAAI;IAClB;IACA,IAAID,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,EAAE;MACzDiP,SAAS,GAAG,IAAI,CAAC9B,yBAAyB,CAAC,CAAC;IAChD;IACA,IAAI8B,SAAS,KAAK,CAAC,CAAC,EAAE;MAClB,IAAI,CAAC7C,sBAAsB,CAACtH,KAAK,EAAEmK,SAAS,CAAC;IACjD;IACA,IAAI,IAAI,CAAC3E,aAAa,EAAE;MACpB6E,YAAY,CAAC,IAAI,CAAC7E,aAAa,CAAC;IACpC;IACA,IAAI,CAACA,aAAa,GAAG0C,UAAU,CAAC,MAAM;MAClC,IAAI,CAAC3C,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,IAAI;IAC7B,CAAC,EAAE,GAAG,CAAC;IACP,OAAO4E,OAAO;EAClB;EACAL,sBAAsBA,CAAChP,aAAa,EAAE;IAClC,OAAOA,aAAa,GAAG,IAAI,CAACnE,YAAY,CAACmE,aAAa,CAACzB,IAAI,CAAC,GAAGkC,SAAS;EAC5E;EACA5E,YAAYA,CAAC0C,IAAI,EAAE;IACf,OAAO,IAAI,CAACtD,WAAW,CAACsD,IAAI,EAAE,OAAO,CAAC;EAC1C;EACAqP,cAAcA,CAAC3I,KAAK,EAAE;IAClB,MAAMjF,aAAa,GAAG,IAAI,CAAC0K,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC;IACrE,MAAM3F,IAAI,GAAGwF,aAAa,GAAGxG,WAAW,CAACqS,OAAO,CAAC7L,aAAa,CAACsL,MAAM,CAAC,GAAG,IAAI;IAC7E,IAAI9Q,IAAI,EAAE;MACN,MAAMmR,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAClP,aAAa,CAAC;MACzD,IAAI2L,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAAC;UAAElH,aAAa,EAAED,KAAK;UAAEjF;QAAc,CAAC,CAAC;QAC1D,IAAI,CAACsK,eAAe,CAAC0B,GAAG,CAAC;UAAE7L,KAAK,EAAE,CAAC,CAAC;UAAEoK,SAAS,EAAEvK,aAAa,CAACuF,GAAG;UAAEhH,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QAC/F,IAAI,CAACwP,eAAe,CAAC9I,KAAK,CAAC;MAC/B;IACJ,CAAC,MACI;MACD,MAAMmK,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACoP,iBAAiB,CAAC,IAAI,CAACjF,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC,GAAG,IAAI,CAACmN,yBAAyB,CAAC,CAAC;MAC/I,IAAI,CAACf,sBAAsB,CAACtH,KAAK,EAAEmK,SAAS,CAAC;MAC7CnK,KAAK,CAACoI,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAU,eAAeA,CAAC9I,KAAK,EAAE;IACnB,MAAMjF,aAAa,GAAG,IAAI,CAAC0K,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC;IACrE,MAAMqP,UAAU,GAAGxP,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAACmL,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrF,GAAG,KAAKvF,aAAa,CAACuK,SAAS,CAAC,GAAG,IAAI;IAC9G,IAAIiF,UAAU,EAAE;MACZ,MAAM7D,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAClP,aAAa,CAAC;MACzD,IAAI2L,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAAC;UAAElH,aAAa,EAAED,KAAK;UAAEjF;QAAc,CAAC,CAAC;QAC1D,IAAI,CAACsK,eAAe,CAAC0B,GAAG,CAAC;UAAE7L,KAAK,EAAE,CAAC,CAAC;UAAEoK,SAAS,EAAEvK,aAAa,CAACuF,GAAG;UAAEhH,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QAC/F,IAAI,CAACqP,cAAc,CAAC3I,KAAK,CAAC;MAC9B;IACJ,CAAC,MACI;MACD,MAAMmK,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACoP,iBAAiB,CAAC,IAAI,CAACjF,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC,GAAG,IAAI,CAACmN,yBAAyB,CAAC,CAAC;MAC/I,IAAI,CAACf,sBAAsB,CAACtH,KAAK,EAAEmK,SAAS,CAAC;MAC7CnK,KAAK,CAACoI,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAQ,YAAYA,CAAC5I,KAAK,EAAE;IAChB,MAAMjF,aAAa,GAAG,IAAI,CAAC0K,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC;IACrE,MAAM3F,IAAI,GAAGhB,WAAW,CAACqS,OAAO,CAAC7L,aAAa,CAACsL,MAAM,CAAC;IACtD,IAAI9Q,IAAI,EAAE;MACN,MAAMmR,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAClP,aAAa,CAAC;MACzD,IAAI2L,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAAC;UAAElH,aAAa,EAAED,KAAK;UAAEjF;QAAc,CAAC,CAAC;QAC1D,IAAI,CAACsK,eAAe,CAAC0B,GAAG,CAAC;UAAE7L,KAAK,EAAE,CAAC,CAAC;UAAEoK,SAAS,EAAEvK,aAAa,CAACuF,GAAG;UAAEhH,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QAC/F,MAAM6Q,SAAS,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;QAC1C,IAAI,CAAClD,sBAAsB,CAACtH,KAAK,EAAEmK,SAAS,CAAC;MACjD;IACJ,CAAC,MACI;MACD,MAAMI,UAAU,GAAG,IAAI,CAAChQ,cAAc,CAAC,CAAC,CAACmL,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrF,GAAG,KAAKvF,aAAa,CAACuK,SAAS,CAAC;MACvF,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,EAAE;QACpC,IAAI,CAACmK,eAAe,CAAC0B,GAAG,CAAC;UAAE7L,KAAK,EAAE,CAAC,CAAC;UAAEoK,SAAS,EAAEiF,UAAU,GAAGA,UAAU,CAACjF,SAAS,GAAG,EAAE;UAAEhM,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QACpH,IAAI,CAACiM,WAAW,GAAG,EAAE;QACrB,IAAI,CAACsD,cAAc,CAAC7I,KAAK,CAAC;QAC1B,MAAMzF,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC1F,MAAM,CAAE8Q,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;QAC5G,IAAI,CAAC/K,cAAc,CAACwM,GAAG,CAACxM,cAAc,CAAC;MAC3C,CAAC,MACI;QACD,MAAM4P,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACuP,iBAAiB,CAAC,IAAI,CAACpF,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC,GAAG,IAAI,CAACwP,wBAAwB,CAAC,CAAC;QAC9I,IAAI,CAACpD,sBAAsB,CAACtH,KAAK,EAAEmK,SAAS,CAAC;MACjD;IACJ;IACAnK,KAAK,CAACoI,cAAc,CAAC,CAAC;EAC1B;EACAS,cAAcA,CAAC7I,KAAK,EAAE;IAClB,MAAMjF,aAAa,GAAG,IAAI,CAAC0K,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC;IACrE,MAAMqP,UAAU,GAAGxP,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAACmL,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrF,GAAG,KAAKvF,aAAa,CAACuK,SAAS,CAAC,GAAG,IAAI;IAC9G,IAAIiF,UAAU,EAAE;MACZ,IAAI,CAACpD,YAAY,CAAC;QAAElH,aAAa,EAAED,KAAK;QAAEjF,aAAa,EAAEwP;MAAW,CAAC,CAAC;MACtE,MAAMhQ,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC1F,MAAM,CAAE8Q,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;MAC5G,IAAI,CAAC/K,cAAc,CAACwM,GAAG,CAACxM,cAAc,CAAC;MACvCyF,KAAK,CAACoI,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,MAAM+B,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACuP,iBAAiB,CAAC,IAAI,CAACpF,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC,GAAG,IAAI,CAACwP,wBAAwB,CAAC,CAAC;MAC9I,IAAI,CAACpD,sBAAsB,CAACtH,KAAK,EAAEmK,SAAS,CAAC;MAC7CnK,KAAK,CAACoI,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAW,SAASA,CAAC/I,KAAK,EAAE;IACb,IAAI,CAACsH,sBAAsB,CAACtH,KAAK,EAAE,IAAI,CAACyJ,kBAAkB,CAAC,CAAC,CAAC;IAC7DzJ,KAAK,CAACoI,cAAc,CAAC,CAAC;EAC1B;EACAY,QAAQA,CAAChJ,KAAK,EAAE;IACZ,IAAI,CAACsH,sBAAsB,CAACtH,KAAK,EAAE,IAAI,CAACwK,iBAAiB,CAAC,CAAC,CAAC;IAC5DxK,KAAK,CAACoI,cAAc,CAAC,CAAC;EAC1B;EACAa,UAAUA,CAACjJ,KAAK,EAAE;IACd,IAAI,CAACkJ,UAAU,CAAClJ,KAAK,CAAC;EAC1B;EACAmJ,WAAWA,CAACnJ,KAAK,EAAE;IACf,IAAI,CAACqH,IAAI,CAACrH,KAAK,EAAE,IAAI,CAAC;IACtB,IAAI,CAACqF,eAAe,CAAC,CAAC,CAACnK,KAAK,GAAG,IAAI,CAACmN,yBAAyB,CAAC,CAAC;IAC/DrI,KAAK,CAACoI,cAAc,CAAC,CAAC;EAC1B;EACAgB,QAAQA,CAACpJ,KAAK,EAAE;IACZ,IAAI,IAAI,CAACqF,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMH,aAAa,GAAG,IAAI,CAAC0K,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACnK,KAAK,CAAC;MACrE,MAAMwL,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAClP,aAAa,CAAC;MACzD,CAAC2L,OAAO,IAAI,IAAI,CAACS,YAAY,CAAC;QAAElH,aAAa,EAAED,KAAK;QAAEjF;MAAc,CAAC,CAAC;IAC1E;IACA,IAAI,CAACsM,IAAI,CAAC,CAAC;EACf;EACA6B,UAAUA,CAAClJ,KAAK,EAAE;IACd,IAAI,IAAI,CAACqF,eAAe,CAAC,CAAC,CAACnK,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMwM,OAAO,GAAG3T,UAAU,CAAC4T,UAAU,CAAC,IAAI,CAAC5C,QAAQ,CAACjG,EAAE,CAACoI,aAAa,EAAE,UAAU,GAAG,IAAI,CAAC1M,aAAa,EAAE,IAAI,CAAC;MAC5G,MAAMmQ,aAAa,GAAGjD,OAAO,IAAI3T,UAAU,CAAC4T,UAAU,CAACD,OAAO,EAAE,6BAA6B,CAAC;MAC9FiD,aAAa,GAAGA,aAAa,CAACC,KAAK,CAAC,CAAC,GAAGlD,OAAO,IAAIA,OAAO,CAACkD,KAAK,CAAC,CAAC;IACtE;IACA5K,KAAK,CAACoI,cAAc,CAAC,CAAC;EAC1B;EACAsC,wBAAwBA,CAAA,EAAG;IACvB,MAAMnB,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAACiB,iBAAiB,CAAC,CAAC,GAAGjB,aAAa;EACvE;EACAiB,iBAAiBA,CAAA,EAAG;IAChB,OAAOjW,WAAW,CAACsW,aAAa,CAAC,IAAI,CAACpF,YAAY,EAAG1K,aAAa,IAAK,IAAI,CAAC4O,WAAW,CAAC5O,aAAa,CAAC,CAAC;EAC3G;EACA0P,iBAAiBA,CAACvP,KAAK,EAAE;IACrB,MAAM4P,gBAAgB,GAAG5P,KAAK,GAAG,CAAC,GAAG3G,WAAW,CAACsW,aAAa,CAAC,IAAI,CAACpF,YAAY,CAAC9E,KAAK,CAAC,CAAC,EAAEzF,KAAK,CAAC,EAAGH,aAAa,IAAK,IAAI,CAAC4O,WAAW,CAAC5O,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1J,OAAO+P,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAG5P,KAAK;EAC3D;EACAoP,iBAAiBA,CAACpP,KAAK,EAAE;IACrB,MAAM4P,gBAAgB,GAAG5P,KAAK,GAAG,IAAI,CAACuK,YAAY,CAACzI,MAAM,GAAG,CAAC,GAAG,IAAI,CAACyI,YAAY,CAAC9E,KAAK,CAACzF,KAAK,GAAG,CAAC,CAAC,CAACwO,SAAS,CAAE3O,aAAa,IAAK,IAAI,CAAC4O,WAAW,CAAC5O,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IACrK,OAAO+P,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAG5P,KAAK,GAAG,CAAC,GAAGA,KAAK;EACvE;EACA6K,kBAAkBA,CAAA,EAAG;IACjB,IAAIzT,iBAAiB,CAAC,IAAI,CAAC8R,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACa,cAAc,EAAE;QACtB,IAAI,CAACA,cAAc,GAAG,IAAI,CAAClG,QAAQ,CAACgM,MAAM,CAAC,IAAI,CAAC5G,QAAQ,CAAC6G,WAAW,EAAE,QAAQ,EAAGhL,KAAK,IAAK;UACvF,IAAI,CAACjM,UAAU,CAACwT,aAAa,CAAC,CAAC,EAAE;YAC7B,IAAI,CAACF,IAAI,CAACrH,KAAK,EAAE,IAAI,CAAC;UAC1B;UACA,IAAI,CAAC5F,YAAY,GAAG,KAAK;QAC7B,CAAC,CAAC;MACN;IACJ;EACJ;EACA0L,wBAAwBA,CAAA,EAAG;IACvB,IAAIxT,iBAAiB,CAAC,IAAI,CAAC8R,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACY,oBAAoB,EAAE;QAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAACjG,QAAQ,CAACgM,MAAM,CAAC,IAAI,CAAC5G,QAAQ,EAAE,OAAO,EAAGnE,KAAK,IAAK;UAChF,MAAMiL,kBAAkB,GAAG,IAAI,CAAClG,QAAQ,CAACjG,EAAE,CAACoI,aAAa,KAAKlH,KAAK,CAACkL,MAAM,IAAI,CAAC,IAAI,CAACnG,QAAQ,CAACjG,EAAE,CAACoI,aAAa,CAACiE,QAAQ,CAACnL,KAAK,CAACkL,MAAM,CAAC;UACpI,MAAME,mBAAmB,GAAG,IAAI,CAAChR,YAAY,IAAI,IAAI,CAAC0K,UAAU,CAACoC,aAAa,KAAKlH,KAAK,CAACkL,MAAM,IAAI,CAAC,IAAI,CAACpG,UAAU,CAACoC,aAAa,CAACiE,QAAQ,CAACnL,KAAK,CAACkL,MAAM,CAAC;UACxJ,IAAID,kBAAkB,EAAE;YACpBG,mBAAmB,GAAI,IAAI,CAAChR,YAAY,GAAG,KAAK,GAAI,IAAI,CAACiN,IAAI,CAAC,CAAC;UACnE;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACArB,0BAA0BA,CAAA,EAAG;IACzB,IAAI,IAAI,CAAChB,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACAiB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAChB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;EACAlE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,oBAAoB,EAAEqB,WAAW,CAAC,CAAC;IACxC,IAAI,CAACgF,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA,OAAO9H,IAAI,YAAAkN,gBAAAhN,CAAA;IAAA,YAAAA,CAAA,IAAwF6F,OAAO,EA7hCjBzR,EAAE,CAAAyO,iBAAA,CA6hCiC3O,QAAQ,GA7hC3CE,EAAE,CAAAyO,iBAAA,CA6hCsD7N,WAAW,GA7hCnEZ,EAAE,CAAAyO,iBAAA,CA6hC8EzO,EAAE,CAAC0O,UAAU,GA7hC7F1O,EAAE,CAAAyO,iBAAA,CA6hCwGzO,EAAE,CAAC2O,SAAS,GA7hCtH3O,EAAE,CAAAyO,iBAAA,CA6hCiIzO,EAAE,CAAC4O,iBAAiB,GA7hCvJ5O,EAAE,CAAAyO,iBAAA,CA6hCkKtN,EAAE,CAAC0X,aAAa,GA7hCpL7Y,EAAE,CAAAyO,iBAAA,CA6hC+LtD,cAAc;EAAA;EACxS,OAAO0D,IAAI,kBA9hC8E7O,EAAE,CAAA8O,iBAAA;IAAA3C,IAAA,EA8hCJsF,OAAO;IAAA1C,SAAA;IAAA+J,cAAA,WAAAC,uBAAA/V,EAAA,EAAAC,GAAA,EAAA+V,QAAA;MAAA,IAAAhW,EAAA;QA9hCLhD,EAAE,CAAAiZ,cAAA,CAAAD,QAAA,EA8hC+nB5X,aAAa;MAAA;MAAA,IAAA4B,EAAA;QAAA,IAAAmM,EAAA;QA9hC9oBnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAApM,GAAA,CAAAmP,SAAA,GAAAjD,EAAA;MAAA;IAAA;IAAAH,SAAA,WAAAkK,cAAAlW,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhD,EAAE,CAAAkP,WAAA,CAAA9F,GAAA;QAAFpJ,EAAE,CAAAkP,WAAA,CAAA7F,GAAA;MAAA;MAAA,IAAArG,EAAA;QAAA,IAAAmM,EAAA;QAAFnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAApM,GAAA,CAAAoP,UAAA,GAAAlD,EAAA,CAAAG,KAAA;QAAFtP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAApM,GAAA,CAAAqP,QAAA,GAAAnD,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAlF,KAAA;MAAA0H,KAAA;MAAAC,UAAA;MAAAxF,UAAA,GAAFzM,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,8BA8hCyIvP,gBAAgB;MAAAuM,UAAA,GA9hC3J1M,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,8BA8hCqMtP,eAAe;MAAAwH,WAAA,GA9hCtN5H,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,gCA8hCmQvP,gBAAgB;MAAAiL,QAAA,GA9hCrRpL,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,0BA8hCyTvP,gBAAgB;MAAAkL,aAAA,GA9hC3UrL,EAAE,CAAAyP,YAAA,CAAAC,0BAAA,oCA8hC8XtP,eAAe;MAAAoK,EAAA;MAAAmC,SAAA;MAAAC,cAAA;IAAA;IAAA+C,OAAA;MAAAuC,OAAA;MAAAC,MAAA;IAAA;IAAAvC,QAAA,GA9hC/Y5P,EAAE,CAAAmZ,kBAAA,CA8hC2jB,CAAChO,cAAc,CAAC,GA9hC7kBnL,EAAE,CAAA6P,wBAAA;IAAAuJ,kBAAA,EAAA9P,GAAA;IAAAwG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAoJ,iBAAArW,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAmN,GAAA,GAAFnQ,EAAE,CAAAgH,gBAAA;QAAFhH,EAAE,CAAAsZ,eAAA;QAAFtZ,EAAE,CAAA6D,cAAA,YA+hC2G,CAAC;QA/hC9G7D,EAAE,CAAA4E,UAAA,IAAA8E,sBAAA,gBAgiCjC,CAAC,IAAAM,oBAAA,cAgBnD,CAAC;QAhjCgFhK,EAAE,CAAA6D,cAAA,wBAwkCnF,CAAC;QAxkCgF7D,EAAE,CAAAiH,UAAA,uBAAAsS,mDAAApS,MAAA;UAAFnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CAmkClEpE,GAAA,CAAAmF,WAAA,CAAAjB,MAAkB,CAAC;QAAA,EAAC,uBAAAqS,mDAAArS,MAAA;UAnkC4CnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CAokClEpE,GAAA,CAAA4S,WAAA,CAAA1O,MAAkB,CAAC;QAAA,EAAC,sBAAAsS,kDAAAtS,MAAA;UApkC4CnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CAqkCnEpE,GAAA,CAAA6S,UAAA,CAAA3O,MAAiB,CAAC;QAAA,EAAC,yBAAAuS,qDAAAvS,MAAA;UArkC8CnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CAskChEpE,GAAA,CAAA8S,SAAA,CAAA5O,MAAgB,CAAC;QAAA,EAAC,4BAAAwS,wDAAAxS,MAAA;UAtkC4CnH,EAAE,CAAAoH,aAAA,CAAA+I,GAAA;UAAA,OAAFnQ,EAAE,CAAAqH,WAAA,CAukC7DpE,GAAA,CAAAwE,gBAAA,CAAAN,MAAuB,CAAC;QAAA,EAAC;QAvkCkCnH,EAAE,CAAA+D,YAAA,CAwkCpE,CAAC;QAxkCiE/D,EAAE,CAAA4E,UAAA,IAAAmG,sBAAA,gBAykCxB,CAAC,IAAAE,8BAAA,gCAzkCqBjL,EAAE,CAAAmF,sBA4kC/D,CAAC;QA5kC4DnF,EAAE,CAAA+D,YAAA,CAilClF,CAAC;MAAA;MAAA,IAAAf,EAAA;QAAA,MAAA4W,SAAA,GAjlC+E5Z,EAAE,CAAAqF,WAAA;QAAFrF,EAAE,CAAA0I,UAAA,CAAAzF,GAAA,CAAAgP,UA+hCwB,CAAC;QA/hC3BjS,EAAE,CAAAsD,UAAA,YAAFtD,EAAE,CAAAsF,eAAA,KAAAiE,GAAA,EAAAtG,GAAA,CAAA0E,YAAA,CA+hCG,CAAC,YAAA1E,GAAA,CAAA+O,KAAsC,CAAC;QA/hC7ChS,EAAE,CAAAyD,WAAA;QAAFzD,EAAE,CAAAiE,SAAA,CAgiCnC,CAAC;QAhiCgCjE,EAAE,CAAAsD,UAAA,SAAAL,GAAA,CAAA2G,aAgiCnC,CAAC;QAhiCgC5J,EAAE,CAAAiE,SAAA,CA4iChD,CAAC;QA5iC6CjE,EAAE,CAAAsD,UAAA,SAAAL,GAAA,CAAAqH,KAAA,IAAArH,GAAA,CAAAqH,KAAA,CAAAC,MAAA,IA4iChD,CAAC;QA5iC6CvK,EAAE,CAAAiE,SAAA,CAsjCxD,CAAC;QAtjCqDjE,EAAE,CAAAsD,UAAA,UAAAL,GAAA,CAAAkQ,cAsjCxD,CAAC,iBAAAlQ,GAAA,CAAA0D,YACI,CAAC,WAAA1D,GAAA,CAAAuH,EACjB,CAAC,aACD,CAAC,eAAAvH,GAAA,CAAAyJ,UACW,CAAC,eAAAzJ,GAAA,CAAAwJ,UACD,CAAC,iBAAAxJ,GAAA,CAAA0E,YACG,CAAC,gBAAA1E,GAAA,CAAA2E,WACH,CAAC,cAAA3E,GAAA,CAAA0J,SACL,CAAC,mBAAA1J,GAAA,CAAA2J,cACS,CAAC,kBAAA3J,GAAA,CAAAyP,OAAA,GAAAzP,GAAA,CAAA8E,aAAA,GAAAgB,SACmB,CAAC,wBAAA9F,GAAA,CAAAgC,mBACX,CAAC,mBAAAhC,GAAA,CAAA6E,cAAA,EACT,CAAC;QAlkC0C9H,EAAE,CAAAiE,SAAA,EAykCrC,CAAC;QAzkCkCjE,EAAE,CAAAsD,UAAA,SAAAL,GAAA,CAAA+H,WAykCrC,CAAC,aAAA4O,SAAU,CAAC;MAAA;IAAA;IAAArJ,YAAA,EAAAA,CAAA,MAS2lC3Q,EAAE,CAAC4Q,OAAO,EAAyG5Q,EAAE,CAAC8Q,IAAI,EAAkH9Q,EAAE,CAAC+Q,gBAAgB,EAAyK/Q,EAAE,CAACgR,OAAO,EAAgGnP,QAAQ,EAA0E2K,UAAU;IAAAyN,MAAA;IAAA5I,aAAA;IAAA6I,eAAA;EAAA;AAC/wD;AACA;EAAA,QAAA7N,SAAA,oBAAAA,SAAA,KAplC6FjM,EAAE,CAAAkM,iBAAA,CAolCJuF,OAAO,EAAc,CAAC;IACrGtF,IAAI,EAAE9L,SAAS;IACf6Q,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAElB,QAAQ,EAAE;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE6J,eAAe,EAAEjZ,uBAAuB,CAACkZ,MAAM;MAAE9I,aAAa,EAAE3Q,iBAAiB,CAAC8Q,IAAI;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE;MACX,CAAC;MAAE0I,SAAS,EAAE,CAAC7O,cAAc,CAAC;MAAE0O,MAAM,EAAE,CAAC,gkCAAgkC;IAAE,CAAC;EACxnC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1N,IAAI,EAAE8N,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9C/N,IAAI,EAAErL,MAAM;MACZoQ,IAAI,EAAE,CAACpR,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEqM,IAAI,EAAEpD,SAAS;IAAEmR,UAAU,EAAE,CAAC;MAClC/N,IAAI,EAAErL,MAAM;MACZoQ,IAAI,EAAE,CAACtQ,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEuL,IAAI,EAAEnM,EAAE,CAAC0O;EAAW,CAAC,EAAE;IAAEvC,IAAI,EAAEnM,EAAE,CAAC2O;EAAU,CAAC,EAAE;IAAExC,IAAI,EAAEnM,EAAE,CAAC4O;EAAkB,CAAC,EAAE;IAAEzC,IAAI,EAAEhL,EAAE,CAAC0X;EAAc,CAAC,EAAE;IAAE1M,IAAI,EAAEhB;EAAe,CAAC,CAAC,EAAkB;IAAEb,KAAK,EAAE,CAAC;MACxK6B,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEyR,KAAK,EAAE,CAAC;MACR7F,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE0R,UAAU,EAAE,CAAC;MACb9F,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEkM,UAAU,EAAE,CAAC;MACbN,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEpR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuM,UAAU,EAAE,CAAC;MACbP,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEnR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEwH,WAAW,EAAE,CAAC;MACduE,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEpR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiL,QAAQ,EAAE,CAAC;MACXe,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEpR;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkL,aAAa,EAAE,CAAC;MAChBc,IAAI,EAAE5L,KAAK;MACX2Q,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAEnR;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEoK,EAAE,EAAE,CAAC;MACL2B,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEoM,SAAS,EAAE,CAAC;MACZR,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEqM,cAAc,EAAE,CAAC;MACjBT,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE2R,OAAO,EAAE,CAAC;MACV/F,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAE2R,MAAM,EAAE,CAAC;MACThG,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAE4R,SAAS,EAAE,CAAC;MACZjG,IAAI,EAAEpL,eAAe;MACrBmQ,IAAI,EAAE,CAAC9P,aAAa;IACxB,CAAC,CAAC;IAAEiR,UAAU,EAAE,CAAC;MACblG,IAAI,EAAE1L,SAAS;MACfyQ,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEoB,QAAQ,EAAE,CAAC;MACXnG,IAAI,EAAE1L,SAAS;MACfyQ,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMiJ,aAAa,CAAC;EAChB,OAAOzO,IAAI,YAAA0O,sBAAAxO,CAAA;IAAA,YAAAA,CAAA,IAAwFuO,aAAa;EAAA;EAChH,OAAOE,IAAI,kBA9rC8Era,EAAE,CAAAsa,gBAAA;IAAAnO,IAAA,EA8rCSgO;EAAa;EACjH,OAAOI,IAAI,kBA/rC8Eva,EAAE,CAAAwa,gBAAA;IAAAC,OAAA,GA+rCkC1a,YAAY,EAAEmB,YAAY,EAAES,YAAY,EAAEE,aAAa,EAAER,YAAY,EAAEI,QAAQ,EAAEF,aAAa,EAAEC,cAAc,EAAEN,YAAY,EAAEW,aAAa,EAAER,YAAY;EAAA;AAC1R;AACA;EAAA,QAAA4K,SAAA,oBAAAA,SAAA,KAjsC6FjM,EAAE,CAAAkM,iBAAA,CAisCJiO,aAAa,EAAc,CAAC;IAC3GhO,IAAI,EAAEnL,QAAQ;IACdkQ,IAAI,EAAE,CAAC;MACCuJ,OAAO,EAAE,CAAC1a,YAAY,EAAEmB,YAAY,EAAES,YAAY,EAAEE,aAAa,EAAER,YAAY,EAAEI,QAAQ,EAAEF,aAAa,EAAEC,cAAc,CAAC;MACzHkZ,OAAO,EAAE,CAACjJ,OAAO,EAAEvQ,YAAY,EAAEW,aAAa,EAAER,YAAY,CAAC;MAC7DsZ,YAAY,EAAE,CAAClJ,OAAO,EAAErF,UAAU;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASqF,OAAO,EAAE0I,aAAa,EAAEhP,cAAc,EAAEiB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}