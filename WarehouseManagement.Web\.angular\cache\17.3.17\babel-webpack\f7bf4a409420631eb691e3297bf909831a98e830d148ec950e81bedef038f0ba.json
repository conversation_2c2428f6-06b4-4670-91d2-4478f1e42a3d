{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let LanguageService = /*#__PURE__*/(() => {\n  class LanguageService {\n    constructor() {\n      this.STORAGE_KEY = 'selected-language';\n      this.languages = [{\n        code: 'en',\n        name: 'English',\n        direction: 'ltr',\n        flag: '🇺🇸'\n      }, {\n        code: 'ar',\n        name: 'العربية',\n        direction: 'rtl',\n        flag: '🇸🇦'\n      }];\n      this.currentLanguageSubject = new BehaviorSubject(this.getDefaultLanguage());\n      this.currentLanguage$ = this.currentLanguageSubject.asObservable();\n      this.initializeLanguage();\n    }\n    getDefaultLanguage() {\n      const savedLanguage = localStorage.getItem(this.STORAGE_KEY);\n      if (savedLanguage) {\n        const found = this.languages.find(lang => lang.code === savedLanguage);\n        if (found) return found;\n      }\n      // Default to Arabic\n      return this.languages.find(lang => lang.code === 'ar') || this.languages[0];\n    }\n    initializeLanguage() {\n      const currentLang = this.currentLanguageSubject.value;\n      this.applyLanguageSettings(currentLang);\n    }\n    getCurrentLanguage() {\n      return this.currentLanguageSubject.value;\n    }\n    getAvailableLanguages() {\n      return [...this.languages];\n    }\n    setLanguage(languageCode) {\n      const language = this.languages.find(lang => lang.code === languageCode);\n      if (language && language.code !== this.currentLanguageSubject.value.code) {\n        this.currentLanguageSubject.next(language);\n        localStorage.setItem(this.STORAGE_KEY, languageCode);\n        this.applyLanguageSettings(language);\n      }\n    }\n    applyLanguageSettings(language) {\n      // Set document direction\n      document.documentElement.dir = language.direction;\n      document.documentElement.lang = language.code;\n      // Add/remove RTL class to body for styling\n      if (language.direction === 'rtl') {\n        document.body.classList.add('rtl');\n        document.body.classList.remove('ltr');\n      } else {\n        document.body.classList.add('ltr');\n        document.body.classList.remove('rtl');\n      }\n      // Update PrimeNG configuration for RTL\n      this.updatePrimeNGDirection(language.direction);\n    }\n    updatePrimeNGDirection(direction) {\n      // This will be used to configure PrimeNG components for RTL\n      const primeNGConfig = window.PrimeNG?.config;\n      if (primeNGConfig) {\n        primeNGConfig.ripple = true;\n        primeNGConfig.inputStyle = 'outlined';\n        primeNGConfig.rtl = direction === 'rtl';\n      }\n    }\n    isRTL() {\n      return this.currentLanguageSubject.value.direction === 'rtl';\n    }\n    isArabic() {\n      return this.currentLanguageSubject.value.code === 'ar';\n    }\n    // Translation helper methods\n    translate(key, params) {\n      // This is a simple implementation. In a real app, you'd use Angular i18n or ngx-translate\n      const translations = this.getTranslations();\n      const currentLang = this.getCurrentLanguage().code;\n      let translation = translations[currentLang]?.[key] || translations['en']?.[key] || key;\n      // Simple parameter replacement\n      if (params) {\n        Object.keys(params).forEach(param => {\n          translation = translation.replace(`{{${param}}}`, params[param]);\n        });\n      }\n      return translation;\n    }\n    getTranslations() {\n      return {\n        en: {\n          // Navigation\n          'nav.dashboard': 'Dashboard',\n          'nav.items': 'Items',\n          'nav.categories': 'Categories',\n          'nav.warehouses': 'Warehouses',\n          'nav.inventory': 'Inventory',\n          'nav.customers': 'Customers',\n          'nav.suppliers': 'Suppliers',\n          'nav.invoices': 'Invoices',\n          'nav.payments': 'Payments',\n          'nav.reports': 'Reports',\n          'nav.settings': 'Settings',\n          'nav.logout': 'Logout',\n          // Dashboard\n          'dashboard.title': 'Dashboard',\n          'dashboard.totalSales': 'Total Sales',\n          'dashboard.totalPurchases': 'Total Purchases',\n          'dashboard.totalItems': 'Total Items',\n          'dashboard.lowStockItems': 'Low Stock Items',\n          'dashboard.recentTransactions': 'Recent Transactions',\n          'dashboard.salesTrend': 'Sales Trend',\n          'dashboard.inventoryOverview': 'Inventory Overview',\n          'dashboard.monthlyRevenue': 'Monthly Revenue',\n          // Common\n          'common.search': 'Search',\n          'common.add': 'Add',\n          'common.edit': 'Edit',\n          'common.delete': 'Delete',\n          'common.save': 'Save',\n          'common.cancel': 'Cancel',\n          'common.confirm': 'Confirm',\n          'common.yes': 'Yes',\n          'common.no': 'No',\n          'common.loading': 'Loading...',\n          'common.noData': 'No data available',\n          'common.actions': 'Actions',\n          'common.status': 'Status',\n          'common.date': 'Date',\n          'common.amount': 'Amount',\n          'common.total': 'Total',\n          'common.refresh': 'Refresh'\n        },\n        ar: {\n          // App\n          'app.title': 'نظام إدارة المستودعات',\n          'app.welcome': 'مرحباً بك',\n          'app.profile': 'الملف الشخصي',\n          'app.notifications': 'الإشعارات',\n          // Navigation\n          'nav.dashboard': 'لوحة التحكم',\n          'nav.inventoryManagement': 'إدارة المخزون',\n          'nav.items': 'الأصناف',\n          'nav.categories': 'الفئات',\n          'nav.warehouses': 'المستودعات',\n          'nav.stockMovements': 'حركات المخزون',\n          'nav.stockAdjustments': 'تسويات المخزون',\n          'nav.transfers': 'التحويلات',\n          'nav.salesPurchases': 'المبيعات والمشتريات',\n          'nav.salesInvoices': 'فواتير المبيعات',\n          'nav.purchaseInvoices': 'فواتير المشتريات',\n          'nav.salesReturns': 'مرتجعات المبيعات',\n          'nav.purchaseReturns': 'مرتجعات المشتريات',\n          'nav.customersSuppliers': 'العملاء والموردين',\n          'nav.customers': 'العملاء',\n          'nav.suppliers': 'الموردين',\n          'nav.financialManagement': 'الإدارة المالية',\n          'nav.payments': 'المدفوعات',\n          'nav.accountStatements': 'كشوف الحسابات',\n          'nav.cashRegister': 'الخزينة',\n          'nav.reports': 'التقارير',\n          'nav.inventoryReports': 'تقارير المخزون',\n          'nav.financialReports': 'التقارير المالية',\n          'nav.salesReports': 'تقارير المبيعات',\n          'nav.purchaseReports': 'تقارير المشتريات',\n          'nav.settings': 'الإعدادات',\n          'nav.logout': 'تسجيل الخروج',\n          // Dashboard\n          'dashboard.title': 'لوحة التحكم',\n          'dashboard.totalSales': 'إجمالي المبيعات',\n          'dashboard.totalPurchases': 'إجمالي المشتريات',\n          'dashboard.totalItems': 'إجمالي الأصناف',\n          'dashboard.lowStockItems': 'أصناف منخفضة المخزون',\n          'dashboard.recentTransactions': 'المعاملات الأخيرة',\n          'dashboard.salesTrend': 'اتجاه المبيعات',\n          'dashboard.inventoryOverview': 'نظرة عامة على المخزون',\n          'dashboard.monthlyRevenue': 'الإيرادات الشهرية',\n          // Common\n          'common.search': 'بحث',\n          'common.add': 'إضافة',\n          'common.edit': 'تعديل',\n          'common.delete': 'حذف',\n          'common.save': 'حفظ',\n          'common.cancel': 'إلغاء',\n          'common.confirm': 'تأكيد',\n          'common.yes': 'نعم',\n          'common.no': 'لا',\n          'common.loading': 'جاري التحميل...',\n          'common.noData': 'لا توجد بيانات متاحة',\n          'common.actions': 'الإجراءات',\n          'common.status': 'الحالة',\n          'common.date': 'التاريخ',\n          'common.amount': 'المبلغ',\n          'common.total': 'الإجمالي',\n          'common.refresh': 'تحديث'\n        }\n      };\n    }\n    static {\n      this.ɵfac = function LanguageService_Factory(t) {\n        return new (t || LanguageService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LanguageService,\n        factory: LanguageService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return LanguageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}