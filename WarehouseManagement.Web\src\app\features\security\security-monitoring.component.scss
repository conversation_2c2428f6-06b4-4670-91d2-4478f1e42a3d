.security-monitoring {
  padding: 1rem;

  .card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb;

    h5 {
      margin-top: 0;
      margin-bottom: 1rem;
      color: #374151;
      font-weight: 600;
    }
  }

  // Severity badges
  .severity-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;

    &.severity-critical {
      background-color: #fee2e2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    &.severity-high {
      background-color: #fed7aa;
      color: #ea580c;
      border: 1px solid #fdba74;
    }

    &.severity-medium {
      background-color: #fef3c7;
      color: #d97706;
      border: 1px solid #fde68a;
    }

    &.severity-low {
      background-color: #dcfce7;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }
  }

  // Status badges
  .status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;

    &.success {
      background-color: #dcfce7;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    &.failure {
      background-color: #fee2e2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
  }

  // Metrics cards
  .grid .card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .text-3xl {
      margin-bottom: 0.5rem;
    }

    .text-2xl {
      margin-bottom: 0.25rem;
    }

    .text-sm {
      opacity: 0.7;
    }
  }

  // Table styling
  :host ::ng-deep {
    .p-datatable {
      .p-datatable-header {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        padding: 1rem;
      }

      .p-datatable-thead > tr > th {
        background: #f9fafb;
        color: #374151;
        font-weight: 600;
        border-bottom: 1px solid #e5e7eb;
        padding: 0.75rem;
      }

      .p-datatable-tbody > tr > td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
      }

      .p-datatable-tbody > tr:hover {
        background: #f9fafb;
      }

      .p-datatable-emptymessage > td {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
        font-style: italic;
      }
    }

    // Paginator styling
    .p-paginator {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-top: none;
      padding: 0.75rem 1rem;

      .p-paginator-pages .p-paginator-page {
        color: #374151;
        border: 1px solid #d1d5db;
        margin: 0 0.125rem;

        &.p-highlight {
          background: #3b82f6;
          border-color: #3b82f6;
          color: white;
        }

        &:hover:not(.p-highlight) {
          background: #f3f4f6;
        }
      }
    }

    // Tab view styling
    .p-tabview {
      .p-tabview-nav {
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;

        li {
          .p-tabview-nav-link {
            color: #6b7280;
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 500;

            &:hover {
              background: #f3f4f6;
              color: #374151;
            }
          }

          &.p-highlight .p-tabview-nav-link {
            color: #3b82f6;
            border-bottom: 2px solid #3b82f6;
            background: white;
          }
        }
      }

      .p-tabview-panels {
        background: white;
        padding: 1.5rem;
      }
    }

    // Chart styling
    .p-chart {
      canvas {
        max-height: 300px;
      }
    }

    // Dropdown styling
    .p-dropdown {
      border: 1px solid #d1d5db;

      &:not(.p-disabled):hover {
        border-color: #9ca3af;
      }

      &.p-focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 1px #3b82f6;
      }
    }

    // Calendar styling
    .p-calendar {
      .p-inputtext {
        border: 1px solid #d1d5db;

        &:hover {
          border-color: #9ca3af;
        }

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 1px #3b82f6;
        }
      }
    }

    // Button styling
    .p-button {
      &.p-button-outlined {
        border: 1px solid #d1d5db;
        color: #374151;

        &:hover {
          background: #f3f4f6;
          border-color: #9ca3af;
        }
      }

      &.p-button-primary {
        background: #3b82f6;
        border-color: #3b82f6;

        &:hover {
          background: #2563eb;
          border-color: #2563eb;
        }
      }

      &.p-button-rounded {
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
      }

      &.p-button-text {
        color: #6b7280;

        &:hover {
          background: #f3f4f6;
          color: #374151;
        }
      }
    }

    // Input switch styling
    .p-inputswitch {
      &.p-inputswitch-checked .p-inputswitch-slider {
        background: #3b82f6;
      }

      .p-inputswitch-slider {
        background: #d1d5db;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 0.5rem;

    .card {
      padding: 1rem;
    }

    .grid .col-12 {
      padding: 0.25rem;
    }

    .flex.gap-2 {
      flex-direction: column;
      gap: 0.5rem;

      .flex-1 {
        width: 100%;
      }
    }
  }

  // RTL support
  &[dir="rtl"] {
    .flex {
      flex-direction: row-reverse;
    }

    .text-left {
      text-align: right;
    }

    .text-right {
      text-align: left;
    }

    :host ::ng-deep {
      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        text-align: right;
      }

      .p-tabview-nav li {
        margin-left: 0;
        margin-right: 0.25rem;
      }
    }
  }

  // Dark mode support (if needed)
  @media (prefers-color-scheme: dark) {
    .card {
      background: #1f2937;
      border-color: #374151;
      color: #f9fafb;

      h5 {
        color: #f9fafb;
      }
    }

    :host ::ng-deep {
      .p-datatable {
        .p-datatable-header,
        .p-datatable-thead > tr > th {
          background: #374151;
          color: #f9fafb;
          border-color: #4b5563;
        }

        .p-datatable-tbody > tr > td {
          border-color: #4b5563;
          color: #f9fafb;
        }

        .p-datatable-tbody > tr:hover {
          background: #374151;
        }
      }

      .p-paginator {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
      }

      .p-tabview .p-tabview-nav {
        background: #374151;
        border-color: #4b5563;
      }
    }
  }
}
