{"ast": null, "code": "import { map } from \"../operators/map\";\nconst {\n  isArray\n} = Array;\nfunction callOrApply(fn, args) {\n  return isArray(args) ? fn(...args) : fn(args);\n}\nexport function mapOneOrManyArgs(fn) {\n  return map(args => callOrApply(fn, args));\n}", "map": {"version": 3, "names": ["map", "isArray", "Array", "callOrApply", "fn", "args", "mapOneOrManyArgs"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/rxjs/dist/esm/internal/util/mapOneOrManyArgs.js"], "sourcesContent": ["import { map } from \"../operators/map\";\nconst { isArray } = Array;\nfunction callOrApply(fn, args) {\n    return isArray(args) ? fn(...args) : fn(args);\n}\nexport function mapOneOrManyArgs(fn) {\n    return map(args => callOrApply(fn, args));\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,kBAAkB;AACtC,MAAM;EAAEC;AAAQ,CAAC,GAAGC,KAAK;AACzB,SAASC,WAAWA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC3B,OAAOJ,OAAO,CAACI,IAAI,CAAC,GAAGD,EAAE,CAAC,GAAGC,IAAI,CAAC,GAAGD,EAAE,CAACC,IAAI,CAAC;AACjD;AACA,OAAO,SAASC,gBAAgBA,CAACF,EAAE,EAAE;EACjC,OAAOJ,GAAG,CAACK,IAAI,IAAIF,WAAW,CAACC,EAAE,EAAEC,IAAI,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}