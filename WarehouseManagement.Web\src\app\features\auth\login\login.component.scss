.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;

  &.rtl {
    direction: rtl;
  }
}

.login-wrapper {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
}

.language-switcher {
  position: absolute;
  top: -60px;
  right: 0;
  z-index: 3;

  .rtl & {
    right: auto;
    left: 0;
  }

  button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }
  }
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 3rem 2.5rem;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .logo {
    margin-bottom: 1rem;
  }

  .login-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 0.5rem 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .login-subtitle {
    color: var(--text-color-secondary);
    margin: 0;
    font-size: 1rem;
  }
}

.login-form {
  .field {
    margin-bottom: 1.5rem;

    .field-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: var(--text-color);
      font-size: 0.9rem;

      .required-asterisk {
        color: #ef4444;
        margin-left: 0.25rem;

        .rtl & {
          margin-left: 0;
          margin-right: 0.25rem;
        }
      }
    }

    .p-inputgroup {
      .p-inputgroup-addon {
        background: var(--surface-100);
        border-color: var(--surface-300);
        color: var(--text-color-secondary);
        min-width: 3rem;
        justify-content: center;

        &.cursor-pointer {
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: var(--surface-200);
            color: var(--primary-color);
          }
        }
      }

      input {
        border-color: var(--surface-300);
        transition: all 0.2s ease;

        &:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.2);
        }

        &.ng-invalid.ng-touched {
          border-color: #ef4444;
          
          &:focus {
            box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.2);
          }
        }
      }
    }

    .p-error {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.8rem;
    }
  }

  .field-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;

    .checkbox-label {
      margin-left: 0.5rem;
      font-size: 0.9rem;
      color: var(--text-color-secondary);
      cursor: pointer;

      .rtl & {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }
  }

  .attempts-warning {
    margin-bottom: 1.5rem;

    ::ng-deep .p-message {
      margin: 0;
    }
  }

  .login-button {
    height: 3rem;
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:not(:disabled):hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:not(:disabled):hover::before {
      left: 100%;
    }
  }

  .forgot-password {
    text-align: center;
    margin-top: 1.5rem;

    .forgot-password-link {
      color: var(--primary-color);
      text-decoration: none;
      font-size: 0.9rem;
      transition: all 0.2s ease;

      &:hover {
        text-decoration: underline;
        color: var(--primary-color-text);
      }
    }
  }
}

.lockout-warning {
  margin-bottom: 1.5rem;

  ::ng-deep .p-message {
    margin: 0;
  }
}

.login-footer {
  margin-top: 2rem;
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-200);

  .footer-text {
    color: var(--text-color-secondary);
    font-size: 0.8rem;
    margin: 0 0 0.5rem 0;
  }

  .footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;

    .footer-link {
      color: var(--text-color-secondary);
      text-decoration: none;
      font-size: 0.8rem;
      transition: color 0.2s ease;

      &:hover {
        color: var(--primary-color);
      }
    }

    .footer-separator {
      color: var(--text-color-secondary);
      font-size: 0.8rem;
    }
  }
}

.security-notice {
  margin-top: 1.5rem;

  ::ng-deep .p-message {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 12px;
  }
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;

  .background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    padding: 2rem 1.5rem;
  }

  .login-header {
    .login-title {
      font-size: 1.5rem;
    }
  }

  .language-switcher {
    position: static;
    margin-bottom: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 1.5rem 1rem;
  }

  .login-form {
    .field {
      margin-bottom: 1rem;
    }

    .login-button {
      height: 2.5rem;
    }
  }
}

// Dark theme support
:host-context(.dark-theme) {
  .login-card {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .login-header {
    .login-title {
      color: white;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .login-card {
    background: white;
    border: 2px solid black;
  }

  .login-button {
    background: black;
    color: white;
  }
}

// RTL Toast styling
::ng-deep .rtl-toast {
  .p-toast-message {
    direction: rtl;
    text-align: right;

    .p-toast-message-content {
      flex-direction: row-reverse;
    }

    .p-toast-message-text {
      text-align: right;
    }
  }
}
