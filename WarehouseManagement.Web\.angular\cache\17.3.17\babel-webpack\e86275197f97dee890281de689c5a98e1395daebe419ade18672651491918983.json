{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:7001/api',\n  appName: 'Warehouse Management System',\n  version: '1.0.0',\n  // Security Configuration\n  security: {\n    enableCSRF: true,\n    enableRateLimiting: true,\n    enableSecurityHeaders: true,\n    enableXSSProtection: true,\n    sessionTimeout: 15,\n    maxLoginAttempts: 5,\n    lockoutDuration: 15,\n    passwordPolicy: {\n      minLength: 8,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecialChars: true,\n      preventCommonPasswords: true\n    },\n    rateLimits: {\n      login: {\n        maxAttempts: 5,\n        windowMinutes: 15\n      },\n      api: {\n        maxAttempts: 100,\n        windowMinutes: 1\n      },\n      passwordReset: {\n        maxAttempts: 3,\n        windowMinutes: 60\n      }\n    }\n  },\n  // Feature Flags\n  features: {\n    enableBiometricAuth: false,\n    enableTwoFactorAuth: false,\n    enableAuditLogging: true,\n    enableSecurityMonitoring: true\n  }\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "appName", "version", "security", "enableCSRF", "enableRateLimiting", "enableSecurityHeaders", "enableXSSProtection", "sessionTimeout", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "passwordPolicy", "<PERSON><PERSON><PERSON><PERSON>", "requireUppercase", "requireLowercase", "requireNumbers", "requireSpecialChars", "preventCommonPasswords", "rateLimits", "login", "maxAttempts", "windowMinutes", "api", "passwordReset", "features", "enableBiometricAuth", "enableTwoFactorAuth", "enableAuditLogging", "enableSecurityMonitoring"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:7001/api',\n  appName: 'Warehouse Management System',\n  version: '1.0.0',\n\n  // Security Configuration\n  security: {\n    enableCSRF: true,\n    enableRateLimiting: true,\n    enableSecurityHeaders: true,\n    enableXSSProtection: true,\n    sessionTimeout: 15, // minutes\n    maxLoginAttempts: 5,\n    lockoutDuration: 15, // minutes\n    passwordPolicy: {\n      minLength: 8,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecialChars: true,\n      preventCommonPasswords: true\n    },\n    rateLimits: {\n      login: { maxAttempts: 5, windowMinutes: 15 },\n      api: { maxAttempts: 100, windowMinutes: 1 },\n      passwordReset: { maxAttempts: 3, windowMinutes: 60 }\n    }\n  },\n\n  // Feature Flags\n  features: {\n    enableBiometricAuth: false,\n    enableTwoFactorAuth: false,\n    enableAuditLogging: true,\n    enableSecurityMonitoring: true\n  }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,4BAA4B;EACpCC,OAAO,EAAE,6BAA6B;EACtCC,OAAO,EAAE,OAAO;EAEhB;EACAC,QAAQ,EAAE;IACRC,UAAU,EAAE,IAAI;IAChBC,kBAAkB,EAAE,IAAI;IACxBC,qBAAqB,EAAE,IAAI;IAC3BC,mBAAmB,EAAE,IAAI;IACzBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE;MACdC,SAAS,EAAE,CAAC;MACZC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,sBAAsB,EAAE;KACzB;IACDC,UAAU,EAAE;MACVC,KAAK,EAAE;QAAEC,WAAW,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAE,CAAE;MAC5CC,GAAG,EAAE;QAAEF,WAAW,EAAE,GAAG;QAAEC,aAAa,EAAE;MAAC,CAAE;MAC3CE,aAAa,EAAE;QAAEH,WAAW,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAE;;GAErD;EAED;EACAG,QAAQ,EAAE;IACRC,mBAAmB,EAAE,KAAK;IAC1BC,mBAAmB,EAAE,KAAK;IAC1BC,kBAAkB,EAAE,IAAI;IACxBC,wBAAwB,EAAE;;CAE7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}