{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=WarehouseManagementDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "WarehouseManagement", "Audience": "WarehouseManagement", "AccessTokenExpirationMinutes": 15, "RefreshTokenExpirationDays": 7}, "Security": {"MaxLoginAttempts": 5, "LockoutDurationMinutes": 15, "PasswordPolicy": {"MinLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireNumbers": true, "RequireSpecialChars": true, "PreventCommonPasswords": true}, "RateLimits": {"Login": {"MaxAttempts": 5, "WindowMinutes": 15}, "Api": {"MaxAttempts": 100, "WindowMinutes": 1}}}, "FileUpload": {"MaxFileSize": 5242880, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif"], "UploadPath": "wwwroot/uploads/images"}}