import { parseTranslation, translate as _translate } from './utils';
/**
 * Load translations for use by `$localize`, if doing runtime translation.
 *
 * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible
 * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,
 * in the browser.
 *
 * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.
 *
 * Note that `$localize` messages are only processed once, when the tagged string is first
 * encountered, and does not provide dynamic language changing without refreshing the browser.
 * Loading new translations later in the application life-cycle will not change the translated text
 * of messages that have already been translated.
 *
 * The message IDs and translations are in the same format as that rendered to "simple JSON"
 * translation files when extracting messages. In particular, placeholders in messages are rendered
 * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:
 *
 * ```html
 * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>
 * ```
 *
 * would have the following form in the `translations` map:
 *
 * ```ts
 * {
 *   "2932901491976224757":
 *      "pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post"
 * }
 * ```
 *
 * @param translations A map from message ID to translated message.
 *
 * These messages are processed and added to a lookup based on their `MessageId`.
 *
 * @see {@link clearTranslations} for removing translations loaded using this function.
 * @see {@link $localize} for tagging messages as needing to be translated.
 * @publicApi
 */
export function loadTranslations(translations) {
    // Ensure the translate function exists
    if (!$localize.translate) {
        $localize.translate = translate;
    }
    if (!$localize.TRANSLATIONS) {
        $localize.TRANSLATIONS = {};
    }
    Object.keys(translations).forEach(key => {
        $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);
    });
}
/**
 * Remove all translations for `$localize`, if doing runtime translation.
 *
 * All translations that had been loading into memory using `loadTranslations()` will be removed.
 *
 * @see {@link loadTranslations} for loading translations at runtime.
 * @see {@link $localize} for tagging messages as needing to be translated.
 *
 * @publicApi
 */
export function clearTranslations() {
    $localize.translate = undefined;
    $localize.TRANSLATIONS = {};
}
/**
 * Translate the text of the given message, using the loaded translations.
 *
 * This function may reorder (or remove) substitutions as indicated in the matching translation.
 */
export function translate(messageParts, substitutions) {
    try {
        return _translate($localize.TRANSLATIONS, messageParts, substitutions);
    }
    catch (e) {
        console.warn(e.message);
        return [messageParts, substitutions];
    }
}
//# sourceMappingURL=data:application/json;base64,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