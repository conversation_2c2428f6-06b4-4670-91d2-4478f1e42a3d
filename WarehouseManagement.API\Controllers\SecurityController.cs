using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WarehouseManagement.Core.Interfaces;
using WarehouseManagement.Core.Models;

namespace WarehouseManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SecurityController : BaseController
{
    private readonly ISecurityService _securityService;
    private readonly ILogger<SecurityController> _logger;

    public SecurityController(ISecurityService securityService, ILogger<SecurityController> logger)
    {
        _securityService = securityService;
        _logger = logger;
    }

    /// <summary>
    /// Get security configuration
    /// </summary>
    /// <returns>Security configuration</returns>
    [HttpGet("config")]
    [AllowAnonymous] // Allow anonymous access for client-side configuration
    public async Task<IActionResult> GetSecurityConfig()
    {
        try
        {
            var result = await _securityService.GetSecurityConfigAsync();

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse(result.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security config");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Generate CSRF token
    /// </summary>
    /// <returns>CSRF token</returns>
    [HttpGet("csrf-token")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCsrfToken()
    {
        try
        {
            var result = await _securityService.GenerateCsrfTokenAsync();

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse(result.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CSRF token");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Log security event
    /// </summary>
    /// <param name="request">Security event data</param>
    /// <returns>Success response</returns>
    [HttpPost("events")]
    public async Task<IActionResult> LogSecurityEvent([FromBody] LogSecurityEventRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var ipAddress = GetClientIpAddress();
            var userAgent = Request.Headers.UserAgent.ToString();

            var result = await _securityService.LogSecurityEventAsync(
                request.EventType,
                request.Description,
                userId,
                ipAddress,
                userAgent,
                request.Severity,
                request.AdditionalData);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse("Security event logged successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging security event");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Log login attempt
    /// </summary>
    /// <param name="request">Login attempt data</param>
    /// <returns>Success response</returns>
    [HttpPost("login-attempts")]
    [AllowAnonymous]
    public async Task<IActionResult> LogLoginAttempt([FromBody] LogLoginAttemptRequest request)
    {
        try
        {
            var ipAddress = GetClientIpAddress();
            var userAgent = Request.Headers.UserAgent.ToString();

            var result = await _securityService.LogLoginAttemptAsync(
                request.Username,
                request.IsSuccessful,
                ipAddress,
                userAgent,
                request.FailureReason,
                request.UserId);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse("Login attempt logged successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging login attempt");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Get security events (Admin only)
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="eventType">Event type filter</param>
    /// <param name="severity">Severity filter</param>
    /// <param name="fromDate">From date filter</param>
    /// <param name="toDate">To date filter</param>
    /// <returns>Paged security events</returns>
    [HttpGet("events")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetSecurityEvents(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] SecurityEventType? eventType = null,
        [FromQuery] SecurityEventSeverity? severity = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _securityService.GetSecurityEventsAsync(
                page, pageSize, eventType, severity, fromDate, toDate);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return HandlePagedResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security events");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Get login attempts (Admin only)
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="isSuccessful">Success filter</param>
    /// <param name="fromDate">From date filter</param>
    /// <param name="toDate">To date filter</param>
    /// <returns>Paged login attempts</returns>
    [HttpGet("login-attempts")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetLoginAttempts(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] bool? isSuccessful = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _securityService.GetLoginAttemptsAsync(
                page, pageSize, isSuccessful, fromDate, toDate);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return HandlePagedResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting login attempts");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Update security configuration (Admin only)
    /// </summary>
    /// <param name="config">Security configuration</param>
    /// <returns>Success response</returns>
    [HttpPut("config")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateSecurityConfig([FromBody] SecurityConfigDto config)
    {
        try
        {
            var result = await _securityService.UpdateSecurityConfigAsync(config);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse("Security configuration updated successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating security config");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    /// <summary>
    /// Get rate limit information
    /// </summary>
    /// <param name="action">Action name</param>
    /// <param name="identifier">Identifier (e.g., IP address)</param>
    /// <returns>Rate limit information</returns>
    [HttpGet("rate-limit/{action}/{identifier}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetRateLimitInfo(string action, string identifier)
    {
        try
        {
            var result = await _securityService.GetRateLimitInfoAsync(action, identifier);

            if (!result.IsSuccess)
            {
                return BadRequest(CreateErrorResponse(result.Error));
            }

            return Ok(CreateSuccessResponse(result.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rate limit info");
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred"));
        }
    }

    private int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                         User.FindFirst(JwtClaims.UserId)?.Value;

        if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out var userId))
        {
            return null;
        }

        return userId;
    }

    private string GetClientIpAddress()
    {
        // Check for forwarded IP first (in case of proxy/load balancer)
        var forwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        // Check for real IP header
        var realIp = Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fall back to connection remote IP
        return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}

// Request DTOs for security endpoints
public class LogSecurityEventRequest
{
    public SecurityEventType EventType { get; set; }
    public string Description { get; set; } = string.Empty;
    public SecurityEventSeverity Severity { get; set; } = SecurityEventSeverity.Low;
    public string? AdditionalData { get; set; }
}

public class LogLoginAttemptRequest
{
    public string Username { get; set; } = string.Empty;
    public bool IsSuccessful { get; set; }
    public string? FailureReason { get; set; }
    public int? UserId { get; set; }
}
