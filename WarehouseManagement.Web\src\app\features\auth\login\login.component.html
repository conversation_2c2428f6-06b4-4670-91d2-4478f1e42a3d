<div class="login-container" [class.rtl]="languageService.isArabic()">
  <div class="login-wrapper">
    <!-- Language Switcher -->
    <div class="language-switcher">
      <button 
        pButton 
        type="button"
        [label]="languageService.isArabic() ? 'English' : 'العربية'"
        class="p-button-text p-button-sm"
        (click)="switchLanguage()">
      </button>
    </div>

    <!-- Login Card -->
    <div class="login-card">
      <!-- Header -->
      <div class="login-header">
        <div class="logo">
          <i class="pi pi-box text-4xl text-primary"></i>
        </div>
        <h1 class="login-title">{{ languageService.translate('auth.login.title') }}</h1>
        <p class="login-subtitle">{{ languageService.translate('auth.login.subtitle') }}</p>
      </div>

      <!-- Lockout Warning -->
      <div *ngIf="isLockedOut" class="lockout-warning">
        <p-message
          severity="warn"
          [text]="languageService.translate('auth.login.lockout.active', { time: remainingLockoutTime })">
        </p-message>
      </div>

      <!-- Login Form -->
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <!-- Username Field -->
        <div class="field">
          <label for="username" class="field-label">
            {{ languageService.translate('auth.login.username') }}
            <span class="required-asterisk">*</span>
          </label>
          <div class="p-inputgroup">
            <span class="p-inputgroup-addon">
              <i class="pi pi-user"></i>
            </span>
            <input
              id="username"
              type="text"
              pInputText
              formControlName="username"
              [placeholder]="languageService.translate('auth.login.usernamePlaceholder')"
              [class.ng-invalid]="isFieldInvalid('username')"
              [disabled]="loading || isLockedOut"
              autocomplete="username">
          </div>
          <small 
            *ngIf="isFieldInvalid('username')" 
            class="p-error">
            {{ getFieldError('username') }}
          </small>
        </div>

        <!-- Password Field -->
        <div class="field">
          <label for="password" class="field-label">
            {{ languageService.translate('auth.login.password') }}
            <span class="required-asterisk">*</span>
          </label>
          <div class="p-inputgroup">
            <span class="p-inputgroup-addon">
              <i class="pi pi-lock"></i>
            </span>
            <input
              id="password"
              [type]="showPassword ? 'text' : 'password'"
              pInputText
              formControlName="password"
              [placeholder]="languageService.translate('auth.login.passwordPlaceholder')"
              [class.ng-invalid]="isFieldInvalid('password')"
              [disabled]="loading || isLockedOut"
              autocomplete="current-password">
            <span class="p-inputgroup-addon cursor-pointer" (click)="togglePasswordVisibility()">
              <i [class]="showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'"></i>
            </span>
          </div>
          <small 
            *ngIf="isFieldInvalid('password')" 
            class="p-error">
            {{ getFieldError('password') }}
          </small>
        </div>

        <!-- Remember Me -->
        <div class="field-checkbox">
          <p-checkbox
            formControlName="rememberMe"
            [binary]="true"
            inputId="rememberMe"
            [disabled]="loading || isLockedOut">
          </p-checkbox>
          <label for="rememberMe" class="checkbox-label">
            {{ languageService.translate('auth.login.rememberMe') }}
          </label>
        </div>

        <!-- Login Attempts Warning -->
        <div *ngIf="loginAttempts > 0 && !isLockedOut" class="attempts-warning">
          <p-message
            severity="warn"
            [text]="languageService.translate('auth.login.attemptsRemaining', { attempts: remainingAttempts })">
          </p-message>
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          pButton
          [label]="languageService.translate('auth.login.signIn')"
          [loading]="loading"
          [disabled]="loginForm.invalid || isLockedOut"
          class="login-button w-full">
        </button>

        <!-- Forgot Password Link -->
        <div class="forgot-password">
          <a 
            href="#" 
            class="forgot-password-link"
            (click)="$event.preventDefault()">
            {{ languageService.translate('auth.login.forgotPassword') }}
          </a>
        </div>
      </form>

      <!-- Footer -->
      <div class="login-footer">
        <p class="footer-text">
          {{ languageService.translate('auth.login.footer.copyright') }}
        </p>
        <div class="footer-links">
          <a href="#" class="footer-link">{{ languageService.translate('auth.login.footer.privacy') }}</a>
          <span class="footer-separator">•</span>
          <a href="#" class="footer-link">{{ languageService.translate('auth.login.footer.terms') }}</a>
          <span class="footer-separator">•</span>
          <a href="#" class="footer-link">{{ languageService.translate('auth.login.footer.support') }}</a>
        </div>
      </div>
    </div>

    <!-- Security Notice -->
    <div class="security-notice">
      <p-message
        severity="info"
        [text]="languageService.translate('auth.login.securityNotice')">
      </p-message>
    </div>
  </div>

  <!-- Background -->
  <div class="login-background">
    <div class="background-pattern"></div>
  </div>
</div>

<!-- Toast Messages -->
<p-toast position="top-right" [class]="languageService.isArabic() ? 'rtl-toast' : ''"></p-toast>
