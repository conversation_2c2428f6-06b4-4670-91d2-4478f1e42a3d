{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { DialogModule } from 'primeng/dialog';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { CardModule } from 'primeng/card';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { ChartModule } from 'primeng/chart';\nimport { TreeModule } from 'primeng/tree';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { ImageModule } from 'primeng/image';\nimport { TagModule } from 'primeng/tag';\nimport { BadgeModule } from 'primeng/badge';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { TabViewModule } from 'primeng/tabview';\nimport { routes } from './app.routes';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptorsFromDi()), provideAnimations(), importProvidersFrom([BrowserModule, BrowserAnimationsModule, FormsModule, ReactiveFormsModule,\n  // PrimeNG Modules\n  ButtonModule, InputTextModule, TableModule, DialogModule, DropdownModule, CalendarModule, InputNumberModule, InputTextareaModule, CheckboxModule, RadioButtonModule, MenubarModule, SidebarModule, PanelMenuModule, CardModule, ToastModule, ConfirmDialogModule, ProgressSpinnerModule, ChartModule, TreeModule, FileUploadModule, ImageModule, TagModule, BadgeModule, ToolbarModule, SplitButtonModule, PaginatorModule, InputSwitchModule, TooltipModule, TabViewModule])]\n};", "map": {"version": 3, "names": ["importProvidersFrom", "provideRouter", "provideHttpClient", "withInterceptorsFromDi", "provideAnimations", "BrowserModule", "BrowserAnimationsModule", "FormsModule", "ReactiveFormsModule", "ButtonModule", "InputTextModule", "TableModule", "DialogModule", "DropdownModule", "CalendarModule", "InputNumberModule", "InputTextareaModule", "CheckboxModule", "RadioButtonModule", "MenubarModule", "SidebarModule", "PanelMenuModule", "CardModule", "ToastModule", "ConfirmDialogModule", "ProgressSpinnerModule", "ChartModule", "TreeModule", "FileUploadModule", "ImageModule", "TagModule", "BadgeModule", "ToolbarModule", "SplitButtonModule", "PaginatorModule", "InputSwitchModule", "TooltipModule", "TabViewModule", "routes", "appConfig", "providers"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { DialogModule } from 'primeng/dialog';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { CardModule } from 'primeng/card';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { ChartModule } from 'primeng/chart';\nimport { TreeModule } from 'primeng/tree';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { ImageModule } from 'primeng/image';\nimport { TagModule } from 'primeng/tag';\nimport { BadgeModule } from 'primeng/badge';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { TabViewModule } from 'primeng/tabview';\n\nimport { routes } from './app.routes';\n\nexport const appConfig: ApplicationConfig = {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(withInterceptorsFromDi()),\n    provideAnimations(),\n    importProvidersFrom([\n      BrowserModule,\n      BrowserAnimationsModule,\n      FormsModule,\n      ReactiveFormsModule,\n      \n      // PrimeNG Modules\n      ButtonModule,\n      InputTextModule,\n      TableModule,\n      DialogModule,\n      DropdownModule,\n      CalendarModule,\n      InputNumberModule,\n      InputTextareaModule,\n      CheckboxModule,\n      RadioButtonModule,\n      MenubarModule,\n      SidebarModule,\n      PanelMenuModule,\n      CardModule,\n      ToastModule,\n      ConfirmDialogModule,\n      ProgressSpinnerModule,\n      ChartModule,\n      TreeModule,\n      FileUploadModule,\n      ImageModule,\n      TagModule,\n      BadgeModule,\n      ToolbarModule,\n      SplitButtonModule,\n      PaginatorModule,\n      InputSwitchModule,\n      TooltipModule,\n      TabViewModule\n    ])\n  ]\n};\n"], "mappings": "AAAA,SAA4BA,mBAAmB,QAAQ,eAAe;AACtE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAChF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE;AACA,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAE/C,SAASC,MAAM,QAAQ,cAAc;AAErC,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTvC,aAAa,CAACqC,MAAM,CAAC,EACrBpC,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,EAC3CC,iBAAiB,EAAE,EACnBJ,mBAAmB,CAAC,CAClBK,aAAa,EACbC,uBAAuB,EACvBC,WAAW,EACXC,mBAAmB;EAEnB;EACAC,YAAY,EACZC,eAAe,EACfC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,mBAAmB,EACnBC,cAAc,EACdC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,UAAU,EACVC,WAAW,EACXC,mBAAmB,EACnBC,qBAAqB,EACrBC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,CACd,CAAC;CAEL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}