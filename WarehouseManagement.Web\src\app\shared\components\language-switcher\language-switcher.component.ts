import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule } from '@angular/forms';
import { LanguageService, Language } from '@core/services/language.service';

@Component({
  selector: 'app-language-switcher',
  standalone: true,
  imports: [
    CommonModule,
    DropdownModule,
    FormsModule
  ],
  template: `
    <p-dropdown
      [options]="languages"
      [(ngModel)]="selectedLanguage"
      (onChange)="onLanguageChange($event)"
      optionLabel="name"
      optionValue="code"
      [style]="{'min-width': '150px'}"
      placeholder="Select Language"
      class="language-switcher">
      
      <ng-template pTemplate="selectedItem">
        <div class="flex align-items-center gap-2" *ngIf="selectedLanguage">
          <span>{{ getLanguageFlag(selectedLanguage) }}</span>
          <span>{{ getLanguageName(selectedLanguage) }}</span>
        </div>
      </ng-template>
      
      <ng-template pTemplate="item" let-language>
        <div class="flex align-items-center gap-2">
          <span>{{ language.flag }}</span>
          <span>{{ language.name }}</span>
        </div>
      </ng-template>
    </p-dropdown>
  `,
  styles: [`
    .language-switcher {
      .p-dropdown {
        border: 1px solid var(--surface-border);
        background: var(--surface-ground);
      }
      
      .p-dropdown:not(.p-disabled):hover {
        border-color: var(--primary-color);
      }
      
      .p-dropdown:not(.p-disabled).p-focus {
        outline: 0 none;
        outline-offset: 0;
        box-shadow: 0 0 0 0.2rem var(--primary-color-text);
        border-color: var(--primary-color);
      }
    }
    
    :host-context(.rtl) .language-switcher {
      .p-dropdown-label {
        text-align: right;
      }
    }
  `]
})
export class LanguageSwitcherComponent implements OnInit {
  languages: Language[] = [];
  selectedLanguage: string = '';

  constructor(private languageService: LanguageService) {}

  ngOnInit(): void {
    this.languages = this.languageService.getAvailableLanguages();
    this.selectedLanguage = this.languageService.getCurrentLanguage().code;
    
    // Subscribe to language changes
    this.languageService.currentLanguage$.subscribe(language => {
      this.selectedLanguage = language.code;
    });
  }

  onLanguageChange(event: any): void {
    const languageCode = event.value;
    this.languageService.setLanguage(languageCode);
  }

  getLanguageFlag(code: string): string {
    const language = this.languages.find(lang => lang.code === code);
    return language?.flag || '';
  }

  getLanguageName(code: string): string {
    const language = this.languages.find(lang => lang.code === code);
    return language?.name || '';
  }
}
