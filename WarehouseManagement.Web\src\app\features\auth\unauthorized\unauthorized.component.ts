import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { LanguageService } from '../../../core/services/language.service';

@Component({
  selector: 'app-unauthorized',
  templateUrl: './unauthorized.component.html',
  styleUrls: ['./unauthorized.component.scss']
})
export class UnauthorizedComponent implements OnInit {

  constructor(
    private router: Router,
    private authService: AuthService,
    public languageService: LanguageService
  ) { }

  ngOnInit(): void {
    // Auto-redirect after 10 seconds if user doesn't take action
    setTimeout(() => {
      this.goToDashboard();
    }, 10000);
  }

  goToDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  goBack(): void {
    window.history.back();
  }

  logout(): void {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        // Force navigation to login even if logout fails
        this.router.navigate(['/login']);
      }
    });
  }

  contactSupport(): void {
    // In a real application, this would open a support ticket or contact form
    window.open('mailto:<EMAIL>?subject=Access Request', '_blank');
  }
}
