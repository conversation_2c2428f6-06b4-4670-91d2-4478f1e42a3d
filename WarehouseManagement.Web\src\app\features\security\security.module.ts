import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

// Import the standalone component instead of declaring it
import { SecurityMonitoringComponent } from './security-monitoring.component';

// Guards
import { AuthGuard, PermissionGuard } from '../../core/guards/auth.guard';

const routes = [
  {
    path: 'monitoring',
    component: SecurityMonitoringComponent,
    canActivate: [AuthGuard, PermissionGuard],
    data: { 
      resource: 'audit_logs', 
      action: 'read',
      title: 'Security Monitoring'
    }
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    // Import the standalone component
    SecurityMonitoringComponent
  ],
  exports: [
    RouterModule,
    // Export the standalone component
    SecurityMonitoringComponent
  ]
})
export class SecurityModule { }
