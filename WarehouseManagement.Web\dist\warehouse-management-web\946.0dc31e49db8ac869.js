"use strict";(self.webpackChunkwarehouse_management_web=self.webpackChunkwarehouse_management_web||[]).push([[946],{1946:(W,h,s)=>{s.r(h),s.d(h,{DashboardComponent:()=>U});var d=s(177),u=s(491),g=s(5895),v=s(8490),b=s(935),f=s(1141),y=s(1579),C=s(7515),e=s(4438),D=s(7673),c=s(1626),F=s(8810),l=s(9437),S=s(6354);let P=(()=>{class n{constructor(t){this.http=t,this.baseUrl="https://localhost:7001/api"}get(t,a){let o=new c.Nl;return a&&Object.keys(a).forEach(r=>{null!=a[r]&&""!==a[r]&&(o=o.set(r,a[r].toString()))}),this.http.get(`${this.baseUrl}/${t}`,{params:o}).pipe((0,l.W)(this.handleError))}getWithPagination(t,a){let o=new c.Nl;return a&&Object.keys(a).forEach(r=>{null!=a[r]&&""!==a[r]&&(o=o.set(r,a[r].toString()))}),this.http.get(`${this.baseUrl}/${t}`,{params:o,observe:"response"}).pipe((0,S.T)(r=>{const m=parseInt(r.headers.get("X-Total-Count")||"0"),p=parseInt(r.headers.get("X-Page-Number")||"1"),G=parseInt(r.headers.get("X-Page-Size")||"10"),x=parseInt(r.headers.get("X-Total-Pages")||"1");return{data:r.body||[],totalCount:m,pageNumber:p,pageSize:G,totalPages:x,hasPreviousPage:p>1,hasNextPage:p<x}}),(0,l.W)(this.handleError))}post(t,a){return this.http.post(`${this.baseUrl}/${t}`,a,{headers:new c.Lr({"Content-Type":"application/json"})}).pipe((0,l.W)(this.handleError))}put(t,a){return this.http.put(`${this.baseUrl}/${t}`,a,{headers:new c.Lr({"Content-Type":"application/json"})}).pipe((0,l.W)(this.handleError))}delete(t){return this.http.delete(`${this.baseUrl}/${t}`).pipe((0,l.W)(this.handleError))}uploadFile(t,a,o){const r=new FormData;return r.append("file",a),o&&Object.keys(o).forEach(m=>{r.append(m,o[m])}),this.http.post(`${this.baseUrl}/${t}`,r).pipe((0,l.W)(this.handleError))}handleError(t){let a="An unknown error occurred";return a=t.error instanceof ErrorEvent?t.error.message:0===t.status?"Unable to connect to the server. Please check your internet connection.":t.status>=400&&t.status<500?t.error?.message||t.error||`Client error: ${t.status}`:t.status>=500?"Server error. Please try again later.":t.error?.message||t.message||a,console.error("API Error:",t),(0,F.$)(()=>new Error(a))}static{this.\u0275fac=function(a){return new(a||n)(e.KVO(c.Qq))}}static{this.\u0275prov=e.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),j=(()=>{class n{constructor(t){this.apiService=t}getDashboardData(){return(0,D.of)(this.getMockDashboardData())}getMockDashboardData(){return{totalItems:1247,totalRevenue:125e3,pendingOrders:23,lowStockItems:[{id:1,name:"Steel Rods",code:"STL001",currentStock:15,minimumLevel:50,warehouseName:"Raw Materials Warehouse"},{id:2,name:"Plastic Sheets",code:"PLS002",currentStock:8,minimumLevel:25,warehouseName:"Raw Materials Warehouse"},{id:3,name:"Finished Product A",code:"FPA001",currentStock:12,minimumLevel:30,warehouseName:"Finished Products Warehouse"},{id:4,name:"Consumable Item B",code:"CON002",currentStock:5,minimumLevel:20,warehouseName:"Raw Materials Warehouse"},{id:5,name:"Semi-Finished C",code:"SFC003",currentStock:18,minimumLevel:40,warehouseName:"Finished Products Warehouse"}],recentTransactions:[{id:1,date:new Date("2024-01-15T10:30:00"),type:"Sale",amount:2500,status:"Completed",reference:"INV-2024-001"},{id:2,date:new Date("2024-01-15T09:15:00"),type:"Purchase",amount:1800,status:"Pending",reference:"PO-2024-045"},{id:3,date:new Date("2024-01-14T16:45:00"),type:"Return",amount:350,status:"Completed",reference:"RET-2024-012"},{id:4,date:new Date("2024-01-14T14:20:00"),type:"Adjustment",amount:120,status:"Completed",reference:"ADJ-2024-008"},{id:5,date:new Date("2024-01-14T11:30:00"),type:"Sale",amount:3200,status:"Completed",reference:"INV-2024-002"}],salesTrend:[{period:"Jan",amount:45e3,quantity:120},{period:"Feb",amount:52e3,quantity:135},{period:"Mar",amount:48e3,quantity:128},{period:"Apr",amount:61e3,quantity:155},{period:"May",amount:55e3,quantity:142},{period:"Jun",amount:67e3,quantity:168},{period:"Jul",amount:71e3,quantity:178},{period:"Aug",amount:69e3,quantity:172},{period:"Sep",amount:75e3,quantity:185},{period:"Oct",amount:82e3,quantity:198},{period:"Nov",amount:78e3,quantity:189},{period:"Dec",amount:85e3,quantity:205}],inventorySummary:{rawMaterials:450,semiFinished:280,finishedProducts:380,consumables:137,totalValue:285e4},monthlyRevenue:[{month:"Jan",revenue:45e3,profit:12e3,expenses:33e3},{month:"Feb",revenue:52e3,profit:15e3,expenses:37e3},{month:"Mar",revenue:48e3,profit:13e3,expenses:35e3},{month:"Apr",revenue:61e3,profit:18e3,expenses:43e3},{month:"May",revenue:55e3,profit:16e3,expenses:39e3},{month:"Jun",revenue:67e3,profit:2e4,expenses:47e3},{month:"Jul",revenue:71e3,profit:22e3,expenses:49e3},{month:"Aug",revenue:69e3,profit:21e3,expenses:48e3},{month:"Sep",revenue:75e3,profit:24e3,expenses:51e3},{month:"Oct",revenue:82e3,profit:27e3,expenses:55e3},{month:"Nov",revenue:78e3,profit:25e3,expenses:53e3},{month:"Dec",revenue:85e3,profit:28e3,expenses:57e3}]}}static{this.\u0275fac=function(a){return new(a||n)(e.KVO(P))}}static{this.\u0275prov=e.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var k=s(3318),w=s(5779);function E(n,i){1&n&&(e.j41(0,"div",6),e.nrm(1,"p-progressSpinner"),e.k0s())}function M(n,i){1&n&&(e.j41(0,"div",35)(1,"h3",36),e.EFF(2,"Sales Trend"),e.k0s()())}function O(n,i){1&n&&(e.j41(0,"div",35)(1,"h3",36),e.EFF(2,"Inventory Distribution"),e.k0s()())}function T(n,i){1&n&&(e.j41(0,"div",35)(1,"h3",36),e.EFF(2,"Monthly Revenue & Profit"),e.k0s()())}function $(n,i){1&n&&(e.j41(0,"div",35)(1,"h3",36),e.EFF(2,"Low Stock Items"),e.k0s()())}function I(n,i){1&n&&(e.j41(0,"tr")(1,"th"),e.EFF(2,"Item"),e.k0s(),e.j41(3,"th"),e.EFF(4,"Current Stock"),e.k0s(),e.j41(5,"th"),e.EFF(6,"Min Level"),e.k0s(),e.j41(7,"th"),e.EFF(8,"Status"),e.k0s()())}function N(n,i){if(1&n&&(e.j41(0,"tr")(1,"td")(2,"div",37),e.EFF(3),e.k0s(),e.j41(4,"div",38),e.EFF(5),e.k0s()(),e.j41(6,"td"),e.EFF(7),e.k0s(),e.j41(8,"td"),e.EFF(9),e.k0s(),e.j41(10,"td"),e.nrm(11,"p-progressBar",39),e.k0s()()),2&n){const t=i.$implicit,a=e.XpG(2);e.R7$(3),e.JRh(t.name),e.R7$(2),e.JRh(t.code),e.R7$(2),e.JRh(t.currentStock),e.R7$(2),e.JRh(t.minimumLevel),e.R7$(2),e.Y8G("value",t.currentStock/t.minimumLevel*100)("showValue",!1)("ngClass","progress-"+a.getStockLevelSeverity(t.currentStock/t.minimumLevel*100))}}function L(n,i){1&n&&(e.j41(0,"div",35)(1,"h3",36),e.EFF(2,"Recent Transactions"),e.k0s()())}function J(n,i){1&n&&(e.j41(0,"tr")(1,"th"),e.EFF(2,"Date"),e.k0s(),e.j41(3,"th"),e.EFF(4,"Type"),e.k0s(),e.j41(5,"th"),e.EFF(6,"Amount"),e.k0s(),e.j41(7,"th"),e.EFF(8,"Status"),e.k0s()())}function A(n,i){if(1&n&&(e.j41(0,"tr")(1,"td"),e.EFF(2),e.nI1(3,"date"),e.k0s(),e.j41(4,"td"),e.nrm(5,"p-tag",40),e.k0s(),e.j41(6,"td"),e.EFF(7),e.nI1(8,"currency"),e.k0s(),e.j41(9,"td"),e.nrm(10,"p-tag",40),e.k0s()()),2&n){const t=i.$implicit,a=e.XpG(2);e.R7$(2),e.JRh(e.i5U(3,6,t.date,"short")),e.R7$(3),e.Y8G("value",t.type)("severity",a.getTransactionTypeSeverity(t.type)),e.R7$(2),e.JRh(e.bMT(8,9,t.amount)),e.R7$(3),e.Y8G("value",t.status)("severity","Completed"===t.status?"success":"warning")}}function _(n,i){if(1&n&&(e.j41(0,"div",7)(1,"div",8)(2,"div",9)(3,"p-card",10)(4,"div",11)(5,"div")(6,"div",12),e.EFF(7),e.k0s(),e.j41(8,"div",13),e.EFF(9),e.nI1(10,"number"),e.k0s()(),e.j41(11,"div",14),e.nrm(12,"i",15),e.k0s()()()(),e.j41(13,"div",9)(14,"p-card",10)(15,"div",11)(16,"div")(17,"div",12),e.EFF(18),e.k0s(),e.j41(19,"div",16),e.EFF(20),e.k0s()(),e.j41(21,"div",17),e.nrm(22,"i",18),e.k0s()()()(),e.j41(23,"div",9)(24,"p-card",10)(25,"div",11)(26,"div")(27,"div",12),e.EFF(28),e.k0s(),e.j41(29,"div",19),e.EFF(30),e.nI1(31,"currency"),e.k0s()(),e.j41(32,"div",20),e.nrm(33,"i",21),e.k0s()()()(),e.j41(34,"div",9)(35,"p-card",10)(36,"div",11)(37,"div")(38,"div",12),e.EFF(39,"Pending Orders"),e.k0s(),e.j41(40,"div",22),e.EFF(41),e.k0s()(),e.j41(42,"div",23),e.nrm(43,"i",24),e.k0s()()()()(),e.j41(44,"div",8)(45,"div",25)(46,"p-card"),e.DNE(47,M,3,0,"ng-template",26),e.nrm(48,"p-chart",27),e.k0s()(),e.j41(49,"div",28)(50,"p-card"),e.DNE(51,O,3,0,"ng-template",26),e.nrm(52,"p-chart",29),e.k0s()()(),e.j41(53,"div",8)(54,"div",30)(55,"p-card"),e.DNE(56,T,3,0,"ng-template",26),e.nrm(57,"p-chart",31),e.k0s()()(),e.j41(58,"div",8)(59,"div",32)(60,"p-card"),e.DNE(61,$,3,0,"ng-template",26),e.j41(62,"p-table",33),e.DNE(63,I,9,0,"ng-template",26)(64,N,12,7,"ng-template",34),e.k0s()()(),e.j41(65,"div",32)(66,"p-card"),e.DNE(67,L,3,0,"ng-template",26),e.j41(68,"p-table",33),e.DNE(69,J,9,0,"ng-template",26)(70,A,11,11,"ng-template",34),e.k0s()()()()()),2&n){const t=e.XpG();e.R7$(7),e.JRh(t.languageService.translate("dashboard.totalItems")),e.R7$(2),e.JRh(e.bMT(10,19,t.dashboardData.totalItems)),e.R7$(9),e.JRh(t.languageService.translate("dashboard.lowStockItems")),e.R7$(2),e.JRh(t.dashboardData.lowStockItems.length),e.R7$(8),e.JRh(t.languageService.translate("dashboard.monthlyRevenue")),e.R7$(2),e.JRh(e.bMT(31,21,t.dashboardData.totalRevenue)),e.R7$(11),e.JRh(t.dashboardData.pendingOrders),e.R7$(7),e.Y8G("data",t.salesChartData)("options",t.salesChartOptions),e.R7$(4),e.Y8G("data",t.inventoryChartData)("options",t.inventoryChartOptions),e.R7$(5),e.Y8G("data",t.revenueChartData)("options",t.revenueChartOptions),e.R7$(5),e.Y8G("value",t.dashboardData.lowStockItems)("paginator",!0)("rows",5),e.R7$(6),e.Y8G("value",t.dashboardData.recentTransactions)("paginator",!0)("rows",5)}}let U=(()=>{class n{constructor(t,a){this.dashboardService=t,this.languageService=a,this.dashboardData=null,this.loading=!0}ngOnInit(){this.loadDashboardData(),this.initializeChartOptions()}loadDashboardData(){this.loading=!0,this.dashboardService.getDashboardData().subscribe({next:t=>{this.dashboardData=t,this.setupCharts(),this.loading=!1},error:t=>{console.error("Error loading dashboard data:",t),this.loading=!1}})}initializeChartOptions(){const t=getComputedStyle(document.documentElement),a=t.getPropertyValue("--text-color"),o=t.getPropertyValue("--text-color-secondary"),r=t.getPropertyValue("--surface-border");this.salesChartOptions={maintainAspectRatio:!1,aspectRatio:.8,plugins:{legend:{labels:{color:a}}},scales:{x:{ticks:{color:o,font:{weight:500}},grid:{color:r,drawBorder:!1}},y:{ticks:{color:o},grid:{color:r,drawBorder:!1}}}},this.inventoryChartOptions={plugins:{legend:{labels:{usePointStyle:!0,color:a}}}},this.revenueChartOptions={maintainAspectRatio:!1,aspectRatio:.6,plugins:{legend:{labels:{color:a}}},scales:{x:{ticks:{color:o},grid:{color:r}},y:{ticks:{color:o},grid:{color:r}}}}}setupCharts(){this.dashboardData&&(this.salesChartData={labels:this.dashboardData.salesTrend.map(t=>t.period),datasets:[{label:"Sales",data:this.dashboardData.salesTrend.map(t=>t.amount),fill:!1,backgroundColor:"#3b82f6",borderColor:"#3b82f6",tension:.4}]},this.inventoryChartData={labels:["Raw Materials","Semi-Finished","Finished Products","Consumables"],datasets:[{data:[this.dashboardData.inventorySummary.rawMaterials,this.dashboardData.inventorySummary.semiFinished,this.dashboardData.inventorySummary.finishedProducts,this.dashboardData.inventorySummary.consumables],backgroundColor:["#3b82f6","#10b981","#f59e0b","#ef4444"],hoverBackgroundColor:["#2563eb","#059669","#d97706","#dc2626"]}]},this.revenueChartData={labels:this.dashboardData.monthlyRevenue.map(t=>t.month),datasets:[{type:"line",label:"Revenue",borderColor:"#3b82f6",borderWidth:2,fill:!1,tension:.4,data:this.dashboardData.monthlyRevenue.map(t=>t.revenue)},{type:"bar",label:"Profit",backgroundColor:"#10b981",data:this.dashboardData.monthlyRevenue.map(t=>t.profit)}]})}getStockLevelSeverity(t){return t<=20?"danger":t<=50?"warning":"success"}getTransactionTypeSeverity(t){switch(t.toLowerCase()){case"sale":return"success";case"purchase":default:return"info";case"return":return"warning";case"adjustment":return"secondary"}}refreshDashboard(){this.loadDashboardData()}static{this.\u0275fac=function(a){return new(a||n)(e.rXU(j),e.rXU(k.g))}}static{this.\u0275cmp=e.VBU({type:n,selectors:[["app-dashboard"]],standalone:!0,features:[e.aNF],decls:7,vars:5,consts:[[1,"dashboard-container"],[1,"flex","justify-content-between","align-items-center","mb-4"],[1,"text-2xl","font-bold","m-0"],["pButton","","type","button","icon","pi pi-refresh",1,"p-button-outlined",3,"click","label","loading"],["class","flex justify-content-center align-items-center","style","height: 400px;",4,"ngIf"],["class","dashboard-content",4,"ngIf"],[1,"flex","justify-content-center","align-items-center",2,"height","400px"],[1,"dashboard-content"],[1,"grid"],[1,"col-12","md:col-6","lg:col-3"],["styleClass","metric-card"],[1,"flex","justify-content-between","align-items-center"],[1,"text-500","font-medium","mb-2"],[1,"text-2xl","font-bold","text-900"],[1,"metric-icon","bg-blue-100","text-blue-600"],[1,"pi","pi-box","text-2xl"],[1,"text-2xl","font-bold","text-orange-600"],[1,"metric-icon","bg-orange-100","text-orange-600"],[1,"pi","pi-exclamation-triangle","text-2xl"],[1,"text-2xl","font-bold","text-green-600"],[1,"metric-icon","bg-green-100","text-green-600"],[1,"pi","pi-dollar","text-2xl"],[1,"text-2xl","font-bold","text-purple-600"],[1,"metric-icon","bg-purple-100","text-purple-600"],[1,"pi","pi-clock","text-2xl"],[1,"col-12","lg:col-8"],["pTemplate","header"],["type","line","height","300px",3,"data","options"],[1,"col-12","lg:col-4"],["type","doughnut","height","300px",3,"data","options"],[1,"col-12"],["type","bar","height","400px",3,"data","options"],[1,"col-12","lg:col-6"],["styleClass","p-datatable-sm",3,"value","paginator","rows"],["pTemplate","body"],[1,"card-header"],[1,"card-title"],[1,"font-medium"],[1,"text-sm","text-500"],["styleClass","w-full",3,"value","showValue","ngClass"],[3,"value","severity"]],template:function(a,o){1&a&&(e.j41(0,"div",0)(1,"div",1)(2,"h1",2),e.EFF(3),e.k0s(),e.j41(4,"button",3),e.bIt("click",function(){return o.refreshDashboard()}),e.k0s()(),e.DNE(5,E,2,0,"div",4)(6,_,71,23,"div",5),e.k0s()),2&a&&(e.R7$(3),e.JRh(o.languageService.translate("dashboard.title")),e.R7$(),e.Y8G("label",o.languageService.translate("common.refresh"))("loading",o.loading),e.R7$(),e.Y8G("ngIf",o.loading),e.R7$(),e.Y8G("ngIf",!o.loading&&o.dashboardData))},dependencies:[d.MD,d.YU,d.bT,d.QX,d.oe,d.vh,u.D,u.Z,w.Ei,g.F,g.X,v.bG,v.XI,b.$,b.v,f.tm,f._f,y.d,y.z,C.d,C.p],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:0}.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]{margin:0}.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:.75rem}.metric-card[_ngcontent-%COMP%]{border:1px solid var(--surface-200);border-radius:var(--border-radius);transition:all .2s}.metric-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #0000001a;transform:translateY(-2px)}.metric-card[_ngcontent-%COMP%]     .p-card-body{padding:1.5rem}.metric-icon[_ngcontent-%COMP%]{width:3rem;height:3rem;border-radius:50%;display:flex;align-items:center;justify-content:center}.card-header[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid var(--surface-200);background:var(--surface-50);margin:-1.5rem -1.5rem 1.5rem;border-radius:var(--border-radius) var(--border-radius) 0 0}.card-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:var(--text-color);margin:0}  .p-chart .p-chart-canvas{max-height:400px}  .p-datatable-sm .p-datatable-thead>tr>th{padding:.5rem;font-size:.875rem;font-weight:600}  .p-datatable-sm .p-datatable-tbody>tr>td{padding:.5rem;font-size:.875rem}  .p-progressbar{height:.5rem}  .p-progressbar .p-progressbar-value{border-radius:.25rem}@media (max-width: 768px){.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:.5rem}.metric-card[_ngcontent-%COMP%]     .p-card-body{padding:1rem}.card-header[_ngcontent-%COMP%]{padding:.75rem 1rem;margin:-1rem -1rem 1rem}.card-title[_ngcontent-%COMP%]{font-size:1rem}.metric-icon[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem}.metric-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem!important}}@media (max-width: 480px){.dashboard-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem!important}.flex.justify-content-between[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important;gap:1rem}.metric-card[_ngcontent-%COMP%]   .flex.justify-content-between[_ngcontent-%COMP%]{flex-direction:row!important;align-items:center!important;gap:0}}"]})}}return n})()}}]);