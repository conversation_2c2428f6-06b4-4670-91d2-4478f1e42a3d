{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i5 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport * as i4 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleUpIcon } from 'primeng/icons/angleup';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"p-inputnumber p-component\": true,\n  \"p-inputnumber-buttons-stacked\": a0,\n  \"p-inputnumber-buttons-horizontal\": a1,\n  \"p-inputnumber-buttons-vertical\": a2\n});\nconst _c2 = () => ({\n  \"p-inputnumber-button p-inputnumber-button-up\": true\n});\nconst _c3 = () => ({\n  \"p-inputnumber-button p-inputnumber-button-down\": true\n});\nfunction InputNumber_ng_container_3_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 8);\n    i0.ɵɵlistener(\"click\", function InputNumber_ng_container_3_TimesIcon_1_Template_TimesIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-inputnumber-clear-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n  }\n}\nfunction InputNumber_ng_container_3_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_ng_container_3_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_ng_container_3_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵlistener(\"click\", function InputNumber_ng_container_3_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵtemplate(1, InputNumber_ng_container_3_span_2_1_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction InputNumber_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_ng_container_3_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 6)(2, InputNumber_ng_container_3_span_2_Template, 2, 2, \"span\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction InputNumber_span_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleUpIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_span_4_ng_container_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_span_4_ng_container_3_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template, 1, 1, \"AngleUpIcon\", 3)(2, InputNumber_span_4_ng_container_3_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.incrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_span_4_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_span_4_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_span_4_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 3)(2, InputNumber_span_4_ng_container_6_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.decrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyUp());\n    });\n    i0.ɵɵtemplate(2, InputNumber_span_4_span_2_Template, 1, 2, \"span\", 13)(3, InputNumber_span_4_ng_container_3_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyUp());\n    });\n    i0.ɵɵtemplate(5, InputNumber_span_4_span_5_Template, 1, 2, \"span\", 13)(6, InputNumber_span_4_ng_container_6_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"buttonGroup\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(17, _c2))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"incrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(18, _c3))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", ctx_r2.decrementbutton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIcon);\n  }\n}\nfunction InputNumber_button_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleUpIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_button_5_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_button_5_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template, 1, 1, \"AngleUpIcon\", 3)(2, InputNumber_button_5_ng_container_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.incrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_5_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_5_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_5_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_5_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_5_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyUp());\n    });\n    i0.ɵɵtemplate(1, InputNumber_button_5_span_1_Template, 1, 2, \"span\", 13)(2, InputNumber_button_5_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(8, _c2))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"incrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIcon);\n  }\n}\nfunction InputNumber_button_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_button_6_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_button_6_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 3)(2, InputNumber_button_6_ng_container_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.decrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_6_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_6_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_6_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_6_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_6_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyUp());\n    });\n    i0.ɵɵtemplate(1, InputNumber_button_6_span_1_Template, 1, 2, \"span\", 13)(2, InputNumber_button_6_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(8, _c3))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"decrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIcon);\n  }\n}\nconst INPUTNUMBER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputNumber),\n  multi: true\n};\n/**\n * InputNumber is an input component to provide numerical input.\n * @group Components\n */\nclass InputNumber {\n  document;\n  el;\n  cd;\n  injector;\n  config;\n  /**\n   * Displays spinner buttons.\n   * @group Props\n   */\n  showButtons = false;\n  /**\n   * Whether to format the value.\n   * @group Props\n   */\n  format = true;\n  /**\n   * Layout of the buttons, valid values are \"stacked\" (default), \"horizontal\" and \"vertical\".\n   * @group Props\n   */\n  buttonLayout = 'stacked';\n  /**\n   * Identifier of the focus input to match a label defined for the component.\n   * @group Props\n   */\n  inputId;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Advisory information to display on input.\n   * @group Props\n   */\n  placeholder;\n  /**\n   * Size of the input field.\n   * @group Props\n   */\n  size;\n  /**\n   * Maximum number of character allows in the input field.\n   * @group Props\n   */\n  maxlength;\n  /**\n   * Specifies tab order of the element.\n   * @group Props\n   */\n  tabindex;\n  /**\n   * Title text of the input text.\n   * @group Props\n   */\n  title;\n  /**\n   * Specifies one or more IDs in the DOM that labels the input field.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Used to define a string that labels the input element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Used to indicate that user input is required on an element before a form can be submitted.\n   * @group Props\n   */\n  ariaRequired;\n  /**\n   * Name of the input field.\n   * @group Props\n   */\n  name;\n  /**\n   * Indicates that whether the input field is required.\n   * @group Props\n   */\n  required;\n  /**\n   * Used to define a string that autocomplete attribute the current element.\n   * @group Props\n   */\n  autocomplete;\n  /**\n   * Mininum boundary value.\n   * @group Props\n   */\n  min;\n  /**\n   * Maximum boundary value.\n   * @group Props\n   */\n  max;\n  /**\n   * Style class of the increment button.\n   * @group Props\n   */\n  incrementButtonClass;\n  /**\n   * Style class of the decrement button.\n   * @group Props\n   */\n  decrementButtonClass;\n  /**\n   * Style class of the increment button.\n   * @group Props\n   */\n  incrementButtonIcon;\n  /**\n   * Style class of the decrement button.\n   * @group Props\n   */\n  decrementButtonIcon;\n  /**\n   * When present, it specifies that an input field is read-only.\n   * @group Props\n   */\n  readonly = false;\n  /**\n   * Step factor to increment/decrement the value.\n   * @group Props\n   */\n  step = 1;\n  /**\n   * Determines whether the input field is empty.\n   * @group Props\n   */\n  allowEmpty = true;\n  /**\n   * Locale to be used in formatting.\n   * @group Props\n   */\n  locale;\n  /**\n   * The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\"; the default is \"best fit\". See Locale Negotiation for details.\n   * @group Props\n   */\n  localeMatcher;\n  /**\n   * Defines the behavior of the component, valid values are \"decimal\" and \"currency\".\n   * @group Props\n   */\n  mode = 'decimal';\n  /**\n   * The currency to use in currency formatting. Possible values are the ISO 4217 currency codes, such as \"USD\" for the US dollar, \"EUR\" for the euro, or \"CNY\" for the Chinese RMB. There is no default value; if the style is \"currency\", the currency property must be provided.\n   * @group Props\n   */\n  currency;\n  /**\n   * How to display the currency in currency formatting. Possible values are \"symbol\" to use a localized currency symbol such as €, ü\"code\" to use the ISO currency code, \"name\" to use a localized currency name such as \"dollar\"; the default is \"symbol\".\n   * @group Props\n   */\n  currencyDisplay;\n  /**\n   * Whether to use grouping separators, such as thousands separators or thousand/lakh/crore separators.\n   * @group Props\n   */\n  useGrouping = true;\n  /**\n   * Specifies the input variant of the component.\n   * @group Props\n   */\n  variant = 'outlined';\n  /**\n   * The minimum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number and percent formatting is 0; the default for currency formatting is the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n   * @group Props\n   */\n  minFractionDigits;\n  /**\n   * The maximum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number formatting is the larger of minimumFractionDigits and 3; the default for currency formatting is the larger of minimumFractionDigits and the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n   * @group Props\n   */\n  maxFractionDigits;\n  /**\n   * Text to display before the value.\n   * @group Props\n   */\n  prefix;\n  /**\n   * Text to display after the value.\n   * @group Props\n   */\n  suffix;\n  /**\n   * Inline style of the input field.\n   * @group Props\n   */\n  inputStyle;\n  /**\n   * Style class of the input field.\n   * @group Props\n   */\n  inputStyleClass;\n  /**\n   * When enabled, a clear icon is displayed to clear the value.\n   * @group Props\n   */\n  showClear = false;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus;\n  /**\n   * When present, it specifies that the element should be disabled.\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(disabled) {\n    if (disabled) this.focused = false;\n    this._disabled = disabled;\n    if (this.timer) this.clearTimer();\n  }\n  /**\n   * Callback to invoke on input.\n   * @param {InputNumberInputEvent} event - Custom input event.\n   * @group Emits\n   */\n  onInput = new EventEmitter();\n  /**\n   * Callback to invoke when the component receives focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to invoke when the component loses focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  /**\n   * Callback to invoke on input key press.\n   * @param {KeyboardEvent} event - Keyboard event.\n   * @group Emits\n   */\n  onKeyDown = new EventEmitter();\n  /**\n   * Callback to invoke when clear token is clicked.\n   * @group Emits\n   */\n  onClear = new EventEmitter();\n  input;\n  templates;\n  clearIconTemplate;\n  incrementButtonIconTemplate;\n  decrementButtonIconTemplate;\n  value;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  focused;\n  initialized;\n  groupChar = '';\n  prefixChar = '';\n  suffixChar = '';\n  isSpecialChar;\n  timer;\n  lastValue;\n  _numeral;\n  numberFormat;\n  _decimal;\n  _decimalChar;\n  _group;\n  _minusSign;\n  _currency;\n  _prefix;\n  _suffix;\n  _index;\n  _disabled;\n  ngControl = null;\n  constructor(document, el, cd, injector, config) {\n    this.document = document;\n    this.el = el;\n    this.cd = cd;\n    this.injector = injector;\n    this.config = config;\n  }\n  ngOnChanges(simpleChange) {\n    const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n    if (props.some(p => !!simpleChange[p])) {\n      this.updateConstructParser();\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'clearicon':\n          this.clearIconTemplate = item.template;\n          break;\n        case 'incrementbuttonicon':\n          this.incrementButtonIconTemplate = item.template;\n          break;\n        case 'decrementbuttonicon':\n          this.decrementButtonIconTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngOnInit() {\n    this.ngControl = this.injector.get(NgControl, null, {\n      optional: true\n    });\n    this.constructParser();\n    this.initialized = true;\n  }\n  getOptions() {\n    return {\n      localeMatcher: this.localeMatcher,\n      style: this.mode,\n      currency: this.currency,\n      currencyDisplay: this.currencyDisplay,\n      useGrouping: this.useGrouping,\n      minimumFractionDigits: this.minFractionDigits ?? undefined,\n      maximumFractionDigits: this.maxFractionDigits ?? undefined\n    };\n  }\n  constructParser() {\n    this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n    const numerals = [...new Intl.NumberFormat(this.locale, {\n      useGrouping: false\n    }).format(9876543210)].reverse();\n    const index = new Map(numerals.map((d, i) => [d, i]));\n    this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n    this._group = this.getGroupingExpression();\n    this._minusSign = this.getMinusSignExpression();\n    this._currency = this.getCurrencyExpression();\n    this._decimal = this.getDecimalExpression();\n    this._decimalChar = this.getDecimalChar();\n    this._suffix = this.getSuffixExpression();\n    this._prefix = this.getPrefixExpression();\n    this._index = d => index.get(d);\n  }\n  updateConstructParser() {\n    if (this.initialized) {\n      this.constructParser();\n    }\n  }\n  escapeRegExp(text) {\n    return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n  }\n  getDecimalExpression() {\n    const decimalChar = this.getDecimalChar();\n    return new RegExp(`[${decimalChar}]`, 'g');\n  }\n  getDecimalChar() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      ...this.getOptions(),\n      useGrouping: false\n    });\n    return formatter.format(1.1).replace(this._currency, '').trim().replace(this._numeral, '');\n  }\n  getGroupingExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      useGrouping: true\n    });\n    this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n    return new RegExp(`[${this.groupChar}]`, 'g');\n  }\n  getMinusSignExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      useGrouping: false\n    });\n    return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n  }\n  getCurrencyExpression() {\n    if (this.currency) {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: 'currency',\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      });\n      return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n    }\n    return new RegExp(`[]`, 'g');\n  }\n  getPrefixExpression() {\n    if (this.prefix) {\n      this.prefixChar = this.prefix;\n    } else {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: this.mode,\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay\n      });\n      this.prefixChar = formatter.format(1).split('1')[0];\n    }\n    return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n  }\n  getSuffixExpression() {\n    if (this.suffix) {\n      this.suffixChar = this.suffix;\n    } else {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: this.mode,\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      });\n      this.suffixChar = formatter.format(1).split('1')[1];\n    }\n    return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n  }\n  get isBlurUpdateOnMode() {\n    return this.ngControl?.control?.updateOn === 'blur';\n  }\n  formatValue(value) {\n    if (value != null) {\n      if (value === '-') {\n        // Minus sign\n        return value;\n      }\n      if (this.format) {\n        let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n        let formattedValue = formatter.format(value);\n        if (this.prefix && value != this.prefix) {\n          formattedValue = this.prefix + formattedValue;\n        }\n        if (this.suffix && value != this.suffix) {\n          formattedValue = formattedValue + this.suffix;\n        }\n        return formattedValue;\n      }\n      return value.toString();\n    }\n    return '';\n  }\n  parseValue(text) {\n    const suffixRegex = new RegExp(this._suffix, '');\n    const prefixRegex = new RegExp(this._prefix, '');\n    const currencyRegex = new RegExp(this._currency, '');\n    let filteredText = text.replace(suffixRegex, '').replace(prefixRegex, '').trim().replace(/\\s/g, '').replace(currencyRegex, '').replace(this._group, '').replace(this._minusSign, '-').replace(this._decimal, '.').replace(this._numeral, this._index);\n    if (filteredText) {\n      if (filteredText === '-')\n        // Minus sign\n        return filteredText;\n      let parsedValue = +filteredText;\n      return isNaN(parsedValue) ? null : parsedValue;\n    }\n    return null;\n  }\n  repeat(event, interval, dir) {\n    if (this.readonly) {\n      return;\n    }\n    let i = interval || 500;\n    this.clearTimer();\n    this.timer = setTimeout(() => {\n      this.repeat(event, 40, dir);\n    }, i);\n    this.spin(event, dir);\n  }\n  spin(event, dir) {\n    let step = this.step * dir;\n    let currentValue = this.parseValue(this.input?.nativeElement.value) || 0;\n    let newValue = this.validateValue(currentValue + step);\n    if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n      return;\n    }\n    this.updateInput(newValue, null, 'spin', null);\n    this.updateModel(event, newValue);\n    this.handleOnInput(event, currentValue, newValue);\n  }\n  clear() {\n    this.value = null;\n    this.onModelChange(this.value);\n    this.onClear.emit();\n  }\n  onUpButtonMouseDown(event) {\n    if (event.button === 2) {\n      this.clearTimer();\n      return;\n    }\n    if (!this.disabled) {\n      this.input?.nativeElement.focus();\n      this.repeat(event, null, 1);\n      event.preventDefault();\n    }\n  }\n  onUpButtonMouseUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onUpButtonMouseLeave() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onUpButtonKeyDown(event) {\n    if (event.keyCode === 32 || event.keyCode === 13) {\n      this.repeat(event, null, 1);\n    }\n  }\n  onUpButtonKeyUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonMouseDown(event) {\n    if (event.button === 2) {\n      this.clearTimer();\n      return;\n    }\n    if (!this.disabled) {\n      this.input?.nativeElement.focus();\n      this.repeat(event, null, -1);\n      event.preventDefault();\n    }\n  }\n  onDownButtonMouseUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonMouseLeave() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonKeyUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonKeyDown(event) {\n    if (event.keyCode === 32 || event.keyCode === 13) {\n      this.repeat(event, null, -1);\n    }\n  }\n  onUserInput(event) {\n    if (this.readonly) {\n      return;\n    }\n    if (this.isSpecialChar) {\n      event.target.value = this.lastValue;\n    }\n    this.isSpecialChar = false;\n  }\n  onInputKeyDown(event) {\n    if (this.readonly) {\n      return;\n    }\n    this.lastValue = event.target.value;\n    if (event.shiftKey || event.altKey || event.key === 'Dead') {\n      this.isSpecialChar = true;\n      return;\n    }\n    let selectionStart = event.target.selectionStart;\n    let selectionEnd = event.target.selectionEnd;\n    let inputValue = event.target.value;\n    let newValueStr = null;\n    if (event.altKey) {\n      event.preventDefault();\n    }\n    switch (event.key) {\n      case 'ArrowUp':\n        this.spin(event, 1);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        this.spin(event, -1);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        for (let index = selectionStart; index <= inputValue.length; index++) {\n          const previousCharIndex = index === 0 ? 0 : index - 1;\n          if (this.isNumeralChar(inputValue.charAt(previousCharIndex))) {\n            this.input.nativeElement.setSelectionRange(index, index);\n            break;\n          }\n        }\n        break;\n      case 'ArrowRight':\n        for (let index = selectionEnd; index >= 0; index--) {\n          if (this.isNumeralChar(inputValue.charAt(index))) {\n            this.input.nativeElement.setSelectionRange(index, index);\n            break;\n          }\n        }\n        break;\n      case 'Tab':\n      case 'Enter':\n        newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n        this.input.nativeElement.value = this.formatValue(newValueStr);\n        this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n        this.updateModel(event, newValueStr);\n        break;\n      case 'Backspace':\n        {\n          event.preventDefault();\n          if (selectionStart === selectionEnd) {\n            if (selectionStart == 1 && this.prefix || selectionStart == inputValue.length && this.suffix) {\n              break;\n            }\n            const deleteChar = inputValue.charAt(selectionStart - 1);\n            const {\n              decimalCharIndex,\n              decimalCharIndexWithoutPrefix\n            } = this.getDecimalCharIndexes(inputValue);\n            if (this.isNumeralChar(deleteChar)) {\n              const decimalLength = this.getDecimalLength(inputValue);\n              if (this._group.test(deleteChar)) {\n                this._group.lastIndex = 0;\n                newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n              } else if (this._decimal.test(deleteChar)) {\n                this._decimal.lastIndex = 0;\n                if (decimalLength) {\n                  this.input?.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                } else {\n                  newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                }\n              } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n              } else if (decimalCharIndexWithoutPrefix === 1) {\n                newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n              } else {\n                newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n              }\n            } else if (this.mode === 'currency' && deleteChar.search(this._currency) != -1) {\n              newValueStr = inputValue.slice(1);\n            }\n            this.updateValue(event, newValueStr, null, 'delete-single');\n          } else {\n            newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n            this.updateValue(event, newValueStr, null, 'delete-range');\n          }\n          break;\n        }\n      case 'Delete':\n        event.preventDefault();\n        if (selectionStart === selectionEnd) {\n          if (selectionStart == 0 && this.prefix || selectionStart == inputValue.length - 1 && this.suffix) {\n            break;\n          }\n          const deleteChar = inputValue.charAt(selectionStart);\n          const {\n            decimalCharIndex,\n            decimalCharIndexWithoutPrefix\n          } = this.getDecimalCharIndexes(inputValue);\n          if (this.isNumeralChar(deleteChar)) {\n            const decimalLength = this.getDecimalLength(inputValue);\n            if (this._group.test(deleteChar)) {\n              this._group.lastIndex = 0;\n              newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n            } else if (this._decimal.test(deleteChar)) {\n              this._decimal.lastIndex = 0;\n              if (decimalLength) {\n                this.input?.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n              } else {\n                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n              }\n            } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n              const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n              newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n            } else if (decimalCharIndexWithoutPrefix === 1) {\n              newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n              newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n            } else {\n              newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n            }\n          }\n          this.updateValue(event, newValueStr, null, 'delete-back-single');\n        } else {\n          newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n          this.updateValue(event, newValueStr, null, 'delete-range');\n        }\n        break;\n      case 'Home':\n        if (this.min) {\n          this.updateModel(event, this.min);\n          event.preventDefault();\n        }\n        break;\n      case 'End':\n        if (this.max) {\n          this.updateModel(event, this.max);\n          event.preventDefault();\n        }\n        break;\n      default:\n        break;\n    }\n    this.onKeyDown.emit(event);\n  }\n  onInputKeyPress(event) {\n    if (this.readonly) {\n      return;\n    }\n    let code = event.which || event.keyCode;\n    let char = String.fromCharCode(code);\n    let isDecimalSign = this.isDecimalSign(char);\n    const isMinusSign = this.isMinusSign(char);\n    if (code != 13) {\n      event.preventDefault();\n    }\n    if (!isDecimalSign && event.code === 'NumpadDecimal') {\n      isDecimalSign = true;\n      char = this._decimalChar;\n      code = char.charCodeAt(0);\n    }\n    const {\n      value,\n      selectionStart,\n      selectionEnd\n    } = this.input.nativeElement;\n    const newValue = this.parseValue(value + char);\n    const newValueStr = newValue != null ? newValue.toString() : '';\n    const selectedValue = value.substring(selectionStart, selectionEnd);\n    const selectedValueParsed = this.parseValue(selectedValue);\n    const selectedValueStr = selectedValueParsed != null ? selectedValueParsed.toString() : '';\n    if (selectionStart !== selectionEnd && selectedValueStr.length > 0) {\n      this.insert(event, char, {\n        isDecimalSign,\n        isMinusSign\n      });\n      return;\n    }\n    if (this.maxlength && newValueStr.length > this.maxlength) {\n      return;\n    }\n    if (48 <= code && code <= 57 || isMinusSign || isDecimalSign) {\n      this.insert(event, char, {\n        isDecimalSign,\n        isMinusSign\n      });\n    }\n  }\n  onPaste(event) {\n    if (!this.disabled && !this.readonly) {\n      event.preventDefault();\n      let data = (event.clipboardData || this.document.defaultView['clipboardData']).getData('Text');\n      if (data) {\n        if (this.maxlength) {\n          data = data.toString().substring(0, this.maxlength);\n        }\n        let filteredData = this.parseValue(data);\n        if (filteredData != null) {\n          this.insert(event, filteredData.toString());\n        }\n      }\n    }\n  }\n  allowMinusSign() {\n    return this.min == null || this.min < 0;\n  }\n  isMinusSign(char) {\n    if (this._minusSign.test(char) || char === '-') {\n      this._minusSign.lastIndex = 0;\n      return true;\n    }\n    return false;\n  }\n  isDecimalSign(char) {\n    if (this._decimal.test(char)) {\n      this._decimal.lastIndex = 0;\n      return true;\n    }\n    return false;\n  }\n  isDecimalMode() {\n    return this.mode === 'decimal';\n  }\n  getDecimalCharIndexes(val) {\n    let decimalCharIndex = val.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    const filteredVal = val.replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '');\n    const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    return {\n      decimalCharIndex,\n      decimalCharIndexWithoutPrefix\n    };\n  }\n  getCharIndexes(val) {\n    const decimalCharIndex = val.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    const minusCharIndex = val.search(this._minusSign);\n    this._minusSign.lastIndex = 0;\n    const suffixCharIndex = val.search(this._suffix);\n    this._suffix.lastIndex = 0;\n    const currencyCharIndex = val.search(this._currency);\n    this._currency.lastIndex = 0;\n    return {\n      decimalCharIndex,\n      minusCharIndex,\n      suffixCharIndex,\n      currencyCharIndex\n    };\n  }\n  insert(event, text, sign = {\n    isDecimalSign: false,\n    isMinusSign: false\n  }) {\n    const minusCharIndexOnText = text.search(this._minusSign);\n    this._minusSign.lastIndex = 0;\n    if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n      return;\n    }\n    let selectionStart = this.input?.nativeElement.selectionStart;\n    let selectionEnd = this.input?.nativeElement.selectionEnd;\n    let inputValue = this.input?.nativeElement.value.trim();\n    const {\n      decimalCharIndex,\n      minusCharIndex,\n      suffixCharIndex,\n      currencyCharIndex\n    } = this.getCharIndexes(inputValue);\n    let newValueStr;\n    if (sign.isMinusSign) {\n      if (selectionStart === 0) {\n        newValueStr = inputValue;\n        if (minusCharIndex === -1 || selectionEnd !== 0) {\n          newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n        }\n        this.updateValue(event, newValueStr, text, 'insert');\n      }\n    } else if (sign.isDecimalSign) {\n      if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n        this.updateValue(event, inputValue, text, 'insert');\n      } else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, 'insert');\n      } else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, 'insert');\n      }\n    } else {\n      const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n      const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n      if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n        if (selectionStart + text.length - (decimalCharIndex + 1) <= maxFractionDigits) {\n          const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length;\n          newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n          this.updateValue(event, newValueStr, text, operation);\n        }\n      } else {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, operation);\n      }\n    }\n  }\n  insertText(value, text, start, end) {\n    let textSplit = text === '.' ? text : text.split('.');\n    if (textSplit.length === 2) {\n      const decimalCharIndex = value.slice(start, end).search(this._decimal);\n      this._decimal.lastIndex = 0;\n      return decimalCharIndex > 0 ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : value || this.formatValue(text);\n    } else if (end - start === value.length) {\n      return this.formatValue(text);\n    } else if (start === 0) {\n      return text + value.slice(end);\n    } else if (end === value.length) {\n      return value.slice(0, start) + text;\n    } else {\n      return value.slice(0, start) + text + value.slice(end);\n    }\n  }\n  deleteRange(value, start, end) {\n    let newValueStr;\n    if (end - start === value.length) newValueStr = '';else if (start === 0) newValueStr = value.slice(end);else if (end === value.length) newValueStr = value.slice(0, start);else newValueStr = value.slice(0, start) + value.slice(end);\n    return newValueStr;\n  }\n  initCursor() {\n    let selectionStart = this.input?.nativeElement.selectionStart;\n    let selectionEnd = this.input?.nativeElement.selectionEnd;\n    let inputValue = this.input?.nativeElement.value;\n    let valueLength = inputValue.length;\n    let index = null;\n    // remove prefix\n    let prefixLength = (this.prefixChar || '').length;\n    inputValue = inputValue.replace(this._prefix, '');\n    // Will allow selecting whole prefix. But not a part of it.\n    // Negative values will trigger clauses after this to fix the cursor position.\n    if (selectionStart === selectionEnd || selectionStart !== 0 || selectionEnd < prefixLength) {\n      selectionStart -= prefixLength;\n    }\n    let char = inputValue.charAt(selectionStart);\n    if (this.isNumeralChar(char)) {\n      return selectionStart + prefixLength;\n    }\n    //left\n    let i = selectionStart - 1;\n    while (i >= 0) {\n      char = inputValue.charAt(i);\n      if (this.isNumeralChar(char)) {\n        index = i + prefixLength;\n        break;\n      } else {\n        i--;\n      }\n    }\n    if (index !== null) {\n      this.input?.nativeElement.setSelectionRange(index + 1, index + 1);\n    } else {\n      i = selectionStart;\n      while (i < valueLength) {\n        char = inputValue.charAt(i);\n        if (this.isNumeralChar(char)) {\n          index = i + prefixLength;\n          break;\n        } else {\n          i++;\n        }\n      }\n      if (index !== null) {\n        this.input?.nativeElement.setSelectionRange(index, index);\n      }\n    }\n    return index || 0;\n  }\n  onInputClick() {\n    const currentValue = this.input?.nativeElement.value;\n    if (!this.readonly && currentValue !== DomHandler.getSelection()) {\n      this.initCursor();\n    }\n  }\n  isNumeralChar(char) {\n    if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n      this.resetRegex();\n      return true;\n    }\n    return false;\n  }\n  resetRegex() {\n    this._numeral.lastIndex = 0;\n    this._decimal.lastIndex = 0;\n    this._group.lastIndex = 0;\n    this._minusSign.lastIndex = 0;\n  }\n  updateValue(event, valueStr, insertedValueStr, operation) {\n    let currentValue = this.input?.nativeElement.value;\n    let newValue = null;\n    if (valueStr != null) {\n      newValue = this.parseValue(valueStr);\n      newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n      this.updateInput(newValue, insertedValueStr, operation, valueStr);\n      this.handleOnInput(event, currentValue, newValue);\n    }\n  }\n  handleOnInput(event, currentValue, newValue) {\n    if (this.isValueChanged(currentValue, newValue)) {\n      this.input.nativeElement.value = this.formatValue(newValue);\n      this.input?.nativeElement.setAttribute('aria-valuenow', newValue);\n      !this.isBlurUpdateOnMode && this.updateModel(event, newValue);\n      this.onInput.emit({\n        originalEvent: event,\n        value: newValue,\n        formattedValue: currentValue\n      });\n    }\n  }\n  isValueChanged(currentValue, newValue) {\n    if (newValue === null && currentValue !== null) {\n      return true;\n    }\n    if (newValue != null) {\n      let parsedCurrentValue = typeof currentValue === 'string' ? this.parseValue(currentValue) : currentValue;\n      return newValue !== parsedCurrentValue;\n    }\n    return false;\n  }\n  validateValue(value) {\n    if (value === '-' || value == null) {\n      return null;\n    }\n    if (this.min != null && value < this.min) {\n      return this.min;\n    }\n    if (this.max != null && value > this.max) {\n      return this.max;\n    }\n    return value;\n  }\n  updateInput(value, insertedValueStr, operation, valueStr) {\n    insertedValueStr = insertedValueStr || '';\n    let inputValue = this.input?.nativeElement.value;\n    let newValue = this.formatValue(value);\n    let currentLength = inputValue.length;\n    if (newValue !== valueStr) {\n      newValue = this.concatValues(newValue, valueStr);\n    }\n    if (currentLength === 0) {\n      this.input.nativeElement.value = newValue;\n      this.input.nativeElement.setSelectionRange(0, 0);\n      const index = this.initCursor();\n      const selectionEnd = index + insertedValueStr.length;\n      this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n    } else {\n      let selectionStart = this.input.nativeElement.selectionStart;\n      let selectionEnd = this.input.nativeElement.selectionEnd;\n      if (this.maxlength && newValue.length > this.maxlength) {\n        newValue = newValue.slice(0, this.maxlength);\n        selectionStart = Math.min(selectionStart, this.maxlength);\n        selectionEnd = Math.min(selectionEnd, this.maxlength);\n      }\n      if (this.maxlength && this.maxlength < newValue.length) {\n        return;\n      }\n      this.input.nativeElement.value = newValue;\n      let newLength = newValue.length;\n      if (operation === 'range-insert') {\n        const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n        const startValueStr = startValue !== null ? startValue.toString() : '';\n        const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n        const sRegex = new RegExp(startExpr, 'g');\n        sRegex.test(newValue);\n        const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n        const tRegex = new RegExp(tExpr, 'g');\n        tRegex.test(newValue.slice(sRegex.lastIndex));\n        selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (newLength === currentLength) {\n        if (operation === 'insert' || operation === 'delete-back-single') this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);else if (operation === 'delete-single') this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);else if (operation === 'delete-range' || operation === 'spin') this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (operation === 'delete-back-single') {\n        let prevChar = inputValue.charAt(selectionEnd - 1);\n        let nextChar = inputValue.charAt(selectionEnd);\n        let diff = currentLength - newLength;\n        let isGroupChar = this._group.test(nextChar);\n        if (isGroupChar && diff === 1) {\n          selectionEnd += 1;\n        } else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n          selectionEnd += -1 * diff + 1;\n        }\n        this._group.lastIndex = 0;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (inputValue === '-' && operation === 'insert') {\n        this.input.nativeElement.setSelectionRange(0, 0);\n        const index = this.initCursor();\n        const selectionEnd = index + insertedValueStr.length + 1;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else {\n        selectionEnd = selectionEnd + (newLength - currentLength);\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      }\n    }\n    this.input.nativeElement.setAttribute('aria-valuenow', value);\n  }\n  concatValues(val1, val2) {\n    if (val1 && val2) {\n      let decimalCharIndex = val2.search(this._decimal);\n      this._decimal.lastIndex = 0;\n      if (this.suffixChar) {\n        return decimalCharIndex !== -1 ? val1 : val1.replace(this.suffixChar, '').split(this._decimal)[0] + val2.replace(this.suffixChar, '').slice(decimalCharIndex) + this.suffixChar;\n      } else {\n        return decimalCharIndex !== -1 ? val1.split(this._decimal)[0] + val2.slice(decimalCharIndex) : val1;\n      }\n    }\n    return val1;\n  }\n  getDecimalLength(value) {\n    if (value) {\n      const valueSplit = value.split(this._decimal);\n      if (valueSplit.length === 2) {\n        return valueSplit[1].replace(this._suffix, '').trim().replace(/\\s/g, '').replace(this._currency, '').length;\n      }\n    }\n    return 0;\n  }\n  onInputFocus(event) {\n    this.focused = true;\n    this.onFocus.emit(event);\n  }\n  onInputBlur(event) {\n    this.focused = false;\n    const newValueNumber = this.validateValue(this.parseValue(this.input.nativeElement.value));\n    const newValueString = newValueNumber?.toString();\n    this.input.nativeElement.value = this.formatValue(newValueString);\n    this.input.nativeElement.setAttribute('aria-valuenow', newValueString);\n    this.updateModel(event, newValueNumber);\n    this.onBlur.emit(event);\n  }\n  formattedValue() {\n    const val = !this.value && !this.allowEmpty ? 0 : this.value;\n    return this.formatValue(val);\n  }\n  updateModel(event, value) {\n    if (this.value !== value) {\n      this.value = value;\n      if (!(this.isBlurUpdateOnMode && this.focused)) {\n        this.onModelChange(value);\n      } else if (this.isBlurUpdateOnMode) {\n        this.onModelChange(value);\n      }\n    }\n    this.onModelTouched();\n  }\n  writeValue(value) {\n    this.value = value;\n    this.cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n  get filled() {\n    return this.value != null && this.value.toString().length > 0;\n  }\n  clearTimer() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  }\n  static ɵfac = function InputNumber_Factory(t) {\n    return new (t || InputNumber)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputNumber,\n    selectors: [[\"p-inputNumber\"]],\n    contentQueries: function InputNumber_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function InputNumber_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n    hostVars: 6,\n    hostBindings: function InputNumber_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused)(\"p-inputnumber-clearable\", ctx.showClear && ctx.buttonLayout != \"vertical\");\n      }\n    },\n    inputs: {\n      showButtons: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showButtons\", \"showButtons\", booleanAttribute],\n      format: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"format\", \"format\", booleanAttribute],\n      buttonLayout: \"buttonLayout\",\n      inputId: \"inputId\",\n      styleClass: \"styleClass\",\n      style: \"style\",\n      placeholder: \"placeholder\",\n      size: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"size\", \"size\", numberAttribute],\n      maxlength: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"maxlength\", \"maxlength\", numberAttribute],\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n      title: \"title\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      ariaLabel: \"ariaLabel\",\n      ariaRequired: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"ariaRequired\", \"ariaRequired\", booleanAttribute],\n      name: \"name\",\n      required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n      autocomplete: \"autocomplete\",\n      min: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"min\", \"min\", numberAttribute],\n      max: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"max\", \"max\", numberAttribute],\n      incrementButtonClass: \"incrementButtonClass\",\n      decrementButtonClass: \"decrementButtonClass\",\n      incrementButtonIcon: \"incrementButtonIcon\",\n      decrementButtonIcon: \"decrementButtonIcon\",\n      readonly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"readonly\", \"readonly\", booleanAttribute],\n      step: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"step\", \"step\", numberAttribute],\n      allowEmpty: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"allowEmpty\", \"allowEmpty\", booleanAttribute],\n      locale: \"locale\",\n      localeMatcher: \"localeMatcher\",\n      mode: \"mode\",\n      currency: \"currency\",\n      currencyDisplay: \"currencyDisplay\",\n      useGrouping: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"useGrouping\", \"useGrouping\", booleanAttribute],\n      variant: \"variant\",\n      minFractionDigits: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"minFractionDigits\", \"minFractionDigits\", value => numberAttribute(value, null)],\n      maxFractionDigits: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"maxFractionDigits\", \"maxFractionDigits\", value => numberAttribute(value, null)],\n      prefix: \"prefix\",\n      suffix: \"suffix\",\n      inputStyle: \"inputStyle\",\n      inputStyleClass: \"inputStyleClass\",\n      showClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showClear\", \"showClear\", booleanAttribute],\n      autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute],\n      disabled: \"disabled\"\n    },\n    outputs: {\n      onInput: \"onInput\",\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\",\n      onKeyDown: \"onKeyDown\",\n      onClear: \"onClear\"\n    },\n    features: [i0.ɵɵProvidersFeature([INPUTNUMBER_VALUE_ACCESSOR]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 7,\n    vars: 43,\n    consts: [[\"input\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"pInputText\", \"\", \"role\", \"spinbutton\", \"inputmode\", \"decimal\", \"pAutoFocus\", \"\", 3, \"input\", \"keydown\", \"keypress\", \"paste\", \"click\", \"focus\", \"blur\", \"ngClass\", \"ngStyle\", \"value\", \"disabled\", \"readonly\", \"autofocus\"], [4, \"ngIf\"], [\"class\", \"p-inputnumber-button-group\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-button-icon-only\", \"tabindex\", \"0\", 3, \"ngClass\", \"class\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\", 4, \"ngIf\"], [3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-inputnumber-clear-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"click\", \"ngClass\"], [1, \"p-inputnumber-clear-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [1, \"p-inputnumber-button-group\"], [\"type\", \"button\", \"pButton\", \"\", \"tabindex\", \"0\", 1, \"p-button-icon-only\", 3, \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\", \"ngClass\", \"disabled\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n    template: function InputNumber_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"span\", 1)(1, \"input\", 2, 0);\n        i0.ɵɵlistener(\"input\", function InputNumber_Template_input_input_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onUserInput($event));\n        })(\"keydown\", function InputNumber_Template_input_keydown_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputKeyDown($event));\n        })(\"keypress\", function InputNumber_Template_input_keypress_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputKeyPress($event));\n        })(\"paste\", function InputNumber_Template_input_paste_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPaste($event));\n        })(\"click\", function InputNumber_Template_input_click_1_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputClick());\n        })(\"focus\", function InputNumber_Template_input_focus_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputFocus($event));\n        })(\"blur\", function InputNumber_Template_input_blur_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputBlur($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, InputNumber_ng_container_3_Template, 3, 2, \"ng-container\", 3)(4, InputNumber_span_4_Template, 7, 19, \"span\", 4)(5, InputNumber_button_5_Template, 3, 9, \"button\", 5)(6, InputNumber_button_6_Template, 3, 9, \"button\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(39, _c1, ctx.showButtons && ctx.buttonLayout === \"stacked\", ctx.showButtons && ctx.buttonLayout === \"horizontal\", ctx.showButtons && ctx.buttonLayout === \"vertical\"))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"inputnumber\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.inputStyleClass);\n        i0.ɵɵclassProp(\"p-variant-filled\", ctx.variant === \"filled\" || ctx.config.inputStyle() === \"filled\");\n        i0.ɵɵproperty(\"ngClass\", \"p-inputnumber-input\")(\"ngStyle\", ctx.inputStyle)(\"value\", ctx.formattedValue())(\"disabled\", ctx.disabled)(\"readonly\", ctx.readonly)(\"autofocus\", ctx.autofocus);\n        i0.ɵɵattribute(\"id\", ctx.inputId)(\"variant\", ctx.variant)(\"aria-valuemin\", ctx.min)(\"aria-valuemax\", ctx.max)(\"aria-valuenow\", ctx.value)(\"placeholder\", ctx.placeholder)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"title\", ctx.title)(\"size\", ctx.size)(\"name\", ctx.name)(\"autocomplete\", ctx.autocomplete)(\"maxlength\", ctx.maxlength)(\"tabindex\", ctx.tabindex)(\"aria-required\", ctx.ariaRequired)(\"required\", ctx.required)(\"min\", ctx.min)(\"max\", ctx.max)(\"data-pc-section\", \"input\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.buttonLayout != \"vertical\" && ctx.showClear && ctx.value);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout === \"stacked\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.InputText, i4.ButtonDirective, i5.AutoFocus, TimesIcon, AngleUpIcon, AngleDownIcon],\n    styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputNumber, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputNumber',\n      template: `\n        <span\n            [ngClass]=\"{\n                'p-inputnumber p-component': true,\n                'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal',\n                'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'inputnumber'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                pInputText\n                #input\n                [attr.id]=\"inputId\"\n                role=\"spinbutton\"\n                [ngClass]=\"'p-inputnumber-input'\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [value]=\"formattedValue()\"\n                [attr.variant]=\"variant\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.title]=\"title\"\n                [attr.size]=\"size\"\n                [attr.name]=\"name\"\n                [attr.autocomplete]=\"autocomplete\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.aria-required]=\"ariaRequired\"\n                [attr.required]=\"required\"\n                [attr.min]=\"min\"\n                [attr.max]=\"max\"\n                inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                (keypress)=\"onInputKeyPress($event)\"\n                (paste)=\"onPaste($event)\"\n                (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                [attr.data-pc-section]=\"'input'\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n                [class.p-variant-filled]=\"variant === 'filled' || config.inputStyle() === 'filled'\"\n            />\n            <ng-container *ngIf=\"buttonLayout != 'vertical' && showClear && value\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [ngClass]=\"'p-inputnumber-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span *ngIf=\"clearIconTemplate\" (click)=\"clear()\" class=\"p-inputnumber-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\" [attr.data-pc-section]=\"'buttonGroup'\">\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"incrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"0\"\n                    (mousedown)=\"onUpButtonMouseDown($event)\"\n                    (mouseup)=\"onUpButtonMouseUp()\"\n                    (mouseleave)=\"onUpButtonMouseLeave()\"\n                    (keydown)=\"onUpButtonKeyDown($event)\"\n                    (keyup)=\"onUpButtonKeyUp()\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.data-pc-section]=\"'incrementbutton'\"\n                >\n                    <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!incrementButtonIcon\">\n                        <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"decrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"0\"\n                    [attr.aria-hidden]=\"true\"\n                    (mousedown)=\"onDownButtonMouseDown($event)\"\n                    (mouseup)=\"onDownButtonMouseUp()\"\n                    (mouseleave)=\"onDownButtonMouseLeave()\"\n                    (keydown)=\"onDownButtonKeyDown($event)\"\n                    (keyup)=\"onDownButtonKeyUp()\"\n                    [attr.data-pc-section]=\"decrementbutton\"\n                >\n                    <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!decrementButtonIcon\">\n                        <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n            </span>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                [class]=\"incrementButtonClass\"\n                class=\"p-button-icon-only\"\n                [disabled]=\"disabled\"\n                tabindex=\"0\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onUpButtonMouseDown($event)\"\n                (mouseup)=\"onUpButtonMouseUp()\"\n                (mouseleave)=\"onUpButtonMouseLeave()\"\n                (keydown)=\"onUpButtonKeyDown($event)\"\n                (keyup)=\"onUpButtonKeyUp()\"\n                [attr.data-pc-section]=\"'incrementbutton'\"\n            >\n                <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!incrementButtonIcon\">\n                    <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                class=\"p-button-icon-only\"\n                [class]=\"decrementButtonClass\"\n                [disabled]=\"disabled\"\n                tabindex=\"0\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onDownButtonMouseDown($event)\"\n                (mouseup)=\"onDownButtonMouseUp()\"\n                (mouseleave)=\"onDownButtonMouseLeave()\"\n                (keydown)=\"onDownButtonKeyDown($event)\"\n                (keyup)=\"onDownButtonKeyUp()\"\n                [attr.data-pc-section]=\"'decrementbutton'\"\n            >\n                <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!decrementButtonIcon\">\n                    <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n        </span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [INPUTNUMBER_VALUE_ACCESSOR],\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-inputnumber-clearable]': 'showClear && buttonLayout != \"vertical\"'\n      },\n      styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Injector\n  }, {\n    type: i1.PrimeNGConfig\n  }], {\n    showButtons: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    format: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    buttonLayout: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    size: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    maxlength: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    title: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaRequired: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autocomplete: [{\n      type: Input\n    }],\n    min: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    max: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    incrementButtonClass: [{\n      type: Input\n    }],\n    decrementButtonClass: [{\n      type: Input\n    }],\n    incrementButtonIcon: [{\n      type: Input\n    }],\n    decrementButtonIcon: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    step: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    allowEmpty: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    locale: [{\n      type: Input\n    }],\n    localeMatcher: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    currency: [{\n      type: Input\n    }],\n    currencyDisplay: [{\n      type: Input\n    }],\n    useGrouping: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    variant: [{\n      type: Input\n    }],\n    minFractionDigits: [{\n      type: Input,\n      args: [{\n        transform: value => numberAttribute(value, null)\n      }]\n    }],\n    maxFractionDigits: [{\n      type: Input,\n      args: [{\n        transform: value => numberAttribute(value, null)\n      }]\n    }],\n    prefix: [{\n      type: Input\n    }],\n    suffix: [{\n      type: Input\n    }],\n    inputStyle: [{\n      type: Input\n    }],\n    inputStyleClass: [{\n      type: Input\n    }],\n    showClear: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input\n    }],\n    onInput: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onKeyDown: [{\n      type: Output\n    }],\n    onClear: [{\n      type: Output\n    }],\n    input: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass InputNumberModule {\n  static ɵfac = function InputNumberModule_Factory(t) {\n    return new (t || InputNumberModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputNumberModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, InputTextModule, ButtonModule, AutoFocusModule, TimesIcon, AngleUpIcon, AngleDownIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputNumberModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, InputTextModule, ButtonModule, AutoFocusModule, TimesIcon, AngleUpIcon, AngleDownIcon],\n      exports: [InputNumber, SharedModule],\n      declarations: [InputNumber]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberModule };", "map": {"version": 3, "names": ["i2", "DOCUMENT", "CommonModule", "i0", "forwardRef", "EventEmitter", "booleanAttribute", "numberAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChildren", "NgModule", "NG_VALUE_ACCESSOR", "NgControl", "i1", "PrimeTemplate", "SharedModule", "i5", "AutoFocusModule", "i4", "ButtonModule", "<PERSON><PERSON><PERSON><PERSON>", "AngleDownIcon", "AngleUpIcon", "TimesIcon", "i3", "InputTextModule", "_c0", "_c1", "a0", "a1", "a2", "_c2", "_c3", "InputNumber_ng_container_3_TimesIcon_1_Template", "rf", "ctx", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "InputNumber_ng_container_3_TimesIcon_1_Template_TimesIcon_click_0_listener", "ɵɵrestoreView", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "clear", "ɵɵelementEnd", "ɵɵproperty", "ɵɵattribute", "InputNumber_ng_container_3_span_2_1_ng_template_0_Template", "InputNumber_ng_container_3_span_2_1_Template", "ɵɵtemplate", "InputNumber_ng_container_3_span_2_Template", "_r4", "InputNumber_ng_container_3_span_2_Template_span_click_0_listener", "ɵɵadvance", "clearIconTemplate", "InputNumber_ng_container_3_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "InputNumber_span_4_span_2_Template", "ɵɵelement", "incrementButtonIcon", "InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template", "InputNumber_span_4_ng_container_3_2_ng_template_0_Template", "InputNumber_span_4_ng_container_3_2_Template", "InputNumber_span_4_ng_container_3_Template", "incrementButtonIconTemplate", "InputNumber_span_4_span_5_Template", "decrementButtonIcon", "InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template", "InputNumber_span_4_ng_container_6_2_ng_template_0_Template", "InputNumber_span_4_ng_container_6_2_Template", "InputNumber_span_4_ng_container_6_Template", "decrementButtonIconTemplate", "InputNumber_span_4_Template", "_r5", "InputNumber_span_4_Template_button_mousedown_1_listener", "$event", "onUpButtonMouseDown", "InputNumber_span_4_Template_button_mouseup_1_listener", "onUpButtonMouseUp", "InputNumber_span_4_Template_button_mouseleave_1_listener", "onUpButtonMouseLeave", "InputNumber_span_4_Template_button_keydown_1_listener", "onUpButtonKeyDown", "InputNumber_span_4_Template_button_keyup_1_listener", "onUpButtonKeyUp", "InputNumber_span_4_Template_button_mousedown_4_listener", "onDownButtonMouseDown", "InputNumber_span_4_Template_button_mouseup_4_listener", "onDownButtonMouseUp", "InputNumber_span_4_Template_button_mouseleave_4_listener", "onDownButtonMouseLeave", "InputNumber_span_4_Template_button_keydown_4_listener", "onDownButtonKeyDown", "InputNumber_span_4_Template_button_keyup_4_listener", "onDownButtonKeyUp", "ɵɵclassMap", "incrementButtonClass", "ɵɵpureFunction0", "disabled", "decrementButtonClass", "decrementbutton", "InputNumber_button_5_span_1_Template", "InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template", "InputNumber_button_5_ng_container_2_2_ng_template_0_Template", "InputNumber_button_5_ng_container_2_2_Template", "InputNumber_button_5_ng_container_2_Template", "InputNumber_button_5_Template", "_r6", "InputNumber_button_5_Template_button_mousedown_0_listener", "InputNumber_button_5_Template_button_mouseup_0_listener", "InputNumber_button_5_Template_button_mouseleave_0_listener", "InputNumber_button_5_Template_button_keydown_0_listener", "InputNumber_button_5_Template_button_keyup_0_listener", "InputNumber_button_6_span_1_Template", "InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template", "InputNumber_button_6_ng_container_2_2_ng_template_0_Template", "InputNumber_button_6_ng_container_2_2_Template", "InputNumber_button_6_ng_container_2_Template", "InputNumber_button_6_Template", "_r7", "InputNumber_button_6_Template_button_mousedown_0_listener", "InputNumber_button_6_Template_button_mouseup_0_listener", "InputNumber_button_6_Template_button_mouseleave_0_listener", "InputNumber_button_6_Template_button_keydown_0_listener", "InputNumber_button_6_Template_button_keyup_0_listener", "INPUTNUMBER_VALUE_ACCESSOR", "provide", "useExisting", "InputNumber", "multi", "document", "el", "cd", "injector", "config", "showButtons", "format", "buttonLayout", "inputId", "styleClass", "style", "placeholder", "size", "maxlength", "tabindex", "title", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaRequired", "name", "required", "autocomplete", "min", "max", "readonly", "step", "allowEmpty", "locale", "localeMatcher", "mode", "currency", "currencyDisplay", "useGrouping", "variant", "minFractionDigits", "maxFractionDigits", "prefix", "suffix", "inputStyle", "inputStyleClass", "showClear", "autofocus", "_disabled", "focused", "timer", "clearTimer", "onInput", "onFocus", "onBlur", "onKeyDown", "onClear", "input", "templates", "value", "onModelChange", "onModelTouched", "initialized", "groupChar", "prefixChar", "suffixChar", "isSpecialChar", "lastValue", "_numeral", "numberFormat", "_decimal", "_decimalChar", "_group", "_minusSign", "_currency", "_prefix", "_suffix", "_index", "ngControl", "constructor", "ngOnChanges", "simpleChange", "props", "some", "p", "updateConstructParser", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "ngOnInit", "get", "optional", "<PERSON><PERSON><PERSON><PERSON>", "getOptions", "minimumFractionDigits", "undefined", "maximumFractionDigits", "Intl", "NumberFormat", "numerals", "reverse", "index", "Map", "map", "d", "i", "RegExp", "join", "getGroupingExpression", "getMinusSignExpression", "getCurrencyExpression", "getDecimalExpression", "getDecimalChar", "getSuffixExpression", "getPrefixExpression", "escapeRegExp", "text", "replace", "decimalChar", "formatter", "trim", "char<PERSON>t", "split", "isBlurUpdateOnMode", "control", "updateOn", "formatValue", "formattedValue", "toString", "parseValue", "suffixRegex", "prefixRegex", "currencyRegex", "filteredText", "parsedValue", "isNaN", "repeat", "event", "interval", "dir", "setTimeout", "spin", "currentValue", "nativeElement", "newValue", "validate<PERSON><PERSON>ue", "length", "updateInput", "updateModel", "handleOnInput", "emit", "button", "focus", "preventDefault", "keyCode", "onUserInput", "target", "onInputKeyDown", "shift<PERSON>ey", "altKey", "key", "selectionStart", "selectionEnd", "inputValue", "newValueStr", "previousCharIndex", "isNumeralChar", "setSelectionRange", "setAttribute", "deleteChar", "decimalCharIndex", "decimalCharIndexWithoutPrefix", "getDecimalCharIndexes", "decimalLength", "getDecimalLength", "test", "lastIndex", "slice", "insertedText", "isDecimalMode", "search", "updateValue", "deleteRange", "onInputKeyPress", "code", "which", "char", "String", "fromCharCode", "isDecimalSign", "isMinusSign", "charCodeAt", "selected<PERSON><PERSON><PERSON>", "substring", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedValueStr", "insert", "onPaste", "data", "clipboardData", "defaultView", "getData", "filteredData", "allowMinusSign", "val", "filteredVal", "getCharIndexes", "minusCharIndex", "suffixCharIndex", "currencyCharIndex", "sign", "minusCharIndexOnText", "insertText", "resolvedOptions", "operation", "charIndex", "start", "end", "textSplit", "initCursor", "valueLength", "prefixLength", "onInputClick", "getSelection", "resetRegex", "valueStr", "insertedValueStr", "isValueChanged", "originalEvent", "parsedCurrentValue", "<PERSON><PERSON><PERSON><PERSON>", "concat<PERSON><PERSON><PERSON>", "Math", "<PERSON><PERSON><PERSON><PERSON>", "startValue", "startValueStr", "startExpr", "sRegex", "tExpr", "tRegex", "prevChar", "nextChar", "diff", "isGroupChar", "val1", "val2", "valueSplit", "onInputFocus", "onInputBlur", "newValueNumber", "newValueString", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "filled", "clearInterval", "ɵfac", "InputNumber_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "Injector", "PrimeNGConfig", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "InputNumber_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "InputNumber_Query", "ɵɵviewQuery", "first", "hostAttrs", "hostVars", "hostBindings", "InputNumber_HostBindings", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "InputNumber_Template", "_r1", "InputNumber_Template_input_input_1_listener", "InputNumber_Template_input_keydown_1_listener", "InputNumber_Template_input_keypress_1_listener", "InputNumber_Template_input_paste_1_listener", "InputNumber_Template_input_click_1_listener", "InputNumber_Template_input_focus_1_listener", "InputNumber_Template_input_blur_1_listener", "ɵɵpureFunction3", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "InputText", "ButtonDirective", "AutoFocus", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "providers", "None", "host", "class", "Document", "decorators", "transform", "InputNumberModule", "InputNumberModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-inputnumber.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i5 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport * as i4 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleUpIcon } from 'primeng/icons/angleup';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\n\nconst INPUTNUMBER_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputNumber),\n    multi: true\n};\n/**\n * InputNumber is an input component to provide numerical input.\n * @group Components\n */\nclass InputNumber {\n    document;\n    el;\n    cd;\n    injector;\n    config;\n    /**\n     * Displays spinner buttons.\n     * @group Props\n     */\n    showButtons = false;\n    /**\n     * Whether to format the value.\n     * @group Props\n     */\n    format = true;\n    /**\n     * Layout of the buttons, valid values are \"stacked\" (default), \"horizontal\" and \"vertical\".\n     * @group Props\n     */\n    buttonLayout = 'stacked';\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Size of the input field.\n     * @group Props\n     */\n    size;\n    /**\n     * Maximum number of character allows in the input field.\n     * @group Props\n     */\n    maxlength;\n    /**\n     * Specifies tab order of the element.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Title text of the input text.\n     * @group Props\n     */\n    title;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Used to indicate that user input is required on an element before a form can be submitted.\n     * @group Props\n     */\n    ariaRequired;\n    /**\n     * Name of the input field.\n     * @group Props\n     */\n    name;\n    /**\n     * Indicates that whether the input field is required.\n     * @group Props\n     */\n    required;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    autocomplete;\n    /**\n     * Mininum boundary value.\n     * @group Props\n     */\n    min;\n    /**\n     * Maximum boundary value.\n     * @group Props\n     */\n    max;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    incrementButtonClass;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    decrementButtonClass;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    incrementButtonIcon;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    decrementButtonIcon;\n    /**\n     * When present, it specifies that an input field is read-only.\n     * @group Props\n     */\n    readonly = false;\n    /**\n     * Step factor to increment/decrement the value.\n     * @group Props\n     */\n    step = 1;\n    /**\n     * Determines whether the input field is empty.\n     * @group Props\n     */\n    allowEmpty = true;\n    /**\n     * Locale to be used in formatting.\n     * @group Props\n     */\n    locale;\n    /**\n     * The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\"; the default is \"best fit\". See Locale Negotiation for details.\n     * @group Props\n     */\n    localeMatcher;\n    /**\n     * Defines the behavior of the component, valid values are \"decimal\" and \"currency\".\n     * @group Props\n     */\n    mode = 'decimal';\n    /**\n     * The currency to use in currency formatting. Possible values are the ISO 4217 currency codes, such as \"USD\" for the US dollar, \"EUR\" for the euro, or \"CNY\" for the Chinese RMB. There is no default value; if the style is \"currency\", the currency property must be provided.\n     * @group Props\n     */\n    currency;\n    /**\n     * How to display the currency in currency formatting. Possible values are \"symbol\" to use a localized currency symbol such as €, ü\"code\" to use the ISO currency code, \"name\" to use a localized currency name such as \"dollar\"; the default is \"symbol\".\n     * @group Props\n     */\n    currencyDisplay;\n    /**\n     * Whether to use grouping separators, such as thousands separators or thousand/lakh/crore separators.\n     * @group Props\n     */\n    useGrouping = true;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    variant = 'outlined';\n    /**\n     * The minimum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number and percent formatting is 0; the default for currency formatting is the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    minFractionDigits;\n    /**\n     * The maximum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number formatting is the larger of minimumFractionDigits and 3; the default for currency formatting is the larger of minimumFractionDigits and the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    maxFractionDigits;\n    /**\n     * Text to display before the value.\n     * @group Props\n     */\n    prefix;\n    /**\n     * Text to display after the value.\n     * @group Props\n     */\n    suffix;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyle;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    inputStyleClass;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(disabled) {\n        if (disabled)\n            this.focused = false;\n        this._disabled = disabled;\n        if (this.timer)\n            this.clearTimer();\n    }\n    /**\n     * Callback to invoke on input.\n     * @param {InputNumberInputEvent} event - Custom input event.\n     * @group Emits\n     */\n    onInput = new EventEmitter();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke on input key press.\n     * @param {KeyboardEvent} event - Keyboard event.\n     * @group Emits\n     */\n    onKeyDown = new EventEmitter();\n    /**\n     * Callback to invoke when clear token is clicked.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    input;\n    templates;\n    clearIconTemplate;\n    incrementButtonIconTemplate;\n    decrementButtonIconTemplate;\n    value;\n    onModelChange = () => { };\n    onModelTouched = () => { };\n    focused;\n    initialized;\n    groupChar = '';\n    prefixChar = '';\n    suffixChar = '';\n    isSpecialChar;\n    timer;\n    lastValue;\n    _numeral;\n    numberFormat;\n    _decimal;\n    _decimalChar;\n    _group;\n    _minusSign;\n    _currency;\n    _prefix;\n    _suffix;\n    _index;\n    _disabled;\n    ngControl = null;\n    constructor(document, el, cd, injector, config) {\n        this.document = document;\n        this.el = el;\n        this.cd = cd;\n        this.injector = injector;\n        this.config = config;\n    }\n    ngOnChanges(simpleChange) {\n        const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n        if (props.some((p) => !!simpleChange[p])) {\n            this.updateConstructParser();\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n                case 'incrementbuttonicon':\n                    this.incrementButtonIconTemplate = item.template;\n                    break;\n                case 'decrementbuttonicon':\n                    this.decrementButtonIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngOnInit() {\n        this.ngControl = this.injector.get(NgControl, null, { optional: true });\n        this.constructParser();\n        this.initialized = true;\n    }\n    getOptions() {\n        return {\n            localeMatcher: this.localeMatcher,\n            style: this.mode,\n            currency: this.currency,\n            currencyDisplay: this.currencyDisplay,\n            useGrouping: this.useGrouping,\n            minimumFractionDigits: this.minFractionDigits ?? undefined,\n            maximumFractionDigits: this.maxFractionDigits ?? undefined\n        };\n    }\n    constructParser() {\n        this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n        const numerals = [...new Intl.NumberFormat(this.locale, { useGrouping: false }).format(9876543210)].reverse();\n        const index = new Map(numerals.map((d, i) => [d, i]));\n        this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n        this._group = this.getGroupingExpression();\n        this._minusSign = this.getMinusSignExpression();\n        this._currency = this.getCurrencyExpression();\n        this._decimal = this.getDecimalExpression();\n        this._decimalChar = this.getDecimalChar();\n        this._suffix = this.getSuffixExpression();\n        this._prefix = this.getPrefixExpression();\n        this._index = (d) => index.get(d);\n    }\n    updateConstructParser() {\n        if (this.initialized) {\n            this.constructParser();\n        }\n    }\n    escapeRegExp(text) {\n        return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n    }\n    getDecimalExpression() {\n        const decimalChar = this.getDecimalChar();\n        return new RegExp(`[${decimalChar}]`, 'g');\n    }\n    getDecimalChar() {\n        const formatter = new Intl.NumberFormat(this.locale, { ...this.getOptions(), useGrouping: false });\n        return formatter\n            .format(1.1)\n            .replace(this._currency, '')\n            .trim()\n            .replace(this._numeral, '');\n    }\n    getGroupingExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: true });\n        this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n        return new RegExp(`[${this.groupChar}]`, 'g');\n    }\n    getMinusSignExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: false });\n        return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n    }\n    getCurrencyExpression() {\n        if (this.currency) {\n            const formatter = new Intl.NumberFormat(this.locale, { style: 'currency', currency: this.currency, currencyDisplay: this.currencyDisplay, minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n        }\n        return new RegExp(`[]`, 'g');\n    }\n    getPrefixExpression() {\n        if (this.prefix) {\n            this.prefixChar = this.prefix;\n        }\n        else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay });\n            this.prefixChar = formatter.format(1).split('1')[0];\n        }\n        return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n    }\n    getSuffixExpression() {\n        if (this.suffix) {\n            this.suffixChar = this.suffix;\n        }\n        else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay, minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            this.suffixChar = formatter.format(1).split('1')[1];\n        }\n        return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n    }\n    get isBlurUpdateOnMode() {\n        return this.ngControl?.control?.updateOn === 'blur';\n    }\n    formatValue(value) {\n        if (value != null) {\n            if (value === '-') {\n                // Minus sign\n                return value;\n            }\n            if (this.format) {\n                let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n                let formattedValue = formatter.format(value);\n                if (this.prefix && value != this.prefix) {\n                    formattedValue = this.prefix + formattedValue;\n                }\n                if (this.suffix && value != this.suffix) {\n                    formattedValue = formattedValue + this.suffix;\n                }\n                return formattedValue;\n            }\n            return value.toString();\n        }\n        return '';\n    }\n    parseValue(text) {\n        const suffixRegex = new RegExp(this._suffix, '');\n        const prefixRegex = new RegExp(this._prefix, '');\n        const currencyRegex = new RegExp(this._currency, '');\n        let filteredText = text\n            .replace(suffixRegex, '')\n            .replace(prefixRegex, '')\n            .trim()\n            .replace(/\\s/g, '')\n            .replace(currencyRegex, '')\n            .replace(this._group, '')\n            .replace(this._minusSign, '-')\n            .replace(this._decimal, '.')\n            .replace(this._numeral, this._index);\n        if (filteredText) {\n            if (filteredText === '-')\n                // Minus sign\n                return filteredText;\n            let parsedValue = +filteredText;\n            return isNaN(parsedValue) ? null : parsedValue;\n        }\n        return null;\n    }\n    repeat(event, interval, dir) {\n        if (this.readonly) {\n            return;\n        }\n        let i = interval || 500;\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(event, 40, dir);\n        }, i);\n        this.spin(event, dir);\n    }\n    spin(event, dir) {\n        let step = this.step * dir;\n        let currentValue = this.parseValue(this.input?.nativeElement.value) || 0;\n        let newValue = this.validateValue(currentValue + step);\n        if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n            return;\n        }\n        this.updateInput(newValue, null, 'spin', null);\n        this.updateModel(event, newValue);\n        this.handleOnInput(event, currentValue, newValue);\n    }\n    clear() {\n        this.value = null;\n        this.onModelChange(this.value);\n        this.onClear.emit();\n    }\n    onUpButtonMouseDown(event) {\n        if (event.button === 2) {\n            this.clearTimer();\n            return;\n        }\n        if (!this.disabled) {\n            this.input?.nativeElement.focus();\n            this.repeat(event, null, 1);\n            event.preventDefault();\n        }\n    }\n    onUpButtonMouseUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onUpButtonMouseLeave() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onUpButtonKeyDown(event) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, 1);\n        }\n    }\n    onUpButtonKeyUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonMouseDown(event) {\n        if (event.button === 2) {\n            this.clearTimer();\n            return;\n        }\n        if (!this.disabled) {\n            this.input?.nativeElement.focus();\n            this.repeat(event, null, -1);\n            event.preventDefault();\n        }\n    }\n    onDownButtonMouseUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonMouseLeave() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonKeyUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonKeyDown(event) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, -1);\n        }\n    }\n    onUserInput(event) {\n        if (this.readonly) {\n            return;\n        }\n        if (this.isSpecialChar) {\n            event.target.value = this.lastValue;\n        }\n        this.isSpecialChar = false;\n    }\n    onInputKeyDown(event) {\n        if (this.readonly) {\n            return;\n        }\n        this.lastValue = event.target.value;\n        if (event.shiftKey || event.altKey || event.key === 'Dead') {\n            this.isSpecialChar = true;\n            return;\n        }\n        let selectionStart = event.target.selectionStart;\n        let selectionEnd = event.target.selectionEnd;\n        let inputValue = event.target.value;\n        let newValueStr = null;\n        if (event.altKey) {\n            event.preventDefault();\n        }\n        switch (event.key) {\n            case 'ArrowUp':\n                this.spin(event, 1);\n                event.preventDefault();\n                break;\n            case 'ArrowDown':\n                this.spin(event, -1);\n                event.preventDefault();\n                break;\n            case 'ArrowLeft':\n                for (let index = selectionStart; index <= inputValue.length; index++) {\n                    const previousCharIndex = index === 0 ? 0 : index - 1;\n                    if (this.isNumeralChar(inputValue.charAt(previousCharIndex))) {\n                        this.input.nativeElement.setSelectionRange(index, index);\n                        break;\n                    }\n                }\n                break;\n            case 'ArrowRight':\n                for (let index = selectionEnd; index >= 0; index--) {\n                    if (this.isNumeralChar(inputValue.charAt(index))) {\n                        this.input.nativeElement.setSelectionRange(index, index);\n                        break;\n                    }\n                }\n                break;\n            case 'Tab':\n            case 'Enter':\n                newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n                this.input.nativeElement.value = this.formatValue(newValueStr);\n                this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n                this.updateModel(event, newValueStr);\n                break;\n            case 'Backspace': {\n                event.preventDefault();\n                if (selectionStart === selectionEnd) {\n                    if ((selectionStart == 1 && this.prefix) || (selectionStart == inputValue.length && this.suffix)) {\n                        break;\n                    }\n                    const deleteChar = inputValue.charAt(selectionStart - 1);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n                        }\n                        else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n                            if (decimalLength) {\n                                this.input?.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                            }\n                            else {\n                                newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                            }\n                        }\n                        else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n                        }\n                        else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                            newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n                        }\n                        else {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                        }\n                    }\n                    else if (this.mode === 'currency' && deleteChar.search(this._currency) != -1) {\n                        newValueStr = inputValue.slice(1);\n                    }\n                    this.updateValue(event, newValueStr, null, 'delete-single');\n                }\n                else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n                break;\n            }\n            case 'Delete':\n                event.preventDefault();\n                if (selectionStart === selectionEnd) {\n                    if ((selectionStart == 0 && this.prefix) || (selectionStart == inputValue.length - 1 && this.suffix)) {\n                        break;\n                    }\n                    const deleteChar = inputValue.charAt(selectionStart);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n                        }\n                        else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n                            if (decimalLength) {\n                                this.input?.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n                            }\n                            else {\n                                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                            }\n                        }\n                        else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n                        }\n                        else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n                            newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n                        }\n                        else {\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                        }\n                    }\n                    this.updateValue(event, newValueStr, null, 'delete-back-single');\n                }\n                else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n                break;\n            case 'Home':\n                if (this.min) {\n                    this.updateModel(event, this.min);\n                    event.preventDefault();\n                }\n                break;\n            case 'End':\n                if (this.max) {\n                    this.updateModel(event, this.max);\n                    event.preventDefault();\n                }\n                break;\n            default:\n                break;\n        }\n        this.onKeyDown.emit(event);\n    }\n    onInputKeyPress(event) {\n        if (this.readonly) {\n            return;\n        }\n        let code = event.which || event.keyCode;\n        let char = String.fromCharCode(code);\n        let isDecimalSign = this.isDecimalSign(char);\n        const isMinusSign = this.isMinusSign(char);\n        if (code != 13) {\n            event.preventDefault();\n        }\n        if (!isDecimalSign && event.code === 'NumpadDecimal') {\n            isDecimalSign = true;\n            char = this._decimalChar;\n            code = char.charCodeAt(0);\n        }\n        const { value, selectionStart, selectionEnd } = this.input.nativeElement;\n        const newValue = this.parseValue(value + char);\n        const newValueStr = newValue != null ? newValue.toString() : '';\n        const selectedValue = value.substring(selectionStart, selectionEnd);\n        const selectedValueParsed = this.parseValue(selectedValue);\n        const selectedValueStr = selectedValueParsed != null ? selectedValueParsed.toString() : '';\n        if (selectionStart !== selectionEnd && selectedValueStr.length > 0) {\n            this.insert(event, char, { isDecimalSign, isMinusSign });\n            return;\n        }\n        if (this.maxlength && newValueStr.length > this.maxlength) {\n            return;\n        }\n        if ((48 <= code && code <= 57) || isMinusSign || isDecimalSign) {\n            this.insert(event, char, { isDecimalSign, isMinusSign });\n        }\n    }\n    onPaste(event) {\n        if (!this.disabled && !this.readonly) {\n            event.preventDefault();\n            let data = (event.clipboardData || this.document.defaultView['clipboardData']).getData('Text');\n            if (data) {\n                if (this.maxlength) {\n                    data = data.toString().substring(0, this.maxlength);\n                }\n                let filteredData = this.parseValue(data);\n                if (filteredData != null) {\n                    this.insert(event, filteredData.toString());\n                }\n            }\n        }\n    }\n    allowMinusSign() {\n        return this.min == null || this.min < 0;\n    }\n    isMinusSign(char) {\n        if (this._minusSign.test(char) || char === '-') {\n            this._minusSign.lastIndex = 0;\n            return true;\n        }\n        return false;\n    }\n    isDecimalSign(char) {\n        if (this._decimal.test(char)) {\n            this._decimal.lastIndex = 0;\n            return true;\n        }\n        return false;\n    }\n    isDecimalMode() {\n        return this.mode === 'decimal';\n    }\n    getDecimalCharIndexes(val) {\n        let decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        const filteredVal = val\n            .replace(this._prefix, '')\n            .trim()\n            .replace(/\\s/g, '')\n            .replace(this._currency, '');\n        const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        return { decimalCharIndex, decimalCharIndexWithoutPrefix };\n    }\n    getCharIndexes(val) {\n        const decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        const minusCharIndex = val.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        const suffixCharIndex = val.search(this._suffix);\n        this._suffix.lastIndex = 0;\n        const currencyCharIndex = val.search(this._currency);\n        this._currency.lastIndex = 0;\n        return { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex };\n    }\n    insert(event, text, sign = { isDecimalSign: false, isMinusSign: false }) {\n        const minusCharIndexOnText = text.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n            return;\n        }\n        let selectionStart = this.input?.nativeElement.selectionStart;\n        let selectionEnd = this.input?.nativeElement.selectionEnd;\n        let inputValue = this.input?.nativeElement.value.trim();\n        const { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex } = this.getCharIndexes(inputValue);\n        let newValueStr;\n        if (sign.isMinusSign) {\n            if (selectionStart === 0) {\n                newValueStr = inputValue;\n                if (minusCharIndex === -1 || selectionEnd !== 0) {\n                    newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n                }\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        }\n        else if (sign.isDecimalSign) {\n            if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n                this.updateValue(event, inputValue, text, 'insert');\n            }\n            else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n            else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        }\n        else {\n            const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n            const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n            if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                if (selectionStart + text.length - (decimalCharIndex + 1) <= maxFractionDigits) {\n                    const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length;\n                    newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n                    this.updateValue(event, newValueStr, text, operation);\n                }\n            }\n            else {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, operation);\n            }\n        }\n    }\n    insertText(value, text, start, end) {\n        let textSplit = text === '.' ? text : text.split('.');\n        if (textSplit.length === 2) {\n            const decimalCharIndex = value.slice(start, end).search(this._decimal);\n            this._decimal.lastIndex = 0;\n            return decimalCharIndex > 0 ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : value || this.formatValue(text);\n        }\n        else if (end - start === value.length) {\n            return this.formatValue(text);\n        }\n        else if (start === 0) {\n            return text + value.slice(end);\n        }\n        else if (end === value.length) {\n            return value.slice(0, start) + text;\n        }\n        else {\n            return value.slice(0, start) + text + value.slice(end);\n        }\n    }\n    deleteRange(value, start, end) {\n        let newValueStr;\n        if (end - start === value.length)\n            newValueStr = '';\n        else if (start === 0)\n            newValueStr = value.slice(end);\n        else if (end === value.length)\n            newValueStr = value.slice(0, start);\n        else\n            newValueStr = value.slice(0, start) + value.slice(end);\n        return newValueStr;\n    }\n    initCursor() {\n        let selectionStart = this.input?.nativeElement.selectionStart;\n        let selectionEnd = this.input?.nativeElement.selectionEnd;\n        let inputValue = this.input?.nativeElement.value;\n        let valueLength = inputValue.length;\n        let index = null;\n        // remove prefix\n        let prefixLength = (this.prefixChar || '').length;\n        inputValue = inputValue.replace(this._prefix, '');\n        // Will allow selecting whole prefix. But not a part of it.\n        // Negative values will trigger clauses after this to fix the cursor position.\n        if (selectionStart === selectionEnd || selectionStart !== 0 || selectionEnd < prefixLength) {\n            selectionStart -= prefixLength;\n        }\n        let char = inputValue.charAt(selectionStart);\n        if (this.isNumeralChar(char)) {\n            return selectionStart + prefixLength;\n        }\n        //left\n        let i = selectionStart - 1;\n        while (i >= 0) {\n            char = inputValue.charAt(i);\n            if (this.isNumeralChar(char)) {\n                index = i + prefixLength;\n                break;\n            }\n            else {\n                i--;\n            }\n        }\n        if (index !== null) {\n            this.input?.nativeElement.setSelectionRange(index + 1, index + 1);\n        }\n        else {\n            i = selectionStart;\n            while (i < valueLength) {\n                char = inputValue.charAt(i);\n                if (this.isNumeralChar(char)) {\n                    index = i + prefixLength;\n                    break;\n                }\n                else {\n                    i++;\n                }\n            }\n            if (index !== null) {\n                this.input?.nativeElement.setSelectionRange(index, index);\n            }\n        }\n        return index || 0;\n    }\n    onInputClick() {\n        const currentValue = this.input?.nativeElement.value;\n        if (!this.readonly && currentValue !== DomHandler.getSelection()) {\n            this.initCursor();\n        }\n    }\n    isNumeralChar(char) {\n        if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n            this.resetRegex();\n            return true;\n        }\n        return false;\n    }\n    resetRegex() {\n        this._numeral.lastIndex = 0;\n        this._decimal.lastIndex = 0;\n        this._group.lastIndex = 0;\n        this._minusSign.lastIndex = 0;\n    }\n    updateValue(event, valueStr, insertedValueStr, operation) {\n        let currentValue = this.input?.nativeElement.value;\n        let newValue = null;\n        if (valueStr != null) {\n            newValue = this.parseValue(valueStr);\n            newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n            this.updateInput(newValue, insertedValueStr, operation, valueStr);\n            this.handleOnInput(event, currentValue, newValue);\n        }\n    }\n    handleOnInput(event, currentValue, newValue) {\n        if (this.isValueChanged(currentValue, newValue)) {\n            this.input.nativeElement.value = this.formatValue(newValue);\n            this.input?.nativeElement.setAttribute('aria-valuenow', newValue);\n            !this.isBlurUpdateOnMode && this.updateModel(event, newValue);\n            this.onInput.emit({ originalEvent: event, value: newValue, formattedValue: currentValue });\n        }\n    }\n    isValueChanged(currentValue, newValue) {\n        if (newValue === null && currentValue !== null) {\n            return true;\n        }\n        if (newValue != null) {\n            let parsedCurrentValue = typeof currentValue === 'string' ? this.parseValue(currentValue) : currentValue;\n            return newValue !== parsedCurrentValue;\n        }\n        return false;\n    }\n    validateValue(value) {\n        if (value === '-' || value == null) {\n            return null;\n        }\n        if (this.min != null && value < this.min) {\n            return this.min;\n        }\n        if (this.max != null && value > this.max) {\n            return this.max;\n        }\n        return value;\n    }\n    updateInput(value, insertedValueStr, operation, valueStr) {\n        insertedValueStr = insertedValueStr || '';\n        let inputValue = this.input?.nativeElement.value;\n        let newValue = this.formatValue(value);\n        let currentLength = inputValue.length;\n        if (newValue !== valueStr) {\n            newValue = this.concatValues(newValue, valueStr);\n        }\n        if (currentLength === 0) {\n            this.input.nativeElement.value = newValue;\n            this.input.nativeElement.setSelectionRange(0, 0);\n            const index = this.initCursor();\n            const selectionEnd = index + insertedValueStr.length;\n            this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        }\n        else {\n            let selectionStart = this.input.nativeElement.selectionStart;\n            let selectionEnd = this.input.nativeElement.selectionEnd;\n            if (this.maxlength && newValue.length > this.maxlength) {\n                newValue = newValue.slice(0, this.maxlength);\n                selectionStart = Math.min(selectionStart, this.maxlength);\n                selectionEnd = Math.min(selectionEnd, this.maxlength);\n            }\n            if (this.maxlength && this.maxlength < newValue.length) {\n                return;\n            }\n            this.input.nativeElement.value = newValue;\n            let newLength = newValue.length;\n            if (operation === 'range-insert') {\n                const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n                const startValueStr = startValue !== null ? startValue.toString() : '';\n                const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n                const sRegex = new RegExp(startExpr, 'g');\n                sRegex.test(newValue);\n                const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n                const tRegex = new RegExp(tExpr, 'g');\n                tRegex.test(newValue.slice(sRegex.lastIndex));\n                selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (newLength === currentLength) {\n                if (operation === 'insert' || operation === 'delete-back-single')\n                    this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);\n                else if (operation === 'delete-single')\n                    this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);\n                else if (operation === 'delete-range' || operation === 'spin')\n                    this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (operation === 'delete-back-single') {\n                let prevChar = inputValue.charAt(selectionEnd - 1);\n                let nextChar = inputValue.charAt(selectionEnd);\n                let diff = currentLength - newLength;\n                let isGroupChar = this._group.test(nextChar);\n                if (isGroupChar && diff === 1) {\n                    selectionEnd += 1;\n                }\n                else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n                    selectionEnd += -1 * diff + 1;\n                }\n                this._group.lastIndex = 0;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (inputValue === '-' && operation === 'insert') {\n                this.input.nativeElement.setSelectionRange(0, 0);\n                const index = this.initCursor();\n                const selectionEnd = index + insertedValueStr.length + 1;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else {\n                selectionEnd = selectionEnd + (newLength - currentLength);\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n        }\n        this.input.nativeElement.setAttribute('aria-valuenow', value);\n    }\n    concatValues(val1, val2) {\n        if (val1 && val2) {\n            let decimalCharIndex = val2.search(this._decimal);\n            this._decimal.lastIndex = 0;\n            if (this.suffixChar) {\n                return decimalCharIndex !== -1 ? val1 : val1.replace(this.suffixChar, '').split(this._decimal)[0] + val2.replace(this.suffixChar, '').slice(decimalCharIndex) + this.suffixChar;\n            }\n            else {\n                return decimalCharIndex !== -1 ? val1.split(this._decimal)[0] + val2.slice(decimalCharIndex) : val1;\n            }\n        }\n        return val1;\n    }\n    getDecimalLength(value) {\n        if (value) {\n            const valueSplit = value.split(this._decimal);\n            if (valueSplit.length === 2) {\n                return valueSplit[1]\n                    .replace(this._suffix, '')\n                    .trim()\n                    .replace(/\\s/g, '')\n                    .replace(this._currency, '').length;\n            }\n        }\n        return 0;\n    }\n    onInputFocus(event) {\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n        this.focused = false;\n        const newValueNumber = this.validateValue(this.parseValue(this.input.nativeElement.value));\n        const newValueString = newValueNumber?.toString();\n        this.input.nativeElement.value = this.formatValue(newValueString);\n        this.input.nativeElement.setAttribute('aria-valuenow', newValueString);\n        this.updateModel(event, newValueNumber);\n        this.onBlur.emit(event);\n    }\n    formattedValue() {\n        const val = !this.value && !this.allowEmpty ? 0 : this.value;\n        return this.formatValue(val);\n    }\n    updateModel(event, value) {\n        if (this.value !== value) {\n            this.value = value;\n            if (!(this.isBlurUpdateOnMode && this.focused)) {\n                this.onModelChange(value);\n            }\n            else if (this.isBlurUpdateOnMode) {\n                this.onModelChange(value);\n            }\n        }\n        this.onModelTouched();\n    }\n    writeValue(value) {\n        this.value = value;\n        this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    get filled() {\n        return this.value != null && this.value.toString().length > 0;\n    }\n    clearTimer() {\n        if (this.timer) {\n            clearInterval(this.timer);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputNumber, deps: [{ token: DOCUMENT }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.Injector }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"18.0.1\", type: InputNumber, selector: \"p-inputNumber\", inputs: { showButtons: [\"showButtons\", \"showButtons\", booleanAttribute], format: [\"format\", \"format\", booleanAttribute], buttonLayout: \"buttonLayout\", inputId: \"inputId\", styleClass: \"styleClass\", style: \"style\", placeholder: \"placeholder\", size: [\"size\", \"size\", numberAttribute], maxlength: [\"maxlength\", \"maxlength\", numberAttribute], tabindex: [\"tabindex\", \"tabindex\", numberAttribute], title: \"title\", ariaLabelledBy: \"ariaLabelledBy\", ariaLabel: \"ariaLabel\", ariaRequired: [\"ariaRequired\", \"ariaRequired\", booleanAttribute], name: \"name\", required: [\"required\", \"required\", booleanAttribute], autocomplete: \"autocomplete\", min: [\"min\", \"min\", numberAttribute], max: [\"max\", \"max\", numberAttribute], incrementButtonClass: \"incrementButtonClass\", decrementButtonClass: \"decrementButtonClass\", incrementButtonIcon: \"incrementButtonIcon\", decrementButtonIcon: \"decrementButtonIcon\", readonly: [\"readonly\", \"readonly\", booleanAttribute], step: [\"step\", \"step\", numberAttribute], allowEmpty: [\"allowEmpty\", \"allowEmpty\", booleanAttribute], locale: \"locale\", localeMatcher: \"localeMatcher\", mode: \"mode\", currency: \"currency\", currencyDisplay: \"currencyDisplay\", useGrouping: [\"useGrouping\", \"useGrouping\", booleanAttribute], variant: \"variant\", minFractionDigits: [\"minFractionDigits\", \"minFractionDigits\", (value) => numberAttribute(value, null)], maxFractionDigits: [\"maxFractionDigits\", \"maxFractionDigits\", (value) => numberAttribute(value, null)], prefix: \"prefix\", suffix: \"suffix\", inputStyle: \"inputStyle\", inputStyleClass: \"inputStyleClass\", showClear: [\"showClear\", \"showClear\", booleanAttribute], autofocus: [\"autofocus\", \"autofocus\", booleanAttribute], disabled: \"disabled\" }, outputs: { onInput: \"onInput\", onFocus: \"onFocus\", onBlur: \"onBlur\", onKeyDown: \"onKeyDown\", onClear: \"onClear\" }, host: { properties: { \"class.p-inputwrapper-filled\": \"filled\", \"class.p-inputwrapper-focus\": \"focused\", \"class.p-inputnumber-clearable\": \"showClear && buttonLayout != \\\"vertical\\\"\" }, classAttribute: \"p-element p-inputwrapper\" }, providers: [INPUTNUMBER_VALUE_ACCESSOR], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"input\", first: true, predicate: [\"input\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <span\n            [ngClass]=\"{\n                'p-inputnumber p-component': true,\n                'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal',\n                'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'inputnumber'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                pInputText\n                #input\n                [attr.id]=\"inputId\"\n                role=\"spinbutton\"\n                [ngClass]=\"'p-inputnumber-input'\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [value]=\"formattedValue()\"\n                [attr.variant]=\"variant\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.title]=\"title\"\n                [attr.size]=\"size\"\n                [attr.name]=\"name\"\n                [attr.autocomplete]=\"autocomplete\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.aria-required]=\"ariaRequired\"\n                [attr.required]=\"required\"\n                [attr.min]=\"min\"\n                [attr.max]=\"max\"\n                inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                (keypress)=\"onInputKeyPress($event)\"\n                (paste)=\"onPaste($event)\"\n                (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                [attr.data-pc-section]=\"'input'\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n                [class.p-variant-filled]=\"variant === 'filled' || config.inputStyle() === 'filled'\"\n            />\n            <ng-container *ngIf=\"buttonLayout != 'vertical' && showClear && value\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [ngClass]=\"'p-inputnumber-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span *ngIf=\"clearIconTemplate\" (click)=\"clear()\" class=\"p-inputnumber-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\" [attr.data-pc-section]=\"'buttonGroup'\">\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"incrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"0\"\n                    (mousedown)=\"onUpButtonMouseDown($event)\"\n                    (mouseup)=\"onUpButtonMouseUp()\"\n                    (mouseleave)=\"onUpButtonMouseLeave()\"\n                    (keydown)=\"onUpButtonKeyDown($event)\"\n                    (keyup)=\"onUpButtonKeyUp()\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.data-pc-section]=\"'incrementbutton'\"\n                >\n                    <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!incrementButtonIcon\">\n                        <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"decrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"0\"\n                    [attr.aria-hidden]=\"true\"\n                    (mousedown)=\"onDownButtonMouseDown($event)\"\n                    (mouseup)=\"onDownButtonMouseUp()\"\n                    (mouseleave)=\"onDownButtonMouseLeave()\"\n                    (keydown)=\"onDownButtonKeyDown($event)\"\n                    (keyup)=\"onDownButtonKeyUp()\"\n                    [attr.data-pc-section]=\"decrementbutton\"\n                >\n                    <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!decrementButtonIcon\">\n                        <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n            </span>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                [class]=\"incrementButtonClass\"\n                class=\"p-button-icon-only\"\n                [disabled]=\"disabled\"\n                tabindex=\"0\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onUpButtonMouseDown($event)\"\n                (mouseup)=\"onUpButtonMouseUp()\"\n                (mouseleave)=\"onUpButtonMouseLeave()\"\n                (keydown)=\"onUpButtonKeyDown($event)\"\n                (keyup)=\"onUpButtonKeyUp()\"\n                [attr.data-pc-section]=\"'incrementbutton'\"\n            >\n                <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!incrementButtonIcon\">\n                    <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                class=\"p-button-icon-only\"\n                [class]=\"decrementButtonClass\"\n                [disabled]=\"disabled\"\n                tabindex=\"0\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onDownButtonMouseDown($event)\"\n                (mouseup)=\"onDownButtonMouseUp()\"\n                (mouseleave)=\"onDownButtonMouseLeave()\"\n                (keydown)=\"onDownButtonKeyDown($event)\"\n                (keyup)=\"onDownButtonKeyUp()\"\n                [attr.data-pc-section]=\"'decrementbutton'\"\n            >\n                <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!decrementButtonIcon\">\n                    <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n        </span>\n    `, isInline: true, styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i2.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.InputText), selector: \"[pInputText]\", inputs: [\"variant\"] }, { kind: \"directive\", type: i0.forwardRef(() => i4.ButtonDirective), selector: \"[pButton]\", inputs: [\"iconPos\", \"loadingIcon\", \"label\", \"icon\", \"loading\", \"severity\", \"raised\", \"rounded\", \"text\", \"outlined\", \"size\", \"plain\"] }, { kind: \"directive\", type: i0.forwardRef(() => i5.AutoFocus), selector: \"[pAutoFocus]\", inputs: [\"autofocus\"] }, { kind: \"component\", type: i0.forwardRef(() => TimesIcon), selector: \"TimesIcon\" }, { kind: \"component\", type: i0.forwardRef(() => AngleUpIcon), selector: \"AngleUpIcon\" }, { kind: \"component\", type: i0.forwardRef(() => AngleDownIcon), selector: \"AngleDownIcon\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputNumber, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-inputNumber', template: `\n        <span\n            [ngClass]=\"{\n                'p-inputnumber p-component': true,\n                'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal',\n                'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'inputnumber'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                pInputText\n                #input\n                [attr.id]=\"inputId\"\n                role=\"spinbutton\"\n                [ngClass]=\"'p-inputnumber-input'\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [value]=\"formattedValue()\"\n                [attr.variant]=\"variant\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.title]=\"title\"\n                [attr.size]=\"size\"\n                [attr.name]=\"name\"\n                [attr.autocomplete]=\"autocomplete\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.aria-required]=\"ariaRequired\"\n                [attr.required]=\"required\"\n                [attr.min]=\"min\"\n                [attr.max]=\"max\"\n                inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                (keypress)=\"onInputKeyPress($event)\"\n                (paste)=\"onPaste($event)\"\n                (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                [attr.data-pc-section]=\"'input'\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n                [class.p-variant-filled]=\"variant === 'filled' || config.inputStyle() === 'filled'\"\n            />\n            <ng-container *ngIf=\"buttonLayout != 'vertical' && showClear && value\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [ngClass]=\"'p-inputnumber-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span *ngIf=\"clearIconTemplate\" (click)=\"clear()\" class=\"p-inputnumber-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\" [attr.data-pc-section]=\"'buttonGroup'\">\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"incrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"0\"\n                    (mousedown)=\"onUpButtonMouseDown($event)\"\n                    (mouseup)=\"onUpButtonMouseUp()\"\n                    (mouseleave)=\"onUpButtonMouseLeave()\"\n                    (keydown)=\"onUpButtonKeyDown($event)\"\n                    (keyup)=\"onUpButtonKeyUp()\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.data-pc-section]=\"'incrementbutton'\"\n                >\n                    <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!incrementButtonIcon\">\n                        <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"decrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"0\"\n                    [attr.aria-hidden]=\"true\"\n                    (mousedown)=\"onDownButtonMouseDown($event)\"\n                    (mouseup)=\"onDownButtonMouseUp()\"\n                    (mouseleave)=\"onDownButtonMouseLeave()\"\n                    (keydown)=\"onDownButtonKeyDown($event)\"\n                    (keyup)=\"onDownButtonKeyUp()\"\n                    [attr.data-pc-section]=\"decrementbutton\"\n                >\n                    <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!decrementButtonIcon\">\n                        <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n            </span>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                [class]=\"incrementButtonClass\"\n                class=\"p-button-icon-only\"\n                [disabled]=\"disabled\"\n                tabindex=\"0\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onUpButtonMouseDown($event)\"\n                (mouseup)=\"onUpButtonMouseUp()\"\n                (mouseleave)=\"onUpButtonMouseLeave()\"\n                (keydown)=\"onUpButtonKeyDown($event)\"\n                (keyup)=\"onUpButtonKeyUp()\"\n                [attr.data-pc-section]=\"'incrementbutton'\"\n            >\n                <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!incrementButtonIcon\">\n                    <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                class=\"p-button-icon-only\"\n                [class]=\"decrementButtonClass\"\n                [disabled]=\"disabled\"\n                tabindex=\"0\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onDownButtonMouseDown($event)\"\n                (mouseup)=\"onDownButtonMouseUp()\"\n                (mouseleave)=\"onDownButtonMouseLeave()\"\n                (keydown)=\"onDownButtonKeyDown($event)\"\n                (keyup)=\"onDownButtonKeyUp()\"\n                [attr.data-pc-section]=\"'decrementbutton'\"\n            >\n                <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!decrementButtonIcon\">\n                    <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n        </span>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, providers: [INPUTNUMBER_VALUE_ACCESSOR], encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element p-inputwrapper',\n                        '[class.p-inputwrapper-filled]': 'filled',\n                        '[class.p-inputwrapper-focus]': 'focused',\n                        '[class.p-inputnumber-clearable]': 'showClear && buttonLayout != \"vertical\"'\n                    }, styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.Injector }, { type: i1.PrimeNGConfig }], propDecorators: { showButtons: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], format: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], buttonLayout: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], size: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], maxlength: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], tabindex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], title: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaRequired: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autocomplete: [{\n                type: Input\n            }], min: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], max: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], incrementButtonClass: [{\n                type: Input\n            }], decrementButtonClass: [{\n                type: Input\n            }], incrementButtonIcon: [{\n                type: Input\n            }], decrementButtonIcon: [{\n                type: Input\n            }], readonly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], step: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], allowEmpty: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], locale: [{\n                type: Input\n            }], localeMatcher: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], currency: [{\n                type: Input\n            }], currencyDisplay: [{\n                type: Input\n            }], useGrouping: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], variant: [{\n                type: Input\n            }], minFractionDigits: [{\n                type: Input,\n                args: [{ transform: (value) => numberAttribute(value, null) }]\n            }], maxFractionDigits: [{\n                type: Input,\n                args: [{ transform: (value) => numberAttribute(value, null) }]\n            }], prefix: [{\n                type: Input\n            }], suffix: [{\n                type: Input\n            }], inputStyle: [{\n                type: Input\n            }], inputStyleClass: [{\n                type: Input\n            }], showClear: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autofocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input\n            }], onInput: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onKeyDown: [{\n                type: Output\n            }], onClear: [{\n                type: Output\n            }], input: [{\n                type: ViewChild,\n                args: ['input']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass InputNumberModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputNumberModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: InputNumberModule, declarations: [InputNumber], imports: [CommonModule, InputTextModule, ButtonModule, AutoFocusModule, TimesIcon, AngleUpIcon, AngleDownIcon], exports: [InputNumber, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputNumberModule, imports: [CommonModule, InputTextModule, ButtonModule, AutoFocusModule, TimesIcon, AngleUpIcon, AngleDownIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputNumberModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, InputTextModule, ButtonModule, AutoFocusModule, TimesIcon, AngleUpIcon, AngleDownIcon],\n                    exports: [InputNumber, SharedModule],\n                    declarations: [InputNumber]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC/M,SAASC,iBAAiB,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,mBAAmB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;EAAA,iCAAAF,EAAA;EAAA,oCAAAC,EAAA;EAAA,kCAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAA;AAAA;AAAA,SAAAC,gDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAonCyCvC,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAAyC,cAAA,kBAwDsD,CAAC;IAxDzDzC,EAAE,CAAA0C,UAAA,mBAAAC,2EAAA;MAAF3C,EAAE,CAAA4C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAwDOF,MAAA,CAAAG,KAAA,CAAM,CAAC;IAAA,EAAC;IAxDjBhD,EAAE,CAAAiD,YAAA,CAwDsD,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAxDzDrC,EAAE,CAAAkD,UAAA,sCAwDJ,CAAC;IAxDClD,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAC,2DAAAf,EAAA,EAAAC,GAAA;AAAA,SAAAe,6CAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAsD,UAAA,IAAAF,0DAAA,qBA0DzB,CAAC;EAAA;AAAA;AAAA,SAAAG,2CAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmB,GAAA,GA1DsBxD,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAAyC,cAAA,aAyDwC,CAAC;IAzD3CzC,EAAE,CAAA0C,UAAA,mBAAAe,iEAAA;MAAFzD,EAAE,CAAA4C,aAAA,CAAAY,GAAA;MAAA,MAAAX,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAyDtCF,MAAA,CAAAG,KAAA,CAAM,CAAC;IAAA,EAAC;IAzD4BhD,EAAE,CAAAsD,UAAA,IAAAD,4CAAA,gBA0DzB,CAAC;IA1DsBrD,EAAE,CAAAiD,YAAA,CA2DzE,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GA3DsE7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAmD,WAAA;IAAFnD,EAAE,CAAA0D,SAAA,CA0D3B,CAAC;IA1DwB1D,EAAE,CAAAkD,UAAA,qBAAAL,MAAA,CAAAc,iBA0D3B,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1DwBrC,EAAE,CAAA6D,uBAAA,EAuDb,CAAC;IAvDU7D,EAAE,CAAAsD,UAAA,IAAAlB,+CAAA,sBAwDsD,CAAC,IAAAmB,0CAAA,iBACf,CAAC;IAzD3CvD,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0D,SAAA,CAwD5C,CAAC;IAxDyC1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAAc,iBAwD5C,CAAC;IAxDyC3D,EAAE,CAAA0D,SAAA,CAyDlD,CAAC;IAzD+C1D,EAAE,CAAAkD,UAAA,SAAAL,MAAA,CAAAc,iBAyDlD,CAAC;EAAA;AAAA;AAAA,SAAAI,mCAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzD+CrC,EAAE,CAAAgE,SAAA,cA8E4C,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GA9E/C7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAkD,UAAA,YAAAL,MAAA,CAAAoB,mBA8EX,CAAC;IA9EQjE,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAe,yDAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAgE,SAAA,iBAgF2B,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAhF9BrC,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAgB,2DAAA9B,EAAA,EAAAC,GAAA;AAAA,SAAA8B,6CAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAsD,UAAA,IAAAa,0DAAA,qBAiFX,CAAC;EAAA;AAAA;AAAA,SAAAE,2CAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjFQrC,EAAE,CAAA6D,uBAAA,EA+EjC,CAAC;IA/E8B7D,EAAE,CAAAsD,UAAA,IAAAY,wDAAA,wBAgF2B,CAAC,IAAAE,4CAAA,gBACvC,CAAC;IAjFQpE,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0D,SAAA,CAgFxB,CAAC;IAhFqB1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAAyB,2BAgFxB,CAAC;IAhFqBtE,EAAE,CAAA0D,SAAA,CAiFb,CAAC;IAjFU1D,EAAE,CAAAkD,UAAA,qBAAAL,MAAA,CAAAyB,2BAiFb,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjFUrC,EAAE,CAAAgE,SAAA,cAoG4C,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GApG/C7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAkD,UAAA,YAAAL,MAAA,CAAA2B,mBAoGX,CAAC;IApGQxE,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAsB,2DAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAgE,SAAA,mBAsG6B,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAtGhCrC,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAuB,2DAAArC,EAAA,EAAAC,GAAA;AAAA,SAAAqC,6CAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAsD,UAAA,IAAAoB,0DAAA,qBAuGX,CAAC;EAAA;AAAA;AAAA,SAAAE,2CAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvGQrC,EAAE,CAAA6D,uBAAA,EAqGjC,CAAC;IArG8B7D,EAAE,CAAAsD,UAAA,IAAAmB,0DAAA,0BAsG6B,CAAC,IAAAE,4CAAA,gBACzC,CAAC;IAvGQ3E,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0D,SAAA,CAsGtB,CAAC;IAtGmB1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAAgC,2BAsGtB,CAAC;IAtGmB7E,EAAE,CAAA0D,SAAA,CAuGb,CAAC;IAvGU1D,EAAE,CAAAkD,UAAA,qBAAAL,MAAA,CAAAgC,2BAuGb,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0C,GAAA,GAvGU/E,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAAyC,cAAA,cA6D8C,CAAC,gBAgB9H,CAAC;IA7E4EzC,EAAE,CAAA0C,UAAA,uBAAAsC,wDAAAC,MAAA;MAAFjF,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAsE9DF,MAAA,CAAAqC,mBAAA,CAAAD,MAA0B,CAAC;IAAA,EAAC,qBAAAE,sDAAA;MAtEgCnF,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAuEhEF,MAAA,CAAAuC,iBAAA,CAAkB,CAAC;IAAA,EAAC,wBAAAC,yDAAA;MAvE0CrF,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAwE7DF,MAAA,CAAAyC,oBAAA,CAAqB,CAAC;IAAA,EAAC,qBAAAC,sDAAAN,MAAA;MAxEoCjF,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAyEhEF,MAAA,CAAA2C,iBAAA,CAAAP,MAAwB,CAAC;IAAA,EAAC,mBAAAQ,oDAAA;MAzEoCzF,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA0ElEF,MAAA,CAAA6C,eAAA,CAAgB,CAAC;IAAA,EAAC;IA1E8C1F,EAAE,CAAAsD,UAAA,IAAAS,kCAAA,kBA8EqC,CAAC,IAAAM,0CAAA,yBACvE,CAAC;IA/E8BrE,EAAE,CAAAiD,YAAA,CAmFvE,CAAC;IAnFoEjD,EAAE,CAAAyC,cAAA,gBAmG/E,CAAC;IAnG4EzC,EAAE,CAAA0C,UAAA,uBAAAiD,wDAAAV,MAAA;MAAFjF,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA6F9DF,MAAA,CAAA+C,qBAAA,CAAAX,MAA4B,CAAC;IAAA,EAAC,qBAAAY,sDAAA;MA7F8B7F,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA8FhEF,MAAA,CAAAiD,mBAAA,CAAoB,CAAC;IAAA,EAAC,wBAAAC,yDAAA;MA9FwC/F,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA+F7DF,MAAA,CAAAmD,sBAAA,CAAuB,CAAC;IAAA,EAAC,qBAAAC,sDAAAhB,MAAA;MA/FkCjF,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAgGhEF,MAAA,CAAAqD,mBAAA,CAAAjB,MAA0B,CAAC;IAAA,EAAC,mBAAAkB,oDAAA;MAhGkCnG,EAAE,CAAA4C,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAiGlEF,MAAA,CAAAuD,iBAAA,CAAkB,CAAC;IAAA,EAAC;IAjG4CpG,EAAE,CAAAsD,UAAA,IAAAiB,kCAAA,kBAoGqC,CAAC,IAAAK,0CAAA,yBACvE,CAAC;IArG8B5E,EAAE,CAAAiD,YAAA,CAyGvE,CAAC,CACP,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GA1G0E7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAmD,WAAA;IAAFnD,EAAE,CAAA0D,SAAA,CAmE9C,CAAC;IAnE2C1D,EAAE,CAAAqG,UAAA,CAAAxD,MAAA,CAAAyD,oBAmE9C,CAAC;IAnE2CtG,EAAE,CAAAkD,UAAA,YAAFlD,EAAE,CAAAuG,eAAA,KAAArE,GAAA,CAiER,CAAC,aAAAW,MAAA,CAAA2D,QAGhD,CAAC;IApEoDxG,EAAE,CAAAmD,WAAA;IAAFnD,EAAE,CAAA0D,SAAA,CA8E5C,CAAC;IA9EyC1D,EAAE,CAAAkD,UAAA,SAAAL,MAAA,CAAAoB,mBA8E5C,CAAC;IA9EyCjE,EAAE,CAAA0D,SAAA,CA+EnC,CAAC;IA/EgC1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAAoB,mBA+EnC,CAAC;IA/EgCjE,EAAE,CAAA0D,SAAA,CAyF9C,CAAC;IAzF2C1D,EAAE,CAAAqG,UAAA,CAAAxD,MAAA,CAAA4D,oBAyF9C,CAAC;IAzF2CzG,EAAE,CAAAkD,UAAA,YAAFlD,EAAE,CAAAuG,eAAA,KAAApE,GAAA,CAuFN,CAAC,aAAAU,MAAA,CAAA2D,QAGlD,CAAC;IA1FoDxG,EAAE,CAAAmD,WAAA,yCAAAN,MAAA,CAAA6D,eAAA;IAAF1G,EAAE,CAAA0D,SAAA,CAoG5C,CAAC;IApGyC1D,EAAE,CAAAkD,UAAA,SAAAL,MAAA,CAAA2B,mBAoG5C,CAAC;IApGyCxE,EAAE,CAAA0D,SAAA,CAqGnC,CAAC;IArGgC1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAA2B,mBAqGnC,CAAC;EAAA;AAAA;AAAA,SAAAmC,qCAAAtE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArGgCrC,EAAE,CAAAgE,SAAA,cA4HwC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GA5H3C7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAkD,UAAA,YAAAL,MAAA,CAAAoB,mBA4Hf,CAAC;IA5HYjE,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAyD,2DAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAgE,SAAA,iBA8HuB,CAAC;EAAA;EAAA,IAAA3B,EAAA;IA9H1BrC,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAA0D,6DAAAxE,EAAA,EAAAC,GAAA;AAAA,SAAAwE,+CAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAsD,UAAA,IAAAuD,4DAAA,qBA+Hf,CAAC;EAAA;AAAA;AAAA,SAAAE,6CAAA1E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/HYrC,EAAE,CAAA6D,uBAAA,EA6HrC,CAAC;IA7HkC7D,EAAE,CAAAsD,UAAA,IAAAsD,0DAAA,wBA8HuB,CAAC,IAAAE,8CAAA,gBACvC,CAAC;IA/HY9G,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0D,SAAA,CA8H5B,CAAC;IA9HyB1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAAyB,2BA8H5B,CAAC;IA9HyBtE,EAAE,CAAA0D,SAAA,CA+HjB,CAAC;IA/Hc1D,EAAE,CAAAkD,UAAA,qBAAAL,MAAA,CAAAyB,2BA+HjB,CAAC;EAAA;AAAA;AAAA,SAAA0C,8BAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4E,GAAA,GA/HcjH,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAAyC,cAAA,gBA2HnF,CAAC;IA3HgFzC,EAAE,CAAA0C,UAAA,uBAAAwE,0DAAAjC,MAAA;MAAFjF,EAAE,CAAA4C,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAqHlEF,MAAA,CAAAqC,mBAAA,CAAAD,MAA0B,CAAC;IAAA,EAAC,qBAAAkC,wDAAA;MArHoCnH,EAAE,CAAA4C,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAsHpEF,MAAA,CAAAuC,iBAAA,CAAkB,CAAC;IAAA,EAAC,wBAAAgC,2DAAA;MAtH8CpH,EAAE,CAAA4C,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAuHjEF,MAAA,CAAAyC,oBAAA,CAAqB,CAAC;IAAA,EAAC,qBAAA+B,wDAAApC,MAAA;MAvHwCjF,EAAE,CAAA4C,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAwHpEF,MAAA,CAAA2C,iBAAA,CAAAP,MAAwB,CAAC;IAAA,EAAC,mBAAAqC,sDAAA;MAxHwCtH,EAAE,CAAA4C,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAyHtEF,MAAA,CAAA6C,eAAA,CAAgB,CAAC;IAAA,EAAC;IAzHkD1F,EAAE,CAAAsD,UAAA,IAAAqD,oCAAA,kBA4HiC,CAAC,IAAAI,4CAAA,yBACvE,CAAC;IA7HkC/G,EAAE,CAAAiD,YAAA,CAiI3E,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GAjIwE7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAqG,UAAA,CAAAxD,MAAA,CAAAyD,oBAgHlD,CAAC;IAhH+CtG,EAAE,CAAAkD,UAAA,YAAFlD,EAAE,CAAAuG,eAAA,IAAArE,GAAA,CA+GZ,CAAC,aAAAW,MAAA,CAAA2D,QAGhD,CAAC;IAlHwDxG,EAAE,CAAAmD,WAAA;IAAFnD,EAAE,CAAA0D,SAAA,CA4HhD,CAAC;IA5H6C1D,EAAE,CAAAkD,UAAA,SAAAL,MAAA,CAAAoB,mBA4HhD,CAAC;IA5H6CjE,EAAE,CAAA0D,SAAA,CA6HvC,CAAC;IA7HoC1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAAoB,mBA6HvC,CAAC;EAAA;AAAA;AAAA,SAAAsD,qCAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7HoCrC,EAAE,CAAAgE,SAAA,cAmJwC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GAnJ3C7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAkD,UAAA,YAAAL,MAAA,CAAA2B,mBAmJf,CAAC;IAnJYxE,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAqE,6DAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAgE,SAAA,mBAqJyB,CAAC;EAAA;EAAA,IAAA3B,EAAA;IArJ5BrC,EAAE,CAAAmD,WAAA;EAAA;AAAA;AAAA,SAAAsE,6DAAApF,EAAA,EAAAC,GAAA;AAAA,SAAAoF,+CAAArF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAsD,UAAA,IAAAmE,4DAAA,qBAsJf,CAAC;EAAA;AAAA;AAAA,SAAAE,6CAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtJYrC,EAAE,CAAA6D,uBAAA,EAoJrC,CAAC;IApJkC7D,EAAE,CAAAsD,UAAA,IAAAkE,4DAAA,0BAqJyB,CAAC,IAAAE,8CAAA,gBACzC,CAAC;IAtJY1H,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0D,SAAA,CAqJ1B,CAAC;IArJuB1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAAgC,2BAqJ1B,CAAC;IArJuB7E,EAAE,CAAA0D,SAAA,CAsJjB,CAAC;IAtJc1D,EAAE,CAAAkD,UAAA,qBAAAL,MAAA,CAAAgC,2BAsJjB,CAAC;EAAA;AAAA;AAAA,SAAA+C,8BAAAvF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwF,GAAA,GAtJc7H,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAAyC,cAAA,gBAkJnF,CAAC;IAlJgFzC,EAAE,CAAA0C,UAAA,uBAAAoF,0DAAA7C,MAAA;MAAFjF,EAAE,CAAA4C,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA4IlEF,MAAA,CAAA+C,qBAAA,CAAAX,MAA4B,CAAC;IAAA,EAAC,qBAAA8C,wDAAA;MA5IkC/H,EAAE,CAAA4C,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA6IpEF,MAAA,CAAAiD,mBAAA,CAAoB,CAAC;IAAA,EAAC,wBAAAkC,2DAAA;MA7I4ChI,EAAE,CAAA4C,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA8IjEF,MAAA,CAAAmD,sBAAA,CAAuB,CAAC;IAAA,EAAC,qBAAAiC,wDAAAhD,MAAA;MA9IsCjF,EAAE,CAAA4C,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA+IpEF,MAAA,CAAAqD,mBAAA,CAAAjB,MAA0B,CAAC;IAAA,EAAC,mBAAAiD,sDAAA;MA/IsClI,EAAE,CAAA4C,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAgJtEF,MAAA,CAAAuD,iBAAA,CAAkB,CAAC;IAAA,EAAC;IAhJgDpG,EAAE,CAAAsD,UAAA,IAAAiE,oCAAA,kBAmJiC,CAAC,IAAAI,4CAAA,yBACvE,CAAC;IApJkC3H,EAAE,CAAAiD,YAAA,CAwJ3E,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GAxJwE7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAqG,UAAA,CAAAxD,MAAA,CAAA4D,oBAwIlD,CAAC;IAxI+CzG,EAAE,CAAAkD,UAAA,YAAFlD,EAAE,CAAAuG,eAAA,IAAApE,GAAA,CAsIV,CAAC,aAAAU,MAAA,CAAA2D,QAGlD,CAAC;IAzIwDxG,EAAE,CAAAmD,WAAA;IAAFnD,EAAE,CAAA0D,SAAA,CAmJhD,CAAC;IAnJ6C1D,EAAE,CAAAkD,UAAA,SAAAL,MAAA,CAAA2B,mBAmJhD,CAAC;IAnJ6CxE,EAAE,CAAA0D,SAAA,CAoJvC,CAAC;IApJoC1D,EAAE,CAAAkD,UAAA,UAAAL,MAAA,CAAA2B,mBAoJvC,CAAC;EAAA;AAAA;AAtwCzD,MAAM2D,0BAA0B,GAAG;EAC/BC,OAAO,EAAEtH,iBAAiB;EAC1BuH,WAAW,EAAEpI,UAAU,CAAC,MAAMqI,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EACdE,QAAQ;EACRC,EAAE;EACFC,EAAE;EACFC,QAAQ;EACRC,MAAM;EACN;AACJ;AACA;AACA;EACIC,WAAW,GAAG,KAAK;EACnB;AACJ;AACA;AACA;EACIC,MAAM,GAAG,IAAI;EACb;AACJ;AACA;AACA;EACIC,YAAY,GAAG,SAAS;EACxB;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,GAAG;EACH;AACJ;AACA;AACA;EACIC,GAAG;EACH;AACJ;AACA;AACA;EACIzD,oBAAoB;EACpB;AACJ;AACA;AACA;EACIG,oBAAoB;EACpB;AACJ;AACA;AACA;EACIxC,mBAAmB;EACnB;AACJ;AACA;AACA;EACIO,mBAAmB;EACnB;AACJ;AACA;AACA;EACIwF,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIC,IAAI,GAAG,CAAC;EACR;AACJ;AACA;AACA;EACIC,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,IAAI,GAAG,SAAS;EAChB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,eAAe;EACf;AACJ;AACA;AACA;EACIC,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACIC,OAAO,GAAG,UAAU;EACpB;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,eAAe;EACf;AACJ;AACA;AACA;EACIC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACI,IAAIzE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC0E,SAAS;EACzB;EACA,IAAI1E,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAIA,QAAQ,EACR,IAAI,CAAC2E,OAAO,GAAG,KAAK;IACxB,IAAI,CAACD,SAAS,GAAG1E,QAAQ;IACzB,IAAI,IAAI,CAAC4E,KAAK,EACV,IAAI,CAACC,UAAU,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAO,GAAG,IAAIpL,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACIqL,OAAO,GAAG,IAAIrL,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACIsL,MAAM,GAAG,IAAItL,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACIuL,SAAS,GAAG,IAAIvL,YAAY,CAAC,CAAC;EAC9B;AACJ;AACA;AACA;EACIwL,OAAO,GAAG,IAAIxL,YAAY,CAAC,CAAC;EAC5ByL,KAAK;EACLC,SAAS;EACTjI,iBAAiB;EACjBW,2BAA2B;EAC3BO,2BAA2B;EAC3BgH,KAAK;EACLC,aAAa,GAAGA,CAAA,KAAM,CAAE,CAAC;EACzBC,cAAc,GAAGA,CAAA,KAAM,CAAE,CAAC;EAC1BZ,OAAO;EACPa,WAAW;EACXC,SAAS,GAAG,EAAE;EACdC,UAAU,GAAG,EAAE;EACfC,UAAU,GAAG,EAAE;EACfC,aAAa;EACbhB,KAAK;EACLiB,SAAS;EACTC,QAAQ;EACRC,YAAY;EACZC,QAAQ;EACRC,YAAY;EACZC,MAAM;EACNC,UAAU;EACVC,SAAS;EACTC,OAAO;EACPC,OAAO;EACPC,MAAM;EACN7B,SAAS;EACT8B,SAAS,GAAG,IAAI;EAChBC,WAAWA,CAACzE,QAAQ,EAAEC,EAAE,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IAC5C,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAsE,WAAWA,CAACC,YAAY,EAAE;IACtB,MAAMC,KAAK,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAC7J,IAAIA,KAAK,CAACC,IAAI,CAAEC,CAAC,IAAK,CAAC,CAACH,YAAY,CAACG,CAAC,CAAC,CAAC,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC5B,SAAS,CAAC6B,OAAO,CAAEC,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,WAAW;UACZ,IAAI,CAAChK,iBAAiB,GAAG+J,IAAI,CAACE,QAAQ;UACtC;QACJ,KAAK,qBAAqB;UACtB,IAAI,CAACtJ,2BAA2B,GAAGoJ,IAAI,CAACE,QAAQ;UAChD;QACJ,KAAK,qBAAqB;UACtB,IAAI,CAAC/I,2BAA2B,GAAG6I,IAAI,CAACE,QAAQ;UAChD;MACR;IACJ,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACb,SAAS,GAAG,IAAI,CAACrE,QAAQ,CAACmF,GAAG,CAAC/M,SAAS,EAAE,IAAI,EAAE;MAAEgN,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAAChC,WAAW,GAAG,IAAI;EAC3B;EACAiC,UAAUA,CAAA,EAAG;IACT,OAAO;MACH7D,aAAa,EAAE,IAAI,CAACA,aAAa;MACjClB,KAAK,EAAE,IAAI,CAACmB,IAAI;MAChBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B0D,qBAAqB,EAAE,IAAI,CAACxD,iBAAiB,IAAIyD,SAAS;MAC1DC,qBAAqB,EAAE,IAAI,CAACzD,iBAAiB,IAAIwD;IACrD,CAAC;EACL;EACAH,eAAeA,CAAA,EAAG;IACd,IAAI,CAACzB,YAAY,GAAG,IAAI8B,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE,IAAI,CAAC8D,UAAU,CAAC,CAAC,CAAC;IACzE,MAAMM,QAAQ,GAAG,CAAC,GAAG,IAAIF,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE;MAAEK,WAAW,EAAE;IAAM,CAAC,CAAC,CAAC1B,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC0F,OAAO,CAAC,CAAC;IAC7G,MAAMC,KAAK,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAACI,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,CAACvC,QAAQ,GAAG,IAAIwC,MAAM,CAAC,IAAIP,QAAQ,CAACQ,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;IACzD,IAAI,CAACrC,MAAM,GAAG,IAAI,CAACsC,qBAAqB,CAAC,CAAC;IAC1C,IAAI,CAACrC,UAAU,GAAG,IAAI,CAACsC,sBAAsB,CAAC,CAAC;IAC/C,IAAI,CAACrC,SAAS,GAAG,IAAI,CAACsC,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAAC1C,QAAQ,GAAG,IAAI,CAAC2C,oBAAoB,CAAC,CAAC;IAC3C,IAAI,CAAC1C,YAAY,GAAG,IAAI,CAAC2C,cAAc,CAAC,CAAC;IACzC,IAAI,CAACtC,OAAO,GAAG,IAAI,CAACuC,mBAAmB,CAAC,CAAC;IACzC,IAAI,CAACxC,OAAO,GAAG,IAAI,CAACyC,mBAAmB,CAAC,CAAC;IACzC,IAAI,CAACvC,MAAM,GAAI6B,CAAC,IAAKH,KAAK,CAACX,GAAG,CAACc,CAAC,CAAC;EACrC;EACArB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACvB,WAAW,EAAE;MAClB,IAAI,CAACgC,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAuB,YAAYA,CAACC,IAAI,EAAE;IACf,OAAOA,IAAI,CAACC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;EAC3D;EACAN,oBAAoBA,CAAA,EAAG;IACnB,MAAMO,WAAW,GAAG,IAAI,CAACN,cAAc,CAAC,CAAC;IACzC,OAAO,IAAIN,MAAM,CAAC,IAAIY,WAAW,GAAG,EAAE,GAAG,CAAC;EAC9C;EACAN,cAAcA,CAAA,EAAG;IACb,MAAMO,SAAS,GAAG,IAAItB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE;MAAE,GAAG,IAAI,CAAC8D,UAAU,CAAC,CAAC;MAAEzD,WAAW,EAAE;IAAM,CAAC,CAAC;IAClG,OAAOmF,SAAS,CACX7G,MAAM,CAAC,GAAG,CAAC,CACX2G,OAAO,CAAC,IAAI,CAAC7C,SAAS,EAAE,EAAE,CAAC,CAC3BgD,IAAI,CAAC,CAAC,CACNH,OAAO,CAAC,IAAI,CAACnD,QAAQ,EAAE,EAAE,CAAC;EACnC;EACA0C,qBAAqBA,CAAA,EAAG;IACpB,MAAMW,SAAS,GAAG,IAAItB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE;MAAEK,WAAW,EAAE;IAAK,CAAC,CAAC;IAC3E,IAAI,CAACyB,SAAS,GAAG0D,SAAS,CAAC7G,MAAM,CAAC,OAAO,CAAC,CAAC8G,IAAI,CAAC,CAAC,CAACH,OAAO,CAAC,IAAI,CAACnD,QAAQ,EAAE,EAAE,CAAC,CAACuD,MAAM,CAAC,CAAC,CAAC;IACtF,OAAO,IAAIf,MAAM,CAAC,IAAI,IAAI,CAAC7C,SAAS,GAAG,EAAE,GAAG,CAAC;EACjD;EACAgD,sBAAsBA,CAAA,EAAG;IACrB,MAAMU,SAAS,GAAG,IAAItB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE;MAAEK,WAAW,EAAE;IAAM,CAAC,CAAC;IAC5E,OAAO,IAAIsE,MAAM,CAAC,IAAIa,SAAS,CAAC7G,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC8G,IAAI,CAAC,CAAC,CAACH,OAAO,CAAC,IAAI,CAACnD,QAAQ,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACzF;EACA4C,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC5E,QAAQ,EAAE;MACf,MAAMqF,SAAS,GAAG,IAAItB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE;QAAEjB,KAAK,EAAE,UAAU;QAAEoB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEC,eAAe,EAAE,IAAI,CAACA,eAAe;QAAE2D,qBAAqB,EAAE,CAAC;QAAEE,qBAAqB,EAAE;MAAE,CAAC,CAAC;MAC/L,OAAO,IAAIU,MAAM,CAAC,IAAIa,SAAS,CAAC7G,MAAM,CAAC,CAAC,CAAC,CAAC2G,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,CAACnD,QAAQ,EAAE,EAAE,CAAC,CAACmD,OAAO,CAAC,IAAI,CAAC/C,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;IAC7H;IACA,OAAO,IAAIoC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC;EAChC;EACAQ,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC1E,MAAM,EAAE;MACb,IAAI,CAACsB,UAAU,GAAG,IAAI,CAACtB,MAAM;IACjC,CAAC,MACI;MACD,MAAM+E,SAAS,GAAG,IAAItB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE;QAAEjB,KAAK,EAAE,IAAI,CAACmB,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEC,eAAe,EAAE,IAAI,CAACA;MAAgB,CAAC,CAAC;MAC1I,IAAI,CAAC2B,UAAU,GAAGyD,SAAS,CAAC7G,MAAM,CAAC,CAAC,CAAC,CAACgH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,IAAIhB,MAAM,CAAC,GAAG,IAAI,CAACS,YAAY,CAAC,IAAI,CAACrD,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC;EACzE;EACAmD,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACxE,MAAM,EAAE;MACb,IAAI,CAACsB,UAAU,GAAG,IAAI,CAACtB,MAAM;IACjC,CAAC,MACI;MACD,MAAM8E,SAAS,GAAG,IAAItB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE;QAAEjB,KAAK,EAAE,IAAI,CAACmB,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEC,eAAe,EAAE,IAAI,CAACA,eAAe;QAAE2D,qBAAqB,EAAE,CAAC;QAAEE,qBAAqB,EAAE;MAAE,CAAC,CAAC;MAC9L,IAAI,CAACjC,UAAU,GAAGwD,SAAS,CAAC7G,MAAM,CAAC,CAAC,CAAC,CAACgH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,IAAIhB,MAAM,CAAC,GAAG,IAAI,CAACS,YAAY,CAAC,IAAI,CAACpD,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC;EACzE;EACA,IAAI4D,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC/C,SAAS,EAAEgD,OAAO,EAAEC,QAAQ,KAAK,MAAM;EACvD;EACAC,WAAWA,CAACrE,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,IAAIA,KAAK,KAAK,GAAG,EAAE;QACf;QACA,OAAOA,KAAK;MAChB;MACA,IAAI,IAAI,CAAC/C,MAAM,EAAE;QACb,IAAI6G,SAAS,GAAG,IAAItB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnE,MAAM,EAAE,IAAI,CAAC8D,UAAU,CAAC,CAAC,CAAC;QACrE,IAAIkC,cAAc,GAAGR,SAAS,CAAC7G,MAAM,CAAC+C,KAAK,CAAC;QAC5C,IAAI,IAAI,CAACjB,MAAM,IAAIiB,KAAK,IAAI,IAAI,CAACjB,MAAM,EAAE;UACrCuF,cAAc,GAAG,IAAI,CAACvF,MAAM,GAAGuF,cAAc;QACjD;QACA,IAAI,IAAI,CAACtF,MAAM,IAAIgB,KAAK,IAAI,IAAI,CAAChB,MAAM,EAAE;UACrCsF,cAAc,GAAGA,cAAc,GAAG,IAAI,CAACtF,MAAM;QACjD;QACA,OAAOsF,cAAc;MACzB;MACA,OAAOtE,KAAK,CAACuE,QAAQ,CAAC,CAAC;IAC3B;IACA,OAAO,EAAE;EACb;EACAC,UAAUA,CAACb,IAAI,EAAE;IACb,MAAMc,WAAW,GAAG,IAAIxB,MAAM,CAAC,IAAI,CAAChC,OAAO,EAAE,EAAE,CAAC;IAChD,MAAMyD,WAAW,GAAG,IAAIzB,MAAM,CAAC,IAAI,CAACjC,OAAO,EAAE,EAAE,CAAC;IAChD,MAAM2D,aAAa,GAAG,IAAI1B,MAAM,CAAC,IAAI,CAAClC,SAAS,EAAE,EAAE,CAAC;IACpD,IAAI6D,YAAY,GAAGjB,IAAI,CAClBC,OAAO,CAACa,WAAW,EAAE,EAAE,CAAC,CACxBb,OAAO,CAACc,WAAW,EAAE,EAAE,CAAC,CACxBX,IAAI,CAAC,CAAC,CACNH,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAACe,aAAa,EAAE,EAAE,CAAC,CAC1Bf,OAAO,CAAC,IAAI,CAAC/C,MAAM,EAAE,EAAE,CAAC,CACxB+C,OAAO,CAAC,IAAI,CAAC9C,UAAU,EAAE,GAAG,CAAC,CAC7B8C,OAAO,CAAC,IAAI,CAACjD,QAAQ,EAAE,GAAG,CAAC,CAC3BiD,OAAO,CAAC,IAAI,CAACnD,QAAQ,EAAE,IAAI,CAACS,MAAM,CAAC;IACxC,IAAI0D,YAAY,EAAE;MACd,IAAIA,YAAY,KAAK,GAAG;QACpB;QACA,OAAOA,YAAY;MACvB,IAAIC,WAAW,GAAG,CAACD,YAAY;MAC/B,OAAOE,KAAK,CAACD,WAAW,CAAC,GAAG,IAAI,GAAGA,WAAW;IAClD;IACA,OAAO,IAAI;EACf;EACAE,MAAMA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,GAAG,EAAE;IACzB,IAAI,IAAI,CAAC/G,QAAQ,EAAE;MACf;IACJ;IACA,IAAI6E,CAAC,GAAGiC,QAAQ,IAAI,GAAG;IACvB,IAAI,CAACzF,UAAU,CAAC,CAAC;IACjB,IAAI,CAACD,KAAK,GAAG4F,UAAU,CAAC,MAAM;MAC1B,IAAI,CAACJ,MAAM,CAACC,KAAK,EAAE,EAAE,EAAEE,GAAG,CAAC;IAC/B,CAAC,EAAElC,CAAC,CAAC;IACL,IAAI,CAACoC,IAAI,CAACJ,KAAK,EAAEE,GAAG,CAAC;EACzB;EACAE,IAAIA,CAACJ,KAAK,EAAEE,GAAG,EAAE;IACb,IAAI9G,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG8G,GAAG;IAC1B,IAAIG,YAAY,GAAG,IAAI,CAACb,UAAU,CAAC,IAAI,CAAC1E,KAAK,EAAEwF,aAAa,CAACtF,KAAK,CAAC,IAAI,CAAC;IACxE,IAAIuF,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACH,YAAY,GAAGjH,IAAI,CAAC;IACtD,IAAI,IAAI,CAACZ,SAAS,IAAI,IAAI,CAACA,SAAS,GAAG,IAAI,CAAC6G,WAAW,CAACkB,QAAQ,CAAC,CAACE,MAAM,EAAE;MACtE;IACJ;IACA,IAAI,CAACC,WAAW,CAACH,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACI,WAAW,CAACX,KAAK,EAAEO,QAAQ,CAAC;IACjC,IAAI,CAACK,aAAa,CAACZ,KAAK,EAAEK,YAAY,EAAEE,QAAQ,CAAC;EACrD;EACApO,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC6I,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,aAAa,CAAC,IAAI,CAACD,KAAK,CAAC;IAC9B,IAAI,CAACH,OAAO,CAACgG,IAAI,CAAC,CAAC;EACvB;EACAxM,mBAAmBA,CAAC2L,KAAK,EAAE;IACvB,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAACtG,UAAU,CAAC,CAAC;MACjB;IACJ;IACA,IAAI,CAAC,IAAI,CAAC7E,QAAQ,EAAE;MAChB,IAAI,CAACmF,KAAK,EAAEwF,aAAa,CAACS,KAAK,CAAC,CAAC;MACjC,IAAI,CAAChB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;MAC3BA,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAzM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACoB,QAAQ,EAAE;MAChB,IAAI,CAAC6E,UAAU,CAAC,CAAC;IACrB;EACJ;EACA/F,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACkB,QAAQ,EAAE;MAChB,IAAI,CAAC6E,UAAU,CAAC,CAAC;IACrB;EACJ;EACA7F,iBAAiBA,CAACqL,KAAK,EAAE;IACrB,IAAIA,KAAK,CAACiB,OAAO,KAAK,EAAE,IAAIjB,KAAK,CAACiB,OAAO,KAAK,EAAE,EAAE;MAC9C,IAAI,CAAClB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/B;EACJ;EACAnL,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACc,QAAQ,EAAE;MAChB,IAAI,CAAC6E,UAAU,CAAC,CAAC;IACrB;EACJ;EACAzF,qBAAqBA,CAACiL,KAAK,EAAE;IACzB,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAACtG,UAAU,CAAC,CAAC;MACjB;IACJ;IACA,IAAI,CAAC,IAAI,CAAC7E,QAAQ,EAAE;MAChB,IAAI,CAACmF,KAAK,EAAEwF,aAAa,CAACS,KAAK,CAAC,CAAC;MACjC,IAAI,CAAChB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAC5BA,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA/L,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACU,QAAQ,EAAE;MAChB,IAAI,CAAC6E,UAAU,CAAC,CAAC;IACrB;EACJ;EACArF,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACQ,QAAQ,EAAE;MAChB,IAAI,CAAC6E,UAAU,CAAC,CAAC;IACrB;EACJ;EACAjF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACI,QAAQ,EAAE;MAChB,IAAI,CAAC6E,UAAU,CAAC,CAAC;IACrB;EACJ;EACAnF,mBAAmBA,CAAC2K,KAAK,EAAE;IACvB,IAAIA,KAAK,CAACiB,OAAO,KAAK,EAAE,IAAIjB,KAAK,CAACiB,OAAO,KAAK,EAAE,EAAE;MAC9C,IAAI,CAAClB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChC;EACJ;EACAkB,WAAWA,CAAClB,KAAK,EAAE;IACf,IAAI,IAAI,CAAC7G,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,IAAI,CAACoC,aAAa,EAAE;MACpByE,KAAK,CAACmB,MAAM,CAACnG,KAAK,GAAG,IAAI,CAACQ,SAAS;IACvC;IACA,IAAI,CAACD,aAAa,GAAG,KAAK;EAC9B;EACA6F,cAAcA,CAACpB,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC7G,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,CAACqC,SAAS,GAAGwE,KAAK,CAACmB,MAAM,CAACnG,KAAK;IACnC,IAAIgF,KAAK,CAACqB,QAAQ,IAAIrB,KAAK,CAACsB,MAAM,IAAItB,KAAK,CAACuB,GAAG,KAAK,MAAM,EAAE;MACxD,IAAI,CAAChG,aAAa,GAAG,IAAI;MACzB;IACJ;IACA,IAAIiG,cAAc,GAAGxB,KAAK,CAACmB,MAAM,CAACK,cAAc;IAChD,IAAIC,YAAY,GAAGzB,KAAK,CAACmB,MAAM,CAACM,YAAY;IAC5C,IAAIC,UAAU,GAAG1B,KAAK,CAACmB,MAAM,CAACnG,KAAK;IACnC,IAAI2G,WAAW,GAAG,IAAI;IACtB,IAAI3B,KAAK,CAACsB,MAAM,EAAE;MACdtB,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;IACA,QAAQhB,KAAK,CAACuB,GAAG;MACb,KAAK,SAAS;QACV,IAAI,CAACnB,IAAI,CAACJ,KAAK,EAAE,CAAC,CAAC;QACnBA,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,WAAW;QACZ,IAAI,CAACZ,IAAI,CAACJ,KAAK,EAAE,CAAC,CAAC,CAAC;QACpBA,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,WAAW;QACZ,KAAK,IAAIpD,KAAK,GAAG4D,cAAc,EAAE5D,KAAK,IAAI8D,UAAU,CAACjB,MAAM,EAAE7C,KAAK,EAAE,EAAE;UAClE,MAAMgE,iBAAiB,GAAGhE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;UACrD,IAAI,IAAI,CAACiE,aAAa,CAACH,UAAU,CAAC1C,MAAM,CAAC4C,iBAAiB,CAAC,CAAC,EAAE;YAC1D,IAAI,CAAC9G,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAAClE,KAAK,EAAEA,KAAK,CAAC;YACxD;UACJ;QACJ;QACA;MACJ,KAAK,YAAY;QACb,KAAK,IAAIA,KAAK,GAAG6D,YAAY,EAAE7D,KAAK,IAAI,CAAC,EAAEA,KAAK,EAAE,EAAE;UAChD,IAAI,IAAI,CAACiE,aAAa,CAACH,UAAU,CAAC1C,MAAM,CAACpB,KAAK,CAAC,CAAC,EAAE;YAC9C,IAAI,CAAC9C,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAAClE,KAAK,EAAEA,KAAK,CAAC;YACxD;UACJ;QACJ;QACA;MACJ,KAAK,KAAK;MACV,KAAK,OAAO;QACR+D,WAAW,GAAG,IAAI,CAACnB,aAAa,CAAC,IAAI,CAAChB,UAAU,CAAC,IAAI,CAAC1E,KAAK,CAACwF,aAAa,CAACtF,KAAK,CAAC,CAAC;QACjF,IAAI,CAACF,KAAK,CAACwF,aAAa,CAACtF,KAAK,GAAG,IAAI,CAACqE,WAAW,CAACsC,WAAW,CAAC;QAC9D,IAAI,CAAC7G,KAAK,CAACwF,aAAa,CAACyB,YAAY,CAAC,eAAe,EAAEJ,WAAW,CAAC;QACnE,IAAI,CAAChB,WAAW,CAACX,KAAK,EAAE2B,WAAW,CAAC;QACpC;MACJ,KAAK,WAAW;QAAE;UACd3B,KAAK,CAACgB,cAAc,CAAC,CAAC;UACtB,IAAIQ,cAAc,KAAKC,YAAY,EAAE;YACjC,IAAKD,cAAc,IAAI,CAAC,IAAI,IAAI,CAACzH,MAAM,IAAMyH,cAAc,IAAIE,UAAU,CAACjB,MAAM,IAAI,IAAI,CAACzG,MAAO,EAAE;cAC9F;YACJ;YACA,MAAMgI,UAAU,GAAGN,UAAU,CAAC1C,MAAM,CAACwC,cAAc,GAAG,CAAC,CAAC;YACxD,MAAM;cAAES,gBAAgB;cAAEC;YAA8B,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAACT,UAAU,CAAC;YAClG,IAAI,IAAI,CAACG,aAAa,CAACG,UAAU,CAAC,EAAE;cAChC,MAAMI,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACX,UAAU,CAAC;cACvD,IAAI,IAAI,CAAC7F,MAAM,CAACyG,IAAI,CAACN,UAAU,CAAC,EAAE;gBAC9B,IAAI,CAACnG,MAAM,CAAC0G,SAAS,GAAG,CAAC;gBACzBZ,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,GAAG,CAAC,CAAC,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,GAAG,CAAC,CAAC;cAChG,CAAC,MACI,IAAI,IAAI,CAAC7F,QAAQ,CAAC2G,IAAI,CAACN,UAAU,CAAC,EAAE;gBACrC,IAAI,CAACrG,QAAQ,CAAC4G,SAAS,GAAG,CAAC;gBAC3B,IAAIH,aAAa,EAAE;kBACf,IAAI,CAACtH,KAAK,EAAEwF,aAAa,CAACwB,iBAAiB,CAACN,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAG,CAAC,CAAC;gBACvF,CAAC,MACI;kBACDG,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,GAAG,CAAC,CAAC,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,CAAC;gBAC5F;cACJ,CAAC,MACI,IAAIS,gBAAgB,GAAG,CAAC,IAAIT,cAAc,GAAGS,gBAAgB,EAAE;gBAChE,MAAMQ,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC7I,iBAAiB,IAAI,CAAC,IAAIuI,aAAa,GAAG,EAAE,GAAG,GAAG;gBACrGT,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,GAAG,CAAC,CAAC,GAAGiB,YAAY,GAAGf,UAAU,CAACc,KAAK,CAAChB,cAAc,CAAC;cAC3G,CAAC,MACI,IAAIU,6BAA6B,KAAK,CAAC,EAAE;gBAC1CP,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,CAAC;gBAC9FG,WAAW,GAAG,IAAI,CAACnC,UAAU,CAACmC,WAAW,CAAC,GAAG,CAAC,GAAGA,WAAW,GAAG,EAAE;cACrE,CAAC,MACI;gBACDA,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,GAAG,CAAC,CAAC,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,CAAC;cAC5F;YACJ,CAAC,MACI,IAAI,IAAI,CAAChI,IAAI,KAAK,UAAU,IAAIwI,UAAU,CAACW,MAAM,CAAC,IAAI,CAAC5G,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;cAC1E4F,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,CAAC;YACrC;YACA,IAAI,CAACI,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAE,IAAI,EAAE,eAAe,CAAC;UAC/D,CAAC,MACI;YACDA,WAAW,GAAG,IAAI,CAACkB,WAAW,CAACnB,UAAU,EAAEF,cAAc,EAAEC,YAAY,CAAC;YACxE,IAAI,CAACmB,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC;UAC9D;UACA;QACJ;MACA,KAAK,QAAQ;QACT3B,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtB,IAAIQ,cAAc,KAAKC,YAAY,EAAE;UACjC,IAAKD,cAAc,IAAI,CAAC,IAAI,IAAI,CAACzH,MAAM,IAAMyH,cAAc,IAAIE,UAAU,CAACjB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACzG,MAAO,EAAE;YAClG;UACJ;UACA,MAAMgI,UAAU,GAAGN,UAAU,CAAC1C,MAAM,CAACwC,cAAc,CAAC;UACpD,MAAM;YAAES,gBAAgB;YAAEC;UAA8B,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAACT,UAAU,CAAC;UAClG,IAAI,IAAI,CAACG,aAAa,CAACG,UAAU,CAAC,EAAE;YAChC,MAAMI,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACX,UAAU,CAAC;YACvD,IAAI,IAAI,CAAC7F,MAAM,CAACyG,IAAI,CAACN,UAAU,CAAC,EAAE;cAC9B,IAAI,CAACnG,MAAM,CAAC0G,SAAS,GAAG,CAAC;cACzBZ,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,CAAC,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,GAAG,CAAC,CAAC;YAC5F,CAAC,MACI,IAAI,IAAI,CAAC7F,QAAQ,CAAC2G,IAAI,CAACN,UAAU,CAAC,EAAE;cACrC,IAAI,CAACrG,QAAQ,CAAC4G,SAAS,GAAG,CAAC;cAC3B,IAAIH,aAAa,EAAE;gBACf,IAAI,CAACtH,KAAK,EAAEwF,aAAa,CAACwB,iBAAiB,CAACN,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAG,CAAC,CAAC;cACvF,CAAC,MACI;gBACDG,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,CAAC,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,GAAG,CAAC,CAAC;cAC5F;YACJ,CAAC,MACI,IAAIS,gBAAgB,GAAG,CAAC,IAAIT,cAAc,GAAGS,gBAAgB,EAAE;cAChE,MAAMQ,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC7I,iBAAiB,IAAI,CAAC,IAAIuI,aAAa,GAAG,EAAE,GAAG,GAAG;cACrGT,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,CAAC,GAAGiB,YAAY,GAAGf,UAAU,CAACc,KAAK,CAAChB,cAAc,GAAG,CAAC,CAAC;YAC3G,CAAC,MACI,IAAIU,6BAA6B,KAAK,CAAC,EAAE;cAC1CP,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,CAAC,GAAG,GAAG,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,GAAG,CAAC,CAAC;cAC9FG,WAAW,GAAG,IAAI,CAACnC,UAAU,CAACmC,WAAW,CAAC,GAAG,CAAC,GAAGA,WAAW,GAAG,EAAE;YACrE,CAAC,MACI;cACDA,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,CAAC,GAAGE,UAAU,CAACc,KAAK,CAAChB,cAAc,GAAG,CAAC,CAAC;YAC5F;UACJ;UACA,IAAI,CAACoB,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAE,IAAI,EAAE,oBAAoB,CAAC;QACpE,CAAC,MACI;UACDA,WAAW,GAAG,IAAI,CAACkB,WAAW,CAACnB,UAAU,EAAEF,cAAc,EAAEC,YAAY,CAAC;UACxE,IAAI,CAACmB,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC;QAC9D;QACA;MACJ,KAAK,MAAM;QACP,IAAI,IAAI,CAAC1I,GAAG,EAAE;UACV,IAAI,CAAC0H,WAAW,CAACX,KAAK,EAAE,IAAI,CAAC/G,GAAG,CAAC;UACjC+G,KAAK,CAACgB,cAAc,CAAC,CAAC;QAC1B;QACA;MACJ,KAAK,KAAK;QACN,IAAI,IAAI,CAAC9H,GAAG,EAAE;UACV,IAAI,CAACyH,WAAW,CAACX,KAAK,EAAE,IAAI,CAAC9G,GAAG,CAAC;UACjC8G,KAAK,CAACgB,cAAc,CAAC,CAAC;QAC1B;QACA;MACJ;QACI;IACR;IACA,IAAI,CAACpG,SAAS,CAACiG,IAAI,CAACb,KAAK,CAAC;EAC9B;EACA8C,eAAeA,CAAC9C,KAAK,EAAE;IACnB,IAAI,IAAI,CAAC7G,QAAQ,EAAE;MACf;IACJ;IACA,IAAI4J,IAAI,GAAG/C,KAAK,CAACgD,KAAK,IAAIhD,KAAK,CAACiB,OAAO;IACvC,IAAIgC,IAAI,GAAGC,MAAM,CAACC,YAAY,CAACJ,IAAI,CAAC;IACpC,IAAIK,aAAa,GAAG,IAAI,CAACA,aAAa,CAACH,IAAI,CAAC;IAC5C,MAAMI,WAAW,GAAG,IAAI,CAACA,WAAW,CAACJ,IAAI,CAAC;IAC1C,IAAIF,IAAI,IAAI,EAAE,EAAE;MACZ/C,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;IACA,IAAI,CAACoC,aAAa,IAAIpD,KAAK,CAAC+C,IAAI,KAAK,eAAe,EAAE;MAClDK,aAAa,GAAG,IAAI;MACpBH,IAAI,GAAG,IAAI,CAACrH,YAAY;MACxBmH,IAAI,GAAGE,IAAI,CAACK,UAAU,CAAC,CAAC,CAAC;IAC7B;IACA,MAAM;MAAEtI,KAAK;MAAEwG,cAAc;MAAEC;IAAa,CAAC,GAAG,IAAI,CAAC3G,KAAK,CAACwF,aAAa;IACxE,MAAMC,QAAQ,GAAG,IAAI,CAACf,UAAU,CAACxE,KAAK,GAAGiI,IAAI,CAAC;IAC9C,MAAMtB,WAAW,GAAGpB,QAAQ,IAAI,IAAI,GAAGA,QAAQ,CAAChB,QAAQ,CAAC,CAAC,GAAG,EAAE;IAC/D,MAAMgE,aAAa,GAAGvI,KAAK,CAACwI,SAAS,CAAChC,cAAc,EAAEC,YAAY,CAAC;IACnE,MAAMgC,mBAAmB,GAAG,IAAI,CAACjE,UAAU,CAAC+D,aAAa,CAAC;IAC1D,MAAMG,gBAAgB,GAAGD,mBAAmB,IAAI,IAAI,GAAGA,mBAAmB,CAAClE,QAAQ,CAAC,CAAC,GAAG,EAAE;IAC1F,IAAIiC,cAAc,KAAKC,YAAY,IAAIiC,gBAAgB,CAACjD,MAAM,GAAG,CAAC,EAAE;MAChE,IAAI,CAACkD,MAAM,CAAC3D,KAAK,EAAEiD,IAAI,EAAE;QAAEG,aAAa;QAAEC;MAAY,CAAC,CAAC;MACxD;IACJ;IACA,IAAI,IAAI,CAAC7K,SAAS,IAAImJ,WAAW,CAAClB,MAAM,GAAG,IAAI,CAACjI,SAAS,EAAE;MACvD;IACJ;IACA,IAAK,EAAE,IAAIuK,IAAI,IAAIA,IAAI,IAAI,EAAE,IAAKM,WAAW,IAAID,aAAa,EAAE;MAC5D,IAAI,CAACO,MAAM,CAAC3D,KAAK,EAAEiD,IAAI,EAAE;QAAEG,aAAa;QAAEC;MAAY,CAAC,CAAC;IAC5D;EACJ;EACAO,OAAOA,CAAC5D,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAACrK,QAAQ,IAAI,CAAC,IAAI,CAACwD,QAAQ,EAAE;MAClC6G,KAAK,CAACgB,cAAc,CAAC,CAAC;MACtB,IAAI6C,IAAI,GAAG,CAAC7D,KAAK,CAAC8D,aAAa,IAAI,IAAI,CAACnM,QAAQ,CAACoM,WAAW,CAAC,eAAe,CAAC,EAAEC,OAAO,CAAC,MAAM,CAAC;MAC9F,IAAIH,IAAI,EAAE;QACN,IAAI,IAAI,CAACrL,SAAS,EAAE;UAChBqL,IAAI,GAAGA,IAAI,CAACtE,QAAQ,CAAC,CAAC,CAACiE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAChL,SAAS,CAAC;QACvD;QACA,IAAIyL,YAAY,GAAG,IAAI,CAACzE,UAAU,CAACqE,IAAI,CAAC;QACxC,IAAII,YAAY,IAAI,IAAI,EAAE;UACtB,IAAI,CAACN,MAAM,CAAC3D,KAAK,EAAEiE,YAAY,CAAC1E,QAAQ,CAAC,CAAC,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA2E,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACjL,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAC;EAC3C;EACAoK,WAAWA,CAACJ,IAAI,EAAE;IACd,IAAI,IAAI,CAACnH,UAAU,CAACwG,IAAI,CAACW,IAAI,CAAC,IAAIA,IAAI,KAAK,GAAG,EAAE;MAC5C,IAAI,CAACnH,UAAU,CAACyG,SAAS,GAAG,CAAC;MAC7B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAa,aAAaA,CAACH,IAAI,EAAE;IAChB,IAAI,IAAI,CAACtH,QAAQ,CAAC2G,IAAI,CAACW,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACtH,QAAQ,CAAC4G,SAAS,GAAG,CAAC;MAC3B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAG,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAClJ,IAAI,KAAK,SAAS;EAClC;EACA2I,qBAAqBA,CAACgC,GAAG,EAAE;IACvB,IAAIlC,gBAAgB,GAAGkC,GAAG,CAACxB,MAAM,CAAC,IAAI,CAAChH,QAAQ,CAAC;IAChD,IAAI,CAACA,QAAQ,CAAC4G,SAAS,GAAG,CAAC;IAC3B,MAAM6B,WAAW,GAAGD,GAAG,CAClBvF,OAAO,CAAC,IAAI,CAAC5C,OAAO,EAAE,EAAE,CAAC,CACzB+C,IAAI,CAAC,CAAC,CACNH,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,IAAI,CAAC7C,SAAS,EAAE,EAAE,CAAC;IAChC,MAAMmG,6BAA6B,GAAGkC,WAAW,CAACzB,MAAM,CAAC,IAAI,CAAChH,QAAQ,CAAC;IACvE,IAAI,CAACA,QAAQ,CAAC4G,SAAS,GAAG,CAAC;IAC3B,OAAO;MAAEN,gBAAgB;MAAEC;IAA8B,CAAC;EAC9D;EACAmC,cAAcA,CAACF,GAAG,EAAE;IAChB,MAAMlC,gBAAgB,GAAGkC,GAAG,CAACxB,MAAM,CAAC,IAAI,CAAChH,QAAQ,CAAC;IAClD,IAAI,CAACA,QAAQ,CAAC4G,SAAS,GAAG,CAAC;IAC3B,MAAM+B,cAAc,GAAGH,GAAG,CAACxB,MAAM,CAAC,IAAI,CAAC7G,UAAU,CAAC;IAClD,IAAI,CAACA,UAAU,CAACyG,SAAS,GAAG,CAAC;IAC7B,MAAMgC,eAAe,GAAGJ,GAAG,CAACxB,MAAM,CAAC,IAAI,CAAC1G,OAAO,CAAC;IAChD,IAAI,CAACA,OAAO,CAACsG,SAAS,GAAG,CAAC;IAC1B,MAAMiC,iBAAiB,GAAGL,GAAG,CAACxB,MAAM,CAAC,IAAI,CAAC5G,SAAS,CAAC;IACpD,IAAI,CAACA,SAAS,CAACwG,SAAS,GAAG,CAAC;IAC5B,OAAO;MAAEN,gBAAgB;MAAEqC,cAAc;MAAEC,eAAe;MAAEC;IAAkB,CAAC;EACnF;EACAb,MAAMA,CAAC3D,KAAK,EAAErB,IAAI,EAAE8F,IAAI,GAAG;IAAErB,aAAa,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAM,CAAC,EAAE;IACrE,MAAMqB,oBAAoB,GAAG/F,IAAI,CAACgE,MAAM,CAAC,IAAI,CAAC7G,UAAU,CAAC;IACzD,IAAI,CAACA,UAAU,CAACyG,SAAS,GAAG,CAAC;IAC7B,IAAI,CAAC,IAAI,CAAC2B,cAAc,CAAC,CAAC,IAAIQ,oBAAoB,KAAK,CAAC,CAAC,EAAE;MACvD;IACJ;IACA,IAAIlD,cAAc,GAAG,IAAI,CAAC1G,KAAK,EAAEwF,aAAa,CAACkB,cAAc;IAC7D,IAAIC,YAAY,GAAG,IAAI,CAAC3G,KAAK,EAAEwF,aAAa,CAACmB,YAAY;IACzD,IAAIC,UAAU,GAAG,IAAI,CAAC5G,KAAK,EAAEwF,aAAa,CAACtF,KAAK,CAAC+D,IAAI,CAAC,CAAC;IACvD,MAAM;MAAEkD,gBAAgB;MAAEqC,cAAc;MAAEC,eAAe;MAAEC;IAAkB,CAAC,GAAG,IAAI,CAACH,cAAc,CAAC3C,UAAU,CAAC;IAChH,IAAIC,WAAW;IACf,IAAI8C,IAAI,CAACpB,WAAW,EAAE;MAClB,IAAI7B,cAAc,KAAK,CAAC,EAAE;QACtBG,WAAW,GAAGD,UAAU;QACxB,IAAI4C,cAAc,KAAK,CAAC,CAAC,IAAI7C,YAAY,KAAK,CAAC,EAAE;UAC7CE,WAAW,GAAG,IAAI,CAACgD,UAAU,CAACjD,UAAU,EAAE/C,IAAI,EAAE,CAAC,EAAE8C,YAAY,CAAC;QACpE;QACA,IAAI,CAACmB,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAEhD,IAAI,EAAE,QAAQ,CAAC;MACxD;IACJ,CAAC,MACI,IAAI8F,IAAI,CAACrB,aAAa,EAAE;MACzB,IAAInB,gBAAgB,GAAG,CAAC,IAAIT,cAAc,KAAKS,gBAAgB,EAAE;QAC7D,IAAI,CAACW,WAAW,CAAC5C,KAAK,EAAE0B,UAAU,EAAE/C,IAAI,EAAE,QAAQ,CAAC;MACvD,CAAC,MACI,IAAIsD,gBAAgB,GAAGT,cAAc,IAAIS,gBAAgB,GAAGR,YAAY,EAAE;QAC3EE,WAAW,GAAG,IAAI,CAACgD,UAAU,CAACjD,UAAU,EAAE/C,IAAI,EAAE6C,cAAc,EAAEC,YAAY,CAAC;QAC7E,IAAI,CAACmB,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAEhD,IAAI,EAAE,QAAQ,CAAC;MACxD,CAAC,MACI,IAAIsD,gBAAgB,KAAK,CAAC,CAAC,IAAI,IAAI,CAACnI,iBAAiB,EAAE;QACxD6H,WAAW,GAAG,IAAI,CAACgD,UAAU,CAACjD,UAAU,EAAE/C,IAAI,EAAE6C,cAAc,EAAEC,YAAY,CAAC;QAC7E,IAAI,CAACmB,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAEhD,IAAI,EAAE,QAAQ,CAAC;MACxD;IACJ,CAAC,MACI;MACD,MAAM7E,iBAAiB,GAAG,IAAI,CAAC4B,YAAY,CAACkJ,eAAe,CAAC,CAAC,CAACrH,qBAAqB;MACnF,MAAMsH,SAAS,GAAGrD,cAAc,KAAKC,YAAY,GAAG,cAAc,GAAG,QAAQ;MAC7E,IAAIQ,gBAAgB,GAAG,CAAC,IAAIT,cAAc,GAAGS,gBAAgB,EAAE;QAC3D,IAAIT,cAAc,GAAG7C,IAAI,CAAC8B,MAAM,IAAIwB,gBAAgB,GAAG,CAAC,CAAC,IAAInI,iBAAiB,EAAE;UAC5E,MAAMgL,SAAS,GAAGN,iBAAiB,IAAIhD,cAAc,GAAGgD,iBAAiB,GAAG,CAAC,GAAGD,eAAe,IAAI/C,cAAc,GAAG+C,eAAe,GAAG7C,UAAU,CAACjB,MAAM;UACvJkB,WAAW,GAAGD,UAAU,CAACc,KAAK,CAAC,CAAC,EAAEhB,cAAc,CAAC,GAAG7C,IAAI,GAAG+C,UAAU,CAACc,KAAK,CAAChB,cAAc,GAAG7C,IAAI,CAAC8B,MAAM,EAAEqE,SAAS,CAAC,GAAGpD,UAAU,CAACc,KAAK,CAACsC,SAAS,CAAC;UAClJ,IAAI,CAAClC,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAEhD,IAAI,EAAEkG,SAAS,CAAC;QACzD;MACJ,CAAC,MACI;QACDlD,WAAW,GAAG,IAAI,CAACgD,UAAU,CAACjD,UAAU,EAAE/C,IAAI,EAAE6C,cAAc,EAAEC,YAAY,CAAC;QAC7E,IAAI,CAACmB,WAAW,CAAC5C,KAAK,EAAE2B,WAAW,EAAEhD,IAAI,EAAEkG,SAAS,CAAC;MACzD;IACJ;EACJ;EACAF,UAAUA,CAAC3J,KAAK,EAAE2D,IAAI,EAAEoG,KAAK,EAAEC,GAAG,EAAE;IAChC,IAAIC,SAAS,GAAGtG,IAAI,KAAK,GAAG,GAAGA,IAAI,GAAGA,IAAI,CAACM,KAAK,CAAC,GAAG,CAAC;IACrD,IAAIgG,SAAS,CAACxE,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMwB,gBAAgB,GAAGjH,KAAK,CAACwH,KAAK,CAACuC,KAAK,EAAEC,GAAG,CAAC,CAACrC,MAAM,CAAC,IAAI,CAAChH,QAAQ,CAAC;MACtE,IAAI,CAACA,QAAQ,CAAC4G,SAAS,GAAG,CAAC;MAC3B,OAAON,gBAAgB,GAAG,CAAC,GAAGjH,KAAK,CAACwH,KAAK,CAAC,CAAC,EAAEuC,KAAK,CAAC,GAAG,IAAI,CAAC1F,WAAW,CAACV,IAAI,CAAC,GAAG3D,KAAK,CAACwH,KAAK,CAACwC,GAAG,CAAC,GAAGhK,KAAK,IAAI,IAAI,CAACqE,WAAW,CAACV,IAAI,CAAC;IACrI,CAAC,MACI,IAAIqG,GAAG,GAAGD,KAAK,KAAK/J,KAAK,CAACyF,MAAM,EAAE;MACnC,OAAO,IAAI,CAACpB,WAAW,CAACV,IAAI,CAAC;IACjC,CAAC,MACI,IAAIoG,KAAK,KAAK,CAAC,EAAE;MAClB,OAAOpG,IAAI,GAAG3D,KAAK,CAACwH,KAAK,CAACwC,GAAG,CAAC;IAClC,CAAC,MACI,IAAIA,GAAG,KAAKhK,KAAK,CAACyF,MAAM,EAAE;MAC3B,OAAOzF,KAAK,CAACwH,KAAK,CAAC,CAAC,EAAEuC,KAAK,CAAC,GAAGpG,IAAI;IACvC,CAAC,MACI;MACD,OAAO3D,KAAK,CAACwH,KAAK,CAAC,CAAC,EAAEuC,KAAK,CAAC,GAAGpG,IAAI,GAAG3D,KAAK,CAACwH,KAAK,CAACwC,GAAG,CAAC;IAC1D;EACJ;EACAnC,WAAWA,CAAC7H,KAAK,EAAE+J,KAAK,EAAEC,GAAG,EAAE;IAC3B,IAAIrD,WAAW;IACf,IAAIqD,GAAG,GAAGD,KAAK,KAAK/J,KAAK,CAACyF,MAAM,EAC5BkB,WAAW,GAAG,EAAE,CAAC,KAChB,IAAIoD,KAAK,KAAK,CAAC,EAChBpD,WAAW,GAAG3G,KAAK,CAACwH,KAAK,CAACwC,GAAG,CAAC,CAAC,KAC9B,IAAIA,GAAG,KAAKhK,KAAK,CAACyF,MAAM,EACzBkB,WAAW,GAAG3G,KAAK,CAACwH,KAAK,CAAC,CAAC,EAAEuC,KAAK,CAAC,CAAC,KAEpCpD,WAAW,GAAG3G,KAAK,CAACwH,KAAK,CAAC,CAAC,EAAEuC,KAAK,CAAC,GAAG/J,KAAK,CAACwH,KAAK,CAACwC,GAAG,CAAC;IAC1D,OAAOrD,WAAW;EACtB;EACAuD,UAAUA,CAAA,EAAG;IACT,IAAI1D,cAAc,GAAG,IAAI,CAAC1G,KAAK,EAAEwF,aAAa,CAACkB,cAAc;IAC7D,IAAIC,YAAY,GAAG,IAAI,CAAC3G,KAAK,EAAEwF,aAAa,CAACmB,YAAY;IACzD,IAAIC,UAAU,GAAG,IAAI,CAAC5G,KAAK,EAAEwF,aAAa,CAACtF,KAAK;IAChD,IAAImK,WAAW,GAAGzD,UAAU,CAACjB,MAAM;IACnC,IAAI7C,KAAK,GAAG,IAAI;IAChB;IACA,IAAIwH,YAAY,GAAG,CAAC,IAAI,CAAC/J,UAAU,IAAI,EAAE,EAAEoF,MAAM;IACjDiB,UAAU,GAAGA,UAAU,CAAC9C,OAAO,CAAC,IAAI,CAAC5C,OAAO,EAAE,EAAE,CAAC;IACjD;IACA;IACA,IAAIwF,cAAc,KAAKC,YAAY,IAAID,cAAc,KAAK,CAAC,IAAIC,YAAY,GAAG2D,YAAY,EAAE;MACxF5D,cAAc,IAAI4D,YAAY;IAClC;IACA,IAAInC,IAAI,GAAGvB,UAAU,CAAC1C,MAAM,CAACwC,cAAc,CAAC;IAC5C,IAAI,IAAI,CAACK,aAAa,CAACoB,IAAI,CAAC,EAAE;MAC1B,OAAOzB,cAAc,GAAG4D,YAAY;IACxC;IACA;IACA,IAAIpH,CAAC,GAAGwD,cAAc,GAAG,CAAC;IAC1B,OAAOxD,CAAC,IAAI,CAAC,EAAE;MACXiF,IAAI,GAAGvB,UAAU,CAAC1C,MAAM,CAAChB,CAAC,CAAC;MAC3B,IAAI,IAAI,CAAC6D,aAAa,CAACoB,IAAI,CAAC,EAAE;QAC1BrF,KAAK,GAAGI,CAAC,GAAGoH,YAAY;QACxB;MACJ,CAAC,MACI;QACDpH,CAAC,EAAE;MACP;IACJ;IACA,IAAIJ,KAAK,KAAK,IAAI,EAAE;MAChB,IAAI,CAAC9C,KAAK,EAAEwF,aAAa,CAACwB,iBAAiB,CAAClE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;IACrE,CAAC,MACI;MACDI,CAAC,GAAGwD,cAAc;MAClB,OAAOxD,CAAC,GAAGmH,WAAW,EAAE;QACpBlC,IAAI,GAAGvB,UAAU,CAAC1C,MAAM,CAAChB,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC6D,aAAa,CAACoB,IAAI,CAAC,EAAE;UAC1BrF,KAAK,GAAGI,CAAC,GAAGoH,YAAY;UACxB;QACJ,CAAC,MACI;UACDpH,CAAC,EAAE;QACP;MACJ;MACA,IAAIJ,KAAK,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC9C,KAAK,EAAEwF,aAAa,CAACwB,iBAAiB,CAAClE,KAAK,EAAEA,KAAK,CAAC;MAC7D;IACJ;IACA,OAAOA,KAAK,IAAI,CAAC;EACrB;EACAyH,YAAYA,CAAA,EAAG;IACX,MAAMhF,YAAY,GAAG,IAAI,CAACvF,KAAK,EAAEwF,aAAa,CAACtF,KAAK;IACpD,IAAI,CAAC,IAAI,CAAC7B,QAAQ,IAAIkH,YAAY,KAAK3P,UAAU,CAAC4U,YAAY,CAAC,CAAC,EAAE;MAC9D,IAAI,CAACJ,UAAU,CAAC,CAAC;IACrB;EACJ;EACArD,aAAaA,CAACoB,IAAI,EAAE;IAChB,IAAIA,IAAI,CAACxC,MAAM,KAAK,CAAC,KAAK,IAAI,CAAChF,QAAQ,CAAC6G,IAAI,CAACW,IAAI,CAAC,IAAI,IAAI,CAACtH,QAAQ,CAAC2G,IAAI,CAACW,IAAI,CAAC,IAAI,IAAI,CAACpH,MAAM,CAACyG,IAAI,CAACW,IAAI,CAAC,IAAI,IAAI,CAACnH,UAAU,CAACwG,IAAI,CAACW,IAAI,CAAC,CAAC,EAAE;MACrI,IAAI,CAACsC,UAAU,CAAC,CAAC;MACjB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAA,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC9J,QAAQ,CAAC8G,SAAS,GAAG,CAAC;IAC3B,IAAI,CAAC5G,QAAQ,CAAC4G,SAAS,GAAG,CAAC;IAC3B,IAAI,CAAC1G,MAAM,CAAC0G,SAAS,GAAG,CAAC;IACzB,IAAI,CAACzG,UAAU,CAACyG,SAAS,GAAG,CAAC;EACjC;EACAK,WAAWA,CAAC5C,KAAK,EAAEwF,QAAQ,EAAEC,gBAAgB,EAAEZ,SAAS,EAAE;IACtD,IAAIxE,YAAY,GAAG,IAAI,CAACvF,KAAK,EAAEwF,aAAa,CAACtF,KAAK;IAClD,IAAIuF,QAAQ,GAAG,IAAI;IACnB,IAAIiF,QAAQ,IAAI,IAAI,EAAE;MAClBjF,QAAQ,GAAG,IAAI,CAACf,UAAU,CAACgG,QAAQ,CAAC;MACpCjF,QAAQ,GAAG,CAACA,QAAQ,IAAI,CAAC,IAAI,CAAClH,UAAU,GAAG,CAAC,GAAGkH,QAAQ;MACvD,IAAI,CAACG,WAAW,CAACH,QAAQ,EAAEkF,gBAAgB,EAAEZ,SAAS,EAAEW,QAAQ,CAAC;MACjE,IAAI,CAAC5E,aAAa,CAACZ,KAAK,EAAEK,YAAY,EAAEE,QAAQ,CAAC;IACrD;EACJ;EACAK,aAAaA,CAACZ,KAAK,EAAEK,YAAY,EAAEE,QAAQ,EAAE;IACzC,IAAI,IAAI,CAACmF,cAAc,CAACrF,YAAY,EAAEE,QAAQ,CAAC,EAAE;MAC7C,IAAI,CAACzF,KAAK,CAACwF,aAAa,CAACtF,KAAK,GAAG,IAAI,CAACqE,WAAW,CAACkB,QAAQ,CAAC;MAC3D,IAAI,CAACzF,KAAK,EAAEwF,aAAa,CAACyB,YAAY,CAAC,eAAe,EAAExB,QAAQ,CAAC;MACjE,CAAC,IAAI,CAACrB,kBAAkB,IAAI,IAAI,CAACyB,WAAW,CAACX,KAAK,EAAEO,QAAQ,CAAC;MAC7D,IAAI,CAAC9F,OAAO,CAACoG,IAAI,CAAC;QAAE8E,aAAa,EAAE3F,KAAK;QAAEhF,KAAK,EAAEuF,QAAQ;QAAEjB,cAAc,EAAEe;MAAa,CAAC,CAAC;IAC9F;EACJ;EACAqF,cAAcA,CAACrF,YAAY,EAAEE,QAAQ,EAAE;IACnC,IAAIA,QAAQ,KAAK,IAAI,IAAIF,YAAY,KAAK,IAAI,EAAE;MAC5C,OAAO,IAAI;IACf;IACA,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAClB,IAAIqF,kBAAkB,GAAG,OAAOvF,YAAY,KAAK,QAAQ,GAAG,IAAI,CAACb,UAAU,CAACa,YAAY,CAAC,GAAGA,YAAY;MACxG,OAAOE,QAAQ,KAAKqF,kBAAkB;IAC1C;IACA,OAAO,KAAK;EAChB;EACApF,aAAaA,CAACxF,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,GAAG,IAAIA,KAAK,IAAI,IAAI,EAAE;MAChC,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAC/B,GAAG,IAAI,IAAI,IAAI+B,KAAK,GAAG,IAAI,CAAC/B,GAAG,EAAE;MACtC,OAAO,IAAI,CAACA,GAAG;IACnB;IACA,IAAI,IAAI,CAACC,GAAG,IAAI,IAAI,IAAI8B,KAAK,GAAG,IAAI,CAAC9B,GAAG,EAAE;MACtC,OAAO,IAAI,CAACA,GAAG;IACnB;IACA,OAAO8B,KAAK;EAChB;EACA0F,WAAWA,CAAC1F,KAAK,EAAEyK,gBAAgB,EAAEZ,SAAS,EAAEW,QAAQ,EAAE;IACtDC,gBAAgB,GAAGA,gBAAgB,IAAI,EAAE;IACzC,IAAI/D,UAAU,GAAG,IAAI,CAAC5G,KAAK,EAAEwF,aAAa,CAACtF,KAAK;IAChD,IAAIuF,QAAQ,GAAG,IAAI,CAAClB,WAAW,CAACrE,KAAK,CAAC;IACtC,IAAI6K,aAAa,GAAGnE,UAAU,CAACjB,MAAM;IACrC,IAAIF,QAAQ,KAAKiF,QAAQ,EAAE;MACvBjF,QAAQ,GAAG,IAAI,CAACuF,YAAY,CAACvF,QAAQ,EAAEiF,QAAQ,CAAC;IACpD;IACA,IAAIK,aAAa,KAAK,CAAC,EAAE;MACrB,IAAI,CAAC/K,KAAK,CAACwF,aAAa,CAACtF,KAAK,GAAGuF,QAAQ;MACzC,IAAI,CAACzF,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAChD,MAAMlE,KAAK,GAAG,IAAI,CAACsH,UAAU,CAAC,CAAC;MAC/B,MAAMzD,YAAY,GAAG7D,KAAK,GAAG6H,gBAAgB,CAAChF,MAAM;MACpD,IAAI,CAAC3F,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,EAAEA,YAAY,CAAC;IAC1E,CAAC,MACI;MACD,IAAID,cAAc,GAAG,IAAI,CAAC1G,KAAK,CAACwF,aAAa,CAACkB,cAAc;MAC5D,IAAIC,YAAY,GAAG,IAAI,CAAC3G,KAAK,CAACwF,aAAa,CAACmB,YAAY;MACxD,IAAI,IAAI,CAACjJ,SAAS,IAAI+H,QAAQ,CAACE,MAAM,GAAG,IAAI,CAACjI,SAAS,EAAE;QACpD+H,QAAQ,GAAGA,QAAQ,CAACiC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAChK,SAAS,CAAC;QAC5CgJ,cAAc,GAAGuE,IAAI,CAAC9M,GAAG,CAACuI,cAAc,EAAE,IAAI,CAAChJ,SAAS,CAAC;QACzDiJ,YAAY,GAAGsE,IAAI,CAAC9M,GAAG,CAACwI,YAAY,EAAE,IAAI,CAACjJ,SAAS,CAAC;MACzD;MACA,IAAI,IAAI,CAACA,SAAS,IAAI,IAAI,CAACA,SAAS,GAAG+H,QAAQ,CAACE,MAAM,EAAE;QACpD;MACJ;MACA,IAAI,CAAC3F,KAAK,CAACwF,aAAa,CAACtF,KAAK,GAAGuF,QAAQ;MACzC,IAAIyF,SAAS,GAAGzF,QAAQ,CAACE,MAAM;MAC/B,IAAIoE,SAAS,KAAK,cAAc,EAAE;QAC9B,MAAMoB,UAAU,GAAG,IAAI,CAACzG,UAAU,CAAC,CAACkC,UAAU,IAAI,EAAE,EAAEc,KAAK,CAAC,CAAC,EAAEhB,cAAc,CAAC,CAAC;QAC/E,MAAM0E,aAAa,GAAGD,UAAU,KAAK,IAAI,GAAGA,UAAU,CAAC1G,QAAQ,CAAC,CAAC,GAAG,EAAE;QACtE,MAAM4G,SAAS,GAAGD,aAAa,CAACjH,KAAK,CAAC,EAAE,CAAC,CAACf,IAAI,CAAC,IAAI,IAAI,CAAC9C,SAAS,IAAI,CAAC;QACtE,MAAMgL,MAAM,GAAG,IAAInI,MAAM,CAACkI,SAAS,EAAE,GAAG,CAAC;QACzCC,MAAM,CAAC9D,IAAI,CAAC/B,QAAQ,CAAC;QACrB,MAAM8F,KAAK,GAAGZ,gBAAgB,CAACxG,KAAK,CAAC,EAAE,CAAC,CAACf,IAAI,CAAC,IAAI,IAAI,CAAC9C,SAAS,IAAI,CAAC;QACrE,MAAMkL,MAAM,GAAG,IAAIrI,MAAM,CAACoI,KAAK,EAAE,GAAG,CAAC;QACrCC,MAAM,CAAChE,IAAI,CAAC/B,QAAQ,CAACiC,KAAK,CAAC4D,MAAM,CAAC7D,SAAS,CAAC,CAAC;QAC7Cd,YAAY,GAAG2E,MAAM,CAAC7D,SAAS,GAAG+D,MAAM,CAAC/D,SAAS;QAClD,IAAI,CAACzH,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,EAAEA,YAAY,CAAC;MAC1E,CAAC,MACI,IAAIuE,SAAS,KAAKH,aAAa,EAAE;QAClC,IAAIhB,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,oBAAoB,EAC5D,IAAI,CAAC/J,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAG,CAAC,CAAC,CAAC,KAC9E,IAAIoD,SAAS,KAAK,eAAe,EAClC,IAAI,CAAC/J,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAG,CAAC,CAAC,CAAC,KAC9E,IAAIoD,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,MAAM,EACzD,IAAI,CAAC/J,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,EAAEA,YAAY,CAAC;MAC9E,CAAC,MACI,IAAIoD,SAAS,KAAK,oBAAoB,EAAE;QACzC,IAAI0B,QAAQ,GAAG7E,UAAU,CAAC1C,MAAM,CAACyC,YAAY,GAAG,CAAC,CAAC;QAClD,IAAI+E,QAAQ,GAAG9E,UAAU,CAAC1C,MAAM,CAACyC,YAAY,CAAC;QAC9C,IAAIgF,IAAI,GAAGZ,aAAa,GAAGG,SAAS;QACpC,IAAIU,WAAW,GAAG,IAAI,CAAC7K,MAAM,CAACyG,IAAI,CAACkE,QAAQ,CAAC;QAC5C,IAAIE,WAAW,IAAID,IAAI,KAAK,CAAC,EAAE;UAC3BhF,YAAY,IAAI,CAAC;QACrB,CAAC,MACI,IAAI,CAACiF,WAAW,IAAI,IAAI,CAAC7E,aAAa,CAAC0E,QAAQ,CAAC,EAAE;UACnD9E,YAAY,IAAI,CAAC,CAAC,GAAGgF,IAAI,GAAG,CAAC;QACjC;QACA,IAAI,CAAC5K,MAAM,CAAC0G,SAAS,GAAG,CAAC;QACzB,IAAI,CAACzH,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,EAAEA,YAAY,CAAC;MAC1E,CAAC,MACI,IAAIC,UAAU,KAAK,GAAG,IAAImD,SAAS,KAAK,QAAQ,EAAE;QACnD,IAAI,CAAC/J,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;QAChD,MAAMlE,KAAK,GAAG,IAAI,CAACsH,UAAU,CAAC,CAAC;QAC/B,MAAMzD,YAAY,GAAG7D,KAAK,GAAG6H,gBAAgB,CAAChF,MAAM,GAAG,CAAC;QACxD,IAAI,CAAC3F,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,EAAEA,YAAY,CAAC;MAC1E,CAAC,MACI;QACDA,YAAY,GAAGA,YAAY,IAAIuE,SAAS,GAAGH,aAAa,CAAC;QACzD,IAAI,CAAC/K,KAAK,CAACwF,aAAa,CAACwB,iBAAiB,CAACL,YAAY,EAAEA,YAAY,CAAC;MAC1E;IACJ;IACA,IAAI,CAAC3G,KAAK,CAACwF,aAAa,CAACyB,YAAY,CAAC,eAAe,EAAE/G,KAAK,CAAC;EACjE;EACA8K,YAAYA,CAACa,IAAI,EAAEC,IAAI,EAAE;IACrB,IAAID,IAAI,IAAIC,IAAI,EAAE;MACd,IAAI3E,gBAAgB,GAAG2E,IAAI,CAACjE,MAAM,CAAC,IAAI,CAAChH,QAAQ,CAAC;MACjD,IAAI,CAACA,QAAQ,CAAC4G,SAAS,GAAG,CAAC;MAC3B,IAAI,IAAI,CAACjH,UAAU,EAAE;QACjB,OAAO2G,gBAAgB,KAAK,CAAC,CAAC,GAAG0E,IAAI,GAAGA,IAAI,CAAC/H,OAAO,CAAC,IAAI,CAACtD,UAAU,EAAE,EAAE,CAAC,CAAC2D,KAAK,CAAC,IAAI,CAACtD,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGiL,IAAI,CAAChI,OAAO,CAAC,IAAI,CAACtD,UAAU,EAAE,EAAE,CAAC,CAACkH,KAAK,CAACP,gBAAgB,CAAC,GAAG,IAAI,CAAC3G,UAAU;MACnL,CAAC,MACI;QACD,OAAO2G,gBAAgB,KAAK,CAAC,CAAC,GAAG0E,IAAI,CAAC1H,KAAK,CAAC,IAAI,CAACtD,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGiL,IAAI,CAACpE,KAAK,CAACP,gBAAgB,CAAC,GAAG0E,IAAI;MACvG;IACJ;IACA,OAAOA,IAAI;EACf;EACAtE,gBAAgBA,CAACrH,KAAK,EAAE;IACpB,IAAIA,KAAK,EAAE;MACP,MAAM6L,UAAU,GAAG7L,KAAK,CAACiE,KAAK,CAAC,IAAI,CAACtD,QAAQ,CAAC;MAC7C,IAAIkL,UAAU,CAACpG,MAAM,KAAK,CAAC,EAAE;QACzB,OAAOoG,UAAU,CAAC,CAAC,CAAC,CACfjI,OAAO,CAAC,IAAI,CAAC3C,OAAO,EAAE,EAAE,CAAC,CACzB8C,IAAI,CAAC,CAAC,CACNH,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,IAAI,CAAC7C,SAAS,EAAE,EAAE,CAAC,CAAC0E,MAAM;MAC3C;IACJ;IACA,OAAO,CAAC;EACZ;EACAqG,YAAYA,CAAC9G,KAAK,EAAE;IAChB,IAAI,CAAC1F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACI,OAAO,CAACmG,IAAI,CAACb,KAAK,CAAC;EAC5B;EACA+G,WAAWA,CAAC/G,KAAK,EAAE;IACf,IAAI,CAAC1F,OAAO,GAAG,KAAK;IACpB,MAAM0M,cAAc,GAAG,IAAI,CAACxG,aAAa,CAAC,IAAI,CAAChB,UAAU,CAAC,IAAI,CAAC1E,KAAK,CAACwF,aAAa,CAACtF,KAAK,CAAC,CAAC;IAC1F,MAAMiM,cAAc,GAAGD,cAAc,EAAEzH,QAAQ,CAAC,CAAC;IACjD,IAAI,CAACzE,KAAK,CAACwF,aAAa,CAACtF,KAAK,GAAG,IAAI,CAACqE,WAAW,CAAC4H,cAAc,CAAC;IACjE,IAAI,CAACnM,KAAK,CAACwF,aAAa,CAACyB,YAAY,CAAC,eAAe,EAAEkF,cAAc,CAAC;IACtE,IAAI,CAACtG,WAAW,CAACX,KAAK,EAAEgH,cAAc,CAAC;IACvC,IAAI,CAACrM,MAAM,CAACkG,IAAI,CAACb,KAAK,CAAC;EAC3B;EACAV,cAAcA,CAAA,EAAG;IACb,MAAM6E,GAAG,GAAG,CAAC,IAAI,CAACnJ,KAAK,IAAI,CAAC,IAAI,CAAC3B,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC2B,KAAK;IAC5D,OAAO,IAAI,CAACqE,WAAW,CAAC8E,GAAG,CAAC;EAChC;EACAxD,WAAWA,CAACX,KAAK,EAAEhF,KAAK,EAAE;IACtB,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,EAAE;MACtB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,EAAE,IAAI,CAACkE,kBAAkB,IAAI,IAAI,CAAC5E,OAAO,CAAC,EAAE;QAC5C,IAAI,CAACW,aAAa,CAACD,KAAK,CAAC;MAC7B,CAAC,MACI,IAAI,IAAI,CAACkE,kBAAkB,EAAE;QAC9B,IAAI,CAACjE,aAAa,CAACD,KAAK,CAAC;MAC7B;IACJ;IACA,IAAI,CAACE,cAAc,CAAC,CAAC;EACzB;EACAgM,UAAUA,CAAClM,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACnD,EAAE,CAACsP,YAAY,CAAC,CAAC;EAC1B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACpM,aAAa,GAAGoM,EAAE;EAC3B;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACnM,cAAc,GAAGmM,EAAE;EAC5B;EACAE,gBAAgBA,CAACpD,GAAG,EAAE;IAClB,IAAI,CAACxO,QAAQ,GAAGwO,GAAG;IACnB,IAAI,CAACtM,EAAE,CAACsP,YAAY,CAAC,CAAC;EAC1B;EACA,IAAIK,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACxM,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,CAACuE,QAAQ,CAAC,CAAC,CAACkB,MAAM,GAAG,CAAC;EACjE;EACAjG,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACD,KAAK,EAAE;MACZkN,aAAa,CAAC,IAAI,CAAClN,KAAK,CAAC;IAC7B;EACJ;EACA,OAAOmN,IAAI,YAAAC,oBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFnQ,WAAW,EAArBtI,EAAE,CAAA0Y,iBAAA,CAAqC5Y,QAAQ,GAA/CE,EAAE,CAAA0Y,iBAAA,CAA0D1Y,EAAE,CAAC2Y,UAAU,GAAzE3Y,EAAE,CAAA0Y,iBAAA,CAAoF1Y,EAAE,CAAC4Y,iBAAiB,GAA1G5Y,EAAE,CAAA0Y,iBAAA,CAAqH1Y,EAAE,CAAC6Y,QAAQ,GAAlI7Y,EAAE,CAAA0Y,iBAAA,CAA6I1X,EAAE,CAAC8X,aAAa;EAAA;EACxP,OAAOC,IAAI,kBAD8E/Y,EAAE,CAAAgZ,iBAAA;IAAAC,IAAA,EACJ3Q,WAAW;IAAA4Q,SAAA;IAAAC,cAAA,WAAAC,2BAAA/W,EAAA,EAAAC,GAAA,EAAA+W,QAAA;MAAA,IAAAhX,EAAA;QADTrC,EAAE,CAAAsZ,cAAA,CAAAD,QAAA,EACinEpY,aAAa;MAAA;MAAA,IAAAoB,EAAA;QAAA,IAAAkX,EAAA;QADhoEvZ,EAAE,CAAAwZ,cAAA,CAAAD,EAAA,GAAFvZ,EAAE,CAAAyZ,WAAA,QAAAnX,GAAA,CAAAsJ,SAAA,GAAA2N,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,kBAAAtX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAA4Z,WAAA,CAAA/X,GAAA;MAAA;MAAA,IAAAQ,EAAA;QAAA,IAAAkX,EAAA;QAAFvZ,EAAE,CAAAwZ,cAAA,CAAAD,EAAA,GAAFvZ,EAAE,CAAAyZ,WAAA,QAAAnX,GAAA,CAAAqJ,KAAA,GAAA4N,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,yBAAA5X,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAAka,WAAA,0BAAA5X,GAAA,CAAA+V,MACM,CAAC,yBAAA/V,GAAA,CAAA6I,OAAD,CAAC,4BAAA7I,GAAA,CAAA0I,SAAA,IAAA1I,GAAA,CAAAyG,YAAA,IAAkB,UAAnB,CAAC;MAAA;IAAA;IAAAoR,MAAA;MAAAtR,WAAA,GADT7I,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,gCAC0Fla,gBAAgB;MAAA2I,MAAA,GAD5G9I,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,sBAC0Ila,gBAAgB;MAAA4I,YAAA;MAAAC,OAAA;MAAAC,UAAA;MAAAC,KAAA;MAAAC,WAAA;MAAAC,IAAA,GAD5JpJ,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,kBAC4Sja,eAAe;MAAAiJ,SAAA,GAD7TrJ,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,4BACoWja,eAAe;MAAAkJ,QAAA,GADrXtJ,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,0BACyZja,eAAe;MAAAmJ,KAAA;MAAAC,cAAA;MAAAC,SAAA;MAAAC,YAAA,GAD1a1J,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,kCACoiBla,gBAAgB;MAAAwJ,IAAA;MAAAC,QAAA,GADtjB5J,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,0BACwmBla,gBAAgB;MAAA0J,YAAA;MAAAC,GAAA,GAD1nB9J,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,gBAC6qBja,eAAe;MAAA2J,GAAA,GAD9rB/J,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,gBACmtBja,eAAe;MAAAkG,oBAAA;MAAAG,oBAAA;MAAAxC,mBAAA;MAAAO,mBAAA;MAAAwF,QAAA,GADpuBhK,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,0BAC47Bla,gBAAgB;MAAA8J,IAAA,GAD98BjK,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,kBACs+Bja,eAAe;MAAA8J,UAAA,GADv/BlK,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,8BACiiCla,gBAAgB;MAAAgK,MAAA;MAAAC,aAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,eAAA;MAAAC,WAAA,GADnjCxK,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,gCAC0tCla,gBAAgB;MAAAsK,OAAA;MAAAC,iBAAA,GAD5uC1K,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,4CACg0CxO,KAAK,IAAKzL,eAAe,CAACyL,KAAK,EAAE,IAAI,CAAC;MAAAlB,iBAAA,GADx2C3K,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,4CACw6CxO,KAAK,IAAKzL,eAAe,CAACyL,KAAK,EAAE,IAAI,CAAC;MAAAjB,MAAA;MAAAC,MAAA;MAAAC,UAAA;MAAAC,eAAA;MAAAC,SAAA,GADh9ChL,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,4BACylDla,gBAAgB;MAAA8K,SAAA,GAD3mDjL,EAAE,CAAAoa,YAAA,CAAAC,0BAAA,4BACkpDla,gBAAgB;MAAAqG,QAAA;IAAA;IAAA8T,OAAA;MAAAhP,OAAA;MAAAC,OAAA;MAAAC,MAAA;MAAAC,SAAA;MAAAC,OAAA;IAAA;IAAA6O,QAAA,GADpqDva,EAAE,CAAAwa,kBAAA,CACiiE,CAACrS,0BAA0B,CAAC,GAD/jEnI,EAAE,CAAAya,wBAAA,EAAFza,EAAE,CAAA0a,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAjN,QAAA,WAAAkN,qBAAAzY,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA0Y,GAAA,GAAF/a,EAAE,CAAAwC,gBAAA;QAAFxC,EAAE,CAAAyC,cAAA,aAavF,CAAC,iBAyCI,CAAC;QAtD+EzC,EAAE,CAAA0C,UAAA,mBAAAsY,4CAAA/V,MAAA;UAAFjF,EAAE,CAAA4C,aAAA,CAAAmY,GAAA;UAAA,OAAF/a,EAAE,CAAA+C,WAAA,CA2CtET,GAAA,CAAAyP,WAAA,CAAA9M,MAAkB,CAAC;QAAA,EAAC,qBAAAgW,8CAAAhW,MAAA;UA3CgDjF,EAAE,CAAA4C,aAAA,CAAAmY,GAAA;UAAA,OAAF/a,EAAE,CAAA+C,WAAA,CA4CpET,GAAA,CAAA2P,cAAA,CAAAhN,MAAqB,CAAC;QAAA,EAAC,sBAAAiW,+CAAAjW,MAAA;UA5C2CjF,EAAE,CAAA4C,aAAA,CAAAmY,GAAA;UAAA,OAAF/a,EAAE,CAAA+C,WAAA,CA6CnET,GAAA,CAAAqR,eAAA,CAAA1O,MAAsB,CAAC;QAAA,EAAC,mBAAAkW,4CAAAlW,MAAA;UA7CyCjF,EAAE,CAAA4C,aAAA,CAAAmY,GAAA;UAAA,OAAF/a,EAAE,CAAA+C,WAAA,CA8CtET,GAAA,CAAAmS,OAAA,CAAAxP,MAAc,CAAC;QAAA,EAAC,mBAAAmW,4CAAA;UA9CoDpb,EAAE,CAAA4C,aAAA,CAAAmY,GAAA;UAAA,OAAF/a,EAAE,CAAA+C,WAAA,CA+CtET,GAAA,CAAA4T,YAAA,CAAa,CAAC;QAAA,EAAC,mBAAAmF,4CAAApW,MAAA;UA/CqDjF,EAAE,CAAA4C,aAAA,CAAAmY,GAAA;UAAA,OAAF/a,EAAE,CAAA+C,WAAA,CAgDtET,GAAA,CAAAqV,YAAA,CAAA1S,MAAmB,CAAC;QAAA,EAAC,kBAAAqW,2CAAArW,MAAA;UAhD+CjF,EAAE,CAAA4C,aAAA,CAAAmY,GAAA;UAAA,OAAF/a,EAAE,CAAA+C,WAAA,CAiDvET,GAAA,CAAAsV,WAAA,CAAA3S,MAAkB,CAAC;QAAA,EAAC;QAjDiDjF,EAAE,CAAAiD,YAAA,CAsDlF,CAAC;QAtD+EjD,EAAE,CAAAsD,UAAA,IAAAM,mCAAA,yBAuDb,CAAC,IAAAkB,2BAAA,kBAM0D,CAAC,IAAAkC,6BAAA,mBA8DlI,CAAC,IAAAY,6BAAA,mBAuBD,CAAC;QAlJgF5H,EAAE,CAAAiD,YAAA,CAyJjF,CAAC;MAAA;MAAA,IAAAZ,EAAA;QAzJ8ErC,EAAE,CAAAqG,UAAA,CAAA/D,GAAA,CAAA2G,UAUhE,CAAC;QAV6DjJ,EAAE,CAAAkD,UAAA,YAAFlD,EAAE,CAAAub,eAAA,KAAAzZ,GAAA,EAAAQ,GAAA,CAAAuG,WAAA,IAAAvG,GAAA,CAAAyG,YAAA,gBAAAzG,GAAA,CAAAuG,WAAA,IAAAvG,GAAA,CAAAyG,YAAA,mBAAAzG,GAAA,CAAAuG,WAAA,IAAAvG,GAAA,CAAAyG,YAAA,gBAQlF,CAAC,YAAAzG,GAAA,CAAA4G,KACc,CAAC;QATgElJ,EAAE,CAAAmD,WAAA;QAAFnD,EAAE,CAAA0D,SAAA,CAqBvD,CAAC;QArBoD1D,EAAE,CAAAqG,UAAA,CAAA/D,GAAA,CAAAyI,eAqBvD,CAAC;QArBoD/K,EAAE,CAAAka,WAAA,qBAAA5X,GAAA,CAAAmI,OAAA,iBAAAnI,GAAA,CAAAsG,MAAA,CAAAkC,UAAA,eAqDG,CAAC;QArDN9K,EAAE,CAAAkD,UAAA,iCAmB/C,CAAC,YAAAZ,GAAA,CAAAwI,UACZ,CAAC,UAAAxI,GAAA,CAAA6N,cAAA,EAEG,CAAC,aAAA7N,GAAA,CAAAkE,QAKN,CAAC,aAAAlE,GAAA,CAAA0H,QACD,CAAC,cAAA1H,GAAA,CAAA2I,SAwBC,CAAC;QApDsDjL,EAAE,CAAAmD,WAAA,OAAAb,GAAA,CAAA0G,OAAA,aAAA1G,GAAA,CAAAmI,OAAA,mBAAAnI,GAAA,CAAAwH,GAAA,mBAAAxH,GAAA,CAAAyH,GAAA,mBAAAzH,GAAA,CAAAuJ,KAAA,iBAAAvJ,GAAA,CAAA6G,WAAA,gBAAA7G,GAAA,CAAAmH,SAAA,qBAAAnH,GAAA,CAAAkH,cAAA,WAAAlH,GAAA,CAAAiH,KAAA,UAAAjH,GAAA,CAAA8G,IAAA,UAAA9G,GAAA,CAAAqH,IAAA,kBAAArH,GAAA,CAAAuH,YAAA,eAAAvH,GAAA,CAAA+G,SAAA,cAAA/G,GAAA,CAAAgH,QAAA,mBAAAhH,GAAA,CAAAoH,YAAA,cAAApH,GAAA,CAAAsH,QAAA,SAAAtH,GAAA,CAAAwH,GAAA,SAAAxH,GAAA,CAAAyH,GAAA;QAAF/J,EAAE,CAAA0D,SAAA,EAuDf,CAAC;QAvDY1D,EAAE,CAAAkD,UAAA,SAAAZ,GAAA,CAAAyG,YAAA,kBAAAzG,GAAA,CAAA0I,SAAA,IAAA1I,GAAA,CAAAuJ,KAuDf,CAAC;QAvDY7L,EAAE,CAAA0D,SAAA,CA6DK,CAAC;QA7DR1D,EAAE,CAAAkD,UAAA,SAAAZ,GAAA,CAAAuG,WAAA,IAAAvG,GAAA,CAAAyG,YAAA,cA6DK,CAAC;QA7DR/I,EAAE,CAAA0D,SAAA,CA4GhC,CAAC;QA5G6B1D,EAAE,CAAAkD,UAAA,SAAAZ,GAAA,CAAAuG,WAAA,IAAAvG,GAAA,CAAAyG,YAAA,cA4GhC,CAAC;QA5G6B/I,EAAE,CAAA0D,SAAA,CAmIhC,CAAC;QAnI6B1D,EAAE,CAAAkD,UAAA,SAAAZ,GAAA,CAAAuG,WAAA,IAAAvG,GAAA,CAAAyG,YAAA,cAmIhC,CAAC;MAAA;IAAA;IAAAyS,YAAA,EAAAA,CAAA,MAuBqjE3b,EAAE,CAAC4b,OAAO,EAAyG5b,EAAE,CAAC6b,IAAI,EAAkH7b,EAAE,CAAC8b,gBAAgB,EAAyK9b,EAAE,CAAC+b,OAAO,EAAgGja,EAAE,CAACka,SAAS,EAAmGxa,EAAE,CAACya,eAAe,EAAiN3a,EAAE,CAAC4a,SAAS,EAAqGra,SAAS,EAA2ED,WAAW,EAA6ED,aAAa;IAAAwa,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACnwG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5J6Fnc,EAAE,CAAAoc,iBAAA,CA4JJ9T,WAAW,EAAc,CAAC;IACzG2Q,IAAI,EAAE5Y,SAAS;IACfgc,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAE1O,QAAQ,EAAE;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEsO,eAAe,EAAE5b,uBAAuB,CAACic,MAAM;MAAEC,SAAS,EAAE,CAACrU,0BAA0B,CAAC;MAAE8T,aAAa,EAAE1b,iBAAiB,CAACkc,IAAI;MAAEC,IAAI,EAAE;QACtHC,KAAK,EAAE,0BAA0B;QACjC,+BAA+B,EAAE,QAAQ;QACzC,8BAA8B,EAAE,SAAS;QACzC,iCAAiC,EAAE;MACvC,CAAC;MAAEX,MAAM,EAAE,CAAC,ohEAAohE;IAAE,CAAC;EAC/iE,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/C,IAAI,EAAE2D,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9C5D,IAAI,EAAEzY,MAAM;MACZ6b,IAAI,EAAE,CAACvc,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEmZ,IAAI,EAAEjZ,EAAE,CAAC2Y;EAAW,CAAC,EAAE;IAAEM,IAAI,EAAEjZ,EAAE,CAAC4Y;EAAkB,CAAC,EAAE;IAAEK,IAAI,EAAEjZ,EAAE,CAAC6Y;EAAS,CAAC,EAAE;IAAEI,IAAI,EAAEjY,EAAE,CAAC8X;EAAc,CAAC,CAAC,EAAkB;IAAEjQ,WAAW,EAAE,CAAC;MACnJoQ,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2I,MAAM,EAAE,CAAC;MACTmQ,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4I,YAAY,EAAE,CAAC;MACfkQ,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEuI,OAAO,EAAE,CAAC;MACViQ,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEwI,UAAU,EAAE,CAAC;MACbgQ,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEyI,KAAK,EAAE,CAAC;MACR+P,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE0I,WAAW,EAAE,CAAC;MACd8P,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE2I,IAAI,EAAE,CAAC;MACP6P,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE1c;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEiJ,SAAS,EAAE,CAAC;MACZ4P,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE1c;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEkJ,QAAQ,EAAE,CAAC;MACX2P,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE1c;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEmJ,KAAK,EAAE,CAAC;MACR0P,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE+I,cAAc,EAAE,CAAC;MACjByP,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEgJ,SAAS,EAAE,CAAC;MACZwP,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEiJ,YAAY,EAAE,CAAC;MACfuP,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwJ,IAAI,EAAE,CAAC;MACPsP,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEmJ,QAAQ,EAAE,CAAC;MACXqP,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0J,YAAY,EAAE,CAAC;MACfoP,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEqJ,GAAG,EAAE,CAAC;MACNmP,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE1c;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE2J,GAAG,EAAE,CAAC;MACNkP,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE1c;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEkG,oBAAoB,EAAE,CAAC;MACvB2S,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEgG,oBAAoB,EAAE,CAAC;MACvBwS,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEwD,mBAAmB,EAAE,CAAC;MACtBgV,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE+D,mBAAmB,EAAE,CAAC;MACtByU,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEuJ,QAAQ,EAAE,CAAC;MACXiP,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8J,IAAI,EAAE,CAAC;MACPgP,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE1c;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE8J,UAAU,EAAE,CAAC;MACb+O,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgK,MAAM,EAAE,CAAC;MACT8O,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE2J,aAAa,EAAE,CAAC;MAChB6O,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE4J,IAAI,EAAE,CAAC;MACP4O,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE6J,QAAQ,EAAE,CAAC;MACX2O,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE8J,eAAe,EAAE,CAAC;MAClB0O,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE+J,WAAW,EAAE,CAAC;MACdyO,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsK,OAAO,EAAE,CAAC;MACVwO,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEiK,iBAAiB,EAAE,CAAC;MACpBuO,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAGjR,KAAK,IAAKzL,eAAe,CAACyL,KAAK,EAAE,IAAI;MAAE,CAAC;IACjE,CAAC,CAAC;IAAElB,iBAAiB,EAAE,CAAC;MACpBsO,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAGjR,KAAK,IAAKzL,eAAe,CAACyL,KAAK,EAAE,IAAI;MAAE,CAAC;IACjE,CAAC,CAAC;IAAEjB,MAAM,EAAE,CAAC;MACTqO,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEoK,MAAM,EAAE,CAAC;MACToO,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEqK,UAAU,EAAE,CAAC;MACbmO,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEsK,eAAe,EAAE,CAAC;MAClBkO,IAAI,EAAExY;IACV,CAAC,CAAC;IAAEuK,SAAS,EAAE,CAAC;MACZiO,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8K,SAAS,EAAE,CAAC;MACZgO,IAAI,EAAExY,KAAK;MACX4b,IAAI,EAAE,CAAC;QAAES,SAAS,EAAE3c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqG,QAAQ,EAAE,CAAC;MACXyS,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE6K,OAAO,EAAE,CAAC;MACV2N,IAAI,EAAEvY;IACV,CAAC,CAAC;IAAE6K,OAAO,EAAE,CAAC;MACV0N,IAAI,EAAEvY;IACV,CAAC,CAAC;IAAE8K,MAAM,EAAE,CAAC;MACTyN,IAAI,EAAEvY;IACV,CAAC,CAAC;IAAE+K,SAAS,EAAE,CAAC;MACZwN,IAAI,EAAEvY;IACV,CAAC,CAAC;IAAEgL,OAAO,EAAE,CAAC;MACVuN,IAAI,EAAEvY;IACV,CAAC,CAAC;IAAEiL,KAAK,EAAE,CAAC;MACRsN,IAAI,EAAEtY,SAAS;MACf0b,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEzQ,SAAS,EAAE,CAAC;MACZqN,IAAI,EAAErY,eAAe;MACrByb,IAAI,EAAE,CAACpb,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8b,iBAAiB,CAAC;EACpB,OAAOxE,IAAI,YAAAyE,0BAAAvE,CAAA;IAAA,YAAAA,CAAA,IAAwFsE,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBAxb8Ejd,EAAE,CAAAkd,gBAAA;IAAAjE,IAAA,EAwbS8D;EAAiB;EACrH,OAAOI,IAAI,kBAzb8End,EAAE,CAAAod,gBAAA;IAAAC,OAAA,GAybsCtd,YAAY,EAAE6B,eAAe,EAAEN,YAAY,EAAEF,eAAe,EAAEM,SAAS,EAAED,WAAW,EAAED,aAAa,EAAEN,YAAY;EAAA;AACtP;AACA;EAAA,QAAAib,SAAA,oBAAAA,SAAA,KA3b6Fnc,EAAE,CAAAoc,iBAAA,CA2bJW,iBAAiB,EAAc,CAAC;IAC/G9D,IAAI,EAAEpY,QAAQ;IACdwb,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAACtd,YAAY,EAAE6B,eAAe,EAAEN,YAAY,EAAEF,eAAe,EAAEM,SAAS,EAAED,WAAW,EAAED,aAAa,CAAC;MAC9G8b,OAAO,EAAE,CAAChV,WAAW,EAAEpH,YAAY,CAAC;MACpCqc,YAAY,EAAE,CAACjV,WAAW;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,0BAA0B,EAAEG,WAAW,EAAEyU,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}