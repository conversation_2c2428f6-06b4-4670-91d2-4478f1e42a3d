{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CardModule } from 'primeng/card';\nimport { ChartModule } from 'primeng/chart';\nimport { TableModule } from 'primeng/table';\nimport { TagModule } from 'primeng/tag';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./dashboard.service\";\nimport * as i2 from \"@core/services/language.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/card\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/chart\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/tag\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/progressbar\";\nimport * as i11 from \"primeng/progressspinner\";\nfunction DashboardComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Sales Trend\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Inventory Distribution\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Monthly Revenue & Profit\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Low Stock Items\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Item\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Current Stock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Min Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵelement(11, \"p-progressBar\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.currentStock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.minimumLevel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", item_r1.currentStock / item_r1.minimumLevel * 100)(\"showValue\", false)(\"ngClass\", \"progress-\" + ctx_r1.getStockLevelSeverity(item_r1.currentStock / item_r1.minimumLevel * 100));\n  }\n}\nfunction DashboardComponent_div_6_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\", 36);\n    i0.ɵɵtext(2, \"Recent Transactions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_6_ng_template_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵelement(5, \"p-tag\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelement(10, \"p-tag\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const transaction_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 6, transaction_r3.date, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", transaction_r3.type)(\"severity\", ctx_r1.getTransactionTypeSeverity(transaction_r3.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 9, transaction_r3.amount));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", transaction_r3.status)(\"severity\", transaction_r3.status === \"Completed\" ? \"success\" : \"warning\");\n  }\n}\nfunction DashboardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"p-card\", 10)(4, \"div\", 11)(5, \"div\")(6, \"div\", 12);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 13);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 14);\n    i0.ɵɵelement(12, \"i\", 15);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 9)(14, \"p-card\", 10)(15, \"div\", 11)(16, \"div\")(17, \"div\", 12);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 16);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 17);\n    i0.ɵɵelement(22, \"i\", 18);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 9)(24, \"p-card\", 10)(25, \"div\", 11)(26, \"div\")(27, \"div\", 12);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 19);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 20);\n    i0.ɵɵelement(33, \"i\", 21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 9)(35, \"p-card\", 10)(36, \"div\", 11)(37, \"div\")(38, \"div\", 12);\n    i0.ɵɵtext(39, \"Pending Orders\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 22);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 23);\n    i0.ɵɵelement(43, \"i\", 24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(44, \"div\", 8)(45, \"div\", 25)(46, \"p-card\");\n    i0.ɵɵtemplate(47, DashboardComponent_div_6_ng_template_47_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelement(48, \"p-chart\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"div\", 28)(50, \"p-card\");\n    i0.ɵɵtemplate(51, DashboardComponent_div_6_ng_template_51_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelement(52, \"p-chart\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 8)(54, \"div\", 30)(55, \"p-card\");\n    i0.ɵɵtemplate(56, DashboardComponent_div_6_ng_template_56_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelement(57, \"p-chart\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 8)(59, \"div\", 32)(60, \"p-card\");\n    i0.ɵɵtemplate(61, DashboardComponent_div_6_ng_template_61_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelementStart(62, \"p-table\", 33);\n    i0.ɵɵtemplate(63, DashboardComponent_div_6_ng_template_63_Template, 9, 0, \"ng-template\", 26)(64, DashboardComponent_div_6_ng_template_64_Template, 12, 7, \"ng-template\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 32)(66, \"p-card\");\n    i0.ɵɵtemplate(67, DashboardComponent_div_6_ng_template_67_Template, 3, 0, \"ng-template\", 26);\n    i0.ɵɵelementStart(68, \"p-table\", 33);\n    i0.ɵɵtemplate(69, DashboardComponent_div_6_ng_template_69_Template, 9, 0, \"ng-template\", 26)(70, DashboardComponent_div_6_ng_template_70_Template, 11, 11, \"ng-template\", 34);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"dashboard.totalItems\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 19, ctx_r1.dashboardData.totalItems));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"dashboard.lowStockItems\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dashboardData.lowStockItems.length);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"dashboard.monthlyRevenue\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 21, ctx_r1.dashboardData.totalRevenue));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r1.dashboardData.pendingOrders);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"data\", ctx_r1.salesChartData)(\"options\", ctx_r1.salesChartOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r1.inventoryChartData)(\"options\", ctx_r1.inventoryChartOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r1.revenueChartData)(\"options\", ctx_r1.revenueChartOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r1.dashboardData.lowStockItems)(\"paginator\", true)(\"rows\", 5);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r1.dashboardData.recentTransactions)(\"paginator\", true)(\"rows\", 5);\n  }\n}\nexport class DashboardComponent {\n  constructor(dashboardService, languageService) {\n    this.dashboardService = dashboardService;\n    this.languageService = languageService;\n    this.dashboardData = null;\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n    this.initializeChartOptions();\n  }\n  loadDashboardData() {\n    this.loading = true;\n    this.dashboardService.getDashboardData().subscribe({\n      next: data => {\n        this.dashboardData = data;\n        this.setupCharts();\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading dashboard data:', error);\n        this.loading = false;\n      }\n    });\n  }\n  initializeChartOptions() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    this.salesChartOptions = {\n      maintainAspectRatio: false,\n      aspectRatio: 0.8,\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary,\n            font: {\n              weight: 500\n            }\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    };\n    this.inventoryChartOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            usePointStyle: true,\n            color: textColor\n          }\n        }\n      }\n    };\n    this.revenueChartOptions = {\n      maintainAspectRatio: false,\n      aspectRatio: 0.6,\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder\n          }\n        }\n      }\n    };\n  }\n  setupCharts() {\n    if (!this.dashboardData) return;\n    // Sales Chart\n    this.salesChartData = {\n      labels: this.dashboardData.salesTrend.map(s => s.period),\n      datasets: [{\n        label: 'Sales',\n        data: this.dashboardData.salesTrend.map(s => s.amount),\n        fill: false,\n        backgroundColor: '#3b82f6',\n        borderColor: '#3b82f6',\n        tension: 0.4\n      }]\n    };\n    // Inventory Chart\n    this.inventoryChartData = {\n      labels: ['Raw Materials', 'Semi-Finished', 'Finished Products', 'Consumables'],\n      datasets: [{\n        data: [this.dashboardData.inventorySummary.rawMaterials, this.dashboardData.inventorySummary.semiFinished, this.dashboardData.inventorySummary.finishedProducts, this.dashboardData.inventorySummary.consumables],\n        backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],\n        hoverBackgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626']\n      }]\n    };\n    // Revenue Chart\n    this.revenueChartData = {\n      labels: this.dashboardData.monthlyRevenue.map(r => r.month),\n      datasets: [{\n        type: 'line',\n        label: 'Revenue',\n        borderColor: '#3b82f6',\n        borderWidth: 2,\n        fill: false,\n        tension: 0.4,\n        data: this.dashboardData.monthlyRevenue.map(r => r.revenue)\n      }, {\n        type: 'bar',\n        label: 'Profit',\n        backgroundColor: '#10b981',\n        data: this.dashboardData.monthlyRevenue.map(r => r.profit)\n      }]\n    };\n  }\n  getStockLevelSeverity(percentage) {\n    if (percentage <= 20) return 'danger';\n    if (percentage <= 50) return 'warning';\n    return 'success';\n  }\n  getTransactionTypeSeverity(type) {\n    switch (type.toLowerCase()) {\n      case 'sale':\n        return 'success';\n      case 'purchase':\n        return 'info';\n      case 'return':\n        return 'warning';\n      case 'adjustment':\n        return 'secondary';\n      default:\n        return 'info';\n    }\n  }\n  refreshDashboard() {\n    this.loadDashboardData();\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.DashboardService), i0.ɵɵdirectiveInject(i2.LanguageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 5,\n      consts: [[1, \"dashboard-container\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"m-0\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-refresh\", 1, \"p-button-outlined\", 3, \"click\", \"label\", \"loading\"], [\"class\", \"flex justify-content-center align-items-center\", \"style\", \"height: 400px;\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"400px\"], [1, \"dashboard-content\"], [1, \"grid\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [\"styleClass\", \"metric-card\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-500\", \"font-medium\", \"mb-2\"], [1, \"text-2xl\", \"font-bold\", \"text-900\"], [1, \"metric-icon\", \"bg-blue-100\", \"text-blue-600\"], [1, \"pi\", \"pi-box\", \"text-2xl\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-600\"], [1, \"metric-icon\", \"bg-orange-100\", \"text-orange-600\"], [1, \"pi\", \"pi-exclamation-triangle\", \"text-2xl\"], [1, \"text-2xl\", \"font-bold\", \"text-green-600\"], [1, \"metric-icon\", \"bg-green-100\", \"text-green-600\"], [1, \"pi\", \"pi-dollar\", \"text-2xl\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-600\"], [1, \"metric-icon\", \"bg-purple-100\", \"text-purple-600\"], [1, \"pi\", \"pi-clock\", \"text-2xl\"], [1, \"col-12\", \"lg:col-8\"], [\"pTemplate\", \"header\"], [\"type\", \"line\", \"height\", \"300px\", 3, \"data\", \"options\"], [1, \"col-12\", \"lg:col-4\"], [\"type\", \"doughnut\", \"height\", \"300px\", 3, \"data\", \"options\"], [1, \"col-12\"], [\"type\", \"bar\", \"height\", \"400px\", 3, \"data\", \"options\"], [1, \"col-12\", \"lg:col-6\"], [\"styleClass\", \"p-datatable-sm\", 3, \"value\", \"paginator\", \"rows\"], [\"pTemplate\", \"body\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"font-medium\"], [1, \"text-sm\", \"text-500\"], [\"styleClass\", \"w-full\", 3, \"value\", \"showValue\", \"ngClass\"], [3, \"value\", \"severity\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_4_listener() {\n            return ctx.refreshDashboard();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, DashboardComponent_div_5_Template, 2, 0, \"div\", 4)(6, DashboardComponent_div_6_Template, 71, 23, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"dashboard.title\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", ctx.languageService.translate(\"common.refresh\"))(\"loading\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardData);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgIf, i3.DecimalPipe, i3.CurrencyPipe, i3.DatePipe, CardModule, i4.Card, i5.PrimeTemplate, ChartModule, i6.UIChart, TableModule, i7.Table, TagModule, i8.Tag, ButtonModule, i9.ButtonDirective, ProgressBarModule, i10.ProgressBar, ProgressSpinnerModule, i11.ProgressSpinner],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n}\\n\\n.metric-card[_ngcontent-%COMP%] {\\n  border: 1px solid var(--surface-200);\\n  border-radius: var(--border-radius);\\n  transition: all 0.2s;\\n}\\n.metric-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-2px);\\n}\\n.metric-card[_ngcontent-%COMP%]     .p-card-body {\\n  padding: 1.5rem;\\n}\\n\\n.metric-icon[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid var(--surface-200);\\n  background: var(--surface-50);\\n  margin: -1.5rem -1.5rem 1.5rem -1.5rem;\\n  border-radius: var(--border-radius) var(--border-radius) 0 0;\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0;\\n}\\n\\n  .p-chart .p-chart-canvas {\\n  max-height: 400px;\\n}\\n\\n  .p-datatable-sm .p-datatable-thead > tr > th {\\n  padding: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n}\\n  .p-datatable-sm .p-datatable-tbody > tr > td {\\n  padding: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n  .p-progressbar {\\n  height: 0.5rem;\\n}\\n  .p-progressbar .p-progressbar-value {\\n  border-radius: 0.25rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .dashboard-content[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .metric-card[_ngcontent-%COMP%]     .p-card-body {\\n    padding: 1rem;\\n  }\\n  .card-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n    margin: -1rem -1rem 1rem -1rem;\\n  }\\n  .card-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .metric-icon[_ngcontent-%COMP%] {\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .metric-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.25rem !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .dashboard-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem !important;\\n  }\\n  .flex.justify-content-between[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n    gap: 1rem;\\n  }\\n  .metric-card[_ngcontent-%COMP%]   .flex.justify-content-between[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    align-items: center !important;\\n    gap: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "CardModule", "ChartModule", "TableModule", "TagModule", "ButtonModule", "ProgressBarModule", "ProgressSpinnerModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "item_r1", "name", "code", "currentStock", "minimumLevel", "ɵɵproperty", "ctx_r1", "getStockLevelSeverity", "ɵɵpipeBind2", "transaction_r3", "date", "type", "getTransactionTypeSeverity", "ɵɵpipeBind1", "amount", "status", "ɵɵtemplate", "DashboardComponent_div_6_ng_template_47_Template", "DashboardComponent_div_6_ng_template_51_Template", "DashboardComponent_div_6_ng_template_56_Template", "DashboardComponent_div_6_ng_template_61_Template", "DashboardComponent_div_6_ng_template_63_Template", "DashboardComponent_div_6_ng_template_64_Template", "DashboardComponent_div_6_ng_template_67_Template", "DashboardComponent_div_6_ng_template_69_Template", "DashboardComponent_div_6_ng_template_70_Template", "languageService", "translate", "dashboardData", "totalItems", "lowStockItems", "length", "totalRevenue", "pendingOrders", "salesChartData", "salesChartOptions", "inventoryChartData", "inventoryChartOptions", "revenueChartData", "revenueChartOptions", "recentTransactions", "DashboardComponent", "constructor", "dashboardService", "loading", "ngOnInit", "loadDashboardData", "initializeChartOptions", "getDashboardData", "subscribe", "next", "data", "<PERSON><PERSON><PERSON><PERSON>", "error", "console", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "maintainAspectRatio", "aspectRatio", "plugins", "legend", "labels", "color", "scales", "x", "ticks", "font", "weight", "grid", "drawBorder", "y", "usePointStyle", "salesTrend", "map", "s", "period", "datasets", "label", "fill", "backgroundColor", "borderColor", "tension", "inventorySummary", "rawMaterials", "semiFinished", "finishedProducts", "consumables", "hoverBackgroundColor", "monthlyRevenue", "r", "month", "borderWidth", "revenue", "profit", "percentage", "toLowerCase", "refreshDashboard", "ɵɵdirectiveInject", "i1", "DashboardService", "i2", "LanguageService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "DashboardComponent_Template_button_click_4_listener", "DashboardComponent_div_5_Template", "DashboardComponent_div_6_Template", "i3", "Ng<PERSON><PERSON>", "NgIf", "DecimalPipe", "C<PERSON><PERSON>cyPipe", "DatePipe", "i4", "Card", "i5", "PrimeTemplate", "i6", "UIChart", "i7", "Table", "i8", "Tag", "i9", "ButtonDirective", "i10", "ProgressBar", "i11", "ProgressSpinner", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\features\\dashboard\\dashboard.component.ts", "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\features\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CardModule } from 'primeng/card';\nimport { ChartModule } from 'primeng/chart';\nimport { TableModule } from 'primeng/table';\nimport { TagModule } from 'primeng/tag';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { DashboardService } from './dashboard.service';\nimport { DashboardData, InventorySummary, RecentTransaction, LowStockItem } from './dashboard.model';\nimport { LanguageService } from '@core/services/language.service';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    CardModule,\n    ChartModule,\n    TableModule,\n    TagModule,\n    ButtonModule,\n    ProgressBarModule,\n    ProgressSpinnerModule\n  ],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  dashboardData: DashboardData | null = null;\n  loading = true;\n\n  // Chart data\n  salesChartData: any;\n  salesChartOptions: any;\n  inventoryChartData: any;\n  inventoryChartOptions: any;\n  revenueChartData: any;\n  revenueChartOptions: any;\n\n  constructor(\n    private dashboardService: DashboardService,\n    public languageService: LanguageService\n  ) {}\n\n  ngOnInit() {\n    this.loadDashboardData();\n    this.initializeChartOptions();\n  }\n\n  loadDashboardData() {\n    this.loading = true;\n    this.dashboardService.getDashboardData().subscribe({\n      next: (data) => {\n        this.dashboardData = data;\n        this.setupCharts();\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading dashboard data:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  private initializeChartOptions() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n\n    this.salesChartOptions = {\n      maintainAspectRatio: false,\n      aspectRatio: 0.8,\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary,\n            font: {\n              weight: 500\n            }\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    };\n\n    this.inventoryChartOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            usePointStyle: true,\n            color: textColor\n          }\n        }\n      }\n    };\n\n    this.revenueChartOptions = {\n      maintainAspectRatio: false,\n      aspectRatio: 0.6,\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder\n          }\n        }\n      }\n    };\n  }\n\n  private setupCharts() {\n    if (!this.dashboardData) return;\n\n    // Sales Chart\n    this.salesChartData = {\n      labels: this.dashboardData.salesTrend.map(s => s.period),\n      datasets: [\n        {\n          label: 'Sales',\n          data: this.dashboardData.salesTrend.map(s => s.amount),\n          fill: false,\n          backgroundColor: '#3b82f6',\n          borderColor: '#3b82f6',\n          tension: 0.4\n        }\n      ]\n    };\n\n    // Inventory Chart\n    this.inventoryChartData = {\n      labels: ['Raw Materials', 'Semi-Finished', 'Finished Products', 'Consumables'],\n      datasets: [\n        {\n          data: [\n            this.dashboardData.inventorySummary.rawMaterials,\n            this.dashboardData.inventorySummary.semiFinished,\n            this.dashboardData.inventorySummary.finishedProducts,\n            this.dashboardData.inventorySummary.consumables\n          ],\n          backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],\n          hoverBackgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626']\n        }\n      ]\n    };\n\n    // Revenue Chart\n    this.revenueChartData = {\n      labels: this.dashboardData.monthlyRevenue.map(r => r.month),\n      datasets: [\n        {\n          type: 'line',\n          label: 'Revenue',\n          borderColor: '#3b82f6',\n          borderWidth: 2,\n          fill: false,\n          tension: 0.4,\n          data: this.dashboardData.monthlyRevenue.map(r => r.revenue)\n        },\n        {\n          type: 'bar',\n          label: 'Profit',\n          backgroundColor: '#10b981',\n          data: this.dashboardData.monthlyRevenue.map(r => r.profit)\n        }\n      ]\n    };\n  }\n\n  getStockLevelSeverity(percentage: number): string {\n    if (percentage <= 20) return 'danger';\n    if (percentage <= 50) return 'warning';\n    return 'success';\n  }\n\n  getTransactionTypeSeverity(type: string): \"success\" | \"secondary\" | \"info\" | \"warning\" | \"danger\" | \"contrast\" {\n    switch (type.toLowerCase()) {\n      case 'sale': return 'success';\n      case 'purchase': return 'info';\n      case 'return': return 'warning';\n      case 'adjustment': return 'secondary';\n      default: return 'info';\n    }\n  }\n\n  refreshDashboard() {\n    this.loadDashboardData();\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Header -->\n  <div class=\"flex justify-content-between align-items-center mb-4\">\n    <h1 class=\"text-2xl font-bold m-0\">{{ languageService.translate('dashboard.title') }}</h1>\n    <button\n      pButton\n      type=\"button\"\n      icon=\"pi pi-refresh\"\n      [label]=\"languageService.translate('common.refresh')\"\n      class=\"p-button-outlined\"\n      (click)=\"refreshDashboard()\"\n      [loading]=\"loading\">\n    </button>\n  </div>\n\n  <div *ngIf=\"loading\" class=\"flex justify-content-center align-items-center\" style=\"height: 400px;\">\n    <p-progressSpinner></p-progressSpinner>\n  </div>\n\n  <div *ngIf=\"!loading && dashboardData\" class=\"dashboard-content\">\n    <!-- Key Metrics Cards -->\n    <div class=\"grid\">\n      <div class=\"col-12 md:col-6 lg:col-3\">\n        <p-card styleClass=\"metric-card\">\n          <div class=\"flex justify-content-between align-items-center\">\n            <div>\n              <div class=\"text-500 font-medium mb-2\">{{ languageService.translate('dashboard.totalItems') }}</div>\n              <div class=\"text-2xl font-bold text-900\">{{ dashboardData.totalItems | number }}</div>\n            </div>\n            <div class=\"metric-icon bg-blue-100 text-blue-600\">\n              <i class=\"pi pi-box text-2xl\"></i>\n            </div>\n          </div>\n        </p-card>\n      </div>\n\n      <div class=\"col-12 md:col-6 lg:col-3\">\n        <p-card styleClass=\"metric-card\">\n          <div class=\"flex justify-content-between align-items-center\">\n            <div>\n              <div class=\"text-500 font-medium mb-2\">{{ languageService.translate('dashboard.lowStockItems') }}</div>\n              <div class=\"text-2xl font-bold text-orange-600\">{{ dashboardData.lowStockItems.length }}</div>\n            </div>\n            <div class=\"metric-icon bg-orange-100 text-orange-600\">\n              <i class=\"pi pi-exclamation-triangle text-2xl\"></i>\n            </div>\n          </div>\n        </p-card>\n      </div>\n\n      <div class=\"col-12 md:col-6 lg:col-3\">\n        <p-card styleClass=\"metric-card\">\n          <div class=\"flex justify-content-between align-items-center\">\n            <div>\n              <div class=\"text-500 font-medium mb-2\">{{ languageService.translate('dashboard.monthlyRevenue') }}</div>\n              <div class=\"text-2xl font-bold text-green-600\">{{ dashboardData.totalRevenue | currency }}</div>\n            </div>\n            <div class=\"metric-icon bg-green-100 text-green-600\">\n              <i class=\"pi pi-dollar text-2xl\"></i>\n            </div>\n          </div>\n        </p-card>\n      </div>\n\n      <div class=\"col-12 md:col-6 lg:col-3\">\n        <p-card styleClass=\"metric-card\">\n          <div class=\"flex justify-content-between align-items-center\">\n            <div>\n              <div class=\"text-500 font-medium mb-2\">Pending Orders</div>\n              <div class=\"text-2xl font-bold text-purple-600\">{{ dashboardData.pendingOrders }}</div>\n            </div>\n            <div class=\"metric-icon bg-purple-100 text-purple-600\">\n              <i class=\"pi pi-clock text-2xl\"></i>\n            </div>\n          </div>\n        </p-card>\n      </div>\n    </div>\n\n    <!-- Charts Row -->\n    <div class=\"grid\">\n      <!-- Sales Trend Chart -->\n      <div class=\"col-12 lg:col-8\">\n        <p-card>\n          <ng-template pTemplate=\"header\">\n            <div class=\"card-header\">\n              <h3 class=\"card-title\">Sales Trend</h3>\n            </div>\n          </ng-template>\n          <p-chart \n            type=\"line\" \n            [data]=\"salesChartData\" \n            [options]=\"salesChartOptions\"\n            height=\"300px\">\n          </p-chart>\n        </p-card>\n      </div>\n\n      <!-- Inventory Distribution -->\n      <div class=\"col-12 lg:col-4\">\n        <p-card>\n          <ng-template pTemplate=\"header\">\n            <div class=\"card-header\">\n              <h3 class=\"card-title\">Inventory Distribution</h3>\n            </div>\n          </ng-template>\n          <p-chart \n            type=\"doughnut\" \n            [data]=\"inventoryChartData\" \n            [options]=\"inventoryChartOptions\"\n            height=\"300px\">\n          </p-chart>\n        </p-card>\n      </div>\n    </div>\n\n    <!-- Revenue & Profit Chart -->\n    <div class=\"grid\">\n      <div class=\"col-12\">\n        <p-card>\n          <ng-template pTemplate=\"header\">\n            <div class=\"card-header\">\n              <h3 class=\"card-title\">Monthly Revenue & Profit</h3>\n            </div>\n          </ng-template>\n          <p-chart \n            type=\"bar\" \n            [data]=\"revenueChartData\" \n            [options]=\"revenueChartOptions\"\n            height=\"400px\">\n          </p-chart>\n        </p-card>\n      </div>\n    </div>\n\n    <!-- Tables Row -->\n    <div class=\"grid\">\n      <!-- Low Stock Items -->\n      <div class=\"col-12 lg:col-6\">\n        <p-card>\n          <ng-template pTemplate=\"header\">\n            <div class=\"card-header\">\n              <h3 class=\"card-title\">Low Stock Items</h3>\n            </div>\n          </ng-template>\n          <p-table \n            [value]=\"dashboardData.lowStockItems\" \n            [paginator]=\"true\" \n            [rows]=\"5\"\n            styleClass=\"p-datatable-sm\">\n            <ng-template pTemplate=\"header\">\n              <tr>\n                <th>Item</th>\n                <th>Current Stock</th>\n                <th>Min Level</th>\n                <th>Status</th>\n              </tr>\n            </ng-template>\n            <ng-template pTemplate=\"body\" let-item>\n              <tr>\n                <td>\n                  <div class=\"font-medium\">{{ item.name }}</div>\n                  <div class=\"text-sm text-500\">{{ item.code }}</div>\n                </td>\n                <td>{{ item.currentStock }}</td>\n                <td>{{ item.minimumLevel }}</td>\n                <td>\n                  <p-progressBar\n                    [value]=\"(item.currentStock / item.minimumLevel) * 100\"\n                    [showValue]=\"false\"\n                    styleClass=\"w-full\"\n                    [ngClass]=\"'progress-' + getStockLevelSeverity((item.currentStock / item.minimumLevel) * 100)\">\n                  </p-progressBar>\n                </td>\n              </tr>\n            </ng-template>\n          </p-table>\n        </p-card>\n      </div>\n\n      <!-- Recent Transactions -->\n      <div class=\"col-12 lg:col-6\">\n        <p-card>\n          <ng-template pTemplate=\"header\">\n            <div class=\"card-header\">\n              <h3 class=\"card-title\">Recent Transactions</h3>\n            </div>\n          </ng-template>\n          <p-table \n            [value]=\"dashboardData.recentTransactions\" \n            [paginator]=\"true\" \n            [rows]=\"5\"\n            styleClass=\"p-datatable-sm\">\n            <ng-template pTemplate=\"header\">\n              <tr>\n                <th>Date</th>\n                <th>Type</th>\n                <th>Amount</th>\n                <th>Status</th>\n              </tr>\n            </ng-template>\n            <ng-template pTemplate=\"body\" let-transaction>\n              <tr>\n                <td>{{ transaction.date | date:'short' }}</td>\n                <td>\n                  <p-tag \n                    [value]=\"transaction.type\" \n                    [severity]=\"getTransactionTypeSeverity(transaction.type)\">\n                  </p-tag>\n                </td>\n                <td>{{ transaction.amount | currency }}</td>\n                <td>\n                  <p-tag \n                    [value]=\"transaction.status\" \n                    [severity]=\"transaction.status === 'Completed' ? 'success' : 'warning'\">\n                  </p-tag>\n                </td>\n              </tr>\n            </ng-template>\n          </p-table>\n        </p-card>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,qBAAqB,QAAQ,yBAAyB;;;;;;;;;;;;;;;ICO7DC,EAAA,CAAAC,cAAA,aAAmG;IACjGD,EAAA,CAAAE,SAAA,wBAAuC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAqEMH,EADF,CAAAC,cAAA,cAAyB,aACA;IAAAD,EAAA,CAAAI,MAAA,kBAAW;IACpCJ,EADoC,CAAAG,YAAA,EAAK,EACnC;;;;;IAgBJH,EADF,CAAAC,cAAA,cAAyB,aACA;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAC/CJ,EAD+C,CAAAG,YAAA,EAAK,EAC9C;;;;;IAkBJH,EADF,CAAAC,cAAA,cAAyB,aACA;IAAAD,EAAA,CAAAI,MAAA,+BAAwB;IACjDJ,EADiD,CAAAG,YAAA,EAAK,EAChD;;;;;IAmBJH,EADF,CAAAC,cAAA,cAAyB,aACA;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IACxCJ,EADwC,CAAAG,YAAA,EAAK,EACvC;;;;;IASFH,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,oBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,aAAM;IACZJ,EADY,CAAAG,YAAA,EAAK,EACZ;;;;;IAKDH,EAFJ,CAAAC,cAAA,SAAI,SACE,cACuB;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAC/CJ,EAD+C,CAAAG,YAAA,EAAM,EAChD;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,SAAA,yBAKgB;IAEpBF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAbwBH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,IAAA,CAAe;IACVR,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAE,IAAA,CAAe;IAE3CT,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAG,YAAA,CAAuB;IACvBV,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAI,YAAA,CAAuB;IAGvBX,EAAA,CAAAK,SAAA,GAAuD;IAGvDL,EAHA,CAAAY,UAAA,UAAAL,OAAA,CAAAG,YAAA,GAAAH,OAAA,CAAAI,YAAA,OAAuD,oBACpC,0BAAAE,MAAA,CAAAC,qBAAA,CAAAP,OAAA,CAAAG,YAAA,GAAAH,OAAA,CAAAI,YAAA,QAE2E;;;;;IAcpGX,EADF,CAAAC,cAAA,cAAyB,aACA;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAC5CJ,EAD4C,CAAAG,YAAA,EAAK,EAC3C;;;;;IASFH,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,aAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,aAAM;IACZJ,EADY,CAAAG,YAAA,EAAK,EACZ;;;;;IAIHH,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAI,MAAA,GAAqC;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,SAAA,gBAGQ;IACVF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAmC;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,SAAA,iBAGQ;IAEZF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAdCH,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAC,cAAA,CAAAC,IAAA,WAAqC;IAGrCjB,EAAA,CAAAK,SAAA,GAA0B;IAC1BL,EADA,CAAAY,UAAA,UAAAI,cAAA,CAAAE,IAAA,CAA0B,aAAAL,MAAA,CAAAM,0BAAA,CAAAH,cAAA,CAAAE,IAAA,EAC+B;IAGzDlB,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAoB,WAAA,OAAAJ,cAAA,CAAAK,MAAA,EAAmC;IAGnCrB,EAAA,CAAAK,SAAA,GAA4B;IAC5BL,EADA,CAAAY,UAAA,UAAAI,cAAA,CAAAM,MAAA,CAA4B,aAAAN,cAAA,CAAAM,MAAA,yCAC2C;;;;;IA5L7EtB,EAPZ,CAAAC,cAAA,aAAiE,aAE7C,aACsB,iBACH,cAC8B,UACtD,cACoC;IAAAD,EAAA,CAAAI,MAAA,GAAuD;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACpGH,EAAA,CAAAC,cAAA,cAAyC;IAAAD,EAAA,CAAAI,MAAA,GAAuC;;IAClFJ,EADkF,CAAAG,YAAA,EAAM,EAClF;IACNH,EAAA,CAAAC,cAAA,eAAmD;IACjDD,EAAA,CAAAE,SAAA,aAAkC;IAI1CF,EAHM,CAAAG,YAAA,EAAM,EACF,EACC,EACL;IAMEH,EAJR,CAAAC,cAAA,cAAsC,kBACH,eAC8B,WACtD,eACoC;IAAAD,EAAA,CAAAI,MAAA,IAA0D;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACvGH,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAI,MAAA,IAAwC;IAC1FJ,EAD0F,CAAAG,YAAA,EAAM,EAC1F;IACNH,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAE,SAAA,aAAmD;IAI3DF,EAHM,CAAAG,YAAA,EAAM,EACF,EACC,EACL;IAMEH,EAJR,CAAAC,cAAA,cAAsC,kBACH,eAC8B,WACtD,eACoC;IAAAD,EAAA,CAAAI,MAAA,IAA2D;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACxGH,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAI,MAAA,IAA2C;;IAC5FJ,EAD4F,CAAAG,YAAA,EAAM,EAC5F;IACNH,EAAA,CAAAC,cAAA,eAAqD;IACnDD,EAAA,CAAAE,SAAA,aAAqC;IAI7CF,EAHM,CAAAG,YAAA,EAAM,EACF,EACC,EACL;IAMEH,EAJR,CAAAC,cAAA,cAAsC,kBACH,eAC8B,WACtD,eACoC;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC3DH,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAI,MAAA,IAAiC;IACnFJ,EADmF,CAAAG,YAAA,EAAM,EACnF;IACNH,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAE,SAAA,aAAoC;IAK9CF,EAJQ,CAAAG,YAAA,EAAM,EACF,EACC,EACL,EACF;IAMFH,EAHJ,CAAAC,cAAA,cAAkB,eAEa,cACnB;IACND,EAAA,CAAAuB,UAAA,KAAAC,gDAAA,0BAAgC;IAKhCxB,EAAA,CAAAE,SAAA,mBAKU;IAEdF,EADE,CAAAG,YAAA,EAAS,EACL;IAIJH,EADF,CAAAC,cAAA,eAA6B,cACnB;IACND,EAAA,CAAAuB,UAAA,KAAAE,gDAAA,0BAAgC;IAKhCzB,EAAA,CAAAE,SAAA,mBAKU;IAGhBF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAkB,eACI,cACV;IACND,EAAA,CAAAuB,UAAA,KAAAG,gDAAA,0BAAgC;IAKhC1B,EAAA,CAAAE,SAAA,mBAKU;IAGhBF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAMFH,EAHJ,CAAAC,cAAA,cAAkB,eAEa,cACnB;IACND,EAAA,CAAAuB,UAAA,KAAAI,gDAAA,0BAAgC;IAKhC3B,EAAA,CAAAC,cAAA,mBAI8B;IAS5BD,EARA,CAAAuB,UAAA,KAAAK,gDAAA,0BAAgC,KAAAC,gDAAA,2BAQO;IAoB7C7B,EAFI,CAAAG,YAAA,EAAU,EACH,EACL;IAIJH,EADF,CAAAC,cAAA,eAA6B,cACnB;IACND,EAAA,CAAAuB,UAAA,KAAAO,gDAAA,0BAAgC;IAKhC9B,EAAA,CAAAC,cAAA,mBAI8B;IAS5BD,EARA,CAAAuB,UAAA,KAAAQ,gDAAA,0BAAgC,KAAAC,gDAAA,4BAQc;IAsBxDhC,EAJQ,CAAAG,YAAA,EAAU,EACH,EACL,EACF,EACF;;;;IArM6CH,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAAM,iBAAA,CAAAO,MAAA,CAAAoB,eAAA,CAAAC,SAAA,yBAAuD;IACrDlC,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAoB,WAAA,SAAAP,MAAA,CAAAsB,aAAA,CAAAC,UAAA,EAAuC;IAazCpC,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAM,iBAAA,CAAAO,MAAA,CAAAoB,eAAA,CAAAC,SAAA,4BAA0D;IACjDlC,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAO,MAAA,CAAAsB,aAAA,CAAAE,aAAA,CAAAC,MAAA,CAAwC;IAajDtC,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAM,iBAAA,CAAAO,MAAA,CAAAoB,eAAA,CAAAC,SAAA,6BAA2D;IACnDlC,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAoB,WAAA,SAAAP,MAAA,CAAAsB,aAAA,CAAAI,YAAA,EAA2C;IAc1CvC,EAAA,CAAAK,SAAA,IAAiC;IAAjCL,EAAA,CAAAM,iBAAA,CAAAO,MAAA,CAAAsB,aAAA,CAAAK,aAAA,CAAiC;IAsBnFxC,EAAA,CAAAK,SAAA,GAAuB;IACvBL,EADA,CAAAY,UAAA,SAAAC,MAAA,CAAA4B,cAAA,CAAuB,YAAA5B,MAAA,CAAA6B,iBAAA,CACM;IAgB7B1C,EAAA,CAAAK,SAAA,GAA2B;IAC3BL,EADA,CAAAY,UAAA,SAAAC,MAAA,CAAA8B,kBAAA,CAA2B,YAAA9B,MAAA,CAAA+B,qBAAA,CACM;IAkBjC5C,EAAA,CAAAK,SAAA,GAAyB;IACzBL,EADA,CAAAY,UAAA,SAAAC,MAAA,CAAAgC,gBAAA,CAAyB,YAAAhC,MAAA,CAAAiC,mBAAA,CACM;IAkB/B9C,EAAA,CAAAK,SAAA,GAAqC;IAErCL,EAFA,CAAAY,UAAA,UAAAC,MAAA,CAAAsB,aAAA,CAAAE,aAAA,CAAqC,mBACnB,WACR;IAyCVrC,EAAA,CAAAK,SAAA,GAA0C;IAE1CL,EAFA,CAAAY,UAAA,UAAAC,MAAA,CAAAsB,aAAA,CAAAY,kBAAA,CAA0C,mBACxB,WACR;;;ADlKtB,OAAM,MAAOC,kBAAkB;EAY7BC,YACUC,gBAAkC,EACnCjB,eAAgC;IAD/B,KAAAiB,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAjB,eAAe,GAAfA,eAAe;IAbxB,KAAAE,aAAa,GAAyB,IAAI;IAC1C,KAAAgB,OAAO,GAAG,IAAI;EAaX;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACD,gBAAgB,CAACK,gBAAgB,EAAE,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACvB,aAAa,GAAGuB,IAAI;QACzB,IAAI,CAACC,WAAW,EAAE;QAClB,IAAI,CAACR,OAAO,GAAG,KAAK;MACtB,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQG,sBAAsBA,CAAA;IAC5B,MAAMQ,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;IAChE,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAgB,CAAC,cAAc,CAAC;IAChE,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAgB,CAAC,wBAAwB,CAAC;IACnF,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAgB,CAAC,kBAAkB,CAAC;IAExE,IAAI,CAACzB,iBAAiB,GAAG;MACvB4B,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE,GAAG;MAChBC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,MAAM,EAAE;YACNC,KAAK,EAAET;;;OAGZ;MACDU,MAAM,EAAE;QACNC,CAAC,EAAE;UACDC,KAAK,EAAE;YACLH,KAAK,EAAEP,kBAAkB;YACzBW,IAAI,EAAE;cACJC,MAAM,EAAE;;WAEX;UACDC,IAAI,EAAE;YACJN,KAAK,EAAEN,aAAa;YACpBa,UAAU,EAAE;;SAEf;QACDC,CAAC,EAAE;UACDL,KAAK,EAAE;YACLH,KAAK,EAAEP;WACR;UACDa,IAAI,EAAE;YACJN,KAAK,EAAEN,aAAa;YACpBa,UAAU,EAAE;;;;KAInB;IAED,IAAI,CAACtC,qBAAqB,GAAG;MAC3B4B,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,MAAM,EAAE;YACNU,aAAa,EAAE,IAAI;YACnBT,KAAK,EAAET;;;;KAId;IAED,IAAI,CAACpB,mBAAmB,GAAG;MACzBwB,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE,GAAG;MAChBC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,MAAM,EAAE;YACNC,KAAK,EAAET;;;OAGZ;MACDU,MAAM,EAAE;QACNC,CAAC,EAAE;UACDC,KAAK,EAAE;YACLH,KAAK,EAAEP;WACR;UACDa,IAAI,EAAE;YACJN,KAAK,EAAEN;;SAEV;QACDc,CAAC,EAAE;UACDL,KAAK,EAAE;YACLH,KAAK,EAAEP;WACR;UACDa,IAAI,EAAE;YACJN,KAAK,EAAEN;;;;KAId;EACH;EAEQV,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACxB,aAAa,EAAE;IAEzB;IACA,IAAI,CAACM,cAAc,GAAG;MACpBiC,MAAM,EAAE,IAAI,CAACvC,aAAa,CAACkD,UAAU,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,CAAC;MACxDC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,OAAO;QACdhC,IAAI,EAAE,IAAI,CAACvB,aAAa,CAACkD,UAAU,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAClE,MAAM,CAAC;QACtDsE,IAAI,EAAE,KAAK;QACXC,eAAe,EAAE,SAAS;QAC1BC,WAAW,EAAE,SAAS;QACtBC,OAAO,EAAE;OACV;KAEJ;IAED;IACA,IAAI,CAACnD,kBAAkB,GAAG;MACxB+B,MAAM,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,mBAAmB,EAAE,aAAa,CAAC;MAC9Ee,QAAQ,EAAE,CACR;QACE/B,IAAI,EAAE,CACJ,IAAI,CAACvB,aAAa,CAAC4D,gBAAgB,CAACC,YAAY,EAChD,IAAI,CAAC7D,aAAa,CAAC4D,gBAAgB,CAACE,YAAY,EAChD,IAAI,CAAC9D,aAAa,CAAC4D,gBAAgB,CAACG,gBAAgB,EACpD,IAAI,CAAC/D,aAAa,CAAC4D,gBAAgB,CAACI,WAAW,CAChD;QACDP,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QAC7DQ,oBAAoB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;OAClE;KAEJ;IAED;IACA,IAAI,CAACvD,gBAAgB,GAAG;MACtB6B,MAAM,EAAE,IAAI,CAACvC,aAAa,CAACkE,cAAc,CAACf,GAAG,CAACgB,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC;MAC3Dd,QAAQ,EAAE,CACR;QACEvE,IAAI,EAAE,MAAM;QACZwE,KAAK,EAAE,SAAS;QAChBG,WAAW,EAAE,SAAS;QACtBW,WAAW,EAAE,CAAC;QACdb,IAAI,EAAE,KAAK;QACXG,OAAO,EAAE,GAAG;QACZpC,IAAI,EAAE,IAAI,CAACvB,aAAa,CAACkE,cAAc,CAACf,GAAG,CAACgB,CAAC,IAAIA,CAAC,CAACG,OAAO;OAC3D,EACD;QACEvF,IAAI,EAAE,KAAK;QACXwE,KAAK,EAAE,QAAQ;QACfE,eAAe,EAAE,SAAS;QAC1BlC,IAAI,EAAE,IAAI,CAACvB,aAAa,CAACkE,cAAc,CAACf,GAAG,CAACgB,CAAC,IAAIA,CAAC,CAACI,MAAM;OAC1D;KAEJ;EACH;EAEA5F,qBAAqBA,CAAC6F,UAAkB;IACtC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,QAAQ;IACrC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,OAAO,SAAS;EAClB;EAEAxF,0BAA0BA,CAACD,IAAY;IACrC,QAAQA,IAAI,CAAC0F,WAAW,EAAE;MACxB,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,UAAU;QAAE,OAAO,MAAM;MAC9B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,YAAY;QAAE,OAAO,WAAW;MACrC;QAAS,OAAO,MAAM;;EAE1B;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACxD,iBAAiB,EAAE;EAC1B;;;uBApMWL,kBAAkB,EAAAhD,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAlBlE,kBAAkB;MAAAmE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArH,EAAA,CAAAsH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1B3B5H,EAHJ,CAAAC,cAAA,aAAiC,aAEmC,YAC7B;UAAAD,EAAA,CAAAI,MAAA,GAAkD;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC1FH,EAAA,CAAAC,cAAA,gBAOsB;UADpBD,EAAA,CAAA8H,UAAA,mBAAAC,oDAAA;YAAA,OAASF,GAAA,CAAAhB,gBAAA,EAAkB;UAAA,EAAC;UAGhC7G,EADE,CAAAG,YAAA,EAAS,EACL;UAMNH,EAJA,CAAAuB,UAAA,IAAAyG,iCAAA,iBAAmG,IAAAC,iCAAA,mBAIlC;UA6MnEjI,EAAA,CAAAG,YAAA,EAAM;;;UA7NiCH,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAM,iBAAA,CAAAuH,GAAA,CAAA5F,eAAA,CAAAC,SAAA,oBAAkD;UAKnFlC,EAAA,CAAAK,SAAA,EAAqD;UAGrDL,EAHA,CAAAY,UAAA,UAAAiH,GAAA,CAAA5F,eAAA,CAAAC,SAAA,mBAAqD,YAAA2F,GAAA,CAAA1E,OAAA,CAGlC;UAIjBnD,EAAA,CAAAK,SAAA,EAAa;UAAbL,EAAA,CAAAY,UAAA,SAAAiH,GAAA,CAAA1E,OAAA,CAAa;UAIbnD,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAAY,UAAA,UAAAiH,GAAA,CAAA1E,OAAA,IAAA0E,GAAA,CAAA1F,aAAA,CAA+B;;;qBDFnC3C,YAAY,EAAA0I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,QAAA,EACZ9I,UAAU,EAAA+I,EAAA,CAAAC,IAAA,EAAAC,EAAA,CAAAC,aAAA,EACVjJ,WAAW,EAAAkJ,EAAA,CAAAC,OAAA,EACXlJ,WAAW,EAAAmJ,EAAA,CAAAC,KAAA,EACXnJ,SAAS,EAAAoJ,EAAA,CAAAC,GAAA,EACTpJ,YAAY,EAAAqJ,EAAA,CAAAC,eAAA,EACZrJ,iBAAiB,EAAAsJ,GAAA,CAAAC,WAAA,EACjBtJ,qBAAqB,EAAAuJ,GAAA,CAAAC,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}