import { NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS } from '@angular/common/http';

// Interceptors
import { 
  AuthInterceptor, 
  CsrfInterceptor, 
  SecurityHeadersInterceptor 
} from './interceptors/auth.interceptor';

// Directives
import { 
  HasPermissionDirective, 
  HasRoleDirective, 
  IsAuthenticatedDirective 
} from './directives/has-permission.directive';

// Guards
import { 
  AuthGuard, 
  NoAuthGuard, 
  RoleGuard, 
  PermissionGuard 
} from './guards/auth.guard';

// Services
import { AuthService } from './services/auth.service';
import { AuthorizationService } from './services/authorization.service';
import { LanguageService } from './services/language.service';

@NgModule({
  declarations: [
    HasPermissionDirective,
    HasRoleDirective,
    IsAuthenticatedDirective
  ],
  imports: [
    CommonModule
  ],
  providers: [
    // Services
    AuthService,
    AuthorizationService,
    LanguageService,
    
    // Guards
    AuthGuard,
    NoAuthGuard,
    RoleGuard,
    PermissionGuard,
    
    // HTTP Interceptors
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SecurityHeadersInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: CsrfInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }
  ],
  exports: [
    HasPermissionDirective,
    HasRoleDirective,
    IsAuthenticatedDirective
  ]
})
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    if (parentModule) {
      throw new Error('CoreModule is already loaded. Import it in the AppModule only.');
    }
  }
}
