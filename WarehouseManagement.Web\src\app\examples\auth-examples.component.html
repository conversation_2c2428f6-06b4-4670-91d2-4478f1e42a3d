<!-- 
  This file demonstrates how to use the authentication and authorization directives
  in your Angular templates. Copy these examples to your actual components.
-->

<!-- 1. Basic Authentication Check -->
<div *appIsAuthenticated="true">
  <h2>Welcome! You are logged in.</h2>
  <p>This content is only visible to authenticated users.</p>
</div>

<div *appIsAuthenticated="false">
  <h2>Please log in to continue</h2>
  <p>This content is only visible to non-authenticated users.</p>
</div>

<!-- 2. Role-Based Access Control -->
<div *appHasRole="'admin'">
  <h3>Admin Panel</h3>
  <p>This section is only visible to administrators.</p>
  <button pButton label="Manage Users" class="p-button-danger"></button>
</div>

<div *appHasRole="['admin', 'manager']">
  <h3>Management Tools</h3>
  <p>This section is visible to both admins and managers.</p>
  <button pButton label="View Reports" class="p-button-info"></button>
</div>

<!-- 3. Permission-Based Access Control -->
<div *appHasPermission="'inventory:read'">
  <h3>Inventory Overview</h3>
  <p>You can view inventory information.</p>
</div>

<div *appHasPermission="'inventory:create'">
  <button pButton label="Add New Item" icon="pi pi-plus" class="p-button-success"></button>
</div>

<div *appHasPermission="'inventory:update'">
  <button pButton label="Edit Item" icon="pi pi-pencil" class="p-button-warning"></button>
</div>

<div *appHasPermission="'inventory:delete'">
  <button pButton label="Delete Item" icon="pi pi-trash" class="p-button-danger"></button>
</div>

<!-- 4. Complex Permission Checks -->
<div *appHasPermission="[
  {resource: 'products', action: 'read'},
  {resource: 'categories', action: 'read'}
]" [appHasPermissionRequireAll]="true">
  <h3>Product Management</h3>
  <p>You can view both products and categories.</p>
</div>

<div *appHasPermission="[
  {resource: 'reports', action: 'read'},
  {resource: 'audit_logs', action: 'read'}
]" [appHasPermissionRequireAll]="false">
  <h3>Analytics</h3>
  <p>You can view either reports or audit logs (or both).</p>
</div>

<!-- 5. Using with Resource and Action Inputs -->
<div *appHasPermission 
     appHasPermissionResource="customers" 
     appHasPermissionAction="create">
  <button pButton label="Add Customer" icon="pi pi-user-plus"></button>
</div>

<!-- 6. Conditional Menu Items -->
<p-menubar [model]="menuItems">
  <ng-template pTemplate="start">
    <img src="assets/logo.png" height="40" class="mr-2">
  </ng-template>
  
  <ng-template pTemplate="end">
    <!-- User Profile Menu -->
    <div *appIsAuthenticated="true" class="flex align-items-center gap-2">
      <!-- Notifications (Admin and Manager only) -->
      <button *appHasRole="['admin', 'manager']"
              pButton 
              type="button" 
              icon="pi pi-bell" 
              class="p-button-rounded p-button-text">
      </button>
      
      <!-- Settings (Admin only) -->
      <button *appHasRole="'admin'"
              pButton 
              type="button" 
              icon="pi pi-cog" 
              class="p-button-rounded p-button-text">
      </button>
      
      <!-- User Menu -->
      <p-menu #userMenu [model]="userMenuItems" [popup]="true"></p-menu>
      <button pButton 
              type="button" 
              icon="pi pi-user" 
              class="p-button-rounded p-button-text"
              (click)="userMenu.toggle($event)">
      </button>
    </div>
  </ng-template>
</p-menubar>

<!-- 7. Data Table with Conditional Actions -->
<p-table [value]="products" [paginator]="true" [rows]="10">
  <ng-template pTemplate="header">
    <tr>
      <th>Name</th>
      <th>Category</th>
      <th>Price</th>
      <th>Stock</th>
      <th *appHasPermission="'products:update'">Actions</th>
    </tr>
  </ng-template>
  
  <ng-template pTemplate="body" let-product>
    <tr>
      <td>{{ product.name }}</td>
      <td>{{ product.category }}</td>
      <td>{{ product.price | currency }}</td>
      <td>{{ product.stock }}</td>
      <td *appHasPermission="'products:update'">
        <div class="flex gap-2">
          <button *appHasPermission="'products:update'"
                  pButton 
                  icon="pi pi-pencil" 
                  class="p-button-rounded p-button-text p-button-warning"
                  (click)="editProduct(product)">
          </button>
          
          <button *appHasPermission="'products:delete'"
                  pButton 
                  icon="pi pi-trash" 
                  class="p-button-rounded p-button-text p-button-danger"
                  (click)="deleteProduct(product)">
          </button>
        </div>
      </td>
    </tr>
  </ng-template>
</p-table>

<!-- 8. Form with Conditional Fields -->
<form [formGroup]="productForm" (ngSubmit)="onSubmit()">
  <div class="field">
    <label for="name">Product Name</label>
    <input id="name" type="text" pInputText formControlName="name">
  </div>
  
  <div class="field">
    <label for="price">Price</label>
    <input id="price" type="number" pInputText formControlName="price">
  </div>
  
  <!-- Advanced pricing (Manager and Admin only) -->
  <div *appHasRole="['admin', 'manager']" class="field">
    <label for="costPrice">Cost Price</label>
    <input id="costPrice" type="number" pInputText formControlName="costPrice">
  </div>
  
  <!-- Supplier information (Admin only) -->
  <div *appHasRole="'admin'" class="field">
    <label for="supplier">Supplier</label>
    <p-dropdown formControlName="supplier" 
                [options]="suppliers" 
                optionLabel="name" 
                optionValue="id">
    </p-dropdown>
  </div>
  
  <div class="flex gap-2">
    <button type="submit" 
            pButton 
            label="Save" 
            [disabled]="productForm.invalid">
    </button>
    
    <button *appHasPermission="'products:delete'"
            type="button" 
            pButton 
            label="Delete" 
            class="p-button-danger"
            (click)="deleteProduct()">
    </button>
  </div>
</form>

<!-- 9. Dashboard Widgets with Role-Based Visibility -->
<div class="grid">
  <!-- Basic stats (All authenticated users) -->
  <div *appIsAuthenticated="true" class="col-12 md:col-6 lg:col-3">
    <div class="card">
      <h5>Total Products</h5>
      <p class="text-2xl font-bold">{{ totalProducts }}</p>
    </div>
  </div>
  
  <!-- Financial data (Manager and Admin only) -->
  <div *appHasRole="['admin', 'manager']" class="col-12 md:col-6 lg:col-3">
    <div class="card">
      <h5>Monthly Revenue</h5>
      <p class="text-2xl font-bold text-green-500">{{ monthlyRevenue | currency }}</p>
    </div>
  </div>
  
  <!-- System metrics (Admin only) -->
  <div *appHasRole="'admin'" class="col-12 md:col-6 lg:col-3">
    <div class="card">
      <h5>Active Users</h5>
      <p class="text-2xl font-bold text-blue-500">{{ activeUsers }}</p>
    </div>
  </div>
  
  <!-- Audit information (Admin only) -->
  <div *appHasPermission="'audit_logs:read'" class="col-12 md:col-6 lg:col-3">
    <div class="card">
      <h5>Security Events</h5>
      <p class="text-2xl font-bold text-orange-500">{{ securityEvents }}</p>
    </div>
  </div>
</div>

<!-- 10. Error Handling for Unauthorized Access -->
<div *appIsAuthenticated="false" class="text-center p-4">
  <i class="pi pi-lock text-6xl text-gray-400 mb-3"></i>
  <h3>Authentication Required</h3>
  <p class="text-gray-600 mb-4">Please log in to access this content.</p>
  <button pButton label="Go to Login" routerLink="/login"></button>
</div>

<!-- 11. Progressive Enhancement Based on Permissions -->
<div class="card">
  <h3>Inventory Management</h3>
  
  <!-- Basic view for all users with inventory:read -->
  <div *appHasPermission="'inventory:read'">
    <p-table [value]="inventoryItems" [paginator]="true" [rows]="10">
      <!-- Basic columns -->
    </p-table>
  </div>
  
  <!-- Enhanced view for users with inventory:update -->
  <div *appHasPermission="'inventory:update'" class="mt-3">
    <button pButton label="Bulk Update" icon="pi pi-upload"></button>
    <button pButton label="Export" icon="pi pi-download" class="ml-2"></button>
  </div>
  
  <!-- Advanced features for users with inventory:manage -->
  <div *appHasPermission="'inventory:manage'" class="mt-3">
    <button pButton label="Advanced Settings" icon="pi pi-cog" class="p-button-secondary"></button>
    <button pButton label="Audit Trail" icon="pi pi-history" class="p-button-info ml-2"></button>
  </div>
</div>
